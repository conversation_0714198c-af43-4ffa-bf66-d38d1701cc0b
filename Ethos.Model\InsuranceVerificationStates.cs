using System.Text.Json.Nodes;
using System.Text.Json.Serialization;

namespace Ethos.Model;

// Base type for all fine-grained job states
[JsonPolymorphic(TypeDiscriminatorPropertyName = "$stateType")]
// --- Initial & Preparation ---
[JsonDerivedType(typeof(VerificationStart), "JobInitialized")]
[JsonDerivedType(typeof(ErrorResourceNotFound), "ErrorResourceNotFound")]
[JsonDerivedType(typeof(ErrorWebhookTimeout), "ErrorWebhookTimeout")]
[JsonDerivedType(typeof(ErrorStuckProcessing), "ErrorStuckProcessing")]
// --- Verification Flow ---
[JsonDerivedType(typeof(VerificationSubmittedAwaitingWebhook), "VerificationSubmittedAwaitingWebhook")]
[JsonDerivedType(typeof(VerificationWebhookReceivedNeedsProcessing), "VerificationWebhookReceivedNeedsProcessing")]
[JsonDerivedType(typeof(VerificationApiCallFailed), "VerificationApiCallFailed")]
[JsonDerivedType(typeof(VerificationSuccessfulAuthNotRequired), "VerificationSuccessfulAuthNotRequired")]
[JsonDerivedType(typeof(VerificationFailedNoCoverageOrDenied), "VerificationFailedNoCoverageOrDenied")]
[JsonDerivedType(typeof(VerificationSuccessfulAuthRequired), "VerificationSuccessfulAuthRequired")]
[JsonDerivedType(typeof(VerificationIndeterminateWebhookVerification), "VerificationIndeterminateWebhookVerification")]
// --- Authorization Flow ---
[JsonDerivedType(typeof(AuthorizationStart), "AuthorizationStart")]
[JsonDerivedType(typeof(AuthorizationSubmittedAwaitingWebhook), "AuthorizationSubmittedAwaitingWebhook")]
[JsonDerivedType(typeof(AuthorizationWebhookReceivedNeedsProcessing), "AuthorizationWebhookReceivedNeedsProcessing")]
[JsonDerivedType(typeof(AuthorizationApiCallFailed), "AuthorizationApiCallFailed")]
[JsonDerivedType(typeof(AuthorizationSuccessful), "AuthorizationSuccessful")]
[JsonDerivedType(typeof(AuthorizationFailedDenied), "AuthorizationFailedDenied")]
[JsonDerivedType(typeof(AuthorizationIndeterminateWebhook), "AuthorizationIndeterminateWebhook")]
public abstract record InsuranceVerificationJobState
{
    public abstract CoarseJobState CoarseJobState { get; }

    public sealed record ErrorResourceNotFound(string ResourceType, Guid ResourceId, string Message)
        : InsuranceVerificationJobState
    {
        public override CoarseJobState CoarseJobState => new CoarseJobState.Error($"Resource not found: {Message}");
    }

    public sealed record ErrorWebhookTimeout(string Message)
        : InsuranceVerificationJobState()
    {
        public override CoarseJobState CoarseJobState => new CoarseJobState.Error($"Webhook timed out: {Message}");
    }

    public sealed record ErrorStuckProcessing(string Message)
        : InsuranceVerificationJobState()
    {
        public override CoarseJobState CoarseJobState => new CoarseJobState.Error($"Processing stalled: {Message}");
    }
    
    // Verification Flow
    public sealed record VerificationStart()
        : InsuranceVerificationJobState()
    {
        public override CoarseJobState CoarseJobState => new CoarseJobState.Pending();
    }

    public sealed record VerificationSubmittedAwaitingWebhook(string CorrelationId, DateTime WebhookTimeout)
        : InsuranceVerificationJobState
    {
        public override CoarseJobState CoarseJobState => new CoarseJobState.WaitingForExternalAction(WebhookTimeout);
    }

    public record VerificationWebhookReceivedNeedsProcessing(string CorrelationId, JsonObject LastWebhookPayload)
        : InsuranceVerificationJobState
    {
        public override CoarseJobState CoarseJobState => new CoarseJobState.Pending();
    }

    public record VerificationApiCallFailed(string CorrelationId, string ErrorMessage)
        : InsuranceVerificationJobState
    {
        public override CoarseJobState CoarseJobState => new CoarseJobState.Error($"API call failed: {ErrorMessage}");
    }

    public record VerificationSuccessfulAuthNotRequired(JsonObject VerificationWebhookPayload)
        : InsuranceVerificationJobState
    {
        public override CoarseJobState CoarseJobState =>
            new CoarseJobState.Completed(true, "Verification successful, authorization not required.");
    }

    public record VerificationFailedNoCoverageOrDenied(JsonObject VerificationWebhookPayload, string? CoverageStatus)
        : InsuranceVerificationJobState
    {
        public override CoarseJobState CoarseJobState => new CoarseJobState.Completed(false,
            $"Authorization not required, but coverage status is: {CoverageStatus}");
    }

    public record VerificationSuccessfulAuthRequired(string CorrelationId, JsonObject VerificationWebhookPayload)
        : InsuranceVerificationJobState
    {
        public override CoarseJobState CoarseJobState => new CoarseJobState.Pending();
    }

    public record VerificationIndeterminateWebhookVerification(
        JsonObject VerificationWebhookPayload,
        string? FastAuthStatus,
        string? CoverageStatus,
        string? AuthRequired)
        : InsuranceVerificationJobState
    {
        public override CoarseJobState CoarseJobState =>
            new CoarseJobState.Error($"Unhandled webhook status combination.");
    }
    
    // Authorization Flow
    public sealed record AuthorizationStart(string CorrelationId, JsonObject VerificationWebhookPayload)
        : InsuranceVerificationJobState
    {
        public override CoarseJobState CoarseJobState => new CoarseJobState.Pending();
    }

    public sealed record AuthorizationSubmittedAwaitingWebhook(string CorrelationId, DateTime WebhookTimeout)
        : InsuranceVerificationJobState
    {
        public override CoarseJobState CoarseJobState => new CoarseJobState.WaitingForExternalAction(WebhookTimeout);
    }

    public record AuthorizationWebhookReceivedNeedsProcessing(string CorrelationId, JsonObject LastWebhookPayload)
        : InsuranceVerificationJobState
    {
        public override CoarseJobState CoarseJobState => new CoarseJobState.Pending();
    }

    public record AuthorizationApiCallFailed(string CorrelationId, string ErrorMessage)
        : InsuranceVerificationJobState
    {
        public override CoarseJobState CoarseJobState =>
            new CoarseJobState.Error($"Authorization API call failed: {ErrorMessage}");
    }

    public record AuthorizationSuccessful(
        string CorrelationId,
        JsonObject AuthorizationWebhookPayload,
        string? AuthId,
        DateTime? EffectiveDate,
        DateTime? ExpirationDate)
        : InsuranceVerificationJobState
    {
        public override CoarseJobState CoarseJobState => new CoarseJobState.Completed(true, "Authorization successful.");
    }

    public record AuthorizationFailedDenied(
        string CorrelationId,
        JsonObject AuthorizationWebhookPayload,
        string? DenialReason)
        : InsuranceVerificationJobState
    {
        public override CoarseJobState CoarseJobState =>
            new CoarseJobState.Completed(false, $"Authorization denied. Reason: {DenialReason}");
    }

    public record AuthorizationIndeterminateWebhook(
        string CorrelationId,
        JsonObject AuthorizationWebhookPayload,
        string? FastAuthStatus)
        : InsuranceVerificationJobState
    {
        public override CoarseJobState CoarseJobState =>
            new CoarseJobState.Error("Indeterminate authorization status from webhook.");
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$coarseStateType")]
[JsonDerivedType(typeof(Pending), "Pending")]
[JsonDerivedType(typeof(WaitingForExternalAction), "WaitingForExternalAction")]
[JsonDerivedType(typeof(WaitingForManualAction), "WaitingForManualAction")] // New for states like NeedsInfo
[JsonDerivedType(typeof(Processing), "Processing")]
[JsonDerivedType(typeof(Completed), "Completed")]
[JsonDerivedType(typeof(Error), "Error")]
public abstract record CoarseJobState
{
    // Job is waiting for an automated processing cycle.
    // WaitingUntil can be used for scheduled retries or delayed starts.
    public sealed record Pending(DateTime? NextRetryTimestamp = null) : CoarseJobState;

    // Job is waiting for an external system (e.g., webhook response).
    public sealed record WaitingForExternalAction(DateTime Timeout) : CoarseJobState;

    // Job is waiting for a human to take action.
    public sealed record WaitingForManualAction(string Reason) : CoarseJobState;

    // Job is actively being processed by a service instance.
    public sealed record Processing : CoarseJobState;

    // Job has reached a terminal state.
    public sealed record Completed(bool Succeeded, string OutcomeMessage) : CoarseJobState;

    // Job is in an error state that might be recoverable or require manual intervention.
    public sealed record Error(string ErrorMessage, InsuranceVerificationJobState? FineGrainedStateOnError = null) : CoarseJobState;
}