import { ApiPropertyOptional } from '@nestjs/swagger';
import { BaseFiltersDto } from '@app/common/dto/base.filters.dto';
import { EEquipmentSort } from '@app/modules/equipment/enums';
import { IsEnum, IsOptional, IsString } from 'class-validator';

export class EquipmentFiltersDto extends BaseFiltersDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ enum: EEquipmentSort })
  @IsOptional()
  @IsEnum(EEquipmentSort)
  orderField: EEquipmentSort = EEquipmentSort.CreatedAt;
}
