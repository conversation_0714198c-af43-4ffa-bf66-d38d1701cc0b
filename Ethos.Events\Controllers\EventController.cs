﻿using Ethos.Auth;
using Ethos.Utilities.Filtering;
using Ethos.Utilities.Pagination;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Events.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Authorize]
    [Route("api/events")]
    [EthosAuthFeature(Name = FeatureConstants.Core)]
    public class EventController : ControllerBase
    {
        private readonly ILogger<EventController> _logger;
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly EventDbContext _dbContext;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="scopeFactory"></param>
        /// <param name="dbContext"></param>
        public EventController(
            ILogger<EventController> logger,
            IServiceScopeFactory scopeFactory,
            EventDbContext dbContext)
        {
            _logger = logger;
            _scopeFactory = scopeFactory;
            _dbContext = dbContext;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="e"></param>
        /// <returns></returns>
        EventDto GetDto(Event e)
        {
            return new EventDto()
            {
                EventData = e.EventData,
                Id = e.Id,
                Code = e.Code,
                Name = e.Name,
                EventTime = e.EventTime,
                Type = e.Type,
                GeneratedBy = e.GeneratedBy,
                TenantId = e.TenantId,
                CorrelationIds = [.. e.CorrelationIds?.Select(ci => new CorrelationIdDto()
                                {
                                    Type = ci.Type,
                                    Value = ci.Value
                                })]
            };
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="evt"></param>
        /// <returns></returns>
        [HttpPost]
        [EthosAuthScope(ScopeDefinitions.EventWrite)]
        public async Task<IActionResult> PostEvent([FromBody] EventDto evt)
        { 
            if (evt is null)
                return Problem("Event entity is required.", null, 400);

            if (string.IsNullOrEmpty(evt.Type))
                return Problem("Event type is required.", null, 400);

            if (evt.CorrelationIds.Any(ci => string.IsNullOrEmpty(ci.Type) || string.IsNullOrEmpty(ci.Value)))
                return Problem("Event correlation IDs must have a type and value.", null, 400);

            if (string.IsNullOrEmpty(evt.Name))
                evt.Name = evt.Code ?? "Unknown";

            if (string.IsNullOrEmpty(evt.GeneratedBy))
                evt.GeneratedBy = HttpContext.User.GetUniqueId() ?? "Unknown";

            if (!evt.EventTime.HasValue || evt.EventTime.Value == default)
                evt.EventTime = DateTimeOffset.UtcNow;

            var isPlatformAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.PlatformAdministrator);
            var tenantId = this.GetTenantIdOrDefault();

            if (tenantId != Guid.Empty)
            {
                if ((!evt.TenantId.HasValue || evt.TenantId.Value == Guid.Empty) && !isPlatformAdmin)
                    evt.TenantId = tenantId;
            }
            else if (!isPlatformAdmin)
                evt.TenantId = null;

            // Begin transaction
            using var transaction = await _dbContext.Database.BeginTransactionAsync();
            
            try
            {
                var newEvt = new Event()
                {
                    Type = evt.Type,
                    Code = evt.Code,
                    EventData = evt.EventData,
                    Name = evt.Name,
                    EventTime = evt.EventTime.Value,
                    GeneratedBy = evt.GeneratedBy,
                    TenantId = evt.TenantId,
                    Id = Guid.NewGuid(),
                };

                _dbContext.Events.Add(newEvt);

                await _dbContext.SaveChangesAsync();

                if (evt.CorrelationIds?.Count > 0)
                {
                    foreach (var eci in evt.CorrelationIds.Distinct())
                    {
                        _dbContext.CorrelationIds.Add(new EventCorrelationId()
                        {
                            Type = eci.Type,
                            Value = eci.Value,
                            EventId = newEvt.Id,
                        });
                    }
                }

                await _dbContext.SaveChangesAsync();
                await transaction.CommitAsync();
                return CreatedAtAction(nameof(GetEventById), new { id = newEvt.Id }, GetDto(newEvt));
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error creating event.");
                return StatusCode(500, "An error occurred while processing your request.");
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.EventRead)]
        public async Task<IActionResult> GetEvents([FromQuery] PagingParameters pagingParameters, [FromQuery] string? filter = null)
        {
            var isPlatformAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.PlatformAdministrator);
            var tenantId = this.GetTenantIdOrDefault();

            try
            {
                var evts = _dbContext.Events
                                     .Include(e => e.CorrelationIds)
                                     .Where(e => e.TenantId == tenantId || isPlatformAdmin)
                                     .Filter(filter)
                                     .OrderByDescending(e => e.EventTime);

                return Ok(await evts.PaginateWithLinksAsync<Event, EventDto>(this, (evts) =>
                {
                    return [.. evts.Select(e => GetDto(e))];
                }, pagingParameters.limit, pagingParameters.offset));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving events");
                return StatusCode(500, "An error occurred while processing your request.");
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        [EthosAuthScope(ScopeDefinitions.EventRead)]
        public async Task<IActionResult> GetEventById(Guid id)
        {
            if (id == Guid.Empty)
                return BadRequest();

            var isPlatformAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.PlatformAdministrator);
            var tenantId = this.GetTenantIdOrDefault();

            try
            {
                var evt = await _dbContext.Events
                                          .Include(e => e.CorrelationIds)
                                          .FirstOrDefaultAsync(e => e.Id == id && (isPlatformAdmin || e.TenantId == tenantId));
                if (evt is null)
                    return NotFound();

                return Ok(GetDto(evt));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving event ID: {EventId}", id);
                return Problem("Unexpected server error while retrieving event.", null, 500);
            }       
        }
    }
}
