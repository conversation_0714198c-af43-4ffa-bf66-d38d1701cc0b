import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsInt, IsOptional, IsString, ValidateNested } from 'class-validator';
import { CreateStudyCredentialDto } from '@app/modules/study/dto/create.study.credential.dto';
import { CreateStudyEquipmentDto } from '@app/modules/study/dto/create.study.equipment.dto';

export class UpdateStudyDto {
  @ApiProperty()
  @IsInt()
  id: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ type: CreateStudyCredentialDto, isArray: true })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateStudyCredentialDto)
  credentials: CreateStudyCredentialDto[];

  @ApiPropertyOptional({ type: CreateStudyEquipmentDto, isArray: true })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateStudyEquipmentDto)
  equipments?: CreateStudyEquipmentDto[];
}
