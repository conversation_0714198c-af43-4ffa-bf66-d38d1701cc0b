using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Ethos.Model;
using Ethos.Workflows.Api;
using Ethos.Workflows.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class CareLocationController(DbContext dbContext)
    : EntityControllerBase<CareLocationDbo, CreateCareLocationDto, CareLocationDto, CareLocationQ>(dbContext)
{
    protected override CareLocationDto MapToDto(CareLocationDbo dbo) => dbo.ToDto();

    protected override CareLocationDbo CreateOrUpdateEntity(CareLocationDbo? entity, CreateCareLocationDto input,
        Guid? requiredId = null)
    {
        if (entity == null)
        {
            var parentCareLocation = _dbSet
                .FirstOrDefault(cl => cl.Id == input.ParentServiceLocationId);
            
            entity = new CareLocationDbo {
                Id = Guid.NewGuid(),
                Name = input.Name,
                CareLocationPath = parentCareLocation != null
                    ? $"{parentCareLocation.CareLocationPath}/{parentCareLocation.Id}"
                    : "/",
                ParentCareLocation = parentCareLocation,
                ParentProviderId = input.ParentProviderId,
                ContactDetail = input.ContactDetail?.ToEntity(),
                Equipment = []
            };
        }
        else
        {
            if (input.Name != null)
            {
                entity.Name = input.Name;
            }
            
            var parentCareLocation = _dbSet
                .FirstOrDefault(cl => cl.Id == (input.ParentServiceLocationId ?? entity.ParentCareLocationId));

            if (input.ParentProviderId != null)
            {
                entity.ParentProviderId = input.ParentProviderId.Value;
            }

            entity.ParentCareLocation = parentCareLocation;
        }
        return entity;
    }
}