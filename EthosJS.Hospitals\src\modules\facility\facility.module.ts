import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FacilityRepository } from '@app/modules/facility/facility.repository';
import { FacilityService } from '@app/modules/facility/facility.service';
import { FacilityController } from '@app/modules/facility/facility.controller';
import { ClinicModule } from '@app/modules/clinic/clinic.module';
import { CityModule } from '@app/modules/city/city.module';
import { EquipmentModule } from '@app/modules/equipment/equipment.module';
import { BedScheduleModule } from '@app/modules/bedSchedule/bed.schedule.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      FacilityRepository,
    ]),
    ClinicModule,
    CityModule,
    EquipmentModule,
    forwardRef(() => BedScheduleModule),
  ],
  providers: [FacilityService],
  controllers: [FacilityController],
  exports: [FacilityService],
})
export class FacilityModule {}
