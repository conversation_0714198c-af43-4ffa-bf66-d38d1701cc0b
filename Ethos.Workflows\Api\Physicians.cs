using System.Net.Http.Headers;
using System.Net.Http.Json;
using Ethos.Model;
using Ethos.Utilities.Pagination;
using Ethos.Workflows.Controllers;

namespace Ethos.Workflows.Api;

// Input DTO
public sealed record CreatePhysicianDto : IInputDto
{
    public required IReadOnlyList<PersonNameDto> Names { get; set; }
    public required DemographicsDto? Demographics { get; set; }
    public required PersonalContactDetailDto? ContactInformation { get; set; }
    public required IReadOnlyList<Guid>? CareLocationIds { get; set; }
    public required IReadOnlyList<IdentifierDto>? Identifiers { get; set; }
}
    
// Output DTO
public sealed record PhysicianDto
{
    public required Guid Id { get; set; }
    public required IReadOnlyList<PersonNameDto> Names { get; set; }
    public required DemographicsDto? Demographics { get; set; }
    public required PersonalContactDetailDto? ContactInformation { get; set; }
    public required IReadOnlyList<Guid> CareLocationIds { get; set; }
    public required IReadOnlyList<IdentifierDto> Identifiers { get; set; }
}

public interface IPhysicianApi : IEntityHttpClient<CreatePhysicianDto, PhysicianDto, PhysicianQ>;

public class PhysicianHttpClient(System.Net.Http.HttpClient httpClient)
    : EntityHttpClientBase<CreatePhysicianDto, PhysicianDto, PhysicianQ>(httpClient, "physician"),
        IPhysicianApi;