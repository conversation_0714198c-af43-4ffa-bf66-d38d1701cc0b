import { Column, Entity, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { BaseEntity } from '@app/common/base.entity';
import { ClinicEntity } from '@app/modules/clinic/clinic.entity';
import { BedScheduleEntity } from '@app/modules/bedSchedule/bed.schedule.entity';
import { TechnicianScheduleEntity } from '@app/modules/technicianSchedule/technician.schedule.entity';
import { ScheduleEntity } from '@app/modules/schedule/schedule.entity';
import { CityEntity } from '@app/modules/city/city.entity';
import { IFacilityEquipment } from '@app/modules/facility/types';

@Entity({ name: 'facilities' })
export class FacilityEntity extends BaseEntity {
  @Column()
  name: string;

  @Column({ nullable: true })
  addressLine1?: string;

  @Column({ nullable: true })
  addressLine2?: string;

  @Column({ nullable: true })
  zip?: string;

  @Column({ nullable: true })
  phone?: string;

  @Column({ nullable: true })
  fax?: string;

  @Column()
  capacity: number;

  @Column()
  cityId: number;

  @ManyToOne(
    () => CityEntity,
    city => city.facilities,
  )
  @JoinColumn()
  city: CityEntity;

  @Column()
  clinicId: number;

  @ManyToOne(
    () => ClinicEntity,
    clinic => clinic.facilities,
    { onDelete: 'CASCADE' }
  )
  @JoinColumn()
  clinic: ClinicEntity;

  @OneToMany(
    () => BedScheduleEntity,
    bedSchedule => bedSchedule.facility,
  )
  bedSchedules: BedScheduleEntity[];

  @OneToMany(
    () => TechnicianScheduleEntity,
    technicianSchedule => technicianSchedule.facility,
  )
  technicianSchedules: TechnicianScheduleEntity[];

  @OneToMany(
    () => ScheduleEntity,
    schedule => schedule.facility,
  )
  schedules: ScheduleEntity[];

  @Column({ type: 'jsonb', default: '{}' })
  equipments: Record<number, IFacilityEquipment>
}
