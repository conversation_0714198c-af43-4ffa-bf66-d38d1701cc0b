﻿
namespace Ethos.Auth
{
    /// <summary>
    /// 
    /// </summary>
    public class JwtProduct : JwtId
    {
        public string Name { get; set; } = null!;
        public JwtTenant? Account { get; set; }
        public JwtProductLicense? License { get; set; }
        public IList<string> Permissions { get; set; } = [];
        public IList<JwtFeature> Features { get; set; } = [];
    }

    /// <summary>
    /// 
    /// </summary>
    public class JwtFeature : JwtId
    {
        public string Name { get; set; } = null!;
    }

    /// <summary>
    /// 
    /// </summary>
    public class JwtId
    {
        public Guid Id { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class JwtTenant : JwtId
    {
    }

    /// <summary>
    /// 
    /// </summary>
    public class JwtProductLicense : JwtId
    {
    }

    /// <summary>
    /// 
    /// </summary>
    public class JwtProducts : List<JwtProduct>, IList<JwtProduct>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="feature"></param>
        /// <returns></returns>
        public bool HasFeature(string feature)
        {
            return this.Any(p => p.Features.Any(f => string.Equals(f.Name, feature, StringComparison.OrdinalIgnoreCase)));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="featureId"></param>
        /// <returns></returns>
        public bool HasFeature(Guid featureId)
        {
            return this.Any(p => p.Features.Any(f => featureId == f.Id));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public List<Guid> GetTenants()
        {
            return [.. this.Where(p => p.Account is not null).Select(p => p.Account!.Id)];
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public List<Guid> GetLicenses()
        {
            return [.. this.Where(p => p.License is not null).Select(p => p.License!.Id)];
        }
    }
}
