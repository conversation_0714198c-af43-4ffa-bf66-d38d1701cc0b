using System.Collections;
using System.Linq.Expressions;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class CareLocationDbo : IAuditableEntity<CareLocationDbo>
{
    public required string Name { get; set; }
    
    public Guid? ParentCareLocationId { get; set; }
    public string CareLocationPath { get; set; } = null!;// concatenation of all parent care locations
    public virtual CareLocationDbo? ParentCareLocation { get; set; } = null!;
    
    public Guid? ParentProviderId { get; set; }
    public virtual ProviderDbo? ParentProvider { get; set; } = null!;
    
    public ICollection<IdentifierDbo> Identifiers { get; set; } = new List<IdentifierDbo>();
    
    public Guid? ContactDetailId { get; set; }
    public virtual OrganizationContactDetailDbo? ContactDetail { get; set; } = null!;
    
    public required ICollection<EquipmentDbo> Equipment { get; set; } = new List<EquipmentDbo>();
    
    public ICollection<long> SupportedEncounterTypes { get; set; } = new List<long>();
    public ICollection<long> SupportedStudyTypes { get; set; } = new List<long>();
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<CareLocationDbo>(Register);

    public new static void Register(EntityTypeBuilder<CareLocationDbo> entity)
    {
        IAuditableEntity<CareLocationDbo>.Register(entity);
        
        entity.Property(e => e.Name)
            .HasMaxLength(200).IsRequired();
        entity.HasIndex(e => e.Name)
            .HasDatabaseName($"IX_{nameof(CareLocationDbo)}_Name");
        
        entity.Property(e => e.CareLocationPath).HasMaxLength(200).IsRequired();
        entity.HasIndex(e => e.CareLocationPath)
            .HasDatabaseName($"IX_{nameof(CareLocationDbo)}_Path");
        
        entity.OwnsMany(p => p.Identifiers, identifiers =>
        {
            identifiers.ToTable($"{nameof(CareLocationDbo)}_{nameof(Identifiers)}", IEntity.DefaultSchema);
            identifiers.WithOwner().HasForeignKey($"{nameof(CareLocationDbo)}Id");
            identifiers.Property<int>("Id").ValueGeneratedOnAdd();
            identifiers.HasKey("Id");
            IdentifierDbo.Configure(identifiers);
        });
        
        entity.HasOne<OrganizationContactDetailDbo>(e => e.ContactDetail)
            .WithMany()
            .HasForeignKey(e => e.ContactDetailId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Cascade);
        
        entity.HasOne<ProviderDbo>(e => e.ParentProvider)
            .WithMany()
            .HasForeignKey(e => e.ParentProviderId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Cascade);

        entity.HasOne<CareLocationDbo>(e => e.ParentCareLocation)
            .WithMany()
            .HasForeignKey(e => e.ParentCareLocationId)
            .OnDelete(DeleteBehavior.Cascade);

        entity.Property(e => e.SupportedEncounterTypes);
        entity.Property(e => e.SupportedStudyTypes);
        
        entity.HasMany(e => e.Equipment)
            .WithOne(e => e.CareLocation)
            .HasForeignKey(e => e.CareLocationId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(CareLocationQ.WithId), "WithId")]
[JsonDerivedType(typeof(CareLocationQ.WithName), "WithName")]
[JsonDerivedType(typeof(CareLocationQ.WithApproximateName), "WithApproximateName")]
[JsonDerivedType(typeof(CareLocationQ.WithIdentifier), "WithIdentifier")]
public abstract record CareLocationQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : CareLocationQ;
    public sealed record WithName(string Name) : CareLocationQ;
    public sealed record WithApproximateName(string Name) : CareLocationQ;
    public sealed record WithIdentifier(string System, string Value) : CareLocationQ;
    
    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithId id => Expression.Equal(Expression.Property(self, nameof(CareLocationDbo.Id)), Expression.Constant(id.Id)),
            WithName name => Expression.Equal(Expression.Property(self, nameof(CareLocationDbo.Name)), Expression.Constant(name.Name)),
            WithApproximateName name => QueryExpressions.BuildSimpleApproximateNamePredicate<CareLocationDbo>(
                Expression.Property(self, nameof(CareLocationDbo.Name)), name.Name),
            WithIdentifier(var system, var value) => QueryExpressions.BuildIdentifierPredicate(system, value, self),
            _ => throw new NotImplementedException()
        };
    }
}