﻿
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Ethos.Auth
{
    /// <summary>
    /// 
    /// </summary>
    public static class ScopeRegistry
    {
        /// <summary>
        /// 
        /// </summary>
        readonly static List<EthosScope> scopes = [];

        /// <summary>
        /// 
        /// </summary>
        /// <param name="scope"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public static bool Register(string scope)
        {
            if (!EthosScope.TryParse(scope, null, out var _scope))
                throw new ArgumentException($"Invalid scope: {scope}", nameof(scope));
            return Register(scope);           
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="scope"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public static bool Register(EthosScope scope)
        {
            if (!scope.IsFullyQualified())
                throw new ArgumentException($"Scope is not fully qualified: {scope}", nameof(scope));

            lock (scopes)
            {
                if (!scopes.Contains(scope))
                {
                    scopes.Add(scope);
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="scope"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public static bool Unregister(EthosScope scope)
        {
            if (!scope.IsFullyQualified())
                throw new ArgumentException($"Scope is not fully qualified: {scope}", nameof(scope));

            lock (scopes)
                return scopes.Remove(scope);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="scope"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public static bool Unregister(string scope)
        {
            if (!EthosScope.TryParse(scope, null, out var _scope))
                throw new ArgumentException($"Invalid scope: {scope}", nameof(scope));
            return Unregister(scope);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="scope"></param>
        /// <returns></returns>
        public static bool IsRegistered(EthosScope scope)
        {
            lock (scopes)
                return scopes.Contains(scope);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="scope"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public static bool IsRegistered(string scope)
        {
            if (!EthosScope.TryParse(scope, null, out var _scope))
                throw new ArgumentException($"Invalid scope: {scope}", nameof(scope));
            return IsRegistered(_scope);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="controller"></param>
        /// <returns></returns>
        public static bool Register(ControllerBase controller)
        {
            var endpoint = controller.HttpContext.GetEndpoint();
            var error = false;

            if (endpoint is null)
                return false;

            var feature = endpoint.GetFeature();

            var scopes = endpoint.GetCustomAttributes<EthosAuthScopeAttribute>();

            foreach (var scope in scopes)
            {
                foreach (var s in scope.Scopes)
                {
                    if (!s.IsFullyQualified() && !string.IsNullOrEmpty(feature))
                        s.Feature = feature;

                    if (!Register(s))
                        error = true;
                }
            }
            return !error;
        }
    }
}
