using System.Net.Http.Headers;
using System.Text.RegularExpressions;
using Ethos.Model;
using Ethos.Workflows.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Controllers;

[ApiController]
[Route("api/[controller]")]
public class MockDbController : ControllerBase
{
    private readonly AppDbContext dbContext;

    public MockDbController(AppDbContext dbContext)
    {
        this.dbContext = dbContext;
    }
    
    private Regex ValidTableNameRegex = new(@"^[a-zA-Z_][a-zA-Z0-9_]*$");

    [Authorize]
    [HttpPost("delete")]
    public async Task<IActionResult> Clear([FromBody] List<string> tables)
    {
        foreach (var t in tables)
        {
            if (!ValidTableNameRegex.IsMatch(t))
            {
                return BadRequest($"Invalid table name: {t}");
            }
        }
        
        foreach (var table in tables)
        {
            await dbContext.Database.ExecuteSqlRawAsync($"DELETE FROM {table}");
        }
        
        await dbContext.SaveChangesAsync();
        return Ok("Tables cleared successfully.");
    }

    [AllowAnonymous]
    [HttpGet("reset")]
    public async Task<IActionResult> Reset()
    {
        var providerA = dbContext.Set<ProviderDbo>().Add(
            new ProviderDbo()
            {
                Name = "Mock Provider A",
                ProviderPath = "/",
            }).Entity;

        var mockLocationA = dbContext.Set<CareLocationDbo>().Add(
            new CareLocationDbo
            {
                Id = Guid.NewGuid(),
                ParentProvider = providerA,
                Name = "Mock Location A",
                CareLocationPath = "/",
                SupportedEncounterTypes = [],
                SupportedStudyTypes = [],
                Equipment = []
            }).Entity;
        var mockLocationB = dbContext.Set<CareLocationDbo>().Add(
            new CareLocationDbo
            {
                Id = Guid.NewGuid(),
                ParentProvider = providerA,
                CareLocationPath = $"{mockLocationA.Id}/",
                ParentCareLocation = mockLocationA,
                Name = "Mock Location B",
                SupportedEncounterTypes = [],
                SupportedStudyTypes = [],
                Equipment = []
            }).Entity;

        var physician = dbContext.Set<PhysicianDbo>().Add(
            new PhysicianDbo
            {
                Names = [
                    new PersonNameDbo()
                    {
                        LastName = "Doe",
                        MiddleName = null,
                        FirstName = "John"
                    }
                ]
            }).Entity;

        //dbContext.AddDefaultReferenceDataValues<RaceEntity>();
        //dbContext.AddDefaultReferenceDataValues<EthnicityEntity>();
        //dbContext.AddDefaultReferenceDataValues<GenderEntity>();
        //dbContext.AddDefaultReferenceDataValues<SexEntity>();
        //dbContext.AddDefaultReferenceDataValues<MaritalStatusEntity>();
        //dbContext.AddDefaultReferenceDataValues<EmailUseEntity>();
        //dbContext.AddDefaultReferenceDataValues<AddressTypeEntity>();
        //dbContext.AddDefaultReferenceDataValues<AddressUseEntity>();
        //dbContext.AddDefaultReferenceDataValues<CountryEntity>();
        //// addDefaultValues<StateEntity>();
        //dbContext.AddDefaultReferenceDataValues<PhoneTypeEntity>();

        await dbContext.SaveChangesAsync();

        // Grab any Gender/Sex rows (we know at least one exists after step 2)
        // var gender = await dbContext.Set<GenderEntity>()
        //     .OrderBy(e => e.Name).FirstAsync();
        // var sex = await dbContext.Set<SexEntity>()
        //     .OrderBy(e => e.Name).FirstAsync();

        var insuranceEntity = new InsuranceDbo
        {
            InsuranceCarrier = 1000, // Changed from string to long
            PolicyId = "POL‑123456",
            GroupNumber = "GRP‑654321",
            MemberId = "MEM‑987654",
            InsuranceId = "INS‑123456",
            InsuranceHolder = new InsuranceHolderDataDbo()
            {

            },
            PhoneNumber = new PhoneNumberWithUseDataDbo()
            {
                PhoneNumber = "555‑1234",
                PhoneNumberTypeId = 0,
            }
        };
        var patient = new PatientDbo
        {
            // Id auto‑generated by EF/Core or your value generator
            Names = [
                new PersonNameDbo()
                {
                    LastName = "Doe",
                    MiddleName = null,
                    FirstName = "John"
                }
            ],
            Demographics = new DemographicsDbo()
            {
                DateOfBirth = new DateOnly(1985, 4, 1),   // pick any DOB you like
                SexId = null,
                GenderId = null,
                MaritalStatusId = null,
                EthnicityId = null,
                RaceId = null
            },
            //Gender = gender,
            //Sex = sex,

            // Insurance (optional).  Keep it simple here:
            Insurances = new List<InsuranceDbo>
            {
                insuranceEntity
            }
        };

        dbContext.Set<PatientDbo>().Add(patient);
        dbContext.Set<InsuranceDbo>().Add(insuranceEntity);
        await dbContext.SaveChangesAsync();

        return Ok("Mock database reset complete.");
    }
}

public sealed class MockDbHttpClient
{
    private readonly System.Net.Http.HttpClient _http;

    public MockDbHttpClient(System.Net.Http.HttpClient httpClient)
    {
        _http = httpClient;
    }

    public void SetBearerToken(string token) =>
        _http.DefaultRequestHeaders.Authorization =
            new AuthenticationHeaderValue("Bearer", token);

    /// <summary>
    /// GET /api/mockdb/reset  
    /// Returns the plain‑text confirmation from the server.
    /// </summary>
    public async Task<string> ResetAsync()
    {
        var resp = await _http.GetAsync("api/mockdb/reset");
        resp.EnsureSuccessStatusCode();

        return await resp.Content.ReadAsStringAsync();
    }
}
