﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using Microsoft.AspNetCore.Http.Extensions;
using System.Web;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Utilities.Pagination
{
    /// <summary>
    /// 
    /// </summary>
    public static class PaginationExtensions
    {
        const string limit = nameof(limit);
        const string offset = nameof(offset);
        const string Link = nameof(Link);
        const string action = nameof(action);
        const string controller = nameof(controller);

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="query"></param>
        /// <param name="limit"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        public static PagedResponse<T> Paginate<T>(this IQueryable<T> query, int limit = 250, int offset = 0)
        {
            if (limit > PagingParameters.MaxPagingLimit)
                limit = PagingParameters.MaxPagingLimit;

            if (offset < 0)
                offset = 0;

            var totalCount = query.Count();

            var items = query.Skip(offset)
                             .Take(limit)
                             .ToList();

            return new PagedResponse<T>(items, offset, limit, totalCount);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="query"></param>
        /// <param name="limit"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        public static async Task<PagedResponse<T>> PaginateAsync<T>(this IQueryable<T> query, int limit = 250, int offset = 0)
        {
            if (limit > PagingParameters.MaxPagingLimit)
                limit = PagingParameters.MaxPagingLimit;

            if (offset < 0)
                offset = 0;

            var totalCount = await query.CountAsync();

            var items = await query.Skip(offset)
                                   .Take(limit)
                                   .ToListAsync();

            return new PagedResponse<T>(items, offset, limit, totalCount);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="pagedResponse"></param>
        /// <param name="controller"></param>
        /// <param name="controllerMethod"></param>
        /// <param name="routeParams"></param>
        /// <returns></returns>
        public static PagedResponse<T> WithLinks<T>(this PagedResponse<T> pagedResponse,
                                                    ControllerBase controller,
                                                    string controllerMethod,
                                                    IDictionary<string, object>? routeParams = null)
        {
            if (pagedResponse.Items.Count == 0)
                return pagedResponse;

            routeParams ??= new Dictionary<string, object>();

            var nextOffset = pagedResponse.GetNextOffset();
            var prevOffset = pagedResponse.GetPreviousOffset();

            if (routeParams.TryGetValue(limit, out _))
                routeParams[limit] = pagedResponse.Limit;
            else
                routeParams.Add(limit, pagedResponse.Limit);

            if (prevOffset.HasValue)
            {
                // add or update for previous link first
                if (routeParams.TryGetValue(offset, out _))
                    routeParams[offset] = prevOffset;
                else
                    routeParams.Add(offset, prevOffset);
            }

            string? prev = pagedResponse.HasPrevious() ? controller.Url.Action(controllerMethod, null, routeParams, controller.Request.Scheme) : null;

            if (nextOffset.HasValue)
            {
                // add or update for next link
                if (routeParams.TryGetValue(offset, out _))
                    routeParams[offset] = nextOffset;
                else
                    routeParams.Add(offset, nextOffset);
            }

            string? next = pagedResponse.HasNext() ? controller.Url.Action(controllerMethod, null, routeParams, controller.Request.Scheme) : null;

            var links = new Dictionary<string, string?>();

            if (!string.IsNullOrEmpty(next))
            {
                pagedResponse.Next = next;
                links.Add(nameof(next), next);
            }

            if (!string.IsNullOrEmpty(prev))
            {
                pagedResponse.Previous = prev;
                links.Add(nameof(prev), prev);
            }

            if (links.Count == 0)
                return pagedResponse;

            var linkValues = links?.Where(kvp => !string.IsNullOrEmpty(kvp.Value))
                                   .Select(kvp => $"<{kvp.Value}>; rel=\"{kvp.Key}\"");

            if (linkValues is null || !linkValues.Any())
                return pagedResponse;

            if (controller.HttpContext.Response.Headers.TryGetValue(Link, out var headerVal))
            {
                var appended = linkValues.Union(headerVal, StringComparer.OrdinalIgnoreCase);
                controller.HttpContext.Response.Headers[Link] = new StringValues([.. appended]);
            }
            else
                controller.HttpContext.Response.Headers.Append(Link, new StringValues([.. linkValues]));

            return pagedResponse;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="query"></param>
        /// <param name="controller"></param>
        /// <param name="limit"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        public static PagedResponse<T> PaginateWithLinks<T>(this IQueryable<T> query, ControllerBase controller, int limit = 250, int offset = 0)
        {
            var pagingResponse = query.Paginate(limit, offset);
            return pagingResponse.WithLinksInternal(controller);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <typeparam name="TDto"></typeparam>
        /// <param name="query"></param>
        /// <param name="controller"></param>
        /// <param name="dtoBuilder"></param>
        /// <param name="limit"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        public static async Task<PagedResponse<TDto>> PaginateWithLinksAsync<T, TDto>(this IQueryable<T> query,
                                                                                      ControllerBase controller,
                                                                                      Func<ICollection<T>, ICollection<TDto>> dtoBuilder,
                                                                                      int limit = 250,
                                                                                      int offset = 0)
        {
            var pagingResponse = await query.PaginateAsync(limit, offset);
            var dtoItems = dtoBuilder(pagingResponse.Items);
            var dtoPagedResponse = new PagedResponse<TDto>(dtoItems, pagingResponse.Offset, pagingResponse.Limit, pagingResponse.TotalCount);
            return dtoPagedResponse.WithLinksInternal(controller);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <typeparam name="TDto"></typeparam>
        /// <param name="query"></param>
        /// <param name="controller"></param>
        /// <param name="dtoBuilder"></param>
        /// <param name="limit"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        public static async Task<PagedResponse<TDto>> PaginateWithLinksAsync<T, TDto>(this IQueryable<T> query,
                                                                                      ControllerBase controller,
                                                                                      Func<ICollection<T>, Task<ICollection<TDto>>> dtoBuilder,
                                                                                      int limit = 250,
                                                                                      int offset = 0)
        {
            var pagingResponse = await query.PaginateAsync(limit, offset);
            var dtoItems = await dtoBuilder(pagingResponse.Items);
            var dtoPagedResponse = new PagedResponse<TDto>(dtoItems, pagingResponse.Offset, pagingResponse.Limit, pagingResponse.TotalCount);
            return dtoPagedResponse.WithLinksInternal(controller);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <typeparam name="TDto"></typeparam>
        /// <param name="query"></param>
        /// <param name="controller"></param>
        /// <param name="dtoBuilder"></param>
        /// <param name="limit"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        public static async Task<PagedResponse<TDto>> PaginateWithLinks<T, TDto>(this IQueryable<T> query,
                                                                                     ControllerBase controller,
                                                                                     Func<ICollection<T>, Task<ICollection<TDto>>> dtoBuilder,
                                                                                     int limit = 250,
                                                                                     int offset = 0)
        {
            var pagingResponse = query.Paginate(limit, offset);
            var dtoItems = await dtoBuilder(pagingResponse.Items);
            var dtoPagedResponse = new PagedResponse<TDto>(dtoItems, pagingResponse.Offset, pagingResponse.Limit, pagingResponse.TotalCount);
            return dtoPagedResponse.WithLinksInternal(controller);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <typeparam name="TDto"></typeparam>
        /// <param name="query"></param>
        /// <param name="controller"></param>
        /// <param name="dtoBuilder"></param>
        /// <param name="limit"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        public static PagedResponse<TDto> PaginateWithLinks<T, TDto>(this IQueryable<T> query,
                                                                     ControllerBase controller,
                                                                     Func<ICollection<T>, ICollection<TDto>> dtoBuilder,
                                                                     int limit = 250,
                                                                     int offset = 0)
        {
            var pagingResponse = query.Paginate(limit, offset);
            var dtoItems = dtoBuilder(pagingResponse.Items);
            var dtoPagedResponse = new PagedResponse<TDto>(dtoItems, pagingResponse.Offset, pagingResponse.Limit, pagingResponse.TotalCount);
            return dtoPagedResponse.WithLinksInternal(controller);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="query"></param>
        /// <param name="controller"></param>
        /// <param name="limit"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        public static async Task<PagedResponse<T>> PaginateWithLinksAsync<T>(this IQueryable<T> query, ControllerBase controller, int limit = 250, int offset = 0)
        {
            var pagingResponse = await query.PaginateAsync(limit, offset);
            return pagingResponse.WithLinksInternal(controller);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="pagedResponse"></param>
        /// <param name="controller"></param>
        /// <param name="limit"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        static PagedResponse<T> WithLinksInternal<T>(this PagedResponse<T> pagedResponse, ControllerBase controller)
        {
            if (!pagedResponse.HasPrevious() && !pagedResponse.HasNext())
                return pagedResponse;

            var actionName = string.Empty;
            var dict = new Dictionary<string, object>();

            foreach (var routeVar in controller.ControllerContext.RouteData.Values)
            {
                if (routeVar.Key == nameof(limit) || routeVar.Key == nameof(offset) || routeVar.Key == nameof(controller))
                    continue;

                if (routeVar.Key == action && routeVar.Value is string _actionName)
                {
                    actionName = _actionName;
                    continue;
                }

                dict.Add(routeVar.Key, routeVar.Value);
            }

            if (string.IsNullOrEmpty(actionName))
                return pagedResponse;

            if (Uri.TryCreate(controller.Request.GetEncodedUrl(), new UriCreationOptions(), out var uri))
            {
                // grab the query items and add to the params
                var nameValueCollection = HttpUtility.ParseQueryString(uri.Query);

                foreach (var key in nameValueCollection.AllKeys)
                {
                    if (key != null)
                    {
                        var value = nameValueCollection[key];
                        if (value is not null)
                            dict.Add(key, value);
                    }
                }
            }
            return pagedResponse.WithLinks(controller, actionName, dict);
        }
    }
}
