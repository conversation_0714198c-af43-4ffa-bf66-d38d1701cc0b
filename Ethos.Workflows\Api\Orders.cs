using System.Text.Json.Nodes;

namespace Ethos.Workflows.Api;

public sealed record CreateOrderDto : IInputDto
{
    public required Guid PatientId { get; set; }
    public required Guid CareLocationId { get; set; }
    public required Guid? PrimaryCarePhysicianId { get; set; }
    public required Guid? ReferringPhysicianId { get; set; }
    public required Guid? InterpretingPhysicianId { get; set; }
}

public sealed record OrderDto
{
    public required Guid Id { get; set; }
    public required Guid PatientId { get; set; }
    public required Guid CareLocationId { get; set; }
    public required Guid? PrimaryCarePhysicianId { get; set; }
    public required Guid? ReferringPhysicianId { get; set; }
    public required Guid? InterpretingPhysicianId { get; set; }
}

public sealed record CreateStudyDto : IInputDto
{
    public required Guid OrderId { get; set; }
    public required long? EncounterType { get; set; }
    public required long? StudyType { get; set; }
    public required JsonObject? StudyAttributes { get; set; }
    public required List<Guid>? Insurances { get; set; }
}

public sealed record StudyDto
{
    public required Guid Id { get; set; }
    public required Guid OrderId { get; set; }
    public required long? EncounterType { get; set; }
    public required long? StudyType { get; set; }
    public required JsonObject? StudyAttributes { get; set; }
    public required List<Guid>? Insurances { get; set; }
}