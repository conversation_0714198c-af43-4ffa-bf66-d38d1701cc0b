import './bootstrap';
import { ClassSerializerInterceptor, ValidationPipe } from '@nestjs/common';
import { NestFactory, Reflector } from '@nestjs/core';
import { Transport } from '@nestjs/microservices';
import { NestExpressApplication, ExpressAdapter } from '@nestjs/platform-express';
import compression from 'compression';
import helmet from 'helmet';
import morgan from 'morgan';
import { initializeTransactionalContext, patchTypeORMRepositoryWithBaseRepository } from 'typeorm-transactional-cls-hooked';

import { AppModule } from '@app/app.module';
import { HttpExceptionFilter } from '@app/common/filters/badRequest.filter';
import { QueryFailedFilter } from '@app/common/filters/queryFailed.filter';
import { setupSwagger } from '@app/swagger';
import { exceptionFactory } from '@app/exceptionFactory';
import { AllExceptionsFilter } from './common/filters/allExceptionsFilter';

import { NestLogger } from '@axmit/class-logger';
import Logger from '@app/common/logger';
import { config } from '@app/config';
const labels = ['APP', 'DEFAULT'];

async function bootstrap() {
  initializeTransactionalContext();
  patchTypeORMRepositoryWithBaseRepository();
  const app = await NestFactory.create<NestExpressApplication>(AppModule, new ExpressAdapter(), {
    cors: true,
    logger: new NestLogger(Logger, labels),
  });
  app.enable('trust proxy'); // only if you're behind a reverse proxy (Heroku, Bluemix, AWS ELB, Nginx, etc)

  app.use(helmet());
  app.use(compression());
  app.use(morgan('combined'));

  const reflector = app.get(Reflector);

  app.useGlobalFilters(new HttpExceptionFilter(reflector), new QueryFailedFilter(reflector), new AllExceptionsFilter(reflector));

  app.useGlobalInterceptors(new ClassSerializerInterceptor(reflector, { strategy: 'excludeAll' }));
  app.useGlobalPipes(
    new ValidationPipe({
      exceptionFactory: exceptionFactory,
      whitelist: true,
      transform: true,
      validationError: {
        target: false,
      },
      forbidUnknownValues: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    })
  );

  app.connectMicroservice({
    transport: Transport.TCP,
    options: {
      port: config.api.transportPort,
      retryAttempts: 5,
      retryDelay: 3000,
    },
  });

  await app.startAllMicroservicesAsync();

  app.setGlobalPrefix('api');

  if (['development', 'staging'].includes(config.nodeEnv)) {
    setupSwagger(app);
  }

  await app.listen(config.api.port);

  Logger.info(labels, `server running on port ${config.api.port}`);
}

try {
  bootstrap().catch((e) => Logger.err(labels, 'Unhandled exception', e));
} catch (e) {
  Logger.err(labels, 'Unhandled exception', e);
}

process.on('unhandledRejection', function (reason) {
  Logger.err(labels, 'Unhandled Promise Rejection', reason);
});

process.on('uncaughtException', function (exception) {
  Logger.err(labels, 'Unhandled Promise Rejection', exception);
});
