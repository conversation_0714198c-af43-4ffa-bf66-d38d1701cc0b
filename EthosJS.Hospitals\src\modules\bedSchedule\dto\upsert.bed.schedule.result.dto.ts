import { Expose, Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { UpsertBedScheduleItemDto } from '@app/modules/bedSchedule/dto/upsert.bed.schedule.dto';
import { BedScheduleDto } from '@app/modules/bedSchedule/dto/bed.schedule.dto';

export class UpsertBedScheduleResultItemDto {
  @ApiPropertyOptional({ type: BedScheduleDto })
  item?: BedScheduleDto;

  @ApiPropertyOptional({ type: UpsertBedScheduleItemDto })
  sourceItem?: UpsertBedScheduleItemDto;

  @ApiPropertyOptional()
  error?: string;
}

export class UpsertBedScheduleResultDto {
  @ApiProperty({ type: UpsertBedScheduleResultItemDto, isArray: true })
  @Type(() => UpsertBedScheduleResultItemDto)
  @Expose()
  items: UpsertBedScheduleResultItemDto[];
}
