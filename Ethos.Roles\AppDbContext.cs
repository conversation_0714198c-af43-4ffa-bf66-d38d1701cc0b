﻿using Ethos.Roles.Model;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Roles
{
    /// <summary>
    /// 
    /// </summary>
    public class AppDbContext : DbContext
    {
        /// <summary>
        /// 
        /// </summary>
        public DbSet<EthosRole> Roles { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<EthosDbScope> Scopes { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<EthosRoleAssignment> RoleAssignments { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<EthosRoleScope> RoleScopes { get; set; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="options"></param>
        public AppDbContext(DbContextOptions<AppDbContext> options)
            : base(options) { }

        public const string RolesDbSchema = "Roles";

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public bool RoleExistsByName(string name, Guid tenantId, Guid? roleId = null)
        {
            return Roles.FirstOrDefault(x => string.Equals(x.Name.ToLower(), name.ToLower()) && x.TenantId == tenantId &&
                                             ((roleId.HasValue && roleId.Value != x.Id) || !roleId.HasValue)) != null;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public bool IsBuiltin(Guid id)
        {
            return false;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="modelBuilder"></param>
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Roles
            modelBuilder.Entity<EthosRole>(entity =>
            {
                entity.ToTable("Roles", RolesDbSchema);

                entity.HasKey(l => new { l.Id, l.TenantId });

                entity.Property(l => l.Name)
                      .IsRequired();

                entity.HasIndex(l => new { l.Name, l.TenantId })
                      .IsUnique();

                entity.Property(l => l.Filters)
                      .HasColumnType("jsonb")
                      .HasDefaultValue(null);

                entity
                    .HasMany(l => l.RoleAssignments)
                    .WithOne(lv => lv.Role)
                    .HasForeignKey(l => l.RoleId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Scopes
            modelBuilder.Entity<EthosDbScope>(entity =>
            {
                entity.ToTable("Scopes", RolesDbSchema);
                entity.HasKey(l => l.RowId);
                entity.Property(l => l.RowId)
                      .ValueGeneratedOnAdd();
                entity.Property(l => l.Name)
                      .IsRequired();
                entity.HasIndex(l => l.Name)
                      .IsUnique();
            });

            // Role assignments
            modelBuilder.Entity<EthosRoleAssignment>(entity =>
            {
                entity.ToTable("Assignments", RolesDbSchema);

                entity.HasKey(l => new { l.RoleId, l.UserId, l.TenantId });

                entity
                    .HasOne(l => l.Role)
                    .WithMany(lv => lv.RoleAssignments)
                    .HasForeignKey(l => new { l.RoleId, l.TenantId })
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Role scopes
            modelBuilder.Entity<EthosRoleScope>(entity =>
            {
                entity.ToTable("RoleScopes", RolesDbSchema);

                entity.HasKey(l => new { l.RoleId, l.Scope, l.TenantId });

                entity
                    .HasOne(l => l.Role)
                    .WithMany(lv => lv.Scopes)
                    .HasForeignKey(l => new { l.RoleId, l.TenantId })
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }
    }
}
