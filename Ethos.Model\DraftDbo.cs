using System.Linq.Expressions;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class DraftDbo : IAuditableEntity<DraftDbo>
{
    public required EntityType EntityType { get; set; }
    public required Guid EntityId { get; set; }
    public required JsonObject Data { get; set; } = null!;

    public new static void Register(ModelBuilder modelBuilder) =>
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<DraftDbo>(Register);

    private static readonly JsonSerializerOptions JsonSerializerOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false,
        DefaultIgnoreCondition = JsonIgnoreCondition.Never
    };
    
    public new static void Register(EntityTypeBuilder<DraftDbo> entity)
    {
        IAuditableEntity<DraftDbo>.Register(entity);
        entity.Property(e => e.EntityType)
            .IsRequired()
            .HasConversion(
                v => v.ToString(),
                v => Enum.Parse<EntityType>(v, true));
        entity.Property(e => e.EntityId)
            .IsRequired();
        entity.Property(e => e.Data).HasConversion(
                v => JsonSerializer.Serialize(v, JsonSerializerOptions),
                v => JsonSerializer.Deserialize<JsonObject>(v, JsonSerializerOptions)!
            )
            .HasColumnType("jsonb")
            .IsRequired(false);
        
        entity.HasIndex(e => e.EntityId);
        
        entity.HasIndex(e => new { e.EntityType, e.EntityId })
            .IsUnique();
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(DraftQ.WithId), "WithId")]
[JsonDerivedType(typeof(DraftQ.WithEntityId), "WithEntityId")]
public abstract record DraftQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : DraftQ;
    public sealed record WithEntityId(EntityType Type, Guid Id) : DraftQ;

    public static DraftQ HasId(Guid id) => new WithId(id);
    public static DraftQ HasEntityId(EntityType type, Guid id) => new WithEntityId(type, id);

    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithId id => Expression.Equal(Expression.Property(self, nameof(DraftDbo.Id)), Expression.Constant(id.Id)),
            WithEntityId entityId => 
                Expression.AndAlso(
                    Expression.Equal(Expression.Property(self, nameof(DraftDbo.EntityType)), Expression.Constant(entityId.Type)),
                    Expression.Equal(Expression.Property(self, nameof(DraftDbo.EntityId)), Expression.Constant(entityId.Id))
                ),
            _ => throw new NotSupportedException($"Query type {this.GetType().Name} is not supported.")
        };
    }
}