﻿namespace Persante.Insurance.FastAuth.Client.Model
{
    public interface IFastAuthErrorDetails : IDictionary<string, string[]>
    {
        string[] Get(string property);
        void Throw();
    }

    public class FastAuthErrorDetails : Dictionary<string, string[]>, IDictionary<string, string[]>, IFastAuthErrorDetails
    {
        public string[] Get(string property)
        {
            return ContainsKey(property) ? this[property] : [];
        }

        public void Throw() {

            if (Count < 1)
                return;

            var exceptions = new List<ArgumentException>();

            foreach (var keyPair in this)
            {
                if (keyPair.Value.Length == 0)
                    continue;

                foreach (var value in keyPair.Value)
                    exceptions.Add(new ArgumentException(value, keyPair.Key));
            }

            if (exceptions.Count > 0)
                throw new AggregateException(exceptions);
        }
    }
}
