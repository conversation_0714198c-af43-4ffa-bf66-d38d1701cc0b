using Ethos.Model;
using Ethos.Workflows.Files;

namespace Ethos.Workflows.Api;

public sealed record TechnicianQualificationDto
{
    public required long QualificationId { get; set; }
    public required DateOnly? DateObtained { get; set; }
    public required DateOnly? DateExpires { get; set; }
}

public sealed record TechnicianDto
{
    public required Guid Id { get; set; }
    public required IReadOnlyList<PersonNameDto> Names { get; set; }
    public required DemographicsDto? Demographics { get; set; }
    public required PersonalContactDetailDto? ContactInformation { get; set; }
    public required IReadOnlyList<TechnicianQualificationDto> Qualifications { get; set; }
}

public sealed record CreateTechnicianDto : IInputDto
{
    public required IReadOnlyList<PersonNameDto>? Names { get; set; }
    public required DemographicsDto? Demographics { get; set; }
    public required PersonalContactDetailDto? ContactInformation { get; set; }
    public required IReadOnlyList<TechnicianQualificationDto>? Qualifications { get; set; }
}

public interface ITechnicianApi : IEntityHttpClient<CreateTechnicianDto, TechnicianDto, TechnicianQ>;

public class TechnicianHttpClient(System.Net.Http.HttpClient httpClient)
    : EntityHttpClientBase<CreateTechnicianDto, TechnicianDto, TechnicianQ>(httpClient, "technician"),
        ITechnicianApi;