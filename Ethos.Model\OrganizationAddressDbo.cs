using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class OrganizationAddressDbo : IAuditableEntity<OrganizationAddressDbo>
{
    public Guid ParentId { get; set; }
    public virtual OrganizationContactDetailDbo Parent { get; set; } = null!;

    public Guid AddressId { get; set; }
    public virtual AddressDbo Address { get; set; } = null!; // Link to internal AddressEntity

    public long UseId { get; set; }
    public long TypeId { get; set; }

    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<OrganizationAddressDbo>(Register);
    
    public new static void Register(EntityTypeBuilder<OrganizationAddressDbo> entity)
    {
        IAuditableEntity<OrganizationAddressDbo>.Register(entity);

        entity.HasOne(e => e.Parent)
            .WithMany(p => p.Addresses)
            .HasForeignKey(e => e.ParentId)
            .OnDelete(DeleteBehavior.Cascade);

        entity.HasOne(e => e.Address)
            .WithMany() // Assuming Address doesn't navigate back to PatientAddress links
            .HasForeignKey(e => e.AddressId)
            .OnDelete(DeleteBehavior.Restrict); // Prevent deleting Address if linked
    }
}