{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=postgres;Database=postgres;Username=postgres;Password=yourpassword"}, "ApiUsageLogging": {"Enabled": true, "TrackDataUsage": true, "EventSystemUrl": "http://************:4006/api/events", "MaxUsageRecordAgeMins": 1440, "TimeBetweenUsageRecordCleanupMins": 30, "MaxOpenTransactionAgeMins": 90, "BillOpenTransactionsOnExpiration": true}, "Auth": {"Local": {"Enabled": true, "Key": "VerySecureJWTKeyMustBeAtLeast32Chars!", "Issuer": "our-app-name", "Audience": "our-app-users"}, "Authorization": {"DebugScopes": ["ReferenceData.Set.*", "ReferenceData.SetValue.*", "*.*.<PERSON><PERSON>"], "DebugScopes_JSON": "[\"ReferenceData.Set.*\",\"ReferenceData.SetValue.*\", \"*.*.Admin\" ]", "DefaultAllow": false, "UseProblemDetails": true, "ProblemDetails": {"Title": "Forbidden", "Detail": "You do not have permission to access this resource."}, "ClaimNames": {"Scope": "http://schemas.microsoft.com/identity/claims/scope", "GivenName": "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname", "Surname": "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname", "Email": "emails", "Uuid": "http://schemas.microsoft.com/identity/claims/objectidentifier", "Products": "extension_products"}}, "AzureAD": {"Enabled": true, "ClientId": "a8d641a8-7112-4c1f-ad32-cf156f81459c", "TenantId": "5d067e44-b2ee-4df6-9bdb-839a7d05c039", "Instance": "https://ethoshbcustnonprod.b2clogin.com/", "Issuer": "https://ethoshbcustnonprod.b2clogin.com/5d067e44-b2ee-4df6-9bdb-839a7d05c039/v2.0/", "Policy": "B2C_1_Sign_Up_and_Sign_On"}}}