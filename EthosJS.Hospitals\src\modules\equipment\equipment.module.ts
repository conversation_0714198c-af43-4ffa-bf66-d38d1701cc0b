import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EquipmentRepository } from '@app/modules/equipment/equipment.repository';
import { EquipmentService } from '@app/modules/equipment/equipment.service';
import { EquipmentController } from '@app/modules/equipment/equipment.controller';

@Module({
  imports: [TypeOrmModule.forFeature([EquipmentRepository])],
  providers: [EquipmentService],
  controllers: [EquipmentController],
  exports: [EquipmentService],
})
export class EquipmentModule {}
