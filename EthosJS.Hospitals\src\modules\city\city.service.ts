import { BadRequestException, Injectable } from '@nestjs/common';
import { IListResult } from '@app/common/types';
import { CityRepository } from '@app/modules/city/city.repository';
import { CityEntity } from '@app/modules/city/city.entity';
import { CityFiltersDto } from '@app/modules/city/dto/city.filters.dto';
import { CreateCityDto } from '@app/modules/city/dto/create.city.dto';
import { StateService } from '@app/modules/state/state.service';

@Injectable()
export class CityService {
  constructor(
    private readonly repository: CityRepository,
    private readonly stateService: StateService,
  ) {}

  async create(dto: CreateCityDto): Promise<CityEntity> {
    await this.stateService.getByIdOrFail(dto.stateId);
    const entity = this.repository.create(dto);

    return this.repository.save(entity);
  }

  async list(filters: CityFiltersDto): Promise<IListResult<CityEntity>> {
    return this.repository.list(filters);
  }

  async getByIdOrFail(cityId: number): Promise<CityEntity> {
    const city = await this.repository.findOne({
      where: {
        id: cityId,
      },
    });

    if (!city) {
      throw new BadRequestException('City is not found');
    }

    return city;
  }
}
