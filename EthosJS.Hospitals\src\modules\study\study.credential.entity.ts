import { Column, Entity, ManyToOne } from 'typeorm';
import { Expose } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { BaseEntity } from '@app/common/base.entity';
import { StudyEntity } from '@app/modules/study/study.entity';
import { StateEntity } from '@app/modules/state/state.entity';
import { CredentialEntity } from '@app/modules/credential/credential.entity';

@Entity({ name: 'study_credentials' })
export class StudyCredentialEntity extends BaseEntity {
  @Column({ nullable: true })
  @ApiPropertyOptional()
  @Expose()
  stateId: number;

  @ManyToOne(
    () => StateEntity,
  )
  state: StateEntity;

  @Column()
  @ApiProperty()
  @Expose()
  studyId: number;

  @ManyToOne(
    () => StudyEntity,
    study => study.credentials,
  )
  study: StudyEntity;

  @Column({ type: 'jsonb', default: [] })
  @ApiProperty({ isArray: true, type: CredentialEntity })
  @Expose()
  credentials: number[];
}
