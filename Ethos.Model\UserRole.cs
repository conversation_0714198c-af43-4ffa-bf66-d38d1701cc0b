using System.Linq.Expressions;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

[JsonConverter(typeof(StringEnumConverter<RoleTarget>))]
public enum RoleTarget
{
    Physician = 1, Patient = 2, Technician = 3
}

public class UserRoleDbo : IAuditableEntity<UserRoleDbo>
{
    public Guid UserId { get; set; }
    public required RoleTarget Target { get; set; }
    public Guid TargetId { get; set; }
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<UserRoleDbo>(Register);

    public new static void Register(EntityTypeBuilder<UserRoleDbo> entity)
    {
        IAuditableEntity<UserRoleDbo>.Register(entity);
        
        entity.Property(e => e.Target)
            .IsRequired()
            .HasConversion(
                v => v.ToString(),
                v => Enum.Parse<RoleTarget>(v, true));
    }

    static UserRoleDbo()
    {
        // Ensure that the set of RoleTarget names is a subset of EntityType
        var roleTargetNames = Enum.GetNames<RoleTarget>();
        var entityTypeNames = Enum.GetNames<EntityType>();
        foreach (var roleTargetName in roleTargetNames)
        {
            if (!entityTypeNames.Contains(roleTargetName))
            {
                throw new InvalidOperationException($"RoleTarget '{roleTargetName}' is not a valid EntityType.");
            }
        }
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(UserRoleQ.WithId), "WithId")]
[JsonDerivedType(typeof(UserRoleQ.WithTargetId), "WithTargetId")]
[JsonDerivedType(typeof(UserRoleQ.WithUserId), "WithUserId")]
public abstract record UserRoleQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : UserRoleQ;
    public sealed record WithTargetId(Guid Value) : UserRoleQ;
    public sealed record WithUserId(Guid Value) : UserRoleQ;

    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithId wid => Expression.Equal(Expression.Property(self, nameof(UserRoleDbo.Id)), Expression.Constant(wid.Id)),
            WithTargetId wti => Expression.Equal(Expression.Property(self, nameof(UserRoleDbo.TargetId)), Expression.Constant(wti.Value)),
            WithUserId wui => Expression.Equal(Expression.Property(self, nameof(UserRoleDbo.UserId)), Expression.Constant(wui.Value)),
            _ => throw new NotSupportedException($"Unsupported TechnicianQ literal type: {this.GetType().Name}")
        };
    }
}