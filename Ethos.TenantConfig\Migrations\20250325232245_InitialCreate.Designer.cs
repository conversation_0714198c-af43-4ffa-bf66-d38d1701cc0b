﻿// <auto-generated />
using System;
using System.Collections.Generic;
using Ethos.TenantConfig;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Ethos.TenantConfig.Migrations
{
    [DbContext(typeof(ConfigDbContext))]
    [Migration("20250325232245_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Ethos.TenantConfig.TenantConfig", b =>
                {
                    b.Property<Guid>("TenantId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Dictionary<string, object>>("Config")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.HasKey("TenantId");

                    b.ToTable("TenantConfig", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
