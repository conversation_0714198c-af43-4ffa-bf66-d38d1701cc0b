﻿using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Swashbuckle.AspNetCore.Annotations;

namespace Ethos.TenantConfig
{
    /// <summary>
    /// 
    /// </summary>
    public class TenantConfigValue
    {
        [JsonConverter(typeof(NewtonsoftJsonDynamicObjectConverter))]
        public object? Value { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class TenantConfigSecretValue
    {
        public string? Value { get; set; }
        public string? Service { get; set; }
        public string? Vault {  get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class TenantConfigStorageValue
    {
        public string? Value { get; set; }
        public string? Service { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class TenantConfigSection
    {
        public string Name { get; set; } = string.Empty;
        [SwaggerIgnore]
        [JsonIgnore]
        public Guid TenantId { get; set; }
        public Dictionary<string, TenantConfigValue> Values { get; set; } = [];
        public Dictionary<string, TenantConfigSecretValue> Secrets { get; set; } = [];
        public Dictionary<string, TenantConfigStorageValue> Storage { get; set; } = [];

        [NotMapped]
        [SwaggerIgnore]
        public string? Etag { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ConfigDbContext : DbContext
    {
        /// <summary>
        /// 
        /// </summary>
        public DbSet<TenantConfigSection> Configurations { get; set; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="options"></param>
        public ConfigDbContext(DbContextOptions<ConfigDbContext> options)
            : base(options) { }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="optionsBuilder"></param>
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="modelBuilder"></param>
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            var jsonSerializerSettings = new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore,
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            };

            modelBuilder.Entity<TenantConfigSection>(entity =>
            {
                entity.ToTable("TenantConfig");

                entity.HasKey(t => new { t.Name, t.TenantId });
                entity.Property(t => t.Name)
                      .IsRequired();

                entity.Property(t => t.Values)
                      .HasColumnType("jsonb")
                      .HasConversion(
                            v => JsonConvert.SerializeObject(v, jsonSerializerSettings),
                            v => JsonConvert.DeserializeObject<Dictionary<string, TenantConfigValue>>(v, jsonSerializerSettings) ?? new Dictionary<string, TenantConfigValue>()
                       ).IsRequired(false);

                entity.Property(t => t.Secrets)
                      .HasColumnType("jsonb")
                      .HasConversion(
                            v => JsonConvert.SerializeObject(v, jsonSerializerSettings),
                            v => JsonConvert.DeserializeObject<Dictionary<string, TenantConfigSecretValue>>(v, jsonSerializerSettings) ?? new Dictionary<string, TenantConfigSecretValue>()
                       ).IsRequired(false);

                entity.Property(t => t.Storage)
                      .HasColumnType("jsonb")
                      .HasConversion(
                            v => JsonConvert.SerializeObject(v, jsonSerializerSettings),
                            v => JsonConvert.DeserializeObject<Dictionary<string, TenantConfigStorageValue>>(v, jsonSerializerSettings) ?? new Dictionary<string, TenantConfigStorageValue>()
                      ).IsRequired(false);
            });
        }
    }
}
