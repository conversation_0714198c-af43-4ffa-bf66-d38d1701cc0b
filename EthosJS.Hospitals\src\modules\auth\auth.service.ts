import { ForbiddenException, Injectable } from '@nestjs/common';
import { UserService } from '@app/modules/user/user.service';
import { UserEntity } from '@app/modules/user/user.entity';

@Injectable()
export class AuthService {
  constructor(
    public readonly userService: UserService,
  ) {}
  private async upsertTokens(email: string): Promise<any> {
    await this.userService.upsertToken(email, 'very-secret-token');

    return {
      id: 1,
      uid: 123,
      token_type: 'Bearer',
      access_token: 'very-secret-token',
      refresh_token: 'very-secret-refresh-token',
      expires_in: 600,
      name: '<PERSON><PERSON><PERSON>',
    };
  }

  async checkUserCredentials(email: string, password: string): Promise<any> {
    if (email === '<EMAIL>' && password === 'persantethebest') {
      return this.upsertTokens(email);
    }

    throw new ForbiddenException('Invalid credentials');
  }

  async refreshTokens(refreshToken: string): Promise<any> {
    if (refreshToken === 'very-secret-refresh-token') {
      return {
        token_type: 'Bearer',
        access_token: 'very-secret-token',
        refresh_token: 'very-secret-refresh-token',
        expires_in: 600,
      };
    }

    throw new ForbiddenException('Invalid refresh token');
  }

  async logout(user: UserEntity): Promise<void> {
    await this.userService.logout(user.id);
  }
}
