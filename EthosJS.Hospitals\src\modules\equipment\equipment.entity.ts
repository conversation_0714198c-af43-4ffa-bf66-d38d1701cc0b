import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { Column, Entity, OneToMany } from 'typeorm';
import { BaseEntity } from '@app/common/base.entity';
import { StudyEntity } from '@app/modules/study/study.entity';
import { ScheduleEntity } from '@app/modules/schedule/schedule.entity';

@Entity({ name: 'equipments' })
export class EquipmentEntity extends BaseEntity {
  @Column()
  @ApiProperty()
  @Expose()
  name: string;

  @OneToMany(
    () => StudyEntity,
    study => study.equipments,
  )
  studies: StudyEntity[];

  @OneToMany(
    () => ScheduleEntity,
    schedule => schedule.equipments,
  )
  schedules: ScheduleEntity[];
}
