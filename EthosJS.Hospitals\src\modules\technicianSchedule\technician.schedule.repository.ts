import { EntityRepository, In } from 'typeorm';
import { IListResult } from '@app/common/types';
import { TechnicianScheduleEntity } from '@app/modules/technicianSchedule/technician.schedule.entity';
import { BaseRepository } from '@app/common/base.repository';
import { BadRequestException } from '@nestjs/common';
import { TechnicianScheduleCollectionDto } from '@app/modules/technicianSchedule/dto/technician.schedule.collection.dto';
import { ITechnicianScheduleFilters } from '@app/modules/technicianSchedule/types';

@EntityRepository(TechnicianScheduleEntity)
export class TechnicianScheduleRepository extends BaseRepository<TechnicianScheduleEntity> {
  collectionDto = TechnicianScheduleCollectionDto;

  async list({ facilityIds, ...filters }: ITechnicianScheduleFilters): Promise<IListResult<TechnicianScheduleEntity>> {
    const where: any = { ...filters };

    if (facilityIds) {
      where.facilityId = In(facilityIds);
    }

    return super.list(where, ['technician']);
  }

  async checkHasSchedules(technicianId: number, date: string): Promise<void> {
    const [{ count }] = await this.query('SELECT COUNT(*) as count FROM schedules WHERE date = $1 AND technician_id = $2 AND deleted_at IS NULL', [date, technicianId]);

    if (Number(count) > 0) {
      throw new BadRequestException('Technician has related schedules');
    }
  }
}
