using System.Xml.Linq;
using Ethos.Auth;
using Ethos.Utilities;
using Ethos.Utilities.Filtering;
using Ethos.Utilities.Pagination;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.PlatformManager.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("api/products")]
    [Authorize]
    [EthosAuthFeature(Name = FeatureConstants.Core)]
    public class ProductController : ControllerBase
    {
        readonly ILogger<ProductController> _logger;
        readonly PlatformManagerDbContext _dbContext;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="dbContext"></param>
        public ProductController(ILogger<ProductController> logger, PlatformManagerDbContext dbContext)
        {
            _logger = logger;
            _dbContext = dbContext;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="prd"></param>
        /// <returns></returns>
        [HttpPost]
        [EthosAuthScope(ScopeDefinitions.ProductWrite, ScopeDefinitions.FeatureRead)]
        public async Task<ActionResult<ProductDto>> CreateProduct([FromBody] ProductDto prd)
        {
            var existingPrd = _dbContext.Products.FirstOrDefault(p => string.Equals(p.Name.ToLower(), prd.Name.ToLower()) || 
                                                                      prd.Id.HasValue && prd.Id.Value != Guid.Empty && p.Id == prd.Id.Value);

            if (existingPrd is not null)
                return this.EthosErrorConflict("Product already exists.");

            using var trans = _dbContext.Database.BeginTransaction();

            try
            {
                List<ProductFeature> features = [];
                var product = new Product()
                {
                    Id = !prd.Id.HasValue || prd.Id.Value == Guid.Empty ? Guid.NewGuid() : prd.Id.Value,
                    Name = prd.Name,
                };

                _dbContext.Products.Add(product);
                await _dbContext.SaveChangesAsync();

                if (prd.Features?.Count > 0)
                {
                    foreach (var feature in prd.Features)
                    {
                        var existingFeature = _dbContext.Features.FirstOrDefault(f => f.Id == feature.FeatureId);
                        if (existingFeature is null)
                        {
                            return this.EthosErrorNotFound($"Feature with ID {feature.FeatureId} was not found.");
                        }

                        var feat = new ProductFeature()
                        {
                            FeatureId = existingFeature.Id,
                            ProductId = product.Id,
                            Enabled = feature.Enabled,
                            Name = string.IsNullOrEmpty(feature.Name) ? existingFeature.Name : feature.Name,
                        };

                        features.Add(feat);
                        _dbContext.ProductFeatures.Add(feat);
                    }
                    await _dbContext.SaveChangesAsync();
                }

                trans.Commit();
                product.Features = features;
                return CreatedAtAction(nameof(GetProduct), new { productId = product.Id }, GetProductDto(product));
            }
            catch (Exception ex)
            {
                trans.Rollback();
                _logger.LogError(ex, "Error creating product.");
                return Problem(ex.Message, null, 500);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="productId"></param>
        /// <param name="prd"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{productId}")]
        [EthosAuthScope(ScopeDefinitions.ProductWrite, ScopeDefinitions.FeatureRead)]
        public async Task<ActionResult<ProductDto>> UpdateProduct([FromRoute] Guid productId, [FromBody] ProductDto prd)
        {
            if (productId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid product ID.");

            var existingPrd = await _dbContext.Products.Include(p => p.Features).ThenInclude(f => f.Feature).FirstOrDefaultAsync(p => p.Id == productId);

            if (existingPrd is null)
                return this.EthosErrorNotFound($"Product with ID {productId} was not found.");

            using var trans = _dbContext.Database.BeginTransaction();

            existingPrd.Name = !string.IsNullOrEmpty(prd.Name) ? prd.Name : existingPrd.Name;

            try
            {
                _dbContext.Products.Update(existingPrd);
                await _dbContext.SaveChangesAsync();

                if (prd.Features?.Count > 0)
                {
                    foreach (var feature in prd.Features)
                    {
                        var existingFeature = _dbContext.Features.FirstOrDefault(f => f.Id == feature.FeatureId);
                        if (existingFeature is null)
                        {
                            await trans.RollbackAsync();
                            return this.EthosErrorNotFound($"Feature with ID {feature.FeatureId} was not found.");
                        }

                        var existingPrdFeat = _dbContext.ProductFeatures.FirstOrDefault(pf => pf.ProductId == existingPrd.Id && pf.FeatureId == feature.FeatureId);
                        var isUpdate = existingPrdFeat is not null;

                        var name = !string.IsNullOrEmpty(existingPrdFeat?.Name) ? existingPrdFeat.Name : existingFeature.Name;

                        existingPrdFeat ??= new ProductFeature()
                        {
                            FeatureId = existingFeature.Id,
                            ProductId = productId,
                        };

                        existingPrdFeat.Enabled = feature.Enabled;
                        existingPrdFeat.Name = string.IsNullOrEmpty(feature.Name) ? name : feature.Name;

                        if (isUpdate)
                            _dbContext.ProductFeatures.Update(existingPrdFeat);
                        else
                            _dbContext.ProductFeatures.Add(existingPrdFeat);
                    }
                    await _dbContext.SaveChangesAsync();
                }

                await trans.CommitAsync();
                return Ok(GetProductDto(existingPrd));
            }
            catch (Exception ex)
            {
                await trans.RollbackAsync();
                _logger.LogError(ex, "Error updating product.");
                return Problem(ex.Message, null, 500);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="productId"></param>
        /// <param name="prdFeat"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{productId}/features")]
        [EthosAuthScope(ScopeDefinitions.ProductWrite, ScopeDefinitions.FeatureRead)]
        public async Task<ActionResult<ProductFeatureDto>> AddFeatureToProduct([FromRoute] Guid productId, [FromBody] ProductFeatureDto prdFeat)
        {
            if (productId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid product ID.");

            var existingPrd = _dbContext.Products.FirstOrDefault(p => p.Id == productId);

            if (existingPrd is null)
                return this.EthosErrorNotFound($"Product with ID {productId} was not found.");

            var existingFeature = _dbContext.Features.FirstOrDefault(f => f.Id == prdFeat.FeatureId);
            if (existingFeature is null)
                return this.EthosErrorNotFound($"Feature with ID {prdFeat.FeatureId} was not found.");

            using var trans = _dbContext.Database.BeginTransaction();

            try
            {
                var feat = new ProductFeature()
                {
                    FeatureId = existingFeature.Id,
                    ProductId = productId,
                    Enabled = prdFeat.Enabled,
                    Name = string.IsNullOrEmpty(prdFeat.Name) ? existingFeature.Name : prdFeat.Name,
                };

                _dbContext.ProductFeatures.Add(feat);
                await _dbContext.SaveChangesAsync();
                trans.Commit();

                return CreatedAtAction(nameof(GetProductFeature), new { productId, featureId = feat.FeatureId }, GetProductFeatureDto(feat));
            }
            catch (Exception ex)
            {
                trans.Rollback();
                _logger.LogError(ex, "Error adding feature to product.");
                return Problem(ex.Message, null, 500);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="productId"></param>
        /// <param name="prdFeat"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{productId}/features/{featureId}")]
        [EthosAuthScope(ScopeDefinitions.ProductWrite, ScopeDefinitions.FeatureRead)]
        public async Task<ActionResult<ProductFeatureDto>> UpdateFeatureOnProduct([FromRoute] Guid productId, [FromRoute] Guid featureId, [FromBody] ProductFeatureDto prdFeat)
        {
            if (productId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid product ID.");

            if (featureId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid feature ID.");

            var existingPrd = _dbContext.Products.FirstOrDefault(p => p.Id == productId);

            if (existingPrd is null)
                return this.EthosErrorNotFound($"Product with ID {productId} was not found.");

            var existingFeature = _dbContext.Features.FirstOrDefault(f => f.Id == featureId);
            if (existingFeature is null)
                return this.EthosErrorNotFound($"Feature with ID {featureId} was not found.");

            var productFeature = _dbContext.ProductFeatures.FirstOrDefault(pf => pf.ProductId == productId && pf.FeatureId == featureId);

            if (productFeature is null)
                return this.EthosErrorNotFound($"Feature with ID {featureId} does not exist on product with ID {productId}.");

            using var trans = _dbContext.Database.BeginTransaction();

            try
            {
                productFeature.Enabled = prdFeat.Enabled;
                productFeature.Name = string.IsNullOrEmpty(prdFeat.Name) ? productFeature.Name : prdFeat.Name;
                _dbContext.ProductFeatures.Update(productFeature);
                await _dbContext.SaveChangesAsync();
                trans.Commit();
                return Ok(GetProductFeatureDto(productFeature));
            }
            catch (Exception ex)
            {
                trans.Rollback();
                _logger.LogError(ex, "Error updating feature on product.");
                return Problem(ex.Message, null, 500);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="productId"></param>
        /// <param name="prdFeat"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{productId}/features/{featureId}")]
        [EthosAuthScope(ScopeDefinitions.ProductWrite, ScopeDefinitions.FeatureRead)]
        public async Task<IActionResult> RemoveFeatureFromProduct([FromRoute] Guid productId, [FromRoute] Guid featureId)
        {
            if (productId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid product ID.");

            if (featureId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid feature ID.");

            var existingPrd = _dbContext.Products.FirstOrDefault(p => p.Id == productId);

            if (existingPrd is null)
                return this.EthosErrorNotFound($"Product with ID {productId} was not found.");

            var existingFeature = _dbContext.Features.FirstOrDefault(f => f.Id == featureId);
            if (existingFeature is null)
                return this.EthosErrorNotFound($"Feature with ID {featureId} was not found.");

            var productFeature = _dbContext.ProductFeatures.FirstOrDefault(pf => pf.ProductId == productId && pf.FeatureId == featureId);

            if (productFeature is null)
                return this.EthosErrorNotFound($"Feature with ID {featureId} does not exist on product with ID {productId}.");

            _dbContext.ProductFeatures.Remove(productFeature);
            await _dbContext.SaveChangesAsync();
            return NoContent();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="productId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{productId}")]
        [EthosAuthScope(ScopeDefinitions.ProductRead, ScopeDefinitions.FeatureRead)]
        public async Task<ActionResult<ProductDto>> GetProduct([FromRoute] Guid productId)
        {
            if (productId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid product ID.");

            var product = await _dbContext.Products.Include(p => p.Features).ThenInclude(f => f.Feature).FirstOrDefaultAsync(p => p.Id == productId);

            if (product is null)
                return this.EthosErrorNotFound($"Product with ID {productId} was not found.");

            return Ok(GetProductDto(product));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="pagingParameters"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{productId}/features")]
        [EthosAuthScope(ScopeDefinitions.ProductRead, ScopeDefinitions.FeatureRead)]
        public async Task<ActionResult<PagedResponse<ProductDto>>> GetProductFeatures([FromRoute] Guid productId, [FromQuery] PagingParameters pagingParameters,
            [FromQuery] string? filter = null)
        {
            var products = _dbContext.ProductFeatures.Include(p => p.Feature).Where(pf => pf.ProductId == productId).Filter(filter).OrderBy(pf => pf.FeatureId);
            return Ok(await products.PaginateWithLinksAsync(this, (pfs) =>
            {
                return pfs.Select(p => GetProductFeatureDto(p)).ToList();
            }, pagingParameters.limit, pagingParameters.offset));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="productId"></param>
        /// <param name="pagingParameters"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{productId}/features/{featureId}")]
        [EthosAuthScope(ScopeDefinitions.ProductRead, ScopeDefinitions.FeatureRead)]
        public async Task<ActionResult<PagedResponse<ProductDto>>> GetProductFeature([FromRoute] Guid productId, [FromRoute] Guid featureId)
        {
            if (productId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid product ID.");

            if (featureId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid feature ID.");

            var existingPrd = await _dbContext.Products.FirstOrDefaultAsync(p => p.Id == productId);

            if (existingPrd is null)
                return this.EthosErrorNotFound($"Product with ID {productId} was not found.");

            var existingFeature = await _dbContext.Features.FirstOrDefaultAsync(f => f.Id == featureId);
            if (existingFeature is null)
                return this.EthosErrorNotFound($"Feature with ID {featureId} was not found.");

            var prdFeat = await _dbContext.ProductFeatures.Include(p => p.Feature).FirstOrDefaultAsync(pf => pf.ProductId == productId && pf.FeatureId == featureId);

            if (prdFeat is null)
                return this.EthosErrorNotFound($"Feature with ID {featureId} does not exist on product with ID {productId}");

            return Ok(GetProductFeatureDto(prdFeat));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="pagingParameters"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.ProductRead, ScopeDefinitions.FeatureRead)]
        public async Task<ActionResult<PagedResponse<ProductDto>>> GetProducts([FromQuery] PagingParameters pagingParameters, [FromQuery] string? filter = null)
        {
            var products = _dbContext.Products.Include(p => p.Features).ThenInclude(f => f.Feature).Filter(filter).OrderBy(p => p.Name);
            return Ok(await products.PaginateWithLinksAsync(this, (pds) =>
            {
                return pds.Select(p => GetProductDto(p)).ToList();
            }, pagingParameters.limit, pagingParameters.offset));

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="feature"></param>
        /// <returns></returns>
        static ProductFeatureDto GetProductFeatureDto(ProductFeature feature)
        {
            return new ProductFeatureDto()
            {
                Enabled = feature.Enabled,
                FeatureId = feature.FeatureId,
                Name = feature.Name,
            };
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="prd"></param>
        /// <returns></returns>
        static ProductDto GetProductDto(Product prd)
        {
            return new ProductDto()
            {
                Name = prd.Name,
                Id = prd.Id,
                Features = [.. (prd.Features ?? []).Select(f => new ProductFeatureDto()
                 {
                     FeatureId = f.FeatureId,
                     Enabled = f.Enabled,
                     Name = f.Name,
                 })]
            };
        }
    }
}
