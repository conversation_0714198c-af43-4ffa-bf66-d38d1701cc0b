using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class TechnicianCareLocationRelationDbo : IAuditableEntity<TechnicianCareLocationRelationDbo>
{
    public Guid TechnicianId { get; set; }
    public TechnicianDbo Technician { get; set; } = null!;
    
    public Guid CareLocationId { get; set; }
    public CareLocationDbo CareLocation { get; set; } = null!;
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<TechnicianCareLocationRelationDbo>(Register);
    
    public new static void Register(EntityTypeBuilder<TechnicianCareLocationRelationDbo> entity)
    {
        IAuditableEntity<TechnicianCareLocationRelationDbo>.Register(entity);
        
        entity.HasOne(e => e.Technician)
            .WithMany()
            .HasForeign<PERSON>ey(e => e.TechnicianId)
            .OnDelete(DeleteBehavior.Cascade);
        
        entity.HasOne(e => e.CareLocation)
            .WithMany()
            .HasForeignKey(e => e.CareLocationId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}