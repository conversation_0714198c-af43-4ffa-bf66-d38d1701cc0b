using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Ethos.Model;
using Ethos.Workflows.Api;
using Ethos.Workflows.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class ProviderController(DbContext dbContext)
    : EntityControllerBase<ProviderDbo, CreateProviderDto, ProviderDto, ProviderQ>(dbContext)
{
    protected override IQueryable<ProviderDbo> ApplyIncludes(IQueryable<ProviderDbo> query)
    {
        return query;
    }

    protected override ProviderDto MapToDto(ProviderDbo dbo) => dbo.ToDto();

    protected override ProviderDbo CreateOrUpdateEntity(ProviderDbo? entity, CreateProviderDto input, Guid? requiredId = null)
    {
        if (entity == null)
        {
            var parentProvider = _dbSet
                .FirstOrDefault(cl => cl.Id == input.ParentProviderId);
            
            entity = new ProviderDbo()
            {
                Id = requiredId ?? Guid.NewGuid(),
                Name = input.Name!,
                ParentProviderId = input.ParentProviderId,
                ProviderPath = parentProvider != null
                    ? $"{parentProvider.ProviderPath}/{parentProvider.Id}"
                    : "/",
                ContactDetail = input.ContactDetail?.ToEntity(),
                Identifiers = input.Identifiers?.Select(i => i.ToEntity())
                    .ToList() ?? new List<IdentifierDbo>()
            };
        }
        else
        {
            if (input.Name != null)
            {
                entity.Name = input.Name;
            }
        }

        return entity;
    }
}