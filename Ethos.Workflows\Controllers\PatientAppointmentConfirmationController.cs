using Ethos.Model;
using Ethos.Workflows.Api;
using Ethos.Workflows.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class PatientAppointmentConfirmationController(DbContext dbContext)
    : EntityControllerBase<PatientAppointmentConfirmationDbo, CreatePatientAppointmentConfirmationDto, PatientAppointmentConfirmationDto, PatientAppointmentConfirmationQ>(dbContext)
{
    protected override PatientAppointmentConfirmationDto MapToDto(PatientAppointmentConfirmationDbo dbo)
    {
        return new PatientAppointmentConfirmationDto
        {
            Id = dbo.Id,
            AppointmentId = dbo.PatientAppointmentId,
            ConfirmationTypeId = dbo.ConfirmationTypeId,
            ConfirmationDate = dbo.ConfirmationDateTime,
        };
    }

    protected override PatientAppointmentConfirmationDbo CreateOrUpdateEntity(PatientAppointmentConfirmationDbo? entity, CreatePatientAppointmentConfirmationDto input,
        Guid? requiredId = null)
    {
        if (entity == null)
        {
            entity = new PatientAppointmentConfirmationDbo
            {
                Id = requiredId ?? Guid.NewGuid(),
                PatientAppointmentId = input.AppointmentId,
                ConfirmationTypeId = input.ConfirmationTypeId,
                ConfirmationDateTime = input.ConfirmationDate
            };
        }
        else
        {
            entity.PatientAppointmentId = input.AppointmentId;
            entity.ConfirmationTypeId = input.ConfirmationTypeId;
            entity.ConfirmationDateTime = input.ConfirmationDate;
        }

        return entity;
    }
}