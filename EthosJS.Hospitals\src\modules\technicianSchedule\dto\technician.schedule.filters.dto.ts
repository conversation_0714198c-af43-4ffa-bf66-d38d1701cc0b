import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, IsPositive } from 'class-validator';
import { Transform } from 'class-transformer';
import { BaseFiltersDto } from '@app/common/dto/base.filters.dto';
import { ETechnicianScheduleShift, ETechnicianScheduleSort } from '@app/modules/technicianSchedule/enums';
import { IsDateOnly, IsIntegerList } from '@app/common/decorators/validators.decorator';

export class TechnicianScheduleFiltersDto extends BaseFiltersDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  @IsPositive()
  technicianId?: number;

  @ApiPropertyOptional({
    isArray: false,
    type: 'string',
    description: 'List of integers concatenated through ,',
    example: '1,2,3',
  })
  @IsOptional()
  @IsIntegerList()
  @Transform((value) => value?.split(',').map((item: string) => Number(item)))
  facilityIds?: number[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  @IsPositive()
  clinicId?: number;

  @ApiPropertyOptional({ enum: ETechnicianScheduleShift })
  @IsOptional()
  @IsEnum(ETechnicianScheduleShift)
  shift?: ETechnicianScheduleShift;

  @ApiPropertyOptional({ format: 'date-time', example: '2020-01-01' })
  @IsOptional()
  @IsDateOnly()
  dateFrom?: string;

  @ApiPropertyOptional({ format: 'date-time', example: '2024-01-01' })
  @IsOptional()
  @IsDateOnly()
  dateTo?: string;

  @ApiPropertyOptional({ enum: ETechnicianScheduleSort })
  @IsOptional()
  @IsEnum(ETechnicianScheduleSort)
  orderField?: ETechnicianScheduleSort = ETechnicianScheduleSort.CreatedAt;
}
