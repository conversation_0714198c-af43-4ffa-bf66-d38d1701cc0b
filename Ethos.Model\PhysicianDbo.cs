using System.Linq.Expressions;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class PhysicianDbo : IAuditableEntity<PhysicianDbo>
{
    public virtual ICollection<PersonNameDbo> Names { get; set; } = new List<PersonNameDbo>();
    public DemographicsDbo? Demographics { get; set; }
    public ICollection<IdentifierDbo> Identifiers { get; set; } = new List<IdentifierDbo>();
    public Guid? ContactDetailId { get; set; }
    public PersonalContactDetailDbo? ContactDetail { get; set; }
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<PhysicianDbo>(Register);

    public new static void Register(EntityTypeBuilder<PhysicianDbo> entity)
    {
        IAuditableEntity<PhysicianDbo>.Register(entity);
        
        entity.OwnsMany(p => p.Names, names =>
        {
            names.ToTable($"{nameof(PhysicianDbo)}_{nameof(Names)}", IEntity.DefaultSchema);
            names.WithOwner().HasForeignKey("PhysicianDboId");
            names.Property<int>("Id").ValueGeneratedOnAdd();
            names.HasKey("Id");
            PersonNameDbo.Configure(names);
        });
        
        entity.OwnsOne(p => p.Demographics, builder =>
        {
            builder.ToTable($"{nameof(PhysicianDbo)}_{nameof(Demographics)}", IEntity.DefaultSchema);
            builder.WithOwner().HasForeignKey($"{nameof(PhysicianDbo)}Id");
            DemographicsDbo.Configure(builder);
        });
        
        entity.OwnsMany(p => p.Identifiers, identifiers =>
        {
            identifiers.ToTable($"{nameof(PhysicianDbo)}_{nameof(Identifiers)}", IEntity.DefaultSchema);
            identifiers.WithOwner().HasForeignKey("PhysicianDboId");
            identifiers.Property<int>("Id").ValueGeneratedOnAdd();
            identifiers.HasKey("Id");
            IdentifierDbo.Configure(identifiers);
        });
        
        entity.HasOne(s => s.ContactDetail)
            .WithMany()
            .HasForeignKey("ContactDetailId")
            .IsRequired(false)
            .HasPrincipalKey(c => c.Id);
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(PhysicianQ.WithId), "WithId")]
[JsonDerivedType(typeof(PhysicianQ.WithFirstName), "WithFirstName")]
[JsonDerivedType(typeof(PhysicianQ.WithLastName), "WithLastName")]
[JsonDerivedType(typeof(PhysicianQ.WithApproximateName), "WithApproximateName")]
[JsonDerivedType(typeof(PhysicianQ.WithIdentifier), "WithIdentifier")]
public abstract record PhysicianQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : PhysicianQ;
    public sealed record WithFirstName(string FirstName) : PhysicianQ;
    public sealed record WithLastName(string LastName) : PhysicianQ;
    public sealed record WithApproximateName(string Name) : PhysicianQ;
    public sealed record WithIdentifier(string System, string Value) : PhysicianQ;
    
    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithId id => Expression.Equal(Expression.Property(self, nameof(PhysicianDbo.Id)), Expression.Constant(id.Id)),
            WithFirstName       wfn => QueryExpressions.BuildFirstNamePredicate<PhysicianDbo>(wfn.FirstName, self),
            WithLastName        wln => QueryExpressions.BuildLastNamePredicate<PhysicianDbo>(wln.LastName, self),
            WithApproximateName wan => QueryExpressions.BuildApproximateNamePredicate<PhysicianDbo>(wan.Name, self),
            WithIdentifier      wnp => QueryExpressions.BuildIdentifierPredicate(wnp.System, wnp.Value, self),
            _ => throw new NotImplementedException()
        };
    }
}