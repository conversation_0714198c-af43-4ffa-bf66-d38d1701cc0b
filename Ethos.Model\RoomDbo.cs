using System.Linq.Expressions;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class RoomDbo : IAuditableEntity<RoomDbo>
{
    public Guid CareLocationId { get; set; }
    public virtual CareLocationDbo CareLocation { get; set; } = null!;
    
    public required string Name { get; set; }
    
    public required ICollection<long>? SupportedStudyTypes { get; set; }
    
    public required ICollection<EquipmentDbo> Equipment { get; set; } = new List<EquipmentDbo>();
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<RoomDbo>(Register);

    public new static void Register(EntityTypeBuilder<RoomDbo> entity)
    {
        IAuditableEntity<RoomDbo>.Register(entity);
        
        entity.Property(e => e.Name).HasMaxLength(200).IsRequired();
        entity.HasIndex(e => e.Name)
            .HasDatabaseName($"IX_{nameof(RoomDbo)}_Name");

        entity.HasOne(e => e.CareLocation)
            .WithMany()
            .HasForeignKey(e => e.CareLocationId);
        
        entity.HasMany(e => e.Equipment)
            .WithOne(e => e.Room)
            .HasForeignKey(e => e.RoomId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Cascade);

        entity.Property(e => e.SupportedStudyTypes);
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(RoomQ.WithId), "WithId")]
[JsonDerivedType(typeof(RoomQ.WithName), "WithName")]
[JsonDerivedType(typeof(RoomQ.WithCareLocationId), "WithCareLocationId")]
public abstract record RoomQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : RoomQ;
    public sealed record WithName(string Id) : RoomQ;
    public sealed record WithCareLocationId(Guid Id) : RoomQ;

    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithId              wid => Expression.Equal(Expression.Property(self, nameof(TechnicianDbo.Id)), Expression.Constant(wid.Id)),
            WithName            wna => Expression.Equal(Expression.Property(self, nameof(RoomDbo.Name)), Expression.Constant(wna.Id)),
            WithCareLocationId  wcl => Expression.Equal(Expression.Property(self, nameof(RoomDbo.CareLocationId)), Expression.Constant(wcl.Id)),
            _ => throw new NotSupportedException($"Unsupported TechnicianQ literal type: {this.GetType().Name}")
        };
    }
}