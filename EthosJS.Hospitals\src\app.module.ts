import { Module, NestModule, MiddlewareConsumer, RequestMethod } from '@nestjs/common';
import { ScheduleModule as NestScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from '@app/modules/auth/auth.module';
import { UserModule } from '@app/modules/user/user.module';
import { AuthMiddleware } from '@app/common/middlewares/auth.middleware';
import { NotificationsModule } from '@app/modules/notifications/notifications.module';
import { ClinicModule } from '@app/modules/clinic/clinic.module';
import { HealthModule } from '@app/modules/health/health.module';
import { config } from '@app/config';
import { BedScheduleModule } from '@app/modules/bedSchedule/bed.schedule.module';
import { EquipmentModule } from '@app/modules/equipment/equipment.module';
import { FacilityModule } from '@app/modules/facility/facility.module';
import { StudyModule } from '@app/modules/study/study.module';
import { TechnicianModule } from '@app/modules/technician/technician.module';
import { TechnicianScheduleModule } from '@app/modules/technicianSchedule/technician.schedule.module';
import { ScheduleModule } from '@app/modules/schedule/schedule.module';
import { CityModule } from '@app/modules/city/city.module';
import { StateModule } from '@app/modules/state/state.module';
import { CredentialModule } from '@app/modules/credential/credential.module';
import { AvailableModule } from '@app/modules/available/available.module';

@Module({
  imports: [
    AuthModule,
    AvailableModule,
    BedScheduleModule,
    ClinicModule,
    CityModule,
    CredentialModule,
    EquipmentModule,
    FacilityModule,
    ScheduleModule,
    StateModule,
    StudyModule,
    TechnicianModule,
    TechnicianScheduleModule,
    UserModule,
    HealthModule,
    NestScheduleModule.forRoot(),
    TypeOrmModule.forRootAsync({
      useFactory: () => config.typeorm,
    }),
    NotificationsModule,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer): MiddlewareConsumer | void {
    consumer.apply(AuthMiddleware).forRoutes({
      path: '*',
      method: RequestMethod.ALL,
    });
  }
}
