using System.Linq.Expressions;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class ProviderDbo : IAuditableEntity<ProviderDbo>
{
    public required string ProviderPath { get; set; } // concatenation of all parent provider ids
    public Guid? ParentProviderId { get; set; }
    public virtual ProviderDbo? ParentProvider { get; set; } = null!;
    
    public Guid? ContactDetailId { get; set; }
    public virtual OrganizationContactDetailDbo? ContactDetail { get; set; } = null!;
    
    public required string Name { get; set; }
    // facilities
    
    public ICollection<IdentifierDbo> Identifiers { get; set; } = new List<IdentifierDbo>();
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<ProviderDbo>(Register);

    public new static void Register(EntityTypeBuilder<ProviderDbo> entity)
    {
        IAuditableEntity<ProviderDbo>.Register(entity);
        
        entity.Property(e => e.Name).HasMaxLength(200).IsRequired();
        entity.HasIndex(e => e.Name)
            .HasDatabaseName($"IX_{nameof(ProviderDbo)}_Name");
        
        entity.OwnsMany(p => p.Identifiers, identifiers =>
        {
            identifiers.ToTable($"{nameof(ProviderDbo)}_{nameof(Identifiers)}", IEntity.DefaultSchema);
            identifiers.WithOwner().HasForeignKey($"{nameof(ProviderDbo)}Id");
            identifiers.Property<int>("Id").ValueGeneratedOnAdd();
            identifiers.HasKey("Id");
            IdentifierDbo.Configure(identifiers);
        });
        
        entity.HasOne<ProviderDbo>(e => e.ParentProvider)
            .WithMany()
            .HasForeignKey(e => e.ParentProviderId)
            .OnDelete(DeleteBehavior.Cascade);
        
        entity.HasOne<OrganizationContactDetailDbo>(e => e.ContactDetail)
            .WithMany()
            .HasForeignKey(e => e.ContactDetailId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.SetNull);
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(ProviderQ.WithId), "WithId")]
[JsonDerivedType(typeof(ProviderQ.WithParentProviderId), "WithParentProviderId")]
[JsonDerivedType(typeof(ProviderQ.WithName), "WithName")]
[JsonDerivedType(typeof(ProviderQ.WithApproximateName), "WithApproximateName")]
[JsonDerivedType(typeof(ProviderQ.WithIdentifier), "WithIdentifier")]
public abstract record ProviderQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : ProviderQ;
    public sealed record WithParentProviderId(Guid Id) : ProviderQ;
    public sealed record WithName(string Name) : ProviderQ;
    public sealed record WithApproximateName(string Name) : ProviderQ;
    public sealed record WithIdentifier(string System, string Value) : ProviderQ;
    
    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithId id => Expression.Equal(Expression.Property(self, nameof(ProviderDbo.Id)), Expression.Constant(id.Id)),
            WithParentProviderId parentId => Expression.Equal(
                Expression.Property(self, nameof(ProviderDbo.ParentProviderId)), 
                Expression.Constant(parentId.Id, typeof(Guid?)) // Explicitly type the constant as Nullable<Guid>
            ),
            WithName name => Expression.Equal(Expression.Property(self, nameof(ProviderDbo.Name)), Expression.Constant(name.Name)),
            WithApproximateName approxName => QueryExpressions.BuildSimpleApproximateNamePredicate<ProviderDbo>(
                Expression.Property(self, nameof(ProviderDbo.Name)), approxName.Name),
            WithIdentifier identifier => QueryExpressions.BuildIdentifierPredicate(identifier.System, identifier.Value, self),
            
            _ => throw new NotImplementedException()
        };
    }
}