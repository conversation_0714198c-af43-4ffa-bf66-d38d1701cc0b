import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserService } from '@app/modules/user/user.service';
import { AuthModule } from '@app/modules/auth/auth.module';
import { UserRepository } from '@app/modules/user/user.repository';

@Module({
  imports: [
    forwardRef(() => AuthModule),
    TypeOrmModule.forFeature([UserRepository]),
  ],
  exports: [UserService],
  providers: [UserService],
})
export class UserModule {}
