﻿using DoctorsApplication.Models;
using MudBlazor;


public class AppStateService
{
    private MudDataGrid<StudiesModel> _StudiesDataGrid;

    public event Action? LoadStateChanged;

    public MudDataGrid<StudiesModel> StudiesDataGrid
    {
        get => _StudiesDataGrid;
        set
        {
            if (_StudiesDataGrid != value)
            {
                _StudiesDataGrid = value;
                LoadStateChanged?.Invoke();
            }
        }
    }
}
