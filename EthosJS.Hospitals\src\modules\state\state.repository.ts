import { EntityRepository } from 'typeorm';
import { IListResult } from '@app/common/types';
import { BaseRepository } from '@app/common/base.repository';
import { StateEntity } from '@app/modules/state/state.entity';
import { StateFiltersDto } from '@app/modules/state/dto/state.filters.dto';
import { StateCollectionDto } from '@app/modules/state/dto/state.collection.dto';

@EntityRepository(StateEntity)
export class StateRepository extends BaseRepository<StateEntity> {
  collectionDto = StateCollectionDto;

  async list(filters: StateFiltersDto): Promise<IListResult<StateEntity>> {
    return super.list(filters);
  }
}
