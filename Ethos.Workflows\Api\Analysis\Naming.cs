using System.Text.RegularExpressions;

namespace Ethos.Workflows.Api.Analysis;

public static class Naming
{
    public static string PascalToKebabCase(string value)
    {
        if (string.IsNullOrEmpty(value))
            return value;

        return Regex.Replace(
                value,
                "(?<!^)([A-Z][a-z]|(?<=[a-z])[A-Z])",
                "-$1",
                RegexOptions.Compiled)
            .Trim()
            .ToLowerInvariant();
    }
    
    public static string PascalTo<PERSON>ebab(string s)
        => Regex.Replace(s, "(?<=.)([A-Z])", "-$1").ToLowerInvariant();
    
    public static string PascalToCamel(string s)
        => char.ToLowerInvariant(s[0]) + s[1..];
    
    public static string PascalToSnake(string s)
        => Regex.Replace(s, "(?<=.)([A-Z])", "_$1").ToLowerInvariant();
    
    public static string Snake(string s) =>
        Regex.Replace(s, "(?<=.)([A-Z])", "_$1").ToLowerInvariant();

    public static string Pascal(string s) =>
        char.ToUpperInvariant(s[0]) + Regex.Replace(s.Substring(1), "_([a-z])", m => m.Groups[1].Value.ToUpperInvariant());
}