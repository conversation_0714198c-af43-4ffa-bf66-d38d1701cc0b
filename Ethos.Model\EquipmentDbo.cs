using System.Linq.Expressions;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class EquipmentDbo : IAuditableEntity<EquipmentDbo>
{
    public Guid CareLocationId { get; set; }
    public virtual CareLocationDbo CareLocation { get; set; } = null!;
    
    public ICollection<IdentifierDbo> Identifiers { get; set; } = new List<IdentifierDbo>();
    
    public Guid? RoomId { get; set; }
    public virtual RoomDbo? Room { get; set; }
    
    public long EquipmentTypeId { get; set; }
    public required JsonObject EquipmentData { get; set; }
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<EquipmentDbo>(Register);
    
    public new static void Register(EntityTypeBuilder<EquipmentDbo> entity)
    {
        IAuditableEntity<EquipmentDbo>.Register(entity);
        
        entity.OwnsMany(p => p.Identifiers, identifiers =>
        {
            identifiers.ToTable($"{nameof(EquipmentDbo)}_{nameof(Identifiers)}", IEntity.DefaultSchema);
            identifiers.WithOwner().HasForeignKey($"{nameof(EquipmentDbo)}Id");
            identifiers.Property<int>("Id").ValueGeneratedOnAdd();
            identifiers.HasKey("Id");
            IdentifierDbo.Configure(identifiers);
        });
        
        entity.Property(n => n.EquipmentData).HasConversion(
                v => v == null || !v.Any() ? null : JsonSerializer.Serialize(v, DefaultJson.SerializerOptions),
                v => string.IsNullOrEmpty(v) ? null : JsonSerializer.Deserialize<JsonObject>(v, DefaultJson.SerializerOptions)
            )
            .HasColumnType("jsonb");
        
        entity.HasOne<CareLocationDbo>(e => e.CareLocation)
            .WithMany()
            .HasForeignKey(e => e.CareLocationId)
            .HasPrincipalKey(e => e.Id);
        
        entity.HasOne<RoomDbo>(e => e.Room)
            .WithMany()
            .HasForeignKey(e => e.RoomId)
            .HasPrincipalKey(e => e.Id);
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(EquipmentQ.WithId), "WithId")]
[JsonDerivedType(typeof(EquipmentQ.WithCareLocationId), "WithCareLocationId")]
[JsonDerivedType(typeof(EquipmentQ.WithRoomId), "WithRoomId")]
[JsonDerivedType(typeof(EquipmentQ.WithEquipmentTypeId), "WithEquipmentTypeId")]
public abstract record EquipmentQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : EquipmentQ;
    public sealed record WithCareLocationId(Guid Id) : EquipmentQ;
    public sealed record WithRoomId(Guid? Id) : EquipmentQ;
    public sealed record WithEquipmentTypeId(long Id) : EquipmentQ;
    
    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithId wid => Expression.Equal(Expression.Property(self, nameof(EquipmentDbo.Id)), Expression.Constant(wid.Id)),
            WithCareLocationId wcl => Expression.Equal(Expression.Property(self, nameof(EquipmentDbo.CareLocationId)), Expression.Constant(wcl.Id)),
            WithRoomId wr => Expression.Equal(
                Expression.Property(self, nameof(EquipmentDbo.RoomId)), 
                Expression.Constant(wr.Id, typeof(Guid?)) // Explicitly type the constant as Nullable<Guid>
            ),
            WithEquipmentTypeId wet => Expression.Equal(Expression.Property(self, nameof(EquipmentDbo.EquipmentTypeId)), Expression.Constant(wet.Id)),
            _ => throw new NotSupportedException($"Unsupported EquipmentQ literal type: {this.GetType().Name}")
        };
    }
}