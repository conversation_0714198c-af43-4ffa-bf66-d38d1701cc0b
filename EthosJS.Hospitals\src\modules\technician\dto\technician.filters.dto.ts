import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, IsPositive, IsString } from 'class-validator';
import { BaseFiltersDto } from '@app/common/dto/base.filters.dto';
import { ETechnicianSort } from '@app/modules/technician/enums';

export class TechnicianFiltersDto extends BaseFiltersDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  @IsPositive()
  clinicId?: number;

  @ApiPropertyOptional({ enum: ETechnicianSort })
  @IsOptional()
  @IsEnum(ETechnicianSort)
  orderField: ETechnicianSort = ETechnicianSort.CreatedAt;
}
