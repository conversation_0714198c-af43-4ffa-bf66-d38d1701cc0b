import moment from 'moment';
import { BadRequestException } from '@nestjs/common';
import { EntityRepository } from 'typeorm';
import { ClinicEntity } from '@app/modules/clinic/clinic.entity';
import { IListResult } from '@app/common/types';
import { ClinicFiltersDto } from '@app/modules/clinic/dto/clinic.filters.dto';
import { BaseRepository } from '@app/common/base.repository';
import { ClinicCollectionDto } from '@app/modules/clinic/dto/clinic.collection.dto';

@EntityRepository(ClinicEntity)
export class ClinicRepository extends BaseRepository<ClinicEntity> {
  collectionDto = ClinicCollectionDto;

  async list(filters: ClinicFiltersDto): Promise<IListResult<ClinicEntity>> {
    return super.list(filters);
  }

  async checkHasSchedules(clinicId: number): Promise<void> {
    const today = moment().startOf('day');

    const [{ count }] = await this.query('SELECT COUNT(*) as count FROM clinics LEFT JOIN facilities ON clinics.id = facilities.clinic_id LEFT JOIN schedules ON facilities.id = schedules.facility_id WHERE clinics.id = $2 AND schedules.date >= $1 AND schedules.deleted_at IS NULL', [today, clinicId]);

    if (Number(count) > 0) {
      throw new BadRequestException('Clinic has related schedules');
    }
  }

  async softRemoveRelations(clinicId: number): Promise<void> {
    const facilities = await this.query('SELECT id FROM "facilities" WHERE "clinic_id" = $1', [clinicId]);

    if (facilities.length) {
      const facilityIds = facilities.map(({ id }: { id: number }) => id);
      await this.query(`UPDATE "bed_schedules" SET "deleted_at" = CURRENT_TIMESTAMP, "updated_at" = CURRENT_TIMESTAMP WHERE "facility_id" IN(${facilityIds.join(',')})`);
    }

    await this.query('UPDATE "facilities" SET "deleted_at" = CURRENT_TIMESTAMP, "updated_at" = CURRENT_TIMESTAMP WHERE "clinic_id" = $1', [clinicId]);

    const technicians = await this.query('SELECT id FROM "technicians" WHERE "clinic_id" = $1', [clinicId]);

    if (technicians.length) {
      const technicianIds = technicians.map(({ id }: { id: number }) => id);
      await this.query(`UPDATE "technician_schedules" SET "deleted_at" = CURRENT_TIMESTAMP, "updated_at" = CURRENT_TIMESTAMP WHERE "technician_id" IN(${technicianIds.join(',')})`);
    }
    await this.query('UPDATE "technicians" SET "deleted_at" = CURRENT_TIMESTAMP, "updated_at" = CURRENT_TIMESTAMP WHERE "clinic_id" = $1', [clinicId]);
  }
}
