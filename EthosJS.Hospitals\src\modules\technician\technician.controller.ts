import { AuthGuard } from '@app/common/guards/auth.guard';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { TechnicianService } from '@app/modules/technician/technician.service';
import { TechnicianCollectionDto } from '@app/modules/technician/dto/technician.collection.dto';
import { TechnicianFiltersDto } from '@app/modules/technician/dto/technician.filters.dto';
import { CreateTechnicianDto } from '@app/modules/technician/dto/create.technician.dto';
import { UpdateTechnicianDto } from '@app/modules/technician/dto/update.technician.dto';
import { DeleteTechnicianDto } from '@app/modules/technician/dto/delete.technician.dto';
import { TechnicianDto } from '@app/modules/technician/dto/technician.dto';
import { TechnicianConflictsDto } from '@app/modules/technician/dto/technician.conflicts.dto';
import { GetConflictsTechnicianDto } from '@app/modules/technician/dto/getConflicts.technician.dto';

@Controller('technician')
@ApiTags('Technicians')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class TechnicianController {
  constructor(private readonly service: TechnicianService) {
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: TechnicianCollectionDto,
    description: 'Get list of technicians',
  })
  async list(@Query() filters: TechnicianFiltersDto): Promise<TechnicianCollectionDto> {
    const { data, count } = await this.service.list(filters);

    return {
      data: data.map(TechnicianService.mapToDto),
      count,
    };
  }

  @Get('/:technicianId')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: TechnicianDto,
    description: 'Get technician by id',
  })
  async getById(@Param('technicianId', new ParseIntPipe()) technicianId: number): Promise<TechnicianDto> {
    const item = await this.service.getByIdOrFail(technicianId);

    return TechnicianService.mapToDto(item);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOkResponse({
    type: TechnicianDto,
    description: 'Create technician',
  })
  async create(@Body() technician: CreateTechnicianDto): Promise<TechnicianDto> {
    const item = await this.service.create(technician);

    return TechnicianService.mapToDto(item);
  }

  @Put()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: TechnicianDto,
    description: 'Update technician',
  })
  async update(@Body() update: UpdateTechnicianDto): Promise<TechnicianDto> {
    const item = await this.service.update(update);

    return TechnicianService.mapToDto(item);
  }

  @Post('/conflicts')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: TechnicianDto,
    description: 'Get list of conflicted schedules',
  })
  async getConflicts(@Body() dto: GetConflictsTechnicianDto): Promise<TechnicianConflictsDto> {
    const conflicts = await this.service.getConflicts(dto);

    return { conflicts };
  }

  @Delete()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: TechnicianDto,
    description: 'Delete technician',
  })
  async delete(@Body() { id }: DeleteTechnicianDto): Promise<TechnicianDto> {
    const item = await this.service.delete(id);

    return TechnicianService.mapToDto(item);
  }
}
