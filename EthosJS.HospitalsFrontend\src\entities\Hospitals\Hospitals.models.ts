//@ts-nocheck
// import '@axmit/persante-hospitals';
import { IRequestSuccess } from 'common/models';

export interface IHospitalsCollectionPayload  {}
export interface IHospitalsCollectionDto  {}
export interface IHospitalModelCreatePayload extends IRequestSuccess {}
export interface IHospitalModelUpdatePayload extends IRequestSuccess {}
export interface IHospitalModelDeletePayload extends IRequestSuccess {}
export interface IHospitalModelDto {}

export interface IHospitalsCollection {
  data: IHospitalsCollectionDto | null;
  loading: boolean;
}

export interface IHospitalModel {
  data: IHospitalModelDto | null;
  loading: boolean;
}
