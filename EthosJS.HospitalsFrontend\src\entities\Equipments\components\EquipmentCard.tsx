//@ts-nocheck
import React, { useEffect } from 'react';
import { Form, Input, Spin } from 'antd';
import { connect } from 'react-redux';
import { useForm } from 'antd/es/form/Form';
import { EDatabaseActions, EDatabaseTabValues, EFormFieldMessages } from 'common/const/enum.const';
import { RootDispatch, RootState, history } from 'app/store';
import { CommonDatabaseContainer } from 'entities/Database/components/CommonDatabaseContainer';
import { getDatabasePath } from 'entities/Database/Database.helper';
import { IEquipmentModelCreatePayload } from 'entities/Equipments/Equipments.models';

interface IComponentProps {
  action?: string;
  id?: string;
}

type AllType = ReturnType<typeof mapState> & ReturnType<typeof mapDispatch> & IComponentProps;

const EquipmentCardComponent: React.FC<AllType> = (props) => {
  const { action, id, equipmentModel, getEquipmentModelById, createEquipmentModel, updateEquipmentModel, clearEquipmentModel } =
    props;
  const { data, loading } = equipmentModel;

  const externalId = id && Number(id);
  const [form] = useForm();

  const onSubmit = (values: IEquipmentModelCreatePayload) => {
    const { name } = values;
    const path = getDatabasePath(EDatabaseTabValues.SpecialEquipment);

    if (action === EDatabaseActions.Add) {
      createEquipmentModel({
        name,
        onSuccess: () => history.push(path),
      });
    } else {
      updateEquipmentModel({
        id: externalId as number,
        name,
        onSuccess: () => history.push(path),
      });
    }
  };

  const onCancel = () => {
    history.back();
  };

  useEffect(() => {
    if (action !== EDatabaseActions.Add && externalId) {
      getEquipmentModelById(externalId);
    }

    return () => {
      clearEquipmentModel();
    };
  }, [action, externalId]);

  useEffect(() => {
    if (data) {
      form.setFieldValue('name', data.name);
    }
  }, [data]);

  return (
    <Form form={form} className="container__form" onFinish={onSubmit}>
      <CommonDatabaseContainer action={action} saveDisabled={loading} cancelDisabled={loading} withFooter onCancel={onCancel}>
        <Spin spinning={loading}>
          <div className="database__container_item">
            <Form.Item label="Name" name="name" rules={[{ required: true, message: EFormFieldMessages.required }]}>
              <Input />
            </Form.Item>
          </div>
        </Spin>
      </CommonDatabaseContainer>
    </Form>
  );
};

const mapState = (state: RootState) => ({
  equipmentModel: state.equipmentModel,
});
const mapDispatch = (dispatch: RootDispatch) => ({
  getEquipmentModelById: dispatch.equipmentModel.getEquipmentModelById,
  createEquipmentModel: dispatch.equipmentModel.createEquipmentModel,
  updateEquipmentModel: dispatch.equipmentModel.updateEquipmentModel,
  clearEquipmentModel: dispatch.equipmentModel.clearEquipmentModel,
});

export const EquipmentCard = connect(mapState, mapDispatch)(EquipmentCardComponent);
