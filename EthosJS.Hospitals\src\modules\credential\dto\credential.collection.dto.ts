import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BaseCollectionDto } from '@app/common/dto/base.collection.dto';
import { CredentialEntity } from '@app/modules/credential/credential.entity';

export class CredentialCollectionDto extends BaseCollectionDto {
  @ApiProperty({ type: CredentialEntity, isArray: true })
  @Expose()
  data: CredentialEntity[]
}
