using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Ethos.Model;
using Ethos.Workflows.Api;
using Ethos.Workflows.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class InsuranceController(DbContext dbContext)
    : EntityControllerBase<InsuranceDbo, CreateInsuranceDto, InsuranceOutputDto, InsuranceQ>(dbContext)
{
    protected override InsuranceOutputDto MapToDto(InsuranceDbo dbo)
    {
        return new InsuranceOutputDto
        {
            Id = dbo.Id,
            InsuranceCarrier = dbo.InsuranceCarrier,
            InsuranceId = dbo.InsuranceId,
            PolicyId = dbo.PolicyId,
            GroupNumber = dbo.GroupNumber,
            MemberId = dbo.MemberId
        };
    }

    protected override InsuranceDbo CreateOrUpdateEntity(InsuranceDbo? entity, CreateInsuranceDto input, Guid? requiredId = null)
    {
        if (entity == null)
        {
            entity = new InsuranceDbo
            {
                Id = Guid.NewGuid(),
                InsuranceCarrier = input.InsuranceCarrier,
                InsuranceId = input.InsuranceId,
                PolicyId = input.PolicyId,
                GroupNumber = input.GroupNumber,
                MemberId = input.MemberId,
                InsuranceHolder = null,
                PhoneNumber = null
            };
            
            _dbSet.Add(entity);
        }
        else
        {
            entity.InsuranceCarrier = input.InsuranceCarrier;
            entity.InsuranceId = input.InsuranceId;
            entity.PolicyId = input.PolicyId;
            entity.GroupNumber = input.GroupNumber;
            entity.MemberId = input.MemberId;
        }
        return entity;
    }
}
