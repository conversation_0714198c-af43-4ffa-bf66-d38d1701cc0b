﻿using System;
using System.CodeDom.Compiler;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace EnsoLib
{

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum Expiring_link_type
    {

        [EnumMember(Value = @"study")]
        Study = 0,

    }

    /// <summary>
    /// Limit results to studies in the specified state.
    /// </summary>
    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum State
    {

        [EnumMember(Value = @"New")]
        New = 0,

        [EnumMember(Value = @"Processing")]
        Processing = 1,

        [EnumMember(Value = @"Done")]
        Done = 2,

        [EnumMember(Value = @"Skipped")]
        Skipped = 3,

        [EnumMember(Value = @"Error")]
        Error = 4,

    }

 

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum Status
    {

        [EnumMember(Value = @"DEBUG")]
        DEBUG = 0,

        [EnumMember(Value = @"CLOUDPAT_CONVERT_ERR")]
        CLOUDPAT_CONVERT_ERR = 1,

        [EnumMember(Value = @"MISSING_CHANNELS_ERR")]
        MISSING_CHANNELS_ERR = 2,

        [EnumMember(Value = @"MALFORMED_LIGHTS_ERR")]
        MALFORMED_LIGHTS_ERR = 3,

        [EnumMember(Value = @"FAIL_ERR")]
        FAIL_ERR = 4,

        [EnumMember(Value = @"NO_LONGER_EXISTS")]
        NO_LONGER_EXISTS = 5,

        [EnumMember(Value = @"PEDIATRIC")]
        PEDIATRIC = 6,

        [EnumMember(Value = @"API_ERR")]
        API_ERR = 7,

        [EnumMember(Value = @"REPORT_ERR")]
        REPORT_ERR = 8,

        [EnumMember(Value = @"MSLT_ERR")]
        MSLT_ERR = 9,

        [EnumMember(Value = @"NATUS_CONV_ERR")]
        NATUS_CONV_ERR = 10,

        [EnumMember(Value = @"SKIP_ERR")]
        SKIP_ERR = 11,

        [EnumMember(Value = @"EDF_ERR")]
        EDF_ERR = 12,

        [EnumMember(Value = @"SMALL_EDF_ERR")]
        SMALL_EDF_ERR = 13,

        [EnumMember(Value = @"BACKEND_ERR")]
        BACKEND_ERR = 14,

        [EnumMember(Value = @"WEB_ERR")]
        WEB_ERR = 15,

        [EnumMember(Value = @"INTEGRATION_SIGNAL_ERR")]
        INTEGRATION_SIGNAL_ERR = 16,

        [EnumMember(Value = @"INTEGRATION_WRITE_ERR")]
        INTEGRATION_WRITE_ERR = 17,

        [EnumMember(Value = @"INTEGRATION_READ_ERR")]
        INTEGRATION_READ_ERR = 18,

        [EnumMember(Value = @"MALFORMED_ERR")]
        MALFORMED_ERR = 19,

        [EnumMember(Value = @"HOLD_ERR")]
        HOLD_ERR = 20,

        [EnumMember(Value = @"DID_ERR")]
        DID_ERR = 21,

        [EnumMember(Value = @"SVC_ERR")]
        SVC_ERR = 22,

        [EnumMember(Value = @"ERROR")]
        ERROR = 23,

        [EnumMember(Value = @"UPLOADING")]
        UPLOADING = 24,

        [EnumMember(Value = @"UPLOADED")]
        UPLOADED = 25,

        [EnumMember(Value = @"DEIDENTIFYING")]
        DEIDENTIFYING = 26,

        [EnumMember(Value = @"TO_CONVERT")]
        TO_CONVERT = 27,

        [EnumMember(Value = @"TO_PROCESS")]
        TO_PROCESS = 28,

        [EnumMember(Value = @"DOWNLOADING")]
        DOWNLOADING = 29,

        [EnumMember(Value = @"COMPLETE")]
        COMPLETE = 30,

        [EnumMember(Value = @"REVERT")]
        REVERT = 31,

        [EnumMember(Value = @"HISTORICAL")]
        HISTORICAL = 32,

        [EnumMember(Value = @"HISTORICAL_DID")]
        HISTORICAL_DID = 33,

        [EnumMember(Value = @"PATIENT_OBJ")]
        PATIENT_OBJ = 34,

        [EnumMember(Value = @"EXTRACT")]
        EXTRACT = 35,

        [EnumMember(Value = @"CLASSIFY")]
        CLASSIFY = 36,

        [EnumMember(Value = @"POST_PROCESS")]
        POST_PROCESS = 37,

        [EnumMember(Value = @"PROCESSED")]
        PROCESSED = 38,

        [EnumMember(Value = @"REPORTING")]
        REPORTING = 39,

        [EnumMember(Value = @"NATUS_CONVERT")]
        NATUS_CONVERT = 40,

        [EnumMember(Value = @"READY_FOR_TRANSFER")]
        READY_FOR_TRANSFER = 41,

        [EnumMember(Value = @"TRANSFER_IN_PROGRESS")]
        TRANSFER_IN_PROGRESS = 42,

        [EnumMember(Value = @"TRANSFER_DOWNLOAD")]
        TRANSFER_DOWNLOAD = 43,

        [EnumMember(Value = @"TRANSFER_DOWNLOADING")]
        TRANSFER_DOWNLOADING = 44,

        [EnumMember(Value = @"HISTORICAL_TO_CONVERT")]
        HISTORICAL_TO_CONVERT = 45,

        [EnumMember(Value = @"REUPLOAD")]
        REUPLOAD = 46,

        [EnumMember(Value = @"DEIDENTIFIED")]
        DEIDENTIFIED = 47,

        [EnumMember(Value = @"READ_EVENTS")]
        READ_EVENTS = 48,

        [EnumMember(Value = @"READING_EVENTS")]
        READING_EVENTS = 49,

        [EnumMember(Value = @"READ_SIGNALS")]
        READ_SIGNALS = 50,

        [EnumMember(Value = @"READING_SIGNALS")]
        READING_SIGNALS = 51,

        [EnumMember(Value = @"WRITE_EVENTS")]
        WRITE_EVENTS = 52,

        [EnumMember(Value = @"WRITING_EVENTS")]
        WRITING_EVENTS = 53,

        [EnumMember(Value = @"DEIDENTIFY")]
        DEIDENTIFY = 54,

        [EnumMember(Value = @"CREATED")]
        CREATED = 55,

        [EnumMember(Value = @"REVERTED")]
        REVERTED = 56,

        [EnumMember(Value = @"UPLOAD_HOLD")]
        UPLOAD_HOLD = 57,

        [EnumMember(Value = @"TO_CLOUDPAT_CONVERT")]
        TO_CLOUDPAT_CONVERT = 58,

        [EnumMember(Value = @"CLOUDPAT_CONVERT")]
        CLOUDPAT_CONVERT = 59,

        [EnumMember(Value = @"WEB_CREATED")]
        WEB_CREATED = 60,

        [EnumMember(Value = @"WEB_UPLOADING")]
        WEB_UPLOADING = 61,

        [EnumMember(Value = @"ACQUIRING")]
        ACQUIRING = 62,

    }

    /// <summary>
    /// Limit results to studies in the specified status.
    /// </summary>
    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum Anonymous2
    {

        [EnumMember(Value = @"DEBUG")]
        DEBUG = 0,

        [EnumMember(Value = @"CLOUDPAT_CONVERT_ERR")]
        CLOUDPAT_CONVERT_ERR = 1,

        [EnumMember(Value = @"MISSING_CHANNELS_ERR")]
        MISSING_CHANNELS_ERR = 2,

        [EnumMember(Value = @"MALFORMED_LIGHTS_ERR")]
        MALFORMED_LIGHTS_ERR = 3,

        [EnumMember(Value = @"FAIL_ERR")]
        FAIL_ERR = 4,

        [EnumMember(Value = @"NO_LONGER_EXISTS")]
        NO_LONGER_EXISTS = 5,

        [EnumMember(Value = @"PEDIATRIC")]
        PEDIATRIC = 6,

        [EnumMember(Value = @"API_ERR")]
        API_ERR = 7,

        [EnumMember(Value = @"REPORT_ERR")]
        REPORT_ERR = 8,

        [EnumMember(Value = @"MSLT_ERR")]
        MSLT_ERR = 9,

        [EnumMember(Value = @"NATUS_CONV_ERR")]
        NATUS_CONV_ERR = 10,

        [EnumMember(Value = @"SKIP_ERR")]
        SKIP_ERR = 11,

        [EnumMember(Value = @"EDF_ERR")]
        EDF_ERR = 12,

        [EnumMember(Value = @"SMALL_EDF_ERR")]
        SMALL_EDF_ERR = 13,

        [EnumMember(Value = @"BACKEND_ERR")]
        BACKEND_ERR = 14,

        [EnumMember(Value = @"WEB_ERR")]
        WEB_ERR = 15,

        [EnumMember(Value = @"INTEGRATION_SIGNAL_ERR")]
        INTEGRATION_SIGNAL_ERR = 16,

        [EnumMember(Value = @"INTEGRATION_WRITE_ERR")]
        INTEGRATION_WRITE_ERR = 17,

        [EnumMember(Value = @"INTEGRATION_READ_ERR")]
        INTEGRATION_READ_ERR = 18,

        [EnumMember(Value = @"MALFORMED_ERR")]
        MALFORMED_ERR = 19,

        [EnumMember(Value = @"HOLD_ERR")]
        HOLD_ERR = 20,

        [EnumMember(Value = @"DID_ERR")]
        DID_ERR = 21,

        [EnumMember(Value = @"SVC_ERR")]
        SVC_ERR = 22,

        [EnumMember(Value = @"ERROR")]
        ERROR = 23,

        [EnumMember(Value = @"UPLOADING")]
        UPLOADING = 24,

        [EnumMember(Value = @"UPLOADED")]
        UPLOADED = 25,

        [EnumMember(Value = @"DEIDENTIFYING")]
        DEIDENTIFYING = 26,

        [EnumMember(Value = @"TO_CONVERT")]
        TO_CONVERT = 27,

        [EnumMember(Value = @"TO_PROCESS")]
        TO_PROCESS = 28,

        [EnumMember(Value = @"DOWNLOADING")]
        DOWNLOADING = 29,

        [EnumMember(Value = @"COMPLETE")]
        COMPLETE = 30,

        [EnumMember(Value = @"REVERT")]
        REVERT = 31,

        [EnumMember(Value = @"HISTORICAL")]
        HISTORICAL = 32,

        [EnumMember(Value = @"HISTORICAL_DID")]
        HISTORICAL_DID = 33,

        [EnumMember(Value = @"PATIENT_OBJ")]
        PATIENT_OBJ = 34,

        [EnumMember(Value = @"EXTRACT")]
        EXTRACT = 35,

        [EnumMember(Value = @"CLASSIFY")]
        CLASSIFY = 36,

        [EnumMember(Value = @"POST_PROCESS")]
        POST_PROCESS = 37,

        [EnumMember(Value = @"PROCESSED")]
        PROCESSED = 38,

        [EnumMember(Value = @"REPORTING")]
        REPORTING = 39,

        [EnumMember(Value = @"NATUS_CONVERT")]
        NATUS_CONVERT = 40,

        [EnumMember(Value = @"READY_FOR_TRANSFER")]
        READY_FOR_TRANSFER = 41,

        [EnumMember(Value = @"TRANSFER_IN_PROGRESS")]
        TRANSFER_IN_PROGRESS = 42,

        [EnumMember(Value = @"TRANSFER_DOWNLOAD")]
        TRANSFER_DOWNLOAD = 43,

        [EnumMember(Value = @"TRANSFER_DOWNLOADING")]
        TRANSFER_DOWNLOADING = 44,

        [EnumMember(Value = @"HISTORICAL_TO_CONVERT")]
        HISTORICAL_TO_CONVERT = 45,

        [EnumMember(Value = @"REUPLOAD")]
        REUPLOAD = 46,

        [EnumMember(Value = @"DEIDENTIFIED")]
        DEIDENTIFIED = 47,

        [EnumMember(Value = @"READ_EVENTS")]
        READ_EVENTS = 48,

        [EnumMember(Value = @"READING_EVENTS")]
        READING_EVENTS = 49,

        [EnumMember(Value = @"READ_SIGNALS")]
        READ_SIGNALS = 50,

        [EnumMember(Value = @"READING_SIGNALS")]
        READING_SIGNALS = 51,

        [EnumMember(Value = @"WRITE_EVENTS")]
        WRITE_EVENTS = 52,

        [EnumMember(Value = @"WRITING_EVENTS")]
        WRITING_EVENTS = 53,

        [EnumMember(Value = @"DEIDENTIFY")]
        DEIDENTIFY = 54,

        [EnumMember(Value = @"CREATED")]
        CREATED = 55,

        [EnumMember(Value = @"REVERTED")]
        REVERTED = 56,

        [EnumMember(Value = @"UPLOAD_HOLD")]
        UPLOAD_HOLD = 57,

        [EnumMember(Value = @"TO_CLOUDPAT_CONVERT")]
        TO_CLOUDPAT_CONVERT = 58,

        [EnumMember(Value = @"CLOUDPAT_CONVERT")]
        CLOUDPAT_CONVERT = 59,

        [EnumMember(Value = @"WEB_CREATED")]
        WEB_CREATED = 60,

        [EnumMember(Value = @"WEB_UPLOADING")]
        WEB_UPLOADING = 61,

        [EnumMember(Value = @"ACQUIRING")]
        ACQUIRING = 62,

    }

    /// <summary>
    /// The version of the study. New studies should upload to the `CURRENT` version and grab EnsoSleep scoring from the `SCORED` version. A backup of the study is made to `ORIGINAL`.
    /// </summary>
    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum Version
    {

        [EnumMember(Value = @"ORIGINAL")]
        ORIGINAL = 0,

        [EnumMember(Value = @"CURRENT")]
        CURRENT = 1,

        [EnumMember(Value = @"SCORED")]
        SCORED = 2,

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum Group_by
    {

        [EnumMember(Value = @"type")]
        Type = 0,

        [EnumMember(Value = @"name")]
        Name = 1,

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum ScoreEventName
    {

        [EnumMember(Value = @"central_apnea")]
        Central_apnea = 0,

        [EnumMember(Value = @"obstructive_apnea")]
        Obstructive_apnea = 1,

        [EnumMember(Value = @"mixed_apnea")]
        Mixed_apnea = 2,

        [EnumMember(Value = @"central_hypopnea")]
        Central_hypopnea = 3,

        [EnumMember(Value = @"mixed_hypopnea")]
        Mixed_hypopnea = 4,

        [EnumMember(Value = @"obstructive_hypopnea")]
        Obstructive_hypopnea = 5,

        [EnumMember(Value = @"central_respiratory")]
        Central_respiratory = 6,

        [EnumMember(Value = @"obstructive_respiratory")]
        Obstructive_respiratory = 7,

        [EnumMember(Value = @"unclassified_respiratory")]
        Unclassified_respiratory = 8,

        [EnumMember(Value = @"periodic_breathing")]
        Periodic_breathing = 9,

        [EnumMember(Value = @"cheyne_stokes_breathing")]
        Cheyne_stokes_breathing = 10,

        [EnumMember(Value = @"rera")]
        Rera = 11,

        [EnumMember(Value = @"hypoventilation")]
        Hypoventilation = 12,

        [EnumMember(Value = @"hypercapnia")]
        Hypercapnia = 13,

        [EnumMember(Value = @"hypopnea")]
        Hypopnea = 14,

        [EnumMember(Value = @"respiratory_event")]
        Respiratory_event = 15,

        [EnumMember(Value = @"bradycardia")]
        Bradycardia = 16,

        [EnumMember(Value = @"tachycardia")]
        Tachycardia = 17,

        [EnumMember(Value = @"sinus_tachycardia")]
        Sinus_tachycardia = 18,

        [EnumMember(Value = @"narrow_complex_tachycardia")]
        Narrow_complex_tachycardia = 19,

        [EnumMember(Value = @"wide_complex_tachycardia")]
        Wide_complex_tachycardia = 20,

        [EnumMember(Value = @"atrial_fibrillation")]
        Atrial_fibrillation = 21,

        [EnumMember(Value = @"asystole")]
        Asystole = 22,

        [EnumMember(Value = @"premature_atrial_contraction")]
        Premature_atrial_contraction = 23,

        [EnumMember(Value = @"premature_ventricular_contraction")]
        Premature_ventricular_contraction = 24,

        [EnumMember(Value = @"desaturation")]
        Desaturation = 25,

        [EnumMember(Value = @"sustained_desaturation")]
        Sustained_desaturation = 26,

        [EnumMember(Value = @"snore")]
        Snore = 27,

        [EnumMember(Value = @"arousal")]
        Arousal = 28,

        [EnumMember(Value = @"rem_behavior_disorder")]
        Rem_behavior_disorder = 29,

        [EnumMember(Value = @"bruxism")]
        Bruxism = 30,

        [EnumMember(Value = @"leg_movement")]
        Leg_movement = 31,

        [EnumMember(Value = @"periodic_leg_movement")]
        Periodic_leg_movement = 32,

        [EnumMember(Value = @"alternating_leg_muscle_activation")]
        Alternating_leg_muscle_activation = 33,

        [EnumMember(Value = @"hypnagogic_foot_tremor")]
        Hypnagogic_foot_tremor = 34,

        [EnumMember(Value = @"excessive_fragmentary_myoclonus")]
        Excessive_fragmentary_myoclonus = 35,

        [EnumMember(Value = @"rhythmic_movement_disorder")]
        Rhythmic_movement_disorder = 36,

        [EnumMember(Value = @"left_leg_movement")]
        Left_leg_movement = 37,

        [EnumMember(Value = @"right_leg_movement")]
        Right_leg_movement = 38,

        [EnumMember(Value = @"unstaged")]
        Unstaged = 39,

        [EnumMember(Value = @"wake")]
        Wake = 40,

        [EnumMember(Value = @"n1")]
        N1 = 41,

        [EnumMember(Value = @"n2")]
        N2 = 42,

        [EnumMember(Value = @"n3")]
        N3 = 43,

        [EnumMember(Value = @"rem")]
        Rem = 44,

        [EnumMember(Value = @"bad_data")]
        Bad_data = 45,

        [EnumMember(Value = @"cpap")]
        Cpap = 46,

        [EnumMember(Value = @"bpap")]
        Bpap = 47,

        [EnumMember(Value = @"apap")]
        Apap = 48,

        [EnumMember(Value = @"adaptive_servo_ventilation")]
        Adaptive_servo_ventilation = 49,

        [EnumMember(Value = @"auto_ventilation")]
        Auto_ventilation = 50,

        [EnumMember(Value = @"oxygen")]
        Oxygen = 51,

        [EnumMember(Value = @"phrenic_nerve_stimulator")]
        Phrenic_nerve_stimulator = 52,

        [EnumMember(Value = @"npap")]
        Npap = 53,

        [EnumMember(Value = @"vpap")]
        Vpap = 54,

        [EnumMember(Value = @"oral_appliance")]
        Oral_appliance = 55,

        [EnumMember(Value = @"hypoglossal_nerve_stimulator")]
        Hypoglossal_nerve_stimulator = 56,

        [EnumMember(Value = @"upright")]
        Upright = 57,

        [EnumMember(Value = @"supine")]
        Supine = 58,

        [EnumMember(Value = @"prone")]
        Prone = 59,

        [EnumMember(Value = @"right")]
        Right = 60,

        [EnumMember(Value = @"left")]
        Left = 61,

        [EnumMember(Value = @"supine_left")]
        Supine_left = 62,

        [EnumMember(Value = @"supine_right")]
        Supine_right = 63,

        [EnumMember(Value = @"prone_left")]
        Prone_left = 64,

        [EnumMember(Value = @"prone_right")]
        Prone_right = 65,

        [EnumMember(Value = @"lights_on")]
        Lights_on = 66,

        [EnumMember(Value = @"lights_off")]
        Lights_off = 67,

        [EnumMember(Value = @"comment")]
        Comment = 68,

        [EnumMember(Value = @"inadequate")]
        Inadequate = 69,

        [EnumMember(Value = @"adequate")]
        Adequate = 70,

        [EnumMember(Value = @"alert")]
        Alert = 71,

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum ScoreEventAssociation
    {

        [EnumMember(Value = @"resp")]
        Resp = 0,

        [EnumMember(Value = @"spont")]
        Spont = 1,

        [EnumMember(Value = @"limb")]
        Limb = 2,

        [EnumMember(Value = @"rera")]
        Rera = 3,

        [EnumMember(Value = @"awakening")]
        Awakening = 4,

        [EnumMember(Value = @"plm")]
        Plm = 5,

        [EnumMember(Value = @"snore")]
        Snore = 6,

        [EnumMember(Value = @"bruxism")]
        Bruxism = 7,

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum StudyPatientGender
    {

        [EnumMember(Value = @"male")]
        Male = 0,

        [EnumMember(Value = @"female")]
        Female = 1,

        [EnumMember(Value = @"other")]
        Other = 2,

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum StudyOptionsDesaturation
    {

        _3 = 3,

        _4 = 4,

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum StudySoftware
    {

        [EnumMember(Value = @"AirView")]
        AirView = 0,

        [EnumMember(Value = @"Alice")]
        Alice = 1,

        [EnumMember(Value = @"Cadwell")]
        Cadwell = 2,

        [EnumMember(Value = @"Carefusion")]
        Carefusion = 3,

        [EnumMember(Value = @"Compumedics")]
        Compumedics = 4,

        [EnumMember(Value = @"EDF")]
        EDF = 5,

        [EnumMember(Value = @"Natus Sandman Elite")]
        Natus_Sandman_Elite = 6,

        [EnumMember(Value = @"NeuroVirtual")]
        NeuroVirtual = 7,

        [EnumMember(Value = @"NihonKohden")]
        NihonKohden = 8,

        [EnumMember(Value = @"Nox")]
        Nox = 9,

        [EnumMember(Value = @"RemLogic")]
        RemLogic = 10,

        [EnumMember(Value = @"Sleepware G3")]
        Sleepware_G3 = 11,

        [EnumMember(Value = @"Snap")]
        Snap = 12,

        [EnumMember(Value = @"Xltek")]
        Xltek = 13,

        [EnumMember(Value = @"DreamClear")]
        DreamClear = 14,

        [EnumMember(Value = @"WatchPAT")]
        WatchPAT = 15,

        [EnumMember(Value = @"Viatom")]
        Viatom = 16,

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum StudyState
    {

        [EnumMember(Value = @"New")]
        New = 0,

        [EnumMember(Value = @"Processing")]
        Processing = 1,

        [EnumMember(Value = @"Done")]
        Done = 2,

        [EnumMember(Value = @"Skipped")]
        Skipped = 3,

        [EnumMember(Value = @"Error")]
        Error = 4,

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum StudyStatus
    {

        [EnumMember(Value = @"DEBUG")]
        DEBUG = 0,

        [EnumMember(Value = @"CLOUDPAT_CONVERT_ERR")]
        CLOUDPAT_CONVERT_ERR = 1,

        [EnumMember(Value = @"MISSING_CHANNELS_ERR")]
        MISSING_CHANNELS_ERR = 2,

        [EnumMember(Value = @"MALFORMED_LIGHTS_ERR")]
        MALFORMED_LIGHTS_ERR = 3,

        [EnumMember(Value = @"FAIL_ERR")]
        FAIL_ERR = 4,

        [EnumMember(Value = @"NO_LONGER_EXISTS")]
        NO_LONGER_EXISTS = 5,

        [EnumMember(Value = @"PEDIATRIC")]
        PEDIATRIC = 6,

        [EnumMember(Value = @"API_ERR")]
        API_ERR = 7,

        [EnumMember(Value = @"REPORT_ERR")]
        REPORT_ERR = 8,

        [EnumMember(Value = @"MSLT_ERR")]
        MSLT_ERR = 9,

        [EnumMember(Value = @"NATUS_CONV_ERR")]
        NATUS_CONV_ERR = 10,

        [EnumMember(Value = @"SKIP_ERR")]
        SKIP_ERR = 11,

        [EnumMember(Value = @"EDF_ERR")]
        EDF_ERR = 12,

        [EnumMember(Value = @"SMALL_EDF_ERR")]
        SMALL_EDF_ERR = 13,

        [EnumMember(Value = @"BACKEND_ERR")]
        BACKEND_ERR = 14,

        [EnumMember(Value = @"WEB_ERR")]
        WEB_ERR = 15,

        [EnumMember(Value = @"INTEGRATION_SIGNAL_ERR")]
        INTEGRATION_SIGNAL_ERR = 16,

        [EnumMember(Value = @"INTEGRATION_WRITE_ERR")]
        INTEGRATION_WRITE_ERR = 17,

        [EnumMember(Value = @"INTEGRATION_READ_ERR")]
        INTEGRATION_READ_ERR = 18,

        [EnumMember(Value = @"MALFORMED_ERR")]
        MALFORMED_ERR = 19,

        [EnumMember(Value = @"HOLD_ERR")]
        HOLD_ERR = 20,

        [EnumMember(Value = @"DID_ERR")]
        DID_ERR = 21,

        [EnumMember(Value = @"SVC_ERR")]
        SVC_ERR = 22,

        [EnumMember(Value = @"ERROR")]
        ERROR = 23,

        [EnumMember(Value = @"UPLOADING")]
        UPLOADING = 24,

        [EnumMember(Value = @"UPLOADED")]
        UPLOADED = 25,

        [EnumMember(Value = @"DEIDENTIFYING")]
        DEIDENTIFYING = 26,

        [EnumMember(Value = @"TO_CONVERT")]
        TO_CONVERT = 27,

        [EnumMember(Value = @"TO_PROCESS")]
        TO_PROCESS = 28,

        [EnumMember(Value = @"DOWNLOADING")]
        DOWNLOADING = 29,

        [EnumMember(Value = @"COMPLETE")]
        COMPLETE = 30,

        [EnumMember(Value = @"REVERT")]
        REVERT = 31,

        [EnumMember(Value = @"HISTORICAL")]
        HISTORICAL = 32,

        [EnumMember(Value = @"HISTORICAL_DID")]
        HISTORICAL_DID = 33,

        [EnumMember(Value = @"PATIENT_OBJ")]
        PATIENT_OBJ = 34,

        [EnumMember(Value = @"EXTRACT")]
        EXTRACT = 35,

        [EnumMember(Value = @"CLASSIFY")]
        CLASSIFY = 36,

        [EnumMember(Value = @"POST_PROCESS")]
        POST_PROCESS = 37,

        [EnumMember(Value = @"PROCESSED")]
        PROCESSED = 38,

        [EnumMember(Value = @"REPORTING")]
        REPORTING = 39,

        [EnumMember(Value = @"NATUS_CONVERT")]
        NATUS_CONVERT = 40,

        [EnumMember(Value = @"READY_FOR_TRANSFER")]
        READY_FOR_TRANSFER = 41,

        [EnumMember(Value = @"TRANSFER_IN_PROGRESS")]
        TRANSFER_IN_PROGRESS = 42,

        [EnumMember(Value = @"TRANSFER_DOWNLOAD")]
        TRANSFER_DOWNLOAD = 43,

        [EnumMember(Value = @"TRANSFER_DOWNLOADING")]
        TRANSFER_DOWNLOADING = 44,

        [EnumMember(Value = @"HISTORICAL_TO_CONVERT")]
        HISTORICAL_TO_CONVERT = 45,

        [EnumMember(Value = @"REUPLOAD")]
        REUPLOAD = 46,

        [EnumMember(Value = @"DEIDENTIFIED")]
        DEIDENTIFIED = 47,

        [EnumMember(Value = @"READ_EVENTS")]
        READ_EVENTS = 48,

        [EnumMember(Value = @"READING_EVENTS")]
        READING_EVENTS = 49,

        [EnumMember(Value = @"READ_SIGNALS")]
        READ_SIGNALS = 50,

        [EnumMember(Value = @"READING_SIGNALS")]
        READING_SIGNALS = 51,

        [EnumMember(Value = @"WRITE_EVENTS")]
        WRITE_EVENTS = 52,

        [EnumMember(Value = @"WRITING_EVENTS")]
        WRITING_EVENTS = 53,

        [EnumMember(Value = @"DEIDENTIFY")]
        DEIDENTIFY = 54,

        [EnumMember(Value = @"CREATED")]
        CREATED = 55,

        [EnumMember(Value = @"REVERTED")]
        REVERTED = 56,

        [EnumMember(Value = @"UPLOAD_HOLD")]
        UPLOAD_HOLD = 57,

        [EnumMember(Value = @"TO_CLOUDPAT_CONVERT")]
        TO_CLOUDPAT_CONVERT = 58,

        [EnumMember(Value = @"CLOUDPAT_CONVERT")]
        CLOUDPAT_CONVERT = 59,

        [EnumMember(Value = @"WEB_CREATED")]
        WEB_CREATED = 60,

        [EnumMember(Value = @"WEB_UPLOADING")]
        WEB_UPLOADING = 61,

        [EnumMember(Value = @"ACQUIRING")]
        ACQUIRING = 62,

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum CreateStudySoftware
    {

        [EnumMember(Value = @"AirView")]
        AirView = 0,

        [EnumMember(Value = @"Alice")]
        Alice = 1,

        [EnumMember(Value = @"Cadwell")]
        Cadwell = 2,

        [EnumMember(Value = @"Carefusion")]
        Carefusion = 3,

        [EnumMember(Value = @"Compumedics")]
        Compumedics = 4,

        [EnumMember(Value = @"EDF")]
        EDF = 5,

        [EnumMember(Value = @"Natus Sandman Elite")]
        Natus_Sandman_Elite = 6,

        [EnumMember(Value = @"NeuroVirtual")]
        NeuroVirtual = 7,

        [EnumMember(Value = @"NihonKohden")]
        NihonKohden = 8,

        [EnumMember(Value = @"Nox")]
        Nox = 9,

        [EnumMember(Value = @"RemLogic")]
        RemLogic = 10,

        [EnumMember(Value = @"Sleepware G3")]
        Sleepware_G3 = 11,

        [EnumMember(Value = @"Snap")]
        Snap = 12,

        [EnumMember(Value = @"Xltek")]
        Xltek = 13,

        [EnumMember(Value = @"DreamClear")]
        DreamClear = 14,

        [EnumMember(Value = @"WatchPAT")]
        WatchPAT = 15,

        [EnumMember(Value = @"Viatom")]
        Viatom = 16,

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum CreateStudyState
    {

        [EnumMember(Value = @"New")]
        New = 0,

        [EnumMember(Value = @"Processing")]
        Processing = 1,

        [EnumMember(Value = @"Done")]
        Done = 2,

        [EnumMember(Value = @"Skipped")]
        Skipped = 3,

        [EnumMember(Value = @"Error")]
        Error = 4,

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum CreateStudyStatus
    {

        [EnumMember(Value = @"DEBUG")]
        DEBUG = 0,

        [EnumMember(Value = @"CLOUDPAT_CONVERT_ERR")]
        CLOUDPAT_CONVERT_ERR = 1,

        [EnumMember(Value = @"MISSING_CHANNELS_ERR")]
        MISSING_CHANNELS_ERR = 2,

        [EnumMember(Value = @"MALFORMED_LIGHTS_ERR")]
        MALFORMED_LIGHTS_ERR = 3,

        [EnumMember(Value = @"FAIL_ERR")]
        FAIL_ERR = 4,

        [EnumMember(Value = @"NO_LONGER_EXISTS")]
        NO_LONGER_EXISTS = 5,

        [EnumMember(Value = @"PEDIATRIC")]
        PEDIATRIC = 6,

        [EnumMember(Value = @"API_ERR")]
        API_ERR = 7,

        [EnumMember(Value = @"REPORT_ERR")]
        REPORT_ERR = 8,

        [EnumMember(Value = @"MSLT_ERR")]
        MSLT_ERR = 9,

        [EnumMember(Value = @"NATUS_CONV_ERR")]
        NATUS_CONV_ERR = 10,

        [EnumMember(Value = @"SKIP_ERR")]
        SKIP_ERR = 11,

        [EnumMember(Value = @"EDF_ERR")]
        EDF_ERR = 12,

        [EnumMember(Value = @"SMALL_EDF_ERR")]
        SMALL_EDF_ERR = 13,

        [EnumMember(Value = @"BACKEND_ERR")]
        BACKEND_ERR = 14,

        [EnumMember(Value = @"WEB_ERR")]
        WEB_ERR = 15,

        [EnumMember(Value = @"INTEGRATION_SIGNAL_ERR")]
        INTEGRATION_SIGNAL_ERR = 16,

        [EnumMember(Value = @"INTEGRATION_WRITE_ERR")]
        INTEGRATION_WRITE_ERR = 17,

        [EnumMember(Value = @"INTEGRATION_READ_ERR")]
        INTEGRATION_READ_ERR = 18,

        [EnumMember(Value = @"MALFORMED_ERR")]
        MALFORMED_ERR = 19,

        [EnumMember(Value = @"HOLD_ERR")]
        HOLD_ERR = 20,

        [EnumMember(Value = @"DID_ERR")]
        DID_ERR = 21,

        [EnumMember(Value = @"SVC_ERR")]
        SVC_ERR = 22,

        [EnumMember(Value = @"ERROR")]
        ERROR = 23,

        [EnumMember(Value = @"UPLOADING")]
        UPLOADING = 24,

        [EnumMember(Value = @"UPLOADED")]
        UPLOADED = 25,

        [EnumMember(Value = @"DEIDENTIFYING")]
        DEIDENTIFYING = 26,

        [EnumMember(Value = @"TO_CONVERT")]
        TO_CONVERT = 27,

        [EnumMember(Value = @"TO_PROCESS")]
        TO_PROCESS = 28,

        [EnumMember(Value = @"DOWNLOADING")]
        DOWNLOADING = 29,

        [EnumMember(Value = @"COMPLETE")]
        COMPLETE = 30,

        [EnumMember(Value = @"REVERT")]
        REVERT = 31,

        [EnumMember(Value = @"HISTORICAL")]
        HISTORICAL = 32,

        [EnumMember(Value = @"HISTORICAL_DID")]
        HISTORICAL_DID = 33,

        [EnumMember(Value = @"PATIENT_OBJ")]
        PATIENT_OBJ = 34,

        [EnumMember(Value = @"EXTRACT")]
        EXTRACT = 35,

        [EnumMember(Value = @"CLASSIFY")]
        CLASSIFY = 36,

        [EnumMember(Value = @"POST_PROCESS")]
        POST_PROCESS = 37,

        [EnumMember(Value = @"PROCESSED")]
        PROCESSED = 38,

        [EnumMember(Value = @"REPORTING")]
        REPORTING = 39,

        [EnumMember(Value = @"NATUS_CONVERT")]
        NATUS_CONVERT = 40,

        [EnumMember(Value = @"READY_FOR_TRANSFER")]
        READY_FOR_TRANSFER = 41,

        [EnumMember(Value = @"TRANSFER_IN_PROGRESS")]
        TRANSFER_IN_PROGRESS = 42,

        [EnumMember(Value = @"TRANSFER_DOWNLOAD")]
        TRANSFER_DOWNLOAD = 43,

        [EnumMember(Value = @"TRANSFER_DOWNLOADING")]
        TRANSFER_DOWNLOADING = 44,

        [EnumMember(Value = @"HISTORICAL_TO_CONVERT")]
        HISTORICAL_TO_CONVERT = 45,

        [EnumMember(Value = @"REUPLOAD")]
        REUPLOAD = 46,

        [EnumMember(Value = @"DEIDENTIFIED")]
        DEIDENTIFIED = 47,

        [EnumMember(Value = @"READ_EVENTS")]
        READ_EVENTS = 48,

        [EnumMember(Value = @"READING_EVENTS")]
        READING_EVENTS = 49,

        [EnumMember(Value = @"READ_SIGNALS")]
        READ_SIGNALS = 50,

        [EnumMember(Value = @"READING_SIGNALS")]
        READING_SIGNALS = 51,

        [EnumMember(Value = @"WRITE_EVENTS")]
        WRITE_EVENTS = 52,

        [EnumMember(Value = @"WRITING_EVENTS")]
        WRITING_EVENTS = 53,

        [EnumMember(Value = @"DEIDENTIFY")]
        DEIDENTIFY = 54,

        [EnumMember(Value = @"CREATED")]
        CREATED = 55,

        [EnumMember(Value = @"REVERTED")]
        REVERTED = 56,

        [EnumMember(Value = @"UPLOAD_HOLD")]
        UPLOAD_HOLD = 57,

        [EnumMember(Value = @"TO_CLOUDPAT_CONVERT")]
        TO_CLOUDPAT_CONVERT = 58,

        [EnumMember(Value = @"CLOUDPAT_CONVERT")]
        CLOUDPAT_CONVERT = 59,

        [EnumMember(Value = @"WEB_CREATED")]
        WEB_CREATED = 60,

        [EnumMember(Value = @"WEB_UPLOADING")]
        WEB_UPLOADING = 61,

        [EnumMember(Value = @"ACQUIRING")]
        ACQUIRING = 62,

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum UpdateStudySoftware
    {

        [EnumMember(Value = @"AirView")]
        AirView = 0,

        [EnumMember(Value = @"Alice")]
        Alice = 1,

        [EnumMember(Value = @"Cadwell")]
        Cadwell = 2,

        [EnumMember(Value = @"Carefusion")]
        Carefusion = 3,

        [EnumMember(Value = @"Compumedics")]
        Compumedics = 4,

        [EnumMember(Value = @"EDF")]
        EDF = 5,

        [EnumMember(Value = @"Natus Sandman Elite")]
        Natus_Sandman_Elite = 6,

        [EnumMember(Value = @"NeuroVirtual")]
        NeuroVirtual = 7,

        [EnumMember(Value = @"NihonKohden")]
        NihonKohden = 8,

        [EnumMember(Value = @"Nox")]
        Nox = 9,

        [EnumMember(Value = @"RemLogic")]
        RemLogic = 10,

        [EnumMember(Value = @"Sleepware G3")]
        Sleepware_G3 = 11,

        [EnumMember(Value = @"Snap")]
        Snap = 12,

        [EnumMember(Value = @"Xltek")]
        Xltek = 13,

        [EnumMember(Value = @"DreamClear")]
        DreamClear = 14,

        [EnumMember(Value = @"WatchPAT")]
        WatchPAT = 15,

        [EnumMember(Value = @"Viatom")]
        Viatom = 16,

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum UpdateStudyState
    {

        [EnumMember(Value = @"New")]
        New = 0,

        [EnumMember(Value = @"Processing")]
        Processing = 1,

        [EnumMember(Value = @"Done")]
        Done = 2,

        [EnumMember(Value = @"Skipped")]
        Skipped = 3,

        [EnumMember(Value = @"Error")]
        Error = 4,

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum UpdateStudyStatus
    {

        [EnumMember(Value = @"DEBUG")]
        DEBUG = 0,

        [EnumMember(Value = @"CLOUDPAT_CONVERT_ERR")]
        CLOUDPAT_CONVERT_ERR = 1,

        [EnumMember(Value = @"MISSING_CHANNELS_ERR")]
        MISSING_CHANNELS_ERR = 2,

        [EnumMember(Value = @"MALFORMED_LIGHTS_ERR")]
        MALFORMED_LIGHTS_ERR = 3,

        [EnumMember(Value = @"FAIL_ERR")]
        FAIL_ERR = 4,

        [EnumMember(Value = @"NO_LONGER_EXISTS")]
        NO_LONGER_EXISTS = 5,

        [EnumMember(Value = @"PEDIATRIC")]
        PEDIATRIC = 6,

        [EnumMember(Value = @"API_ERR")]
        API_ERR = 7,

        [EnumMember(Value = @"REPORT_ERR")]
        REPORT_ERR = 8,

        [EnumMember(Value = @"MSLT_ERR")]
        MSLT_ERR = 9,

        [EnumMember(Value = @"NATUS_CONV_ERR")]
        NATUS_CONV_ERR = 10,

        [EnumMember(Value = @"SKIP_ERR")]
        SKIP_ERR = 11,

        [EnumMember(Value = @"EDF_ERR")]
        EDF_ERR = 12,

        [EnumMember(Value = @"SMALL_EDF_ERR")]
        SMALL_EDF_ERR = 13,

        [EnumMember(Value = @"BACKEND_ERR")]
        BACKEND_ERR = 14,

        [EnumMember(Value = @"WEB_ERR")]
        WEB_ERR = 15,

        [EnumMember(Value = @"INTEGRATION_SIGNAL_ERR")]
        INTEGRATION_SIGNAL_ERR = 16,

        [EnumMember(Value = @"INTEGRATION_WRITE_ERR")]
        INTEGRATION_WRITE_ERR = 17,

        [EnumMember(Value = @"INTEGRATION_READ_ERR")]
        INTEGRATION_READ_ERR = 18,

        [EnumMember(Value = @"MALFORMED_ERR")]
        MALFORMED_ERR = 19,

        [EnumMember(Value = @"HOLD_ERR")]
        HOLD_ERR = 20,

        [EnumMember(Value = @"DID_ERR")]
        DID_ERR = 21,

        [EnumMember(Value = @"SVC_ERR")]
        SVC_ERR = 22,

        [EnumMember(Value = @"ERROR")]
        ERROR = 23,

        [EnumMember(Value = @"UPLOADING")]
        UPLOADING = 24,

        [EnumMember(Value = @"UPLOADED")]
        UPLOADED = 25,

        [EnumMember(Value = @"DEIDENTIFYING")]
        DEIDENTIFYING = 26,

        [EnumMember(Value = @"TO_CONVERT")]
        TO_CONVERT = 27,

        [EnumMember(Value = @"TO_PROCESS")]
        TO_PROCESS = 28,

        [EnumMember(Value = @"DOWNLOADING")]
        DOWNLOADING = 29,

        [EnumMember(Value = @"COMPLETE")]
        COMPLETE = 30,

        [EnumMember(Value = @"REVERT")]
        REVERT = 31,

        [EnumMember(Value = @"HISTORICAL")]
        HISTORICAL = 32,

        [EnumMember(Value = @"HISTORICAL_DID")]
        HISTORICAL_DID = 33,

        [EnumMember(Value = @"PATIENT_OBJ")]
        PATIENT_OBJ = 34,

        [EnumMember(Value = @"EXTRACT")]
        EXTRACT = 35,

        [EnumMember(Value = @"CLASSIFY")]
        CLASSIFY = 36,

        [EnumMember(Value = @"POST_PROCESS")]
        POST_PROCESS = 37,

        [EnumMember(Value = @"PROCESSED")]
        PROCESSED = 38,

        [EnumMember(Value = @"REPORTING")]
        REPORTING = 39,

        [EnumMember(Value = @"NATUS_CONVERT")]
        NATUS_CONVERT = 40,

        [EnumMember(Value = @"READY_FOR_TRANSFER")]
        READY_FOR_TRANSFER = 41,

        [EnumMember(Value = @"TRANSFER_IN_PROGRESS")]
        TRANSFER_IN_PROGRESS = 42,

        [EnumMember(Value = @"TRANSFER_DOWNLOAD")]
        TRANSFER_DOWNLOAD = 43,

        [EnumMember(Value = @"TRANSFER_DOWNLOADING")]
        TRANSFER_DOWNLOADING = 44,

        [EnumMember(Value = @"HISTORICAL_TO_CONVERT")]
        HISTORICAL_TO_CONVERT = 45,

        [EnumMember(Value = @"REUPLOAD")]
        REUPLOAD = 46,

        [EnumMember(Value = @"DEIDENTIFIED")]
        DEIDENTIFIED = 47,

        [EnumMember(Value = @"READ_EVENTS")]
        READ_EVENTS = 48,

        [EnumMember(Value = @"READING_EVENTS")]
        READING_EVENTS = 49,

        [EnumMember(Value = @"READ_SIGNALS")]
        READ_SIGNALS = 50,

        [EnumMember(Value = @"READING_SIGNALS")]
        READING_SIGNALS = 51,

        [EnumMember(Value = @"WRITE_EVENTS")]
        WRITE_EVENTS = 52,

        [EnumMember(Value = @"WRITING_EVENTS")]
        WRITING_EVENTS = 53,

        [EnumMember(Value = @"DEIDENTIFY")]
        DEIDENTIFY = 54,

        [EnumMember(Value = @"CREATED")]
        CREATED = 55,

        [EnumMember(Value = @"REVERTED")]
        REVERTED = 56,

        [EnumMember(Value = @"UPLOAD_HOLD")]
        UPLOAD_HOLD = 57,

        [EnumMember(Value = @"TO_CLOUDPAT_CONVERT")]
        TO_CLOUDPAT_CONVERT = 58,

        [EnumMember(Value = @"CLOUDPAT_CONVERT")]
        CLOUDPAT_CONVERT = 59,

        [EnumMember(Value = @"WEB_CREATED")]
        WEB_CREATED = 60,

        [EnumMember(Value = @"WEB_UPLOADING")]
        WEB_UPLOADING = 61,

        [EnumMember(Value = @"ACQUIRING")]
        ACQUIRING = 62,

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum UpdateStudyPatient__gender
    {

        [EnumMember(Value = @"male")]
        Male = 0,

        [EnumMember(Value = @"female")]
        Female = 1,

        [EnumMember(Value = @"other")]
        Other = 2,

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum UpdateStudyOptions__desaturation
    {

        _3 = 3,

        _4 = 4,

    }

}
