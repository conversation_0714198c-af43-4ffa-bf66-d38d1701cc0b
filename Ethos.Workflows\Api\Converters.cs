using Ethos.Model;
using Ethos.Utilities;
using Ethos.Workflows.Files;

namespace Ethos.Workflows.Api;

public static class Converters
{
    public interface IConvertContext
    {
        void Delete<T>(T entity) where T : class, IEntity;
    }
    
    public static AddressDto ToDto(this AddressDbo dbo) =>
        new AddressDto
        {
            Line1 = dbo.Line1,
            Line2 = dbo.Line2,
            City = dbo.City,
            State = dbo.StateId,
            PostalCode = dbo.PostalCode,
            Country = dbo.CountryId
        };
    public static AddressDbo ToEntity(this AddressDto dto) =>
        new AddressDbo
        {
            Line1 = dto.Line1,
            Line2 = dto.Line2,
            City = dto.City,
            StateId = dto.State,
            PostalCode = dto.PostalCode,
            CountryId = dto.Country
        };
    
    public static OrganizationEmailDto ToDto(this OrganizationEmailDbo entity) => 
        new OrganizationEmailDto
        {
            Email = entity.Email
        };
    public static OrganizationEmailDbo ToEntity(this OrganizationEmailDto dto) =>
        new OrganizationEmailDbo
        {
            Email = dto.Email
        };
    
    public static OrganizationPhoneNumberDto ToDto(this OrganizationPhoneNumberDbo entity) =>
        new OrganizationPhoneNumberDto
        {
            PhoneNumber = entity.PhoneNumber
        };
    public static OrganizationPhoneNumberDbo ToEntity(this OrganizationPhoneNumberDto dto) =>
        new OrganizationPhoneNumberDbo
        {
            PhoneNumber = dto.PhoneNumber
        };

    public static OrganizationAddressDto ToDto(this OrganizationAddressDbo entity) =>
        new OrganizationAddressDto()
        {
            Use = entity.UseId,
            Type = entity.TypeId,
            Address = entity.Address.ToDto()
        };
    public static OrganizationAddressDbo ToEntity(this OrganizationAddressDto dto) =>
        new OrganizationAddressDbo
        {
            UseId = dto.Use,
            TypeId = dto.Type,
            Address = dto.Address.ToEntity()
        };
    
    public static PersonalEmailDto ToDto(this PersonalEmailDbo entity) =>
        new PersonalEmailDto
        {
            Value = entity.Email,
            Use = entity.Use,
            IsPreferred = entity.IsPreferred
        };
    public static PersonalEmailDbo ToEntity(this PersonalEmailDto dto) =>
        new PersonalEmailDbo
        {
            Email = dto.Value,
            Use = dto.Use,
            IsPreferred = dto.IsPreferred
        };
    
    public static PersonalPhoneNumberDto ToDto(this PersonalPhoneNumberDbo entity) =>
        new PersonalPhoneNumberDto
        {
            Type = entity.Type,
            Value = entity.PhoneNumber,
            AllowsSms = entity.AllowsTextMessages,
            AllowsVoice = entity.AllowsVoice,
            IsPreferred = entity.IsPreferred,
            PreferredTime = entity.PreferredTimeId
        };
    public static PersonalPhoneNumberDbo ToEntity(this PersonalPhoneNumberDto dto) =>
        new PersonalPhoneNumberDbo
        {
            Type = dto.Type,
            PhoneNumber = dto.Value,
            AllowsVoice = dto.AllowsVoice,
            IsPreferred = dto.IsPreferred,
            AllowsTextMessages = dto.AllowsSms,
            PreferredTimeId = dto.PreferredTime
        };

    public static PersonalEmergencyContactDto ToDto(this PersonalEmergencyContactDbo entity) =>
        new PersonalEmergencyContactDto
        {
            Prefix = entity.Name.PrefixId,
            Suffix = entity.Name.SuffixId,
            FirstName = entity.Name.FirstName,
            MiddleName = entity.Name.MiddleName,
            LastName = entity.Name.LastName,
            Relationship = entity.RelationshipId,
            ContactInformation = entity.ContactInformation
        };
    public static PersonalEmergencyContactDbo ToEntity(this PersonalEmergencyContactDto dto) =>
        new PersonalEmergencyContactDbo
        {
            Name = new PersonNameDbo()
            {
                PrefixId = dto.Prefix,
                SuffixId = dto.Suffix,
                FirstName = dto.FirstName,
                MiddleName = dto.MiddleName,
                LastName = dto.LastName
            },
            RelationshipId = dto.Relationship,
            ContactInformation = dto.ContactInformation
        };
    
    public static PersonNameDto ToDto(this PersonNameDbo entity) =>
        new PersonNameDto
        {
            Prefix = entity.PrefixId,
            Suffix = entity.SuffixId,
            FirstName = entity.FirstName,
            MiddleName = entity.MiddleName,
            LastName = entity.LastName
        };
    
    public static PersonNameDbo ToEntity(this PersonNameDto dto) =>
        new PersonNameDbo
        {
            PrefixId = dto.Prefix,
            SuffixId = dto.Suffix,
            FirstName = dto.FirstName,
            MiddleName = dto.MiddleName,
            LastName = dto.LastName
        };
    
    public static OrganizationContactPersonDto ToDto(this OrganizationContactPersonDbo entity) =>
        new OrganizationContactPersonDto
        {
            Name = entity.Name.ToDto(),
            ContactDetail = entity.ContactDetail.ToDto()
        };
    public static OrganizationContactPersonDbo ToEntity(this OrganizationContactPersonDto dto) =>
        new OrganizationContactPersonDbo
        {
            Name = dto.Name.ToEntity(),
            ContactDetail = dto.ContactDetail.ToEntity()
        };
    
    public static PersonalAddressDto ToDto(this PersonalAddressDbo entity) =>
        new PersonalAddressDto
        {
            Address = entity.Address.ToDto(),
            Use = entity.UseId,
            Type = entity.TypeId
        };
    public static PersonalAddressDbo ToEntity(this PersonalAddressDto dto) =>
        new PersonalAddressDbo
        {
            Address = dto.Address.ToEntity(),
            UseId = dto.Use,
            TypeId = dto.Type
        };
    
    public static PersonalContactDetailDto ToDto(this PersonalContactDetailDbo dbo) => 
        new PersonalContactDetailDto
        {
            Emails = dbo.Emails.Select(e => e.ToDto()).ToList(),
            PhoneNumbers = dbo.PhoneNumbers.Select(p => p.ToDto()).ToList(),
            EmergencyContacts = dbo.EmergencyContacts.Select(ec => ec.ToDto()).ToList(),
            Addresses = dbo.Addresses.Select(a => a.ToDto()).ToList()
        };
    public static PersonalContactDetailDbo ToEntity(this PersonalContactDetailDto dto) =>
        new PersonalContactDetailDbo
        {
            Emails = dto.Emails.Select(e => e.ToEntity()).ToList(),
            PhoneNumbers = dto.PhoneNumbers.Select(p => p.ToEntity()).ToList(),
            Addresses = dto.Addresses.Select(a => a.ToEntity()).ToList(),
            EmergencyContacts = dto.EmergencyContacts.Select(ec => ec.ToEntity()).ToList()
        };
    
    public static OrganizationContactDetailDto ToDto(this OrganizationContactDetailDbo dbo)
    {
        return new OrganizationContactDetailDto
        {
            Emails = dbo.Emails.Select(e => e.ToDto()).ToList(),
            PhoneNumbers = dbo.PhoneNumbers.Select(p => p.ToDto()).ToList(),
            Addresses = dbo.Addresses.Select(a => a.ToDto()).ToList(),
            ContactPersons = dbo.ContactPersons.Select(c => c.ToDto()).ToList()
        };
    }
    public static OrganizationContactDetailDbo ToEntity(this OrganizationContactDetailDto dto)
    {
        return new OrganizationContactDetailDbo
        {
            Emails = dto.Emails.Select(e => e.ToEntity()).ToList(),
            PhoneNumbers = dto.PhoneNumbers.Select(p => p.ToEntity()).ToList(),
            Addresses = dto.Addresses.Select(a => a.ToEntity()).ToList(),
            ContactPersons = dto.ContactPersons.Select(c => c.ToEntity()).ToList()
        };
    }

    public static IdentifierDto ToDto(this IdentifierDbo entity) =>
        new IdentifierDto
        {
            Value = entity.Value,
            System = entity.System,
        };
    public static IdentifierDbo ToEntity(this IdentifierDto dto) =>
        new IdentifierDbo
        {
            System = dto.System,
            Value = dto.Value
        };
    
    public static FileStatusResponseDto ToDto(this FileMetadataDbo entity) =>
        new FileStatusResponseDto
        {
            FileId = entity.Id,
            Status = entity.Status,
            OriginalFileName = entity.UploadFileName,
            FileSize = entity.UploadFileSize,
            MimeType = entity.UploadMimeType,
            Failure = entity.Failure,
            UploadTimestamp = entity.UploadTimestamp,
            LastUpdateTimestamp = entity.LastUpdatedAt,
            ContextEntityType = entity.ContextEntityType,
            ContextEntityId = entity.ContextEntityId,
            Purpose = entity.Purpose,
            ThumbnailUrl = null, // TODO: Generate thumbnail URL if applicable
            MalwareScanPassed = null // TODO: Add malware scan status if applicable
        };

    public static TechnicianQualificationDto ToDto(this TechnicianQualificationDbo entity) =>
        new TechnicianQualificationDto()
        {
            DateExpires = entity.DateExpires,
            DateObtained = entity.DateObtained,
            QualificationId = entity.QualificationId,
        };

    public static TechnicianQualificationDbo ToEntity(this TechnicianQualificationDto dto) =>
        new TechnicianQualificationDbo()
        {
            DateExpires = dto.DateExpires,
            DateObtained = dto.DateObtained,
            QualificationId = dto.QualificationId,
        };

    public static DemographicsDto ToDto(this DemographicsDbo entity) =>
        new DemographicsDto
        {
            BirthSex = entity.SexId,
            Gender = entity.GenderId,
            MaritalStatus = entity.MaritalStatusId,
            Race = entity.RaceId,
            Ethnicity = entity.EthnicityId,
            DateOfBirth = entity.DateOfBirth
        };
    public static DemographicsDbo ToEntity(this DemographicsDto dto) =>
        new DemographicsDbo
        {
            SexId = dto.BirthSex,
            GenderId = dto.Gender,
            MaritalStatusId = dto.MaritalStatus,
            RaceId = dto.Race,
            EthnicityId = dto.Ethnicity,
            DateOfBirth = dto.DateOfBirth
        };

    public static PhysicalMeasurementsDto ToDto(this PhysicalMeasurementsDbo entity) =>
        new PhysicalMeasurementsDto
        {
            Bmi = entity.Bmi,
            HeightInches = entity.HeightInches,
            NeckSize = entity.NeckSize,
            WeightPounds = entity.WeightPounds
        };
    public static PhysicalMeasurementsDbo ToEntity(this PhysicalMeasurementsDto dto) =>
        new PhysicalMeasurementsDbo
        {
            Bmi = dto.Bmi,
            HeightInches = dto.HeightInches,
            NeckSize = dto.NeckSize,
            WeightPounds = dto.WeightPounds
        };
    
    
    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // Entities (not-trivial transformations)
    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    public static DraftDto ToDto(this DraftDbo dbo) =>
        new DraftDto
        {
            Id = dbo.Id,
            Data = dbo.Data,
            EntityId = dbo.EntityId,
            EntityType = dbo.EntityType,
        };
    public static DraftDbo ToEntity(this DraftDto dto) =>
        new DraftDbo
        {
            Id = dto.Id,
            Data = dto.Data,
            EntityId = dto.EntityId,
            EntityType = dto.EntityType,
        };
    public static CreateDraftDto ToCreateDto(this DraftDbo dbo) =>
        new CreateDraftDto
        {
            Data = dbo.Data,
            EntityId = dbo.EntityId,
            EntityType = dbo.EntityType,
        };
    public static DraftDbo ToEntity(this CreateDraftDto dto, Guid guid) =>
        new DraftDbo
        {
            Id = guid,
            Data = dto.Data,
            EntityId = dto.EntityId,
            EntityType = dto.EntityType,
        };
    
    public static CareLocationDto ToDto(this CareLocationDbo dbo) =>
        new CareLocationDto
        {
            Id = dbo.Id,
            Name = dbo.Name,
            ParentServiceLocationId = dbo.ParentCareLocationId,
            ParentProviderId = dbo.ParentProviderId,
            ContactDetail = dbo.ContactDetail?.ToDto(),
            SupportedEncounterTypes = dbo.SupportedEncounterTypes.ToList(),
            SupportedStudyTypes = dbo.SupportedStudyTypes.ToList()
        };
    // public static CareLocationEntity ToEntity(this CareLocationDto dto) =>
    //     new CareLocationEntity
    //     {
    //         Id = dto.Id,
    //         Name = dto.Name,
    //         ParentCareLocationId = dto.ParentServiceLocationId,
    //         ParentProviderId = dto.ParentProviderId,
    //         ContactDetail = dto.ContactDetail.ToEntity()
    //     };
    
    public static EquipmentDto ToDto(this EquipmentDbo dbo) =>
        new EquipmentDto
        {
            Id = dbo.Id,
            CareLocationId = dbo.CareLocationId,
            RoomId = dbo.RoomId,
            EquipmentTypeId = dbo.EquipmentTypeId,
            EquipmentData = dbo.EquipmentData
        };
    public static EquipmentDbo ToEntity(this EquipmentDto dto) =>
        new EquipmentDbo
        {
            Id = dto.Id,
            CareLocationId = dto.CareLocationId,
            RoomId = dto.RoomId,
            EquipmentTypeId = dto.EquipmentTypeId,
            EquipmentData = dto.EquipmentData
        };
    
    public static ProviderDto ToDto(this ProviderDbo dbo) =>
        new ProviderDto
        {
            Id = dbo.Id,
            Name = dbo.Name,
            ParentProviderId = dbo.ParentProviderId,
            ContactDetail = dbo.ContactDetail?.ToDto(),
            Identifiers = dbo.Identifiers.Select(i => i.ToDto()).ToList()
        };
    // public static ProviderEntity ToEntity(this ProviderEntityDto dto) =>
    //     new ProviderEntity
    //     {
    //         Id = dto.Id,
    //         Name = dto.Name,
    //         ParentProviderId = dto.ParentProviderId,
    //         ContactDetail = dto.ContactDetail.ToEntity(),
    //         Identifiers = dto.Identifiers.Select(i => i.ToEntity()).ToList()
    //     };

    public static IssueDto ToDto(this Issue issue) => new IssueDto()
    {
        Message = issue.Message,
        Paths = issue.Paths.ToList(),
        IssueId = issue.IssueId,
        Data = issue.Data
    };
}