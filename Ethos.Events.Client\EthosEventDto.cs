﻿namespace Ethos.Events.Client
{
    /// <summary>
    /// 
    /// </summary>
    public interface IEthosEventDto
    {
        Guid? Id { get; set; }
        string Type { get; set; }
        string? Name { get; set; }
        string? Code { get; set; }
        Guid? TenantId { get; set; }
        string? GeneratedBy { get; set; }
        DateTimeOffset EventTime { get; init; }
        ICollection<EthosEventCorrelationIdDto> CorrelationIds { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class EthosEventDto<T> : EthosEventDto, IEthosEventDto
    {
        public T? EventData { get; init; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosEventDto : IEthosEventDto
    {
        public Guid? Id { get; set; }
        public string Type { get; set; } = null!;
        public string? Name { get; set; }
        public string? Code { get; set; }
        public Guid? TenantId { get; set; }
        public string? GeneratedBy { get; set; }
        public DateTimeOffset EventTime { get; init; }
        public ICollection<EthosEventCorrelationIdDto> CorrelationIds { get; set; } = [];
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosEventCorrelationIdDto
    {
        public string Type { get; set; } = null!;
        public string Value { get; set; } = null!;
    }
}