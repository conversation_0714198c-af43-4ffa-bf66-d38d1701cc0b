﻿using Ethos.Auth;

namespace Ethos.Roles.Model
{
    /// <summary>
    /// 
    /// </summary>
    public static class BuiltinRoleIds
    {
        public static readonly Guid InterpretingPhysician = new("0b469dbc-95ee-4d14-929e-cec638b7b289");
        public static readonly Guid ReferringPhysician = new("6f8079e0-980e-4fd7-941f-bca77bb6c858");
        public static readonly Guid AlliedHealth = new Guid("d93b0144-c852-467d-93df-c0ebfbda0ed8");
        public static readonly Guid PracticeAdministrator = new Guid("e9296b7f-**************-2d45ea5d8042");
        public static readonly Guid SystemAdministrator = new Guid("fe38a51d-c325-4af8-b02e-7599c2413f5f");
        public static readonly Guid ScoringTechnician = new Guid("2f20eccb-2a24-44c4-8cac-dac26a70ec95");
        public static readonly Guid BillingCoordinator = new Guid("5598a139-6949-4a78-a3de-25bbf93f8d7c");
        public static readonly Guid BillingAdministrator = new Guid("da81d11e-daca-4bf5-8e0d-************");
        public static readonly Guid RegistrationIntakeCoordinator = new Guid("ca6bf22f-41f0-4f5b-8fe2-e16d04cb8fb6");
        public static readonly Guid SchedulingCoordinator = new Guid("8030cda4-2e66-43e6-848b-727934688df4");
        public static readonly Guid SecurityAdministrator = new Guid("3fefe844-5a79-437e-b4c8-6a04ddcc3697");
    }

    /// <summary>
    /// 
    /// </summary>
    public class BuiltinRole
    {
        /// <summary>
        /// 
        /// </summary>
        public Guid Id {  get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Name { get; set; } = null!;

        /// <summary>
        /// 
        /// </summary>
        public static readonly List<BuiltinRole> All = [
                 new() { Name = "Interpreting Physician", Id = BuiltinRoleIds.InterpretingPhysician },
                 new() { Name = "Referring Physician", Id = BuiltinRoleIds.ReferringPhysician },
                 new() { Name = "Scoring Technician", Id = BuiltinRoleIds.ScoringTechnician },
                 new() { Name = "Allied Health/Clinical Care Team", Id = BuiltinRoleIds.AlliedHealth },
                 new() { Name = "Practice/Clinic Administrator", Id = BuiltinRoleIds.PracticeAdministrator },
                 new() { Name = "IT/System Administrator", Id = BuiltinRoleIds.SystemAdministrator },
                 new() { Name = "Registration/Intake Coordinator", Id = BuiltinRoleIds.RegistrationIntakeCoordinator },
                 new() { Name = "Scheduling Coordinator", Id = BuiltinRoleIds.SchedulingCoordinator },
                 new() { Name = "Billing Coordinator", Id = BuiltinRoleIds.BillingCoordinator },
                 new() { Name = "Billing Administrator", Id = BuiltinRoleIds.BillingAdministrator },
                 new() { Name = "Security Administrator", Id = BuiltinRoleIds.SecurityAdministrator },
            ];

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static bool IsBuiltin(Guid id)
        {
            return All.Any(r => id == r.Id);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static bool IsBuiltin(Guid? id)
        {
            return IsBuiltin(id ?? Guid.Empty);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public static bool IsBuiltinName(string? name)
        {
            return All.Any(r => r.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public static Guid? GetBuiltinId(string name)
        {
            var b = All.FirstOrDefault(r => r.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
            return b?.Id;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="role"></param>
        /// <returns></returns>
        public static bool IsBuiltin(EthosRoleDto role)
        {
            return IsBuiltin(role.Id ?? Guid.Empty);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="role"></param>
        /// <returns></returns>
        public static bool IsBuiltin(EthosRole role)
        {
            return IsBuiltin(role.Id);
        }
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosRole
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = null!;
        public string[] Filters { get; set; } = [];
        public ICollection<EthosRoleScope> Scopes { get; set; } = [];
        public Guid TenantId { get; set; }
        public ICollection<EthosRoleAssignment> RoleAssignments { get; set; } = [];
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosRoleAssignment
    {
        public Guid RoleId { get; set; }
        public EthosRole Role { get; set; } = null!;
        public Guid UserId { get; set; }
        public Guid TenantId { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosDbScope
    {
        public string Name { get; set; } = null!;
        public string? Description { get; set; }
        public bool Privileged { get; set; }
        public bool Assignable { get; set; }
        public long RowId { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosRoleScope
    {
        public Guid RoleId { get; set; }
        public Guid TenantId { get; set; }
        public EthosRole Role { get; set; } = null!;

        public string Scope
        {
            get
            {
                return scope ?? string.Empty;
            }

            set
            {
                if (!EthosScope.TryParse(value, null, out var _scope))
                    throw new ArgumentException($"Invalid scope: {value}", nameof(value));

                if (!_scope.IsFullyQualified())
                    throw new ArgumentException($"Invalid scope: {value}", nameof(value));

                scope = _scope.ToString();
            }
        }
        public string DisplayName { get; set; } = null!;
        string? scope;
    }
}
