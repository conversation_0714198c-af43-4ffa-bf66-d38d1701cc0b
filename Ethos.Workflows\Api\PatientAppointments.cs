using Ethos.Model;

namespace Ethos.Workflows.Api;

public sealed record CreatePatientAppointmentDto : IInputDto
{
    public Guid StudyId { get; set; }
    public Guid RoomId { get; set; }
    public Guid CareLocationShiftId { get; set; }
    public DateOnly Date { get; set; }
}

public sealed record PatientAppointmentDto
{
    public Guid Id { get; set; }
    public Guid StudyId { get; set; }
    public Guid RoomId { get; set; }
    public Guid CareLocationShiftId { get; set; }
    public DateOnly Date { get; set; }
    
    public DateTimeOffset CreatedAt { get; set; }
    public DateTimeOffset UpdatedAt { get; set; }
    public Guid ScheduledById { get; set; }
}

public interface IPatientAppointmentApi : IEntityHttpClient<CreatePatientAppointmentDto, PatientAppointmentDto, PatientAppointmentQ>;

public class PatientAppointmentHttpClient(System.Net.Http.HttpClient httpClient)
    : EntityHttpClientBase<CreatePatientAppointmentDto, PatientAppointmentDto, PatientAppointmentQ>(httpClient, "patientappointment"),
        IPatientAppointmentApi;