import _ from 'lodash';
import { In } from 'typeorm';
import { BadRequestException, Injectable } from '@nestjs/common';
import { IListResult } from '@app/common/types';
import { CredentialRepository } from '@app/modules/credential/credential.repository';
import { CredentialFiltersDto } from '@app/modules/credential/dto/credential.filters.dto';
import { CredentialEntity } from '@app/modules/credential/credential.entity';
import { CreateCredentialDto } from '@app/modules/credential/dto/create.credential.dto';
import { UpdateCredentialDto } from '@app/modules/credential/dto/update.credential.dto';

@Injectable()
export class CredentialService {
  constructor(
    private readonly repository: CredentialRepository,
  ) {}

  async list(filters: CredentialFiltersDto): Promise<IListResult<CredentialEntity>> {
    return this.repository.list(filters);
  }

  async getByIds(credentialIds: number[]): Promise<CredentialEntity[]> {
    if (!credentialIds.length) {
      return [];
    }

    return this.repository.find({
      where: {
        id: In(_.uniq(credentialIds)),
      },
    });
  }

  async getByIdOrFail(credentialId: number): Promise<CredentialEntity> {
    const credential = await this.repository.findOne({
      where: {
        id: credentialId,
      },
    });

    if (!credential) {
      throw new BadRequestException(`Credential ${credentialId} not found`);
    }

    return credential;
  }

  async create(credential: CreateCredentialDto): Promise<CredentialEntity> {
    const entity = this.repository.create(credential);

    return this.repository.save(entity);
  }

  async update({ id, ...update }: UpdateCredentialDto): Promise<CredentialEntity> {
    const credential = await this.getByIdOrFail(id);
    const entity = this.repository.merge(credential, update);

    return this.repository.save(entity);
  }

  async delete(credentialId: number): Promise<CredentialEntity> {
    const credential = await this.getByIdOrFail(credentialId);
    await this.repository.checkHasRelatedEntities(credentialId);
    await this.repository.softDelete({ id: credentialId });

    return credential;
  }

  async checkExistence(credentialIds?: number[]): Promise<CredentialEntity[]> {
    if (!credentialIds?.length) {
      return [];
    }

    const credentials = await this.repository.find({
      where: {
        id: In(credentialIds),
      },
    });

    const notExistedCredentials = credentialIds.filter((id) => !credentials.find((item) => item.id === id));

    if (notExistedCredentials.length) {
      throw new BadRequestException(`Credentials ${notExistedCredentials.join(', ')} don't exist`);
    }

    return credentials;
  }
}
