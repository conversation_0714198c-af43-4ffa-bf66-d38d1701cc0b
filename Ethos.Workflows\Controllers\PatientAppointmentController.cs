using Ethos.Model;
using Ethos.Workflows.Api;
using Ethos.Workflows.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class PatientAppointmentController(DbContext dbContext)
    : EntityControllerBase<PatientAppointmentDbo, CreatePatientAppointmentDto, PatientAppointmentDto, PatientAppointmentQ>(dbContext)
{
    protected override PatientAppointmentDto MapToDto(PatientAppointmentDbo dbo)
    {
        return new PatientAppointmentDto
        {
            Id = dbo.Id,
            StudyId = dbo.StudyId,
            RoomId = dbo.RoomId,
            CareLocationShiftId = dbo.CareLocationShiftId,
            Date = dbo.Date,
            ScheduledById = dbo.ScheduledById
        };
    }

    protected override PatientAppointmentDbo CreateOrUpdateEntity(PatientAppointmentDbo? entity, CreatePatientAppointmentDto input,
        Guid? requiredId = null)
    {
        if (entity == null)
        {
            entity = new PatientAppointmentDbo
            {
                Id = requiredId ?? Guid.NewGuid(),
                StudyId = input.StudyId,
                RoomId = input.RoomId,
                CareLocationShiftId = input.CareLocationShiftId,
                Date = input.Date,
            };
        }
        else
        {
            entity.StudyId = input.StudyId;
            entity.RoomId = input.RoomId;
            entity.CareLocationShiftId = input.CareLocationShiftId;
            entity.Date = input.Date;
        }

        return entity;
    }
}