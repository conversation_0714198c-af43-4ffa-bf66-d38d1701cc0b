name: Ethos.Events Pipeline

trigger:
  branches:
    include:
      - '*'
  paths:
    include:
      - Ethos.Events/*

pool:
  name: 'Default'  # Self-hosted agent pool

variables:
  - group: DatabaseCredentials
  - name: acrName
    value: 'ethoscrdev.azurecr.io'
  # Tag will be read from .env-promote file

stages:
  - stage: Build
    displayName: "Build and Test Projects"
    jobs:
      - job: Build_Ethos_Events
        displayName: "Build and Test Ethos.Events"
        steps:
          - script: |
              echo "Reading tag from .env-promote file..."
              
              # Check if .env-promote file exists
              if [ ! -f "Ethos.Events/External-assets/k8s-manifests/.env-promote" ]; then
                echo " .env-promote file not found. Using default tag for first-time setup."
                echo "##vso[task.setvariable variable=tag]latest"
              else
                # Read TAG from .env-promote file
                TAG_VALUE=$(grep "^TAG=" Ethos.Events/External-assets/k8s-manifests/.env-promote | cut -d'=' -f2 | tr -d '"' | tr -d "'")
              
                if [ -z "$TAG_VALUE" ]; then
                  echo " TAG not found in .env-promote file. Using default tag."
                  echo "##vso[task.setvariable variable=tag]latest"
                else
                  echo " Tag found: $TAG_VALUE"
                  echo "##vso[task.setvariable variable=tag]$TAG_VALUE"
                fi
              fi
              
              echo "Using tag: $(tag)"
            displayName: 'Read Tag from .env-promote File'

          - task: UseDotNet@2
            inputs:
              packageType: 'sdk'
              version: '8.x'
            displayName: 'Install .NET SDK'

          - script: |
              dotnet restore ./Ethos.Events/Ethos.Events.csproj
              dotnet build ./Ethos.Events/Ethos.Events.csproj --configuration Release
              dotnet test ./Ethos.Events/Ethos.Events.csproj --configuration Release
            displayName: 'Restore, Build, and Test Ethos.Events'

          - script: |
              echo "Building Docker image with tag: $(tag)"
            displayName: 'Display Tag Information'

          - task: Docker@2
            inputs:
              containerRegistry: 'acrdev-service-connection'
              repository: 'ethos-events'
              command: 'buildAndPush'
              Dockerfile: './Ethos.Events/Dockerfile'
              buildContext: '.'
              tags: |
                $(tag)
                latest
            displayName: 'Build and Push Ethos.Events Image'

  # - stage: PreDeploy
  #   displayName: "Prepare Database for Deployment"
  #   condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/master'))
  #   jobs:
  #     - job: Apply_EF_Migrations
  #       displayName: "Apply EF Core Migrations"
  #       steps:
  #         - task: UseDotNet@2
  #           inputs:
  #             packageType: 'sdk'
  #             version: '8.x'
  #           displayName: 'Install .NET SDK'

  #         - script: dotnet tool install --global dotnet-ef
  #           displayName: 'Install dotnet-ef CLI'

  #         - script: |
  #             export PATH="$PATH:$HOME/.dotnet/tools"
  #             dotnet restore ./Ethos.Events/Ethos.Events.csproj
  #             dotnet ef database update --project ./Ethos.Events/Ethos.Events.csproj
  #           env:
  #             ConnectionStrings__DefaultConnection: 'Host=api-dev.ethos.net;Port=30101;Database=postgres;Username=postgres;Password=$(WORKFLOW_DEV_DB_PASSWORD)'
  #           displayName: 'Apply EF Migrations to Database'

  - stage: Deploy
    displayName: "Deploy to Kubernetes"
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/develop'))
    jobs:
      - job: Deploy_to_AKS
        displayName: "Deploy Updated Services"
        steps:
          - script: |
              echo "Reading tag from .env-promote file for deployment..."
              
              # Check if .env-promote file exists  
              if [ ! -f "Ethos.Events/External-assets/k8s-manifests/.env-promote" ]; then
                echo " .env-promote file not found. Using default tag."
                echo "##vso[task.setvariable variable=deployTag]latest"
              else
                # Read TAG from .env-promote file
                TAG_VALUE=$(grep "^TAG=" Ethos.Events/External-assets/k8s-manifests/.env-promote | cut -d'=' -f2 | tr -d '"' | tr -d "'")
              
                if [ -z "$TAG_VALUE" ]; then
                  echo " TAG not found in .env-promote file. Using default tag."
                  echo "##vso[task.setvariable variable=deployTag]latest"
                else
                  echo " Deploying with tag: $TAG_VALUE"
                  echo "##vso[task.setvariable variable=deployTag]$TAG_VALUE"
                fi
              fi
            displayName: 'Read Tag for Deployment'

          # - script: |
          #     echo "Updating deployment.yaml with dynamic tag..."
              
          #     # Update the image tag in deployment.yaml
          #     sed -i "s|ethoscrdev.azurecr.io/ethos-events:.*|ethoscrdev.azurecr.io/ethos-events:$(deployTag)|g" Ethos.Events/External-assets/k8s-manifests/deployment.yaml
              
          #     echo "Updated deployment.yaml:"
          #     cat Ethos.Events/External-assets/k8s-manifests/deployment.yaml | grep "image:"

          #   displayName: 'Update Deployment Manifest with Dynamic Tag'

          - script: |
              echo "Checking if kubectl is installed..."
              if ! command -v kubectl &> /dev/null; then
                echo "kubectl not found! Installing..."
                sudo apt-get update && sudo apt-get install -y kubectl
              else
                echo "kubectl is already installed.."
              fi
            displayName: 'Ensure kubectl is Installed'

          - script: |
              echo "Validating kubectl version..."
              kubectl version --client
            displayName: 'Validate kubectl Installation'

          - task: KubernetesManifest@1
            inputs:
              action: 'deploy'
              namespace: 'ethos-ns-dev'
              manifests: |
                ./Ethos.Events/External-assets/k8s-manifests/deployment.yaml
                ./Ethos.Events/External-assets/k8s-manifests/service.yaml
              kubernetesServiceEndpoint: 'aks-service-connection'
            displayName: 'Deploy Application to AKS'

          - task: Kubernetes@1
            displayName: 'Restart Deployment'
            inputs:
              connectionType: 'Kubernetes Service Connection'
              kubernetesServiceEndpoint: 'aks-service-connection'
              command: 'rollout'
              arguments: 'restart deployment ethos-events -n ethos-ns-dev'

          - script: |
              echo "Cleaning up local Docker images..."
              docker images | grep ethos-events | awk '{print $3}' | xargs -r docker rmi || true
            displayName: 'Clean Up Local Docker Images'
            continueOnError: true

          - script: |
              echo "============================================"
              echo " DEPLOYMENT COMPLETED SUCCESSFULLY! "
              echo "============================================"
              echo "Deployed Tag: $(deployTag)"
              echo "Environment: ethos-ns-dev"
              echo "Application: ethos-events"
              echo "============================================"
            displayName: 'Deployment Summary' 