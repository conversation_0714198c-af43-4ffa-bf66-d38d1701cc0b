using Ethos.Auth;
using Ethos.Workflows.Api;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Ethos.Workflows.Controllers;

[ApiController]
[Route("api/[controller]")]
public class LoginController(IEthosLocalJWTBuilder jwtBuilder) : ControllerBase
{
    // POST: /api/login
    [HttpPost]
    public IActionResult Login([FromBody] LoginRequestDto request)
    {
        // Replace this dummy validation with your own user validation logic.
        if (IsValidUser(request))
        {
            var token = jwtBuilder.GenerateJwtToken(request.Username);
            return Ok(new { token });
        }
        return Unauthorized();
    }

    private bool IsValidUser(LoginRequestDto request)
    {
        // Dummy check: in production, validate against your user store (e.g., a database)
        return request.Username == "test" && request.Password == "password";
    }
    
    // New endpoint: GET /api/login/check
    [HttpGet("check")]
    [Authorize]
    public IActionResult CheckTokenType()
    {
        // Retrieve the issuer claim from the current token.
        var issuer = User.FindFirst("iss")?.Value;
        
        if (!string.IsNullOrEmpty(issuer) && issuer.Contains("b2clogin.com"))
        {
            return Ok(new { tokenType = "AzureAD" });
        }
        return Ok(new { tokenType = "LocalJWT" });
    }
}