import { WebSocketGateway, WebSocketServer, OnGatewayConnection, OnGatewayDisconnect, ConnectedSocket } from '@nestjs/websockets';

import { Server, Socket } from 'socket.io';
import { UserEntity } from '@app/modules/user/user.entity';
import { NotificationsService } from './notifications.service';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
export class NotificationsGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  private server: Server;
  private clients: { [name: string]: [Socket, UserEntity] } = {};

  constructor(private readonly service: NotificationsService) {}

  async handleConnection(@ConnectedSocket() client: Socket): Promise<void> {
    const { authorization } = client.handshake.headers;
    await this.service
      .getUserFromSocket(authorization)
      .then((u) => {
        this.clients[client.id] = [client, u];
      })
      .catch((e) => {
        client.emit('exception', { error: e.message });
        client.disconnect(true);
      });
  }

  async handleDisconnect(@ConnectedSocket() client: Socket): Promise<void> {
    delete this.clients[client.id];
  }

  async sendTeamNotificationEventsChanged(message: string): Promise<void> {
    await this.sendTeamNotification('events', message);
  }

  private async sendTeamNotification(event: string, message: string): Promise<void> {
    const values: [Socket, UserEntity][] = Object.values(this.clients);
    values.forEach((value) => {
      const [client] = value;
      client.emit(event, message);
    });
  }
}
