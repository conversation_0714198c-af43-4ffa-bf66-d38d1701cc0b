using Ethos.Events;
using Ethos.Auth;
using Microsoft.EntityFrameworkCore;
using Npgsql;
using Microsoft.OpenApi.Models;
using Microsoft.OpenApi.Writers;
using Swashbuckle.AspNetCore.Swagger;
using Microsoft.AspNetCore.Authentication;
using Newtonsoft.Json.Serialization;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

builder.Services
    .AddControllers()
    .AddNewtonsoftJson(options =>
    {
        options.SerializerSettings.Converters.Add(new NewtonsoftJsonDynamicObjectConverter());
        options.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();
        options.SerializerSettings.Formatting = Newtonsoft.Json.Formatting.Indented;
        options.SerializerSettings.NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore;
    });

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();

builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v3", new OpenApiInfo
    {
        Title = "Ethos Event API",
        Version = "v3",
        Description = "Ethos Event API Documentation",
        Contact = new OpenApiContact
        {
            Name = "Ethos Team",
            Email = "<EMAIL>"
        }
    });

    // Force Swagger to use OpenAPI 3.0
    c.UseAllOfToExtendReferenceSchemas();

    // Add JWT Authentication support in Swagger UI
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Type = SecuritySchemeType.Http,
        Scheme = "bearer",
        BearerFormat = "JWT",
        Description = "JWT Authorization header using the Bearer scheme."
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    Array.Empty<string>()
                }
            });
});

builder.Services.AddSwaggerGen();

builder.Services.AddProblemDetails();

builder.Services.ConfigureEthosAuthorization(builder.Configuration);

// CORS is good
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowLocalhost",
        builder => builder
            .AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader());
});

builder.Services.AddEthosAuthorization();

builder.Configuration.SetBasePath(Directory.GetCurrentDirectory());
builder.Configuration.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
builder.Configuration.AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", optional: true);
builder.Configuration.AddEnvironmentVariables();

builder.Services.AddSingleton(builder.Configuration);

builder.Services.AddDbContext<EventDbContext>(options =>
{
    var dataSourceBuilder = new NpgsqlDataSourceBuilder(builder.Configuration.GetConnectionString("DefaultConnection"));
    dataSourceBuilder.EnableDynamicJson();
    var dataSource = dataSourceBuilder.Build();
    options.UseNpgsql(dataSource, builder => builder.MigrationsHistoryTable("__EFMigrationsHistory", "Evt"));
}, contextLifetime: ServiceLifetime.Scoped, optionsLifetime: ServiceLifetime.Singleton);

builder.Services.AddScoped<IClaimsTransformation, DebugClaimsTransformer>();

var app = builder.Build();


app.UseCors("AllowLocalhost");

// Do EF migrations
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<EventDbContext>();
    context.Database.EnsureCreated();
    context.Database.Migrate();
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger(c =>
    {
        c.SerializeAsV2 = false; // Ensure OpenAPI 3.0 output
    });
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v3/swagger.json", "Ethos Event API v3");
        c.RoutePrefix = "swagger";
    });
}

app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();
app.UseEthosAuthorization();

app.UseEndpoints(endpoints =>
{
    // Updated custom endpoint to use proper serialization
    endpoints.MapGet("/api/openapi.json", async context =>
    {
        var swaggerProvider = context.RequestServices.GetRequiredService<ISwaggerProvider>();
        var swagger = swaggerProvider.GetSwagger("v3");

        // Use Swagger's built-in serializer
        var jsonWriter = new StringWriter();
        swagger.SerializeAsV3(new OpenApiJsonWriter(jsonWriter));

        context.Response.ContentType = "application/json";
        await context.Response.WriteAsync(jsonWriter.ToString());
    }).AllowAnonymous();

    endpoints.MapControllers();
});

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseExceptionHandler();
app.UseStatusCodePages();

app.MapControllers();

app.Run();
