import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BaseCollectionDto } from '@app/common/dto/base.collection.dto';
import { AvailableStudyDayDto } from '@app/modules/available/dto/available.study.day.dto';

export class AvailableStudyDayCollectionDto extends BaseCollectionDto {
  @ApiProperty({ type: AvailableStudyDayDto, isArray: true })
  @Expose()
  data: AvailableStudyDayDto[]
}
