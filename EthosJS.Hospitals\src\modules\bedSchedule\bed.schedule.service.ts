import _ from 'lodash';
import { BadRequestException, forwardRef, Inject, Injectable } from '@nestjs/common';
import { IListResult } from '@app/common/types';
import { BedScheduleRepository } from '@app/modules/bedSchedule/bed.schedule.repository';
import { BedScheduleEntity } from '@app/modules/bedSchedule/bed.schedule.entity';
import { BedScheduleFiltersDto } from '@app/modules/bedSchedule/dto/bed.schedule.filters.dto';
import { CreateBedScheduleDto } from '@app/modules/bedSchedule/dto/create.bed.schedule.dto';
import { FacilityService } from '@app/modules/facility/facility.service';
import { UpsertBedScheduleDto } from '@app/modules/bedSchedule/dto/upsert.bed.schedule.dto';
import { BedScheduleDto } from '@app/modules/bedSchedule/dto/bed.schedule.dto';
import { BedScheduleEquipmentDto } from '@app/modules/bedSchedule/dto/bed.schedule.equipment.dto';
import { UpsertBedScheduleResultDto } from '@app/modules/bedSchedule/dto/upsert.bed.schedule.result.dto';
import { FacilityEntity } from '@app/modules/facility/facility.entity';
import { IBedScheduleEquipment } from '@app/modules/bedSchedule/types';

@Injectable()
export class BedScheduleService {
  constructor(
    private readonly repository: BedScheduleRepository,
    @Inject(forwardRef(() => FacilityService)) private readonly facilityService: FacilityService,
  ) {}

  async list({ clinicId, ...filters }: BedScheduleFiltersDto): Promise<IListResult<BedScheduleDto>> {
    if (clinicId) {
      const { data: facilities } = await this.facilityService.list({ clinicId });
      filters.facilityIds = facilities.map(({ id }) => id);
    }

    const { data, count } = await this.repository.list(filters);

    return {
      data: data.map((item) => BedScheduleService.mapToDto(item)),
      count,
    };
  }

  async getByIdOrFail(scheduleId: number): Promise<BedScheduleEntity> {
    const schedule = await this.repository.findOne({
      where: {
        id: scheduleId,
      },
      relations: ['facility'],
    });

    if (!schedule) {
      throw new BadRequestException(`Bed schedule ${scheduleId} not found`);
    }

    return schedule;
  }

  async create({ equipments, ...schedule }: CreateBedScheduleDto): Promise<BedScheduleEntity> {
    const facility = await this.facilityService.getByIdOrFail(schedule.facilityId);

    if (schedule.dayShiftBeds > facility.capacity) {
      throw new BadRequestException('Wrong number of day shift beds');
    }

    if (schedule.nightShiftBeds > facility.capacity) {
      throw new BadRequestException('Wrong number of night shift beds');
    }

    if (equipments?.length) {
      equipments.forEach((equipment) => {
        const facilityEquipment = facility.equipments[equipment.equipmentId];

        if (!facilityEquipment.count) {
          throw new BadRequestException(`Facility doesn't have equipment ${equipment.equipmentId}`);
        }

        if (facilityEquipment.count < equipment.count) {
          throw new BadRequestException(`Facility doesn't have enough equipment ${equipment.equipmentId}. Facility: ${facilityEquipment.count}. Required: ${equipment.count}`);
        }
      });
    }

    const existEntity = await this.repository.findOne({
      where: {
        facilityId: schedule.facilityId,
        date: schedule.date,
      },
    });

    if (existEntity) {
      throw new BadRequestException('Bed schedule exists');
    }

    await this.repository.checkHasSchedules(schedule);

    const entity = this.repository.create(schedule);

    if (equipments?.length) {
      entity.equipments = equipments.reduce((acc, item) => {
        const equipment = facility.equipments[item.equipmentId];
        acc[item.equipmentId] = {
          equipmentId: item.equipmentId,
          count: item.count,
          equipmentName: equipment.equipmentName,
        };
        return acc;
      }, {} as Record<number, IBedScheduleEquipment>);
    }

    await entity.save();

    return this.getByIdOrFail(entity.id);
  }

  async upsert(dto: UpsertBedScheduleDto): Promise<UpsertBedScheduleResultDto> {
    const items = await Promise.all(dto.items.map(async (sourceItem) => {
      try {
        const item = await this.create(_.omit(sourceItem, 'id'));

        if (sourceItem.id) {
          await this.delete(sourceItem.id);
        }

        return {
          item: BedScheduleService.mapToDto(item),
        };
      } catch (error: any) {

        return {
          sourceItem,
          error: error?.message,
        };
      }
    }));

    return { items };
  }

  async delete(bedScheduleId: number): Promise<BedScheduleEntity> {
    const bedSchedule = await this.getByIdOrFail(bedScheduleId);

    await this.repository.softDelete({ id: bedScheduleId });

    return bedSchedule;
  }

  async getBedScheduleByDate(facility: FacilityEntity, date: string): Promise<BedScheduleEntity> {
    const bedSchedule = await this.repository.findOne({
      where: {
        facilityId: facility.id,
        date,
      },
    });

    if (!bedSchedule) {
      const schedule = new BedScheduleEntity();

      schedule.facilityId = facility.id;
      schedule.date = date;
      schedule.dayShiftBeds = facility.capacity;
      schedule.nightShiftBeds = facility.capacity;
      schedule.equipments = facility.equipments;

      return schedule;
    }

    return bedSchedule;
  }

  async addEquipmentToSchedules(facilityId: number, equipmentsToAdd: Record<number, IBedScheduleEquipment>): Promise<void> {
    await this.repository.addEquipmentToSchedules(facilityId, equipmentsToAdd);
  }

  static mapToDto(bedSchedule: BedScheduleEntity): BedScheduleDto {
    const dto = new BedScheduleDto();
    dto.id = bedSchedule.id;
    dto.createdAt = bedSchedule.createdAt;
    dto.updatedAt = bedSchedule.updatedAt;
    dto.facilityId = bedSchedule.facilityId;
    dto.facilityName = bedSchedule.facility.name;
    dto.date = bedSchedule.date;
    dto.dayShiftBeds = bedSchedule.dayShiftBeds;
    dto.nightShiftBeds = bedSchedule.nightShiftBeds;

    dto.equipments = Object.values(bedSchedule.equipments).map(({ equipmentId, count, equipmentName }) => {
      const entity = new BedScheduleEquipmentDto();

      entity.equipmentId = Number(equipmentId);
      entity.bedScheduleId = bedSchedule.id;
      entity.count = count;
      entity.equipmentName = equipmentName;

      return entity;
    });

    return dto;
  }
}
