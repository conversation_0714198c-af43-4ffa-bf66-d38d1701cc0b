import {
  Controller,
  Post,
  Put,
  Delete,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  UnauthorizedException,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiTags,
  ApiBearerAuth,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiNotAcceptableResponse,
  ApiNoContentResponse,
} from '@nestjs/swagger';
import { AuthUser } from '@app/common/decorators/authUser.decorator';
import { AuthGuard } from '@app/common/guards/auth.guard';
import { UserEntity } from '@app/modules/user/user.entity';
import { AuthService } from './auth.service';
import { LoginResponse } from './dto/login.response.dto';
import { RefreshRequest } from './dto/refresh.request.dto';
import { LoginRequest } from './dto/login.request.dto';
import { IAuthResponse } from '@app/modules/auth/types';

@Controller('auth')
@ApiTags('Auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: LoginResponse,
    description: 'User info with access token',
  })
  @ApiForbiddenResponse({
    description: 'Username or password is wrong.',
  })
  @ApiNotAcceptableResponse({
    description: 'Email is not confirmed',
  })
  async userLogin(@Body() { email, password }: LoginRequest): Promise<IAuthResponse> {
    try {
      return await this.authService.checkUserCredentials(email.toLowerCase(), password);
    } catch (e) {
      throw new UnauthorizedException('Invalid credentials');
    }
  }

  @Post('/login')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: LoginResponse,
    description: 'User info with access token',
  })
  @ApiForbiddenResponse({
    description: 'Username or password is wrong.',
  })
  @ApiNotAcceptableResponse({
    description: 'Email is not confirmed',
  })
  async login(@Body() { email, password }: LoginRequest): Promise<IAuthResponse> {
    try {
      return await this.authService.checkUserCredentials(email.toLowerCase(), password);
    } catch (e) {
      throw new UnauthorizedException('Invalid credentials');
    }
  }
  @Delete()
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'Logout' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiBearerAuth()
  async userLogout(@AuthUser() user: UserEntity) {
    return this.authService.logout(user);
  }

  @Put()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: LoginResponse,
    description: 'Refresh user token pair by refresh token',
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse()
  async refreshTokens(@Body() { refreshToken }: RefreshRequest): Promise<IAuthResponse> {
    return this.authService.refreshTokens(refreshToken);
  }
}
