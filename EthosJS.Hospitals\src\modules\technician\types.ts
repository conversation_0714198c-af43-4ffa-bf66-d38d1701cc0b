import { EDayWeek } from '@app/common/enums';
import { ETechnicianScheduleShift } from '@app/modules/technicianSchedule/enums';

export interface IScheduledStudy {
    studyId: number;
    facilityId: number;
    date: string;
}

export interface ITechnicianStandardScheduleDay {
    shift: ETechnicianScheduleShift;
    capacity?: number;
    facilityId?: number;
}

export interface ITechnicianStandardSchedule {
    [EDayWeek.Monday]: ITechnicianStandardScheduleDay;
    [EDayWeek.Tuesday]: ITechnicianStandardScheduleDay;
    [EDayWeek.Wednesday]: ITechnicianStandardScheduleDay;
    [EDayWeek.Thursday]: ITechnicianStandardScheduleDay;
    [EDayWeek.Friday]: ITechnicianStandardScheduleDay;
    [EDayWeek.Saturday]: ITechnicianStandardScheduleDay;
    [EDayWeek.Sunday]: ITechnicianStandardScheduleDay;
}