import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsInt, IsOptional, IsPositive, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { TechnicianStandardScheduleDto } from '@app/modules/technician/dto/technician.standard.schedule.dto';
import { CreateTechnicianCredentialDto } from '@app/modules/technician/dto/create.technician.credential.dto';

export class UpdateTechnicianDto {
  @ApiProperty()
  @IsInt()
  id: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  @IsPositive()
  capacity?: number;

  @ApiPropertyOptional({ isArray: true, type: CreateTechnicianCredentialDto })
  @IsOptional()
  @IsArray()
  @ValidateNested()
  @Type(() => CreateTechnicianCredentialDto)
  credentials?: CreateTechnicianCredentialDto[];

  @ApiPropertyOptional()
  @IsOptional()
  @ValidateNested()
  @Type(() => TechnicianStandardScheduleDto)
  standardSchedule: TechnicianStandardScheduleDto;
}
