﻿using System.IdentityModel.Tokens.Jwt;
using System.Text;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.IdentityModel.Tokens;

namespace Ethos.Auth;

public static class EthosAuthStartupHelpers
{
    public static IServiceCollection AddEthosAuthorization(this IServiceCollection services)
    {
        services.TryAddScoped<IEthosAuthConfigProvider, EthosAuthConfigProvider>();
        var configProvider = services.BuildServiceProvider().GetService<IEthosAuthConfigProvider>();
        // Fail if no configuration provider is registered.
        if (configProvider is null)
        {
            throw new InvalidOperationException("No configuration provider registered for Ethos Auth.");
        }
        
        services.TryAddScoped<IAuthorizationHandler, EthosAuthorizeHandler>();
        services.TryAddScoped<IEthosUserContextProvider, EthosUserContextProvider>();
        
        // Func<AuthenticationBuilder, AuthenticationBuilder> builder = builder => builder;
        
        if (!configProvider.IsAzureAuthEnabled && !configProvider.IsLocalAuthEnabled)
        {
            throw new InvalidOperationException("No authentication schemes enabled.");
        }

        if (configProvider.IsLocalAuthEnabled)
        {
            services.TryAddScoped<IEthosLocalJWTBuilder, EthosLocalJWTBuilder>();
        }

        AuthenticationBuilder builder;
        if (configProvider.IsLocalAuthEnabled && configProvider.IsAzureAuthEnabled)
        {
            builder = services.AddAuthentication(options =>
                {
                    // Set the default scheme to our policy scheme
                    options.DefaultScheme = "MultiJwt";
                })
                .AddPolicyScheme("MultiJwt", "JWT Bearer or Azure", options =>
                {
                    // Forward the request to the appropriate scheme based on the token.
                    options.ForwardDefaultSelector = context =>
                    {
                        var authHeader = context.Request.Headers["Authorization"].FirstOrDefault();
                        if (!string.IsNullOrEmpty(authHeader) && authHeader.StartsWith("Bearer "))
                        {
                            var token = authHeader.Substring("Bearer ".Length).Trim();
                            var handler = new JwtSecurityTokenHandler();
                            try
                            {
                                var jwtToken = handler.ReadJwtToken(token);
                                // If the token is issued by B2C (contains "b2clogin.com" and a tfp claim), use AzureAD.
                                if (jwtToken.Issuer.Contains("b2clogin.com") &&
                                    jwtToken.Payload.TryGetValue("tfp", out object? policy))
                                {
                                    return "AzureAD";
                                }
                            }
                            catch
                            {
                                // Fallback to local if token reading fails.
                            }
                        }

                        return "LocalJWT";
                    };
                });
        }
        else
        {
            builder = services.AddAuthentication(options =>
                {
                    // Set the default scheme to our policy scheme
                    options.DefaultScheme = configProvider.IsAzureAuthEnabled ? "AzureAD" : "LocalJWT";
                });
        }
        
        if (configProvider.IsAzureAuthEnabled)
        {
            builder.AddJwtBearer("AzureAD", options =>
            {
                // Azure AD B2C configuration.
                var tenantId = configProvider.AzureTenantId;
                var policy = configProvider.AzurePolicy;
                var instance = configProvider.AzureInstance;

                // Construct the authority URL with the policy for metadata discovery.
                options.Authority = $"{instance}{tenantId}/{policy}/v2.0";
                options.MetadataAddress = $"{instance}{tenantId}/{policy}/v2.0/.well-known/openid-configuration";

                // For local development only (remove RequireHttpsMetadata = true in production)
                // options.RequireHttpsMetadata = false;

                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    // The token issued by Azure AD B2C contains an issuer without the policy segment,
                    // so we use the value from configuration.
                    ValidIssuer = configProvider.AzureIssuer,
                    ValidateAudience = true,
                    ValidAudience = configProvider.AzureClientId,
                    ValidateLifetime = true
                };
            });
        }
        
        if (configProvider.IsLocalAuthEnabled)
        {
            builder.AddJwtBearer("LocalJWT", options =>
            {
                // Local JWT configuration.
                var key = Encoding.UTF8.GetBytes(configProvider.LocalJwtKey);
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ValidIssuer = configProvider.LocalJwtIssuer,
                    ValidAudience = configProvider.LocalJwtAudience,
                    IssuerSigningKey = new SymmetricSecurityKey(key)
                };
            });
        }
        
        return services;
    }
}