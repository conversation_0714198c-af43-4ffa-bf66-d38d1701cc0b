using System.Linq.Expressions;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class PatientAppointmentDbo : IAuditableEntity<PatientAppointmentDbo>
{
    public Guid StudyId { get; set; }
    public virtual StudyDbo Study { get; set; } = null!;
    
    public Guid RoomId { get; set; }
    public virtual RoomDbo Room { get; set; } = null!;
    
    public Guid CareLocationShiftId { get; set; }
    public virtual CareLocationShiftDbo CareLocationShift { get; set; } = null!;
    
    public DateOnly Date { get; set; }
    public Guid ScheduledById { get; set; }
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<PatientAppointmentDbo>(Register);

    public new static void Register(EntityTypeBuilder<PatientAppointmentDbo> entity)
    {
        IAuditableEntity<PatientAppointmentDbo>.Register(entity);
        
        entity.HasOne(e => e.Study)
            .WithMany()
            .HasForeignKey(e => e.StudyId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);
        
        entity.HasOne(e => e.Room)
            .WithMany()
            .IsRequired()
            .HasForeignKey(e => e.RoomId);

        entity.HasOne(e => e.CareLocationShift)
            .WithMany()
            .IsRequired()
            .HasForeignKey(e => e.CareLocationShiftId);
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(PatientAppointmentQ.WithId), "WithId")]
[JsonDerivedType(typeof(PatientAppointmentQ.WithStudyId), "WithStudyId")]
[JsonDerivedType(typeof(PatientAppointmentQ.WithOrderId), "WithOrderId")]
[JsonDerivedType(typeof(PatientAppointmentQ.WithPatientId), "WithPatientId")]
[JsonDerivedType(typeof(PatientAppointmentQ.WithCareLocationId), "WithCareLocationId")]
[JsonDerivedType(typeof(PatientAppointmentQ.WithRoomId), "WithRoomId")]
[JsonDerivedType(typeof(PatientAppointmentQ.WithCareLocationShiftId), "WithCareLocationShiftId")]
[JsonDerivedType(typeof(PatientAppointmentQ.WithDateInRange), "WithDateInRange")]
public abstract record PatientAppointmentQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : PatientAppointmentQ;
    public sealed record WithStudyId(Guid Id) : PatientAppointmentQ;
    public sealed record WithOrderId(Guid Id) : PatientAppointmentQ;
    public sealed record WithPatientId(Guid Id) : PatientAppointmentQ;
    public sealed record WithCareLocationId(Guid Id) : PatientAppointmentQ;
    public sealed record WithRoomId(Guid Id) : PatientAppointmentQ;
    public sealed record WithCareLocationShiftId(Guid Id) : PatientAppointmentQ;
    public sealed record WithDateInRange(DateOnly Start, DateOnly End) : PatientAppointmentQ;

    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithId                   wid => Expression.Equal(self.Get(nameof(PatientAppointmentDbo.Id)), Expression.Constant(wid.Id)),
            WithStudyId              wsi => Expression.Equal(self.Get(nameof(PatientAppointmentDbo.StudyId)), Expression.Constant(wsi.Id)),
            WithOrderId              woi => Expression.Equal(self.Get(nameof(PatientAppointmentDbo.Study)).Get(nameof(StudyDbo.OrderId)), Expression.Constant(woi.Id)),
            WithPatientId            wna => Expression.Equal(self.Get(nameof(PatientAppointmentDbo.Study)).Get(nameof(StudyDbo.Order)).Get(nameof(OrderDbo.PatientId)), Expression.Constant(wna.Id)),
            WithCareLocationId       wcl => Expression.Equal(self.Get(nameof(PatientAppointmentDbo.Room)).Get(nameof(RoomDbo.CareLocationId)), Expression.Constant(wcl.Id)),
            WithRoomId               wcl => Expression.Equal(self.Get(nameof(PatientAppointmentDbo.RoomId)), Expression.Constant(wcl.Id)),
            WithCareLocationShiftId  wcl => Expression.Equal(self.Get(nameof(PatientAppointmentDbo.CareLocationShiftId)), Expression.Constant(wcl.Id)),
            WithDateInRange range => 
                Expression.AndAlso(
                    Expression.GreaterThanOrEqual(self.Get(nameof(PatientAppointmentDbo.Date)), Expression.Constant(range.Start)),
                    Expression.LessThanOrEqual(self.Get(nameof(PatientAppointmentDbo.Date)), Expression.Constant(range.End))
                ),
            _ => throw new NotSupportedException($"Unsupported {this.GetType().Name} literal type.")
        };
    }
}