import { In } from 'typeorm';
import { BadRequestException, Injectable } from '@nestjs/common';
import { IListResult } from '@app/common/types';
import { EquipmentEntity } from '@app/modules/equipment/equipment.entity';
import { EquipmentRepository } from '@app/modules/equipment/equipment.repository';
import { EquipmentFiltersDto } from '@app/modules/equipment/dto/equipment.filters.dto';
import { UpdateEquipmentDto } from '@app/modules/equipment/dto/update.equipment.dto';
import { CreateEquipmentDto } from '@app/modules/equipment/dto/create.equipment.dto';

@Injectable()
export class EquipmentService {
  constructor(
    private readonly repository: EquipmentRepository,
  ) {}

  async list(filters: EquipmentFiltersDto): Promise<IListResult<EquipmentEntity>> {
    return this.repository.list(filters);
  }

  async getByIdOrFail(equipmentId: number): Promise<EquipmentEntity> {
    const equipment = await this.repository.findOne({
      where: {
        id: equipmentId,
      },
    });

    if (!equipment) {
      throw new BadRequestException('Clinic is not found');
    }

    return equipment;
  }

  async create(equipment: CreateEquipmentDto): Promise<EquipmentEntity> {
    const entity = this.repository.create(equipment);

    return this.repository.save(entity);
  }

  async update({ id, ...update }: UpdateEquipmentDto): Promise<EquipmentEntity> {
    const equipment = await this.getByIdOrFail(id);
    const entity = this.repository.merge(equipment, update);

    return this.repository.save(entity);
  }

  async checkExistence(equipmentIds: number[]): Promise<Record<number, EquipmentEntity>> {
    const equipments = await this.repository.find({
      where: {
        id: In(equipmentIds),
      },
    });

    const notExistedEquipments = equipmentIds.filter((id) => !equipments.find((item) => item.id === id));

    if (notExistedEquipments.length) {
      throw new BadRequestException(`Equipments ${notExistedEquipments.join(', ')} don't exist`);
    }

    return equipments.reduce((acc: Record<number, EquipmentEntity>, item) => {
      acc[item.id] = item;

      return acc;
    }, {});
  }

  async delete(equipmentId: number): Promise<EquipmentEntity> {
    const equipment = await this.repository.findOne({
      where: {
        id: equipmentId,
      },
    });

    if (!equipment) {
      throw new BadRequestException(`Equipment ${equipmentId} is not found`);
    }

    await this.repository.checkHasSchedules(equipmentId);
    await this.repository.checkHasStudies(equipmentId);
    await this.repository.checkHasFacilities(equipmentId);
    await this.repository.softDelete({ id: equipmentId });

    return equipment;
  }
}
