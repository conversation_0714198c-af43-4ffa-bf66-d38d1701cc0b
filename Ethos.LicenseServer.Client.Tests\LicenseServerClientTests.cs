﻿using Xunit;
using FluentAssertions;
using System.Diagnostics;
using Ethos.ReferenceData.Client;

namespace Ethos.LicenseServer.Client.Tests
{
    /// <summary>
    /// 
    /// </summary>
    public class LicenseServerClientTests
    {
        readonly Guid userId = new("39a8236c-1a5c-4c2c-a95a-866950e71844");
        readonly Guid tenantId = new("9dfcefaa-0533-4719-92cf-3747a597ed89");
        readonly Guid productId = new("8646f887-52c5-4a0c-9844-ba7424b6af01");
        Guid licenseId = Guid.Empty;
        Guid licensedProductId = Guid.Empty;
        const string bearerToken = "eyJhbGciOiJSUzI1NiIsImtpZCI6Ilg1ZVhrNHh5b2pORnVtMWtsMll0djhkbE5QNC1jNTdkTzZRR1RWQndhTmsiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mpITj4zEfeeD7jNbIQsNgKaiFpVtMKi7KgZh2jOehXb3RntivQJpcTcYNKK25-NMTD0XsDTUBrcyVyoIYAJ11HdxazGb-KyWlFR9KvmGS4rIFNw-ChEO3vhuE2My9YIXiyU5fyVjQThflmPMyfuwHMgFb8r0nggjA-o41oSHVVF8Kd3n1TVk_oBiwuBcBjjqR29NCoz5Sa-43E_HSF3f3EEaMPB1SSxQzTPjPfvOmFctt-F0K7RCQgGZEfK9gC3nGHZ5mrw6KUTPyR9NANF0g17pyJlrhzCtk2WFBbZrCnn5De5QnUz6fbRTGOK4728TQpmc_hQktWIgWVi0OE0rRg";
        const string baseUri = "https://apidev.ethosbridge.com";
        const string refDataBaseUri = "https://localhost:64286";
        readonly LicenseServerClient licClient;
        readonly ReferenceDataClient refClient;


        /// <summary>
        /// 
        /// </summary>
        public LicenseServerClientTests()
        {
            licClient = new LicenseServerClient(new HttpClient(), baseUri);
            refClient = new ReferenceDataClient(new HttpClient())
            {
                BaseUri = refDataBaseUri
            };

            if (!string.IsNullOrEmpty(bearerToken))
            {
                licClient.BearerToken = bearerToken;
                refClient.BearerToken = bearerToken;
            }

            refClient.ResponseReceived += (obj, e) =>
            {
                var method = e.Response?.RequestMessage?.Method;
                var startMsg = $"{method} {e.Response?.RequestMessage?.RequestUri}{Environment.NewLine}";
                var msg = startMsg;

                if (method != HttpMethod.Get && method != HttpMethod.Options && method != HttpMethod.Head)
                    msg += $"{e.Response?.RequestMessage?.Content?.ReadAsStringAsync()?.Result}{Environment.NewLine}";

                msg += $"{e.ContentType}{Environment.NewLine}{e.ResponseString}{Environment.NewLine}Response:{Environment.NewLine}";

                Console.WriteLine(msg);
                Debug.WriteLine(msg);
            };

            licClient.ResponseReceived += (obj, e) =>
            {
                var method = e.Response?.RequestMessage?.Method;
                var startMsg = $"{method} {e.Response?.RequestMessage?.RequestUri}{Environment.NewLine}";
                var msg = startMsg;

                if (method != HttpMethod.Get && method != HttpMethod.Options && method != HttpMethod.Head)
                    msg += $"{e.Response?.RequestMessage?.Content?.ReadAsStringAsync()?.Result}{Environment.NewLine}";

                msg += $"{e.ContentType}{Environment.NewLine}{e.ResponseString}{Environment.NewLine}Response:{Environment.NewLine}";

                Console.WriteLine(msg);
                Debug.WriteLine(msg);
            };
        }

        //[Fact]
        //public async Task ValidateReferenceDataSuccess()
        //{
        //    var validationresult = await refClient.ValidateSetValues(new ReferenceDataValidationDto()
        //    {
        //        SetName = "us-states",
        //        Version = "1.0",
        //        Value = "CO"
        //    },
        //    new ReferenceDataValidationDto()
        //    {
        //        SetName = "us-states",
        //        Version = "1.0",
        //        Value = "NJ"
        //    }
        //    );
        //    validationresult.Should().BeTrue();
        //}

        ///// <summary>
        ///// 
        ///// </summary>
        ///// <returns></returns>
        //[Fact]
        //public async Task ValidateReferenceDataFailure()
        //{

        //    await Assert.ThrowsAsync<AggregateException>(async () => await refClient.ValidateSetValues(new ReferenceDataValidationDto()
        //                                                    {
        //                                                        SetName = "us-states",
        //                                                        Version = "1.0",
        //                                                        Value = "Apples"
        //                                                    },
        //                                                    new ReferenceDataValidationDto()
        //                                                    {
        //                                                        SetName = "us-states",
        //                                                        Version = "1.0",
        //                                                        Value = "Oranges"
        //                                                    }
        //    ));
        //}

        ///// <summary>
        ///// 
        ///// </summary>
        ///// <returns></returns>
        //[Fact]
        //public async Task AddValueToSetSuccess()
        //{

        //    var result = await refClient.AddValueToSet(new Dictionary<string, object>() { { "description", "Description" }, { "code", Guid.NewGuid().ToString() } },
        //                                               "yesNoUnknown", "1");
        //    result.Should().BeGreaterThan(0);
        //}

        ///// <summary>
        ///// 
        ///// </summary>
        ///// <returns></returns>
        //[Fact]
        //public async Task AddObjectToSetSuccess()
        //{

        //    var result = await refClient.AddObjectToSet(new { code = "code", description = "description" },
        //                                               "yesNoUnknown", "1");
        //    result.Should().BeGreaterThan(0);
        //}

       
        [Fact]
        public async Task AddLicenseAndProductWithFeatures()
        {
            var lic = await licClient.CreateTenantLicense(tenantId, DateTimeOffset.UtcNow, DateTimeOffset.UtcNow.AddDays(1000));
            lic.Should().NotBeNull();
            lic.Id.Should().NotBeEmpty();
            licenseId = lic.Id;

            var prd = await licClient.AddProductToLicense(tenantId, licenseId, productId, "Ethos Core", ["Set.*", "SetValue.*", "Patient.*"]);
            prd.Should().NotBeNull();
            prd.Id.Should().NotBeEmpty();
            licensedProductId = prd.Id;

            var feat = await licClient.AddFeatureToProduct(licensedProductId, "ReferenceData", "Reference Data");
            feat.Should().NotBeNull();
            feat.Id.Should().NotBeEmpty();
        }

        [Fact]
        public async Task AddUserToTenant()
        {
            var account = await licClient.AddUserToTenant(tenantId, userId);
            account.Should().NotBeNull();
            account.Id.Should().NotBeEmpty();
        }

        [Fact]
        public async Task GetAllTenantsForUser()
        {
            var accounts = await licClient.ListTenantsForUser(userId);
            accounts.Should().NotBeNull();
            accounts.Count.Should().BeGreaterThan(0);
        }

        [Fact]
        public async Task DoTokenPreIssuance()
        {
            var preToken = await licClient.DoAzurePreTokenIssuance(Guid.NewGuid().ToString(), userId, "<EMAIL>", "Alex Jennings");
            preToken.Should().NotBeNull();
            preToken.ExtensionProducts.Should().NotBeNull();
            var ext = preToken.GetProductsExtension();
            ext.Should().NotBeNull();
        }
    }
}
