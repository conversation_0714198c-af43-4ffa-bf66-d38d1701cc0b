using Ethos.Auth;
using Ethos.Utilities;
using Ethos.Utilities.Filtering;
using Ethos.Utilities.Pagination;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.PlatformManager.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("api/tenants")]
    [Authorize]
    [EthosAuthFeature(Name = FeatureConstants.Core)]
    public class TenantController : ControllerBase
    {
        readonly ILogger<TenantController> _logger;
        readonly PlatformManagerDbContext _dbContext;
        readonly IServiceScopeFactory _scopeFactory;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="dbContext"></param>
        public TenantController(ILogger<TenantController> logger, PlatformManagerDbContext dbContext, IServiceScopeFactory scopeFactory)
        {
            _logger = logger;
            _dbContext = dbContext;
            _scopeFactory = scopeFactory;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="license"></param>
        /// <returns></returns>
        [HttpPost]
        [EthosAuthScope(ScopeDefinitions.TenantAdministrator)]
        public async Task<ActionResult<EthosTenant>> CreateTenant([FromBody] TenantDto tenant)
        {
            if (!tenant.Id.HasValue || tenant.Id.Value == Guid.Empty)
                tenant.Id = Guid.NewGuid();

            var existingTenant = _dbContext.Tenants.FirstOrDefault(t => t.Id == tenant.Id.Value);

            if (existingTenant is not null)
                return this.EthosErrorConflict($"Tenant already exists with ID {tenant.Id.Value}");

            if (string.IsNullOrEmpty(tenant.Status))
                tenant.Status = TenantStatus.Active.ToString();

            if (!Enum.TryParse<TenantStatus>(tenant.Status, true, out var status))
                return this.EthosErrorBadRequest($"Invalid tenant status: {tenant.Status}");

            using var trans = _dbContext.Database.BeginTransaction();

            var newTenant = new EthosTenant()
            {
                Id = tenant.Id.Value,
                Status = status,
                OrganizationName = tenant.OrganizationName,
                Address1 = tenant.Address1,
                Address2 = tenant.Address2,
                Address3 = tenant.Address3,
                City = tenant.City,
                Region = tenant.Region,
                PostalCode = tenant.PostalCode,
                Country = tenant.Country,
                OrganizationEmail = tenant.OrganizationEmail,
                OrganizationPhone = tenant.OrganizationPhone,
            };

            _dbContext.Tenants.Add(newTenant);
            await _dbContext.SaveChangesAsync();

            if (tenant.Contacts.Count > 0)
            {
                foreach (var contact in tenant.Contacts)
                {
                    if (!Enum.TryParse<TenantContactType>(contact.ContactType, true, out var type))
                    {
                        await trans.RollbackAsync();
                        return this.EthosErrorBadRequest($"Invalid contact type: {contact.ContactType}");
                    }

                    if (string.IsNullOrEmpty(contact.FirstName) || string.IsNullOrEmpty(contact.LastName))
                    {
                        await trans.RollbackAsync();
                        return this.EthosErrorBadRequest("Contact first and last name are required.");
                    }

                    var newContact = new TenantContactPerson()
                    {
                        Id = Guid.NewGuid(),
                        TenantId = newTenant.Id,
                        Email = contact.Email,
                        FirstName = contact.FirstName,
                        LastName = contact.LastName,
                        MiddleName = contact.MiddleName,
                        OrganizationName = contact.OrganizationName,
                        PhoneNumber = contact.PhoneNumber,
                        Title = contact.Title,
                        ContactType = type
                    };
                }
                await _dbContext.SaveChangesAsync();
            }
            await trans.CommitAsync();
            _ = Task.Run(async () => await CompleteTenantSetup(newTenant));
            return CreatedAtAction(nameof(GetTenant), new { tenantId = newTenant.Id }, GetTenantDto(newTenant));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="licProd"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{tenantId}")]
        [EthosAuthScope(ScopeDefinitions.TenantWrite)]
        public async Task<ActionResult<TenantDto>> UpdateTenant([FromRoute] Guid tenantId, [FromBody] TenantDto tenant)
        {
            if (tenantId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid tenant ID.");

            var isTenantAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.TenantAdministrator);
            var thisTenantId = this.GetTenantIdOrDefault();

            if (thisTenantId != tenantId && !isTenantAdmin)
                return this.EthosErrorNotFound($"Tenant with ID {tenantId} does not exist.");

            if (tenant.Id.HasValue && tenant.Id.Value != Guid.Empty && tenantId != tenant.Id.Value)
                return this.EthosErrorBadRequest($"Tenant ID {tenantId} does not match entity tenant ID {tenant.Id.Value}.");

            var existingTenant = _dbContext.Tenants.Include(t => t.Contacts).FirstOrDefault(l => l.Id == tenantId);
            if (existingTenant is null)
                return this.EthosErrorNotFound($"Tenant with ID {tenantId} does not exist.");

            if (string.IsNullOrEmpty(tenant.Status))
                tenant.Status = existingTenant.Status.ToString();

            if (!Enum.TryParse<TenantStatus>(tenant.Status, true, out var status))
                return this.EthosErrorBadRequest($"Invalid tenant status: {tenant.Status}");

            if (status != existingTenant.Status && !isTenantAdmin)
                return this.EthosErrorForbidden($"Current user cannot change status for tenant {tenantId}.");

            using var trans = _dbContext.Database.BeginTransaction();

            existingTenant.OrganizationName = tenant.OrganizationName;
            existingTenant.Status = status;
            existingTenant.Address1 = tenant.Address1;
            existingTenant.Address2 = tenant.Address2;
            existingTenant.Address3 = tenant.Address3;
            existingTenant.City = tenant.City;
            existingTenant.Country = tenant.Country;
            existingTenant.Region = tenant.Region;
            existingTenant.PostalCode = tenant.PostalCode;
            existingTenant.OrganizationEmail = tenant.OrganizationEmail;
            existingTenant.OrganizationPhone = tenant.OrganizationPhone;

            _dbContext.Tenants.Update(existingTenant);
            await _dbContext.SaveChangesAsync();

            if (tenant.Contacts.Count > 0)
            {
                foreach (var contact in tenant.Contacts)
                {
                    if (!Enum.TryParse<TenantContactType>(contact.ContactType, true, out var type))
                    {
                        await trans.RollbackAsync();
                        return this.EthosErrorBadRequest($"Invalid contact type: {contact.ContactType}");
                    }

                    if (string.IsNullOrEmpty(contact.FirstName) || string.IsNullOrEmpty(contact.LastName))
                    {
                        await trans.RollbackAsync();
                        return this.EthosErrorBadRequest("Contact first and last name are required.");
                    }

                    var existingContact = existingTenant.Contacts.FirstOrDefault(c => contact.Id.HasValue && c.Id == contact.Id.Value);
                    var isUpdate = existingContact is not null;

                    existingContact ??= new TenantContactPerson();
                    existingContact.FirstName = contact.FirstName;
                    existingContact.LastName = contact.LastName;
                    existingContact.MiddleName = contact.MiddleName;
                    existingContact.OrganizationName = contact.OrganizationName;
                    existingContact.PhoneNumber = contact.PhoneNumber;
                    existingContact.Email = contact.Email;
                    existingContact.Title = contact.Title;
                    existingContact.ContactType = type;
                    existingContact.TenantId = tenantId;
                    if (existingContact.Id == Guid.Empty)
                        existingContact.Id = Guid.NewGuid();

                    if (isUpdate)
                        _dbContext.TenantContacts.Update(existingContact);
                    else
                        _dbContext.TenantContacts.Add(existingContact);
                }
                await _dbContext.SaveChangesAsync();
            }
            await trans.CommitAsync();
            return Ok(GetTenantDto(existingTenant));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.TenantRead)]
        public async Task<ActionResult<PagedResponse<TenantDto>>> GetTenants([FromQuery] PagingParameters pagingParameters, [FromQuery] string? filter = null)
        {
            var tenantId = this.GetTenantIdOrDefault();
            var canReadAllTenants = HttpContext.User.IsAllowedScope(ScopeDefinitions.TenantReadAll);

            var tenants = _dbContext.Tenants.Include(t => t.Contacts)
                                            .Where(t => t.Id == tenantId || canReadAllTenants)
                                            .Filter(filter)
                                            .OrderBy(t => t.Id);

            return Ok(await tenants.PaginateWithLinksAsync(this, (ts) =>
            {
                return ts.Select(t => GetTenantDto(t)).ToList();
            }, pagingParameters.limit, pagingParameters.offset));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("me")]
        [EthosAuthScope(ScopeDefinitions.TenantRead)]
        public async Task<ActionResult<TenantDto>> GetCurrentTenant()
        {
            var tenantId = this.GetTenantIdOrDefault();

            if (tenantId == Guid.Empty)
                return this.EthosErrorBadRequest("Tenant ID not found in token.");

            var tenant = await _dbContext.Tenants.Include(t => t.Contacts).FirstOrDefaultAsync(t => t.Id == tenantId);

            if (tenant is null)
                return this.EthosErrorNotFound($"Tenant with ID {tenantId} does not exist.");

            return Ok(GetTenantDto(tenant));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tenant"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("me")]
        [EthosAuthScope(ScopeDefinitions.TenantRead)]
        public async Task<ActionResult<TenantDto>> UpdateCurrentTenant([FromBody] TenantDto tenant)
        {
            var tenantId = this.GetTenantIdOrDefault();

            if (tenantId == Guid.Empty)
                return this.EthosErrorBadRequest("Tenant ID not found in token.");

            return await UpdateTenant(tenantId, tenant);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("{tenantId}")]
        [EthosAuthScope(ScopeDefinitions.TenantRead)]
        public async Task<ActionResult<TenantDto>> GetTenant([FromRoute] Guid tenantId)
        {
            if (tenantId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid tenant ID.");

            var thisTenantId = this.GetTenantIdOrDefault();

            if (thisTenantId != tenantId && !HttpContext.User.IsAllowedScope(ScopeDefinitions.TenantReadAll))
                return this.EthosErrorNotFound($"Tenant with ID {tenantId} does not exist.");

            var tenant = await _dbContext.Tenants.Include(t => t.Contacts).FirstOrDefaultAsync(t => t.Id == tenantId);

            if (tenant is null)
                return this.EthosErrorNotFound($"Tenant with ID {tenantId} does not exist.");

            return Ok(GetTenantDto(tenant));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tenant"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        async Task CompleteTenantSetup(EthosTenant tenant)
        {
            if (tenant.Id == Guid.Empty)
                throw new Exception("Invalid tenant ID.");

            using var scope = _scopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<PlatformManagerDbContext>();
            foreach (var builtinRole in BuiltinRole.All)
            {
                if (!dbContext.Roles.Any(r => r.Id == builtinRole.Id && r.TenantId == tenant.Id))
                {
                    dbContext.Roles.Add(new EthosRole()
                    {
                        Id = builtinRole.Id,
                        TenantId = tenant.Id,
                        Name = builtinRole.Name,
                    });
                }
            }
            await dbContext.SaveChangesAsync();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="lic"></param>
        /// <returns></returns>
        static TenantDto GetTenantDto(EthosTenant t)
        {
            return new TenantDto()
            {
                Id = t.Id,
                Status = t.Status.ToString(),
                Address1 = t.Address1,
                Address2 = t.Address2,
                Address3 = t.Address3,
                City = t.City,
                Region = t.Region,
                PostalCode = t.PostalCode,
                Country = t.Country,
                OrganizationEmail = t.OrganizationEmail,
                OrganizationPhone = t.OrganizationPhone,
                OrganizationName = t.OrganizationName,
                Contacts = [..t.Contacts.Select(c => new TenantContactDto()
                {
                    ContactType = c.ContactType.ToString(),
                    Email = c.Email,
                    FirstName = c.FirstName,
                    LastName = c.LastName,
                    MiddleName = c.MiddleName,
                    OrganizationName  = c.OrganizationName,
                    PhoneNumber = c.PhoneNumber,
                    Title = c.Title,
                    Id  = c.Id,
                })]
            };
        }
    }
}
