import { Column, Entity, OneToMany } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BaseEntity } from '@app/common/base.entity';
import { ScheduleEntity } from '@app/modules/schedule/schedule.entity';

@Entity({ name: 'patients' })
export class PatientEntity extends BaseEntity {
  @Column({ nullable: true })
  @ApiPropertyOptional()
  @Expose()
  externalId?: number;

  @Column()
  @ApiProperty()
  @Expose()
  name: string;

  @OneToMany(
    () => ScheduleEntity,
    schedule => schedule.patient,
    {
      onDelete: 'CASCADE',
    }
  )
  schedules: ScheduleEntity;
}
