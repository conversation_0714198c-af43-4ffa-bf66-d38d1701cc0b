//@ts-nocheck
// import '@axmit/persante-hospitals';

export interface ITechnicianScheduleCollectionPayload  {}
export interface ITechnicianScheduleCollectionUpdatePayload {}
export interface ITechnicianScheduleCollectionUpdateResponse {}
export interface ITechnicianScheduleCollectionUpsertItem {}
export interface ITechnicianScheduleCollectionDto {}
export interface ITechnicianScheduleDto {
  edited?: boolean;
  scheduleId?: number;
}

export interface IScheduleItem {
  facilityId?: number;
  shift?: 'day' | 'night' | 'day_off';
  capacity?: number;
}

export interface ITechnicianScheduleEntity {
  [key: string]: string | number | IScheduleItem | undefined;
}

export interface ITechnicianScheduleCollection {
  data: ITechnicianScheduleDto[] | null;
  loading: boolean;
}

export interface ITechnicianScheduleEntityChangePayload {
  colId: string;
  fieldId: string;
  rowId: string;
  value: string;
  facilityId?: number;
}
