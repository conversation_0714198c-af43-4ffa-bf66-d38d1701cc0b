variables:
  IMAGE: $CI_REGISTRY/$CI_PROJECT_PATH:$CI_COMMIT_REF_SLUG
  YARN_IMAGE: $CI_REGISTRY/$CI_PROJECT_PATH/yarn:$CI_COMMIT_REF_SLUG
  CHART: app-front

stages:
  - dependencies
  - build
  - pack
  - deploy

.Kaniko:
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: [""]
  before_script:
    - echo "{\"auths\":{\"$CI_REGISTRY\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$CI_REGISTRY_PASSWORD\"}}}" > /kaniko/.docker/config.json

dependencies:
  extends: .Kaniko
  stage: dependencies
  script:
    - echo "//registry.npmjs.org/:_authToken=$NPM_TOKEN" > .npmrc
    - /kaniko/executor --cache --context $CI_PROJECT_DIR --dockerfile $CI_PROJECT_DIR/.deploy/Dockerfile.yarn --destination $YARN_IMAGE

build:
  image: $YARN_IMAGE
  stage: build
  artifacts:
    expire_in: 1h
    paths:
      - build/
  script:
    - ln -s /app/node_modules $CI_PROJECT_DIR/node_modules
    - yarn build

docker:
  extends: .Kaniko
  dependencies:
    - build
  stage: pack
  script:
    - /kaniko/executor --cache --context $CI_PROJECT_DIR --dockerfile $CI_PROJECT_DIR/.deploy/Dockerfile --destination $IMAGE

include:
  - project: axmit-infra/ci-templates
    file: deploy_to_axmit.yml