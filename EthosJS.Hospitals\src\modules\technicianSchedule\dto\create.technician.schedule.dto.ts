import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsInt, IsPositive, ValidateIf } from 'class-validator';
import { ETechnicianScheduleShift } from '@app/modules/technicianSchedule/enums';
import { IsDateOnly } from '@app/common/decorators/validators.decorator';

export class CreateTechnicianScheduleDto {
  @ApiProperty()
  @IsInt()
  @IsPositive()
  technicianId: number;

  @ApiPropertyOptional()
  @ValidateIf((obj) => obj.shift !== ETechnicianScheduleShift.DayOff)
  @IsInt()
  @IsPositive()
  facilityId?: number;

  @ApiProperty({ enum: ETechnicianScheduleShift })
  @IsEnum(ETechnicianScheduleShift)
  shift: ETechnicianScheduleShift;

  @ApiPropertyOptional()
  @ValidateIf((obj) => obj.shift !== ETechnicianScheduleShift.DayOff)
  @IsInt()
  @IsPositive()
  capacity?: number;

  @ApiProperty({ format: 'date-time', example: '2020-01-01' })
  @IsDateOnly()
  date: string;
}
