import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { Column, Entity, ManyToOne } from 'typeorm';
import { BaseEntity } from '@app/common/base.entity';
import { ETechnicianScheduleShift } from '@app/modules/technicianSchedule/enums';
import { TechnicianEntity } from '@app/modules/technician/technician.entity';
import { FacilityEntity } from '@app/modules/facility/facility.entity';

@Entity({ name: 'technician_schedules' })
export class TechnicianScheduleEntity extends BaseEntity {
  @Column()
  @ApiProperty()
  @Expose()
  technicianId: number;

  @ManyToOne(
    () => TechnicianEntity,
    technician => technician.technicianSchedules,
  )
  @ApiProperty({ type: () => TechnicianEntity })
  @Expose()
  technician: TechnicianEntity;

  @Column({ nullable: true })
  @ApiPropertyOptional()
  @Expose()
  facilityId?: number;

  @ManyToOne(
    () => FacilityEntity,
    facility => facility.technicianSchedules,
  )
  facility?: TechnicianEntity;

  @Column({ enum: ETechnicianScheduleShift, type: 'enum' })
  @ApiProperty({ enum: ETechnicianScheduleShift })
  @Expose()
  shift: ETechnicianScheduleShift;

  @Column({ nullable: true })
  @ApiPropertyOptional()
  @Expose()
  capacity?: number;

  @Column({ type: 'date' })
  @ApiProperty()
  @Expose()
  date: string;
}
