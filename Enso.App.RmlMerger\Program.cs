﻿using System.Reflection.PortableExecutable;
using System.Xml;
using System.Linq;
using System.Globalization;
using System.Xml.Linq;
using System.Data;
using System.Collections.Specialized;
using static System.Net.Mime.MediaTypeNames;
using System.Reflection.Metadata;
using System.Security.Principal;
using static RmlMerger.RmlMerge;

namespace RmlMerger
{
    internal class Program
    {
        static void Main(string[] args)
        {
            RmlMerge.MergeTestFiles();
        }
    }
    internal class RmlMerge
    {
        public static void MergeTestFiles()
        {
            var files = new string[]
            {
                @"\\sleepcarecenter.com\devwebfiles\ClinicalData\CPR - Cooper Voorhees\00002211-112225\00002211-112225.rml",
                @"\\sleepcarecenter.com\devwebfiles\ClinicalData\CPR - Cooper Voorhees\00002212-112225\00002212-112225.rml",
                @"\\sleepcarecenter.com\devwebfiles\ClinicalData\CPR - Cooper Voorhees\00002213-112225\00002213-112225.rml",
                @"\\sleepcarecenter.com\devwebfiles\ClinicalData\CPR - Cooper Voorhees\00002214-112225\00002214-112225.rml",
                @"\\sleepcarecenter.com\devwebfiles\ClinicalData\CPR - Cooper Voorhees\00002215-112225\00002215-112225.rml",
            };
            MergeSessions(files);
        }

        static Func<string, XmlWriter> CreateWriter;
        private static void MergeSessions(string[] files)
        {

            string[] xmlFilePaths = files; // Replace with your XML file paths
            string outputSessionsFilePath = "merged.xml"; // Replace with the desired output file path
            string outputEventsFilePath = "mergedevents.xml"; // Replace with the desired output file path
            string outputUserDataFilePath = "mergedUserData.xml"; // Replace with the desired output file path
            string outputMachineDataFilePath = "mergedMachineData.xml"; // Replace with the desired output file path
            string outputTechNotesDataFilePath = "techNotes.xml"; // Replace with the desired output file path
            XmlWriterSettings settings = new XmlWriterSettings
            {
                Indent = true,
                IndentChars = "   ", // Use four spaces for indentation
                NewLineChars = "\n",
                NewLineHandling = NewLineHandling.Replace,
                OmitXmlDeclaration = false // Include XML declaration, 


            };

            CreateWriter = (path) => XmlWriter.Create(path, settings);

            Dictionary<string, List<DateTime>> sessionDates = new();
            List<Session> sessions = new();
            GetSessionDates(xmlFilePaths, sessionDates, sessions);

            MergeSessions(xmlFilePaths, outputSessionsFilePath);
            MergeEvents(xmlFilePaths, outputEventsFilePath, sessionDates);
            MergeUserStagingData(xmlFilePaths, outputUserDataFilePath, sessionDates);
            MergeMachineStagingData(xmlFilePaths, outputMachineDataFilePath, sessionDates);
            MergeTechNotes(xmlFilePaths, outputTechNotesDataFilePath, sessionDates);
            var outputFile = "merge.rml";
            var collectionOutputDirectory = sessions.First().CollectionFolder;
            CombineMergedData(outputFile, xmlFilePaths.First(), outputSessionsFilePath, outputEventsFilePath, outputUserDataFilePath, outputMachineDataFilePath, outputTechNotesDataFilePath, collectionOutputDirectory, sessions);

            var outputRmlFileName = Path.Combine(collectionOutputDirectory, Path.GetFileName(xmlFilePaths.First()));
            using (XmlWriter writer = CreateWriter(outputRmlFileName))
            {
                using var reader = XmlReader.Create(outputFile);
                writer.WriteNode(reader, true);
            }



            Console.WriteLine("Merged XML written to merged.xml");
        }

        private static void CombineMergedData(
            string outputFilePath,
            string srcFilePath,
            string outputSessionsFilePath,
            string outputEventsFilePath,
            string outputUserDataFilePath,
            string outputMachineDataFilePath,
            string outputTechNotesDataFilePath,
            string collectionOutputDirectory,
            List<Session> sessions)
        {


            using XmlWriter writer = CreateWriter(outputFilePath);
            using var reader = XmlReader.Create(srcFilePath);
            reader.Read();
            writer.WriteNode(reader, true);


            MergeElements(
                reader,
                writer,
                outputFilePath,
                srcFilePath,
                outputSessionsFilePath,
                outputEventsFilePath,
                outputUserDataFilePath,
                outputMachineDataFilePath,
                outputTechNotesDataFilePath,
                collectionOutputDirectory,
                sessions
                );

        }

        public class SegmentMap
        {
            public FileInfo SrcFile { get; set; }
            public FileInfo DestFile { get; set; }
        }
        private static void MergeElements(
            XmlReader reader,
            XmlWriter writer,
            string outputFilePath,
            string srcFilePath,
            string outputSessionsFilePath,
            string outputEventsFilePath,
            string outputUserDataFilePath,
            string outputMachineDataFilePath,
            string outputTechNotesDataFilePath,
            string collectionOutputDirectory,
            List<Session> sessions)
        {
            var di = new DirectoryInfo(collectionOutputDirectory);
            di.Create();
            var videoDirectory = new DirectoryInfo(Path.Combine(di.FullName, $"{di.Name}-Videos"));
            videoDirectory.Create();

            var collectionFolderName = di.Name;
            int segmentCount = 0;
            int videoSegmentCount = 0;
            List<SegmentMap> segmentPaths = new();
            List<SegmentMap> videoSegmentPaths = new();
            foreach (var session in sessions)
            {
                var collectionFolder = new DirectoryInfo(session.CollectionFolderPath);
                var edfFiles = collectionFolder.GetFiles("*.edf").Where(x => !x.Name.Contains("-T", StringComparison.OrdinalIgnoreCase)).ToList();
                if (edfFiles.Count != session.Segments.Count)
                {
                    throw new Exception($"Session is missing edf files: Expected: {session.Segments.Count} - Actual: {edfFiles.Count}");
                }
                else
                {
                    for (var i = 0; i < session.Segments.Count; i++)
                    {
                        var src = edfFiles[i];
                        var dest = Path.Combine(di.FullName, $"{collectionFolderName}[{(++segmentCount).ToString().PadLeft(3, '0')}].edf");
                        segmentPaths.Add(new SegmentMap { SrcFile = src, DestFile = new FileInfo(dest) });
                    }
                }

                var videoFolder = new DirectoryInfo(Path.Combine(collectionFolder.FullName, $"{collectionFolder.Name}-Video"));
                var videoFiles = videoFolder.GetFiles("*.asf").ToList();
                if (videoFiles.Count != session.Segments.Count)
                {
                    throw new Exception($"Session is missing edf files: Expected: {session.Segments.Count} - Actual: {edfFiles.Count}");
                }
                else
                {
                    for (var i = 0; i < session.VideoSegments.Count; i++)
                    {
                        var src = videoFiles[i];
                        var dest = Path.Combine(videoDirectory.FullName, $"{(++videoSegmentCount).ToString().PadLeft(4, '0')}.asf");
                        videoSegmentPaths.Add(new SegmentMap { SrcFile = src, DestFile = new FileInfo(dest) });
                    }
                }
            }

            segmentPaths.ForEach(x =>
            {
                if (!x.DestFile.Exists)
                    x.SrcFile.CopyTo(x.DestFile.FullName);
            });
            videoSegmentPaths.ForEach(x =>
            {
                if (!x.DestFile.Exists)
                    x.SrcFile.CopyTo(x.DestFile.FullName);
            });
            while (reader.Read())
            {
                if (reader.NodeType == XmlNodeType.Element)
                {
                    switch (reader.Name)
                    {
                        //import merged session
                        case "Sessions":
                            {

                                // <Sessions>
                                var indent = "   ";
                                writer.WriteStartElement(reader.Prefix, "Sessions", reader.NamespaceURI);

                                {
                                    // <Session>
                                    writer.WriteWhitespace($"\n{indent}{indent}{indent}");
                                    writer.WriteStartElement(reader.Prefix, "Session", reader.NamespaceURI);
                                    {
                                        //using var node = GetReaderForDocumentNode(outputSessionsFilePath, reader.Name);

                                        //<RecordingStart>2023-06-22T07:31:11</RecordingStart>
                                        writer.WriteWhitespace($"\n{indent}{indent}{indent}{indent}");
                                        writer.WriteStartElement(reader.Prefix, nameof(Session.RecordingStart), reader.NamespaceURI);
                                        writer.WriteString(sessions.First().RecordingStart.ToString("yyyy-MM-ddTHH:mm:ss"));
                                        writer.WriteEndElement();

                                        // <Duration>1506</Duration>
                                        writer.WriteWhitespace($"\n{indent}{indent}{indent}{indent}");
                                        var duration = (sessions.Last().RecordingStart.AddSeconds(sessions.Last().Duration) - sessions.First().RecordingStart).TotalSeconds;
                                        writer.WriteStartElement(reader.Prefix, nameof(Session.Duration), reader.NamespaceURI);
                                        writer.WriteString(duration.ToString());
                                        writer.WriteEndElement();

                                        // <LightsOff>210</LightsOff>
                                        writer.WriteWhitespace($"\n{indent}{indent}{indent}{indent}");
                                        var lightsOff = sessions.First().LightsOff;
                                        writer.WriteStartElement(reader.Prefix, nameof(Session.LightsOff), reader.NamespaceURI);
                                        writer.WriteString(lightsOff.ToString());
                                        writer.WriteEndElement();

                                        // <LightsOff>21430</LightsOff>
                                        writer.WriteWhitespace($"\n{indent}{indent}{indent}{indent}");
                                        var LightsOn = sessions.Last().LightsOn;
                                        writer.WriteStartElement(reader.Prefix, nameof(Session.LightsOn), reader.NamespaceURI);
                                        writer.WriteString(LightsOn.ToString());
                                        writer.WriteEndElement();


                                        // <Segments>
                                        writer.WriteWhitespace($"\n{indent}{indent}{indent}{indent}");
                                        writer.WriteStartElement(reader.Prefix, nameof(Session.Segments), reader.NamespaceURI);

                                        foreach (var session in sessions)
                                        {
                                            //Todo copy session.CollectionFolder *.edf
                                            foreach (var segment in session.Segments)
                                            {

                                                // <Segment>
                                                writer.WriteWhitespace($"\n{indent}{indent}{indent}{indent}{indent}");
                                                writer.WriteStartElement(reader.Prefix, nameof(Segment), reader.NamespaceURI);
                                                {

                                                    // <StartTime>
                                                    writer.WriteWhitespace($"\n{indent}{indent}{indent}{indent}{indent}{indent}");
                                                    writer.WriteStartElement(reader.Prefix, nameof(Segment.StartTime), reader.NamespaceURI);
                                                    writer.WriteString(segment.StartTime.ToString("yyyy-MM-ddTHH:mm:ss"));
                                                    writer.WriteEndElement();
                                                    writer.WriteString(" ");
                                                    // </StartTime>

                                                    // <Duration>
                                                    writer.WriteWhitespace($"\n{indent}{indent}{indent}{indent}{indent}{indent}");
                                                    writer.WriteStartElement(reader.Prefix, nameof(Segment.Duration), reader.NamespaceURI);
                                                    writer.WriteString(segment.Duration.ToString());
                                                    writer.WriteEndElement();
                                                    writer.WriteString(" ");
                                                    // </Duration>

                                                }
                                                // </Segment>
                                                writer.WriteWhitespace($"\n{indent}{indent}{indent}{indent}{indent}");
                                                writer.WriteEndElement();
                                            }

                                        }
                                        // </Segments>
                                        writer.WriteWhitespace($"\n{indent}{indent}{indent}{indent}");
                                        writer.WriteEndElement();

                                        // <VideoSegments>
                                        writer.WriteWhitespace($"\n{indent}{indent}{indent}{indent}");
                                        writer.WriteStartElement(reader.Prefix, nameof(Session.Segments), reader.NamespaceURI);

                                        int videoSegmentNumber = 0;
                                        foreach (var session in sessions)
                                        {

                                            foreach (var segment in session.VideoSegments)
                                            {
                                                // <VideoSegment>
                                                var videoSegmentMap = videoSegmentPaths[videoSegmentNumber++];
                                                writer.WriteWhitespace($"\n{indent}{indent}{indent}{indent}{indent}");
                                                writer.WriteStartElement(reader.Prefix, nameof(VideoSegment), reader.NamespaceURI);
                                                {
                                                    // <StartTime>
                                                    writer.WriteWhitespace($"\n{indent}{indent}{indent}{indent}{indent}{indent}");
                                                    writer.WriteStartElement(reader.Prefix, nameof(VideoSegment.StartTime), reader.NamespaceURI);
                                                    writer.WriteString(segment.StartTime.ToString("yyyy-MM-ddTHH:mm:ss"));
                                                    writer.WriteEndElement();
                                                    writer.WriteString(" ");
                                                    // </StartTime>

                                                    // <Duration>
                                                    writer.WriteWhitespace($"\n{indent}{indent}{indent}{indent}{indent}{indent}");
                                                    writer.WriteStartElement(reader.Prefix, nameof(VideoSegment.Duration), reader.NamespaceURI);
                                                    writer.WriteString(segment.Duration.ToString());
                                                    writer.WriteEndElement();
                                                    writer.WriteString(" ");
                                                    // </Duration>

                                                    // <FileName>
                                                    writer.WriteWhitespace($"\n{indent}{indent}{indent}{indent}{indent}{indent}");
                                                    writer.WriteStartElement(reader.Prefix, nameof(VideoSegment.FileName), reader.NamespaceURI);
                                                    writer.WriteString(videoSegmentMap.DestFile.Name);
                                                    writer.WriteEndElement();
                                                    writer.WriteString(" ");
                                                    // </FileName>
                                                }
                                                // </VideoSegment>
                                                writer.WriteWhitespace($"\n{indent}{indent}{indent}{indent}{indent}");
                                                writer.WriteEndElement();

                                            }
                                        }


                                    }
                                    writer.WriteWhitespace($"\n{indent}{indent}{indent}{indent}");
                                    writer.WriteEndElement(); // </VideoSegment>
                                                              // </Session>
                                    writer.WriteWhitespace($"\n{indent}{indent}{indent}");
                                    writer.WriteEndElement(); //close Session tag;
                                }
                                // </Sessions>
                                writer.WriteWhitespace($"\n{indent}{indent}");
                                writer.WriteEndElement(); //close Sessions tag;
                                reader.ReadInnerXml();
                            }
                            break;

                        //import merged events
                        case "Events":
                            {
                                using var node = GetReaderForDocumentNode(outputEventsFilePath, reader.Name);
                                writer.WriteNode(node, true);
                                reader.ReadInnerXml();
                            }
                            break;
                        //import merged events
                        case "StagingData":
                            {
                                writer.WriteStartElement(reader.Prefix, reader.LocalName, reader.NamespaceURI);
                                writer.WriteAttributes(reader, true);
                                using var node = GetReaderForDocumentNode(outputUserDataFilePath, "UserStaging");
                                writer.WriteNode(node, true);
                                using var machinenode = GetReaderForDocumentNode(outputUserDataFilePath, "MachineStaging");
                                writer.WriteEndElement();
                                reader.ReadInnerXml();


                            }
                            break;

                        case "TechNotes":
                            {
                                using var node = GetReaderForDocumentNode(outputTechNotesDataFilePath, reader.Name);
                                writer.WriteNode(node, true);
                                reader.ReadInnerXml();
                                writer.WriteWhitespace("\n");
                            }
                            break;
                        default:
                            if (reader.IsEmptyElement)
                            {
                                writer.WriteStartElement(reader.Prefix, reader.LocalName, reader.NamespaceURI);
                                writer.WriteAttributes(reader, true);
                                writer.WriteString("");
                                writer.WriteEndElement();
                            }
                            else
                            {
                                writer.WriteStartElement(reader.Prefix, reader.LocalName, reader.NamespaceURI);
                                writer.WriteAttributes(reader, true);
                            }



                            //MergeElements(reader, writer, outputFilePath,
                            //       srcFilePath,
                            //       outputSessionsFilePath,
                            //       outputEventsFilePath,
                            //       outputUserDataFilePath,
                            //       outputMachineDataFilePath,
                            //       settings);

                            //writer.WriteEndElement();
                            break;

                    }
                }
                else
                {
                    switch (reader.NodeType)
                    {
                        case XmlNodeType.None:
                            return;//
                        case XmlNodeType.EndElement:
                            writer.WriteEndElement();
                            break;
                        case XmlNodeType.Attribute:
                            writer.WriteAttributes(reader, true);
                            break;
                        case XmlNodeType.Text:
                            writer.WriteString(reader.Value);
                            break;
                        case XmlNodeType.CDATA:
                            break;
                        case XmlNodeType.EntityReference:
                            break;
                        case XmlNodeType.Entity:
                            break;
                        case XmlNodeType.ProcessingInstruction:
                            break;
                        case XmlNodeType.Comment:
                            writer.WriteComment(reader.Value);
                            break;
                        case XmlNodeType.Document:
                            break;
                        case XmlNodeType.DocumentType:
                            break;
                        case XmlNodeType.DocumentFragment:
                            break;
                        case XmlNodeType.Notation:
                            break;
                        case XmlNodeType.Whitespace:
                            writer.WriteString(reader.Value); break;
                        case XmlNodeType.SignificantWhitespace:
                            writer.WriteString(reader.Value); break;
                        case XmlNodeType.EndEntity:
                            break;
                        case XmlNodeType.XmlDeclaration:
                            writer.WriteNode(reader, true);

                            continue;
                        case XmlNodeType.Element:
                            throw new Exception("This should not hit");
                        default:
                            break;
                    }
                    //MergeElements(reader, writer, outputFilePath,
                    //            srcFilePath,
                    //            outputSessionsFilePath,
                    //            outputEventsFilePath,
                    //            outputUserDataFilePath,
                    //            outputMachineDataFilePath,
                    //            settings);
                }
            }
        }

        private static void WriteAttributes(XmlWriter writer, XmlReader reader)
        {
            for (var k = 0; k < reader.AttributeCount; k++)
            {
                reader.MoveToNextAttribute();
                writer.WriteAttributeString(reader.Name, reader.Value);
            }
        }

        private static void WriteStartElement(XmlWriter writer, XmlReader reader)
        {
            writer.WriteStartElement(reader.Prefix, reader.LocalName, reader.NamespaceURI);
        }

        private static XmlReader GetReaderForDocumentNode(string xmlFilePath, string node)
        {
            var reader = XmlReader.Create(xmlFilePath);
            while (reader.Read())
            {
                if (reader.NodeType == XmlNodeType.Element && reader.Name == node)
                {
                    break;
                }
            }
            return reader;
        }

        private static void MergeSessions(string[] xmlFilePaths, string outputFilePath)
        {
            using (XmlWriter writer = CreateWriter(outputFilePath))
            {
                //writer.WriteStartDocument();
                //writer.WriteStartElement("Sessions"); // Root element for merged data

                bool wroteRoot = false;

                foreach (string xmlFilePath in xmlFilePaths)
                {
                    using (XmlReader reader = XmlReader.Create(xmlFilePath))
                    {
                        // Find the <Session> elements and copy them to the merged output
                        while (reader.Read())
                        {
                            if (wroteRoot == false)
                            {
                                if (reader.NodeType == XmlNodeType.XmlDeclaration)
                                {
                                    writer.WriteNode(reader, true);
                                }
                                else if (reader.NodeType == XmlNodeType.Element)
                                {
                                    switch (reader.Name)
                                    {
                                        case "Sessions":
                                        case "PatientStudy":
                                            writer.WriteStartElement(reader.Prefix, reader.LocalName, reader.NamespaceURI);
                                            writer.WriteAttributes(reader, true);
                                            wroteRoot = reader.Name == "Sessions";
                                            break;
                                        default:
                                            break;

                                    }
                                }

                            }

                            else if (reader.NodeType == XmlNodeType.Element && reader.Name == "Session")
                            {
                                writer.WriteNode(reader, true);
                            }
                        }
                    }
                }
                writer.WriteEndElement(); // Close the Sessions element
                writer.WriteEndElement(); // Close the PatientStudy element
                writer.WriteEndDocument();


            }
        }

        private static void MergeEvents(
            string[] xmlFilePaths,
            string outputFilePath,
            Dictionary<string, List<DateTime>> sessions
            )
        {
            var lastSessionDate = sessions.First().Value.First();
            using (XmlWriter writer = CreateWriter(outputFilePath))
            {
                //writer.WriteStartDocument();
                //writer.WriteStartElement("Sessions"); // Root element for merged data

                bool wroteRoot = false;

                for (var i = 0; i < xmlFilePaths.Length; i++)
                //foreach (string xmlFilePath in xmlFilePaths)
                {
                    var xmlFilePath = xmlFilePaths[i];
                    var sessionStart = sessions[xmlFilePath].First();
                    var offset = (int)(sessionStart - lastSessionDate).TotalSeconds;
                    //lastSessionDate = sessionStart;
                    using (XmlReader reader = XmlReader.Create(xmlFilePath))
                    {
                        // Find the <Session> elements and copy them to the merged output
                        bool wroteEventStartComment = false;
                        while (reader.Read())
                        {
                            if (wroteRoot == false)
                            {
                                if (reader.NodeType == XmlNodeType.XmlDeclaration)
                                {
                                    writer.WriteNode(reader, true);
                                }
                                else if (reader.NodeType == XmlNodeType.Element)
                                {
                                    switch (reader.Name)
                                    {
                                        case "PatientStudy":
                                        case "ScoringData":
                                        case "Events":
                                            writer.WriteStartElement(reader.Prefix, reader.LocalName, reader.NamespaceURI);
                                            writer.WriteAttributes(reader, true);
                                            wroteRoot = reader.Name == "Events";

                                            break;
                                        case "LastModified":
                                            if (i == xmlFilePaths.Length)
                                            {
                                                writer.WriteNode(reader, true);
                                            }
                                            break;
                                        default:
                                            break;

                                    }
                                }

                            }

                            else if (reader.NodeType == XmlNodeType.Element && reader.Name == "Event")
                            {
                                if (!wroteEventStartComment)
                                {
                                    writer.WriteComment($"Begin Events: {Path.GetFileName(xmlFilePath)} - {sessionStart} - Offset: {offset}");
                                    wroteEventStartComment = true;
                                }
                                writer.WriteStartElement(reader.Prefix, reader.LocalName, reader.NamespaceURI);


                                for (var k = 0; k < reader.AttributeCount; k++)
                                {
                                    reader.MoveToNextAttribute();
                                    if (reader.Name == "Start")
                                    {
                                        int.TryParse(reader.Value, out int start);
                                        start += offset;
                                        writer.WriteAttributeString(reader.Name, start.ToString());
                                    }
                                    else
                                    {
                                        writer.WriteAttributeString(reader.Name, reader.Value);
                                    }


                                }

                                reader.Read();
                                reader.MoveToContent();
                                //write the inner xml contents.
                                writer.WriteNode(reader, true);
                                writer.WriteEndElement();//close the Event tag;

                            }
                        }
                    }
                }
                writer.WriteEndElement(); // Close the Events element
                writer.WriteEndElement(); // Close the ScoringData element
                writer.WriteEndElement(); // Close the PatientStudy element
                writer.WriteEndDocument();


            }
        }

        private static void MergeUserStagingData(
         string[] xmlFilePaths,
             string outputFilePath,
             Dictionary<string, List<DateTime>> sessions
         )
        {
            var lastSessionDate = sessions.First().Value.First();
            using (XmlWriter writer = CreateWriter(outputFilePath))
            {
                //writer.WriteStartDocument();
                //writer.WriteStartElement("Sessions"); // Root element for merged data

                bool wroteRoot = false;
                int outerElementCount = 0;
                for (var i = 0; i < xmlFilePaths.Length; i++)
                //foreach (string xmlFilePath in xmlFilePaths)
                {
                    var xmlFilePath = xmlFilePaths[i];
                    var sessionStart = sessions[xmlFilePath].First();
                    var offset = (int)(sessionStart - lastSessionDate).TotalSeconds;
                    //lastSessionDate = sessionStart;
                    using (XmlReader reader = XmlReader.Create(xmlFilePath))
                    {
                        // Find the <Session> elements and copy them to the merged output
                        bool wroteEventStartComment = false;
                        while (reader.Read())
                        {
                            if (reader.NodeType == XmlNodeType.Whitespace)
                                continue;
                            if (wroteRoot == false)
                            {
                                if (reader.NodeType == XmlNodeType.XmlDeclaration)
                                {
                                    writer.WriteNode(reader, true);
                                }
                                else if (reader.NodeType == XmlNodeType.Element)
                                {
                                    switch (reader.Name)
                                    {
                                        case "PatientStudy":
                                        case "ScoringData":
                                        case "StagingData":
                                        case "UserStaging":
                                        case "NeuroAdultAASMStaging":
                                            outerElementCount++;
                                            writer.WriteStartElement(reader.Prefix, reader.LocalName, reader.NamespaceURI);
                                            writer.WriteAttributes(reader, true);
                                            wroteRoot = reader.Name == "NeuroAdultAASMStaging";
                                            break;

                                    }
                                }

                            }

                            else if (reader.NodeType == XmlNodeType.Element && reader.Name == "Stage")
                            {
                                if (!wroteEventStartComment)
                                {
                                    writer.WriteComment($"Begin Stage: {Path.GetFileName(xmlFilePath)} - {sessionStart} - Offset: {offset}");
                                    wroteEventStartComment = true;
                                }
                                writer.WriteStartElement(reader.Prefix, reader.LocalName, reader.NamespaceURI);


                                for (var k = 0; k < reader.AttributeCount; k++)
                                {
                                    reader.MoveToNextAttribute();
                                    if (reader.Name == "Start")
                                    {
                                        int.TryParse(reader.Value, out int start);
                                        start += offset;
                                        writer.WriteAttributeString(reader.Name, start.ToString());
                                    }
                                    else
                                    {
                                        writer.WriteAttributeString(reader.Name, reader.Value);
                                    }


                                }

                                reader.Read();
                                writer.WriteEndElement();//close the Event tag;

                            }
                        }
                    }
                }
                for (var i = 0; i < outerElementCount; i++)
                {
                    writer.WriteEndElement();
                }
                //if (wroteRoot)
                //{
                //    writer.WriteEndElement(); // Close the NeuroAdultAASMStaging element
                //    writer.WriteEndElement(); // Close the UserStaging element
                //}
                //writer.WriteEndElement(); // Close the StagingData element
                //writer.WriteEndElement(); // Close the ScoringData element
                //writer.WriteEndElement(); // Close the PatientStudy element
                writer.WriteEndDocument();


            }
        }
        private static void MergeMachineStagingData(
        string[] xmlFilePaths,
            string outputFilePath,
            Dictionary<string, List<DateTime>> sessions
        )
        {
            var lastSessionDate = sessions.First().Value.First();
            using (XmlWriter writer = CreateWriter(outputFilePath))
            {
                //writer.WriteStartDocument();
                //writer.WriteStartElement("Sessions"); // Root element for merged data

                bool wroteRoot = false;
                int outerElementCount = 0;
                for (var i = 0; i < xmlFilePaths.Length; i++)
                //foreach (string xmlFilePath in xmlFilePaths)
                {
                    var xmlFilePath = xmlFilePaths[i];
                    var sessionStart = sessions[xmlFilePath].First();
                    var offset = (int)(sessionStart - lastSessionDate).TotalSeconds;
                    bool wroteMachineStaging = false;

                    //lastSessionDate = sessionStart;
                    using (XmlReader reader = XmlReader.Create(xmlFilePath))
                    {
                        if (!wroteRoot)
                        {
                            ReadToElement(reader, "PatientStudy");
                            writer.WriteStartElement(reader.Prefix, reader.LocalName, reader.NamespaceURI);
                            writer.WriteAttributes(reader, true);

                            ReadToElement(reader, "ScoringData");
                            writer.WriteStartElement(reader.Prefix, reader.LocalName, reader.NamespaceURI);
                            writer.WriteAttributes(reader, true);

                            ReadToElement(reader, "StagingData");
                            writer.WriteStartElement(reader.Prefix, reader.LocalName, reader.NamespaceURI);
                            writer.WriteAttributes(reader, true);
                            wroteRoot = true;
                        }
                        ReadToElement(reader, "MachineStaging");
                        if (reader.NodeType == XmlNodeType.Element)
                        {
                            if (!wroteMachineStaging)
                            {
                                writer.WriteStartElement(reader.Prefix, reader.LocalName, reader.NamespaceURI);
                                writer.WriteAttributes(reader, true);
                                wroteMachineStaging = true;
                            }

                            bool wroteEventStartComment = false;
                            while (ReadToElement(reader, "Stage"))
                            {
                                if (!wroteEventStartComment)
                                {
                                    writer.WriteComment($"Begin Stage: {Path.GetFileName(xmlFilePath)} - {sessionStart} - Offset: {offset}");
                                    wroteEventStartComment = true;
                                }
                                writer.WriteStartElement(reader.Prefix, reader.LocalName, reader.NamespaceURI);
                                for (var k = 0; k < reader.AttributeCount; k++)
                                {
                                    reader.MoveToNextAttribute();
                                    if (reader.Name == "Start")
                                    {
                                        int.TryParse(reader.Value, out int start);
                                        start += offset;
                                        writer.WriteAttributeString(reader.Name, start.ToString());
                                    }
                                    else
                                    {
                                        writer.WriteAttributeString(reader.Name, reader.Value);
                                    }
                                }

                                reader.Read();
                                writer.WriteEndElement();//close the Event tag;
                            }
                            writer.WriteEndElement();//close the MachineStaging tag;
                        }

                    }
                }
                writer.WriteEndElement();   //close the StagingData tag;
                writer.WriteEndElement();   //close the ScoringData tag;
                writer.WriteEndElement();   //close the PatientStudy tag;
                writer.WriteEndDocument();


            }
        }

        private static void MergeTechNotes(
            string[] xmlFilePaths,
            string outputFilePath,
            Dictionary<string, List<DateTime>> sessions
        )
        {

            using (XmlWriter writer = CreateWriter(outputFilePath))
            {
                //writer.WriteStartDocument();
                //writer.WriteStartElement("Sessions"); // Root element for merged data

                bool wroteRoot = false;
                int outerElementCount = 0;
                bool wroteAnyTechNotes = false;
                var indent = $"   ";
                for (var i = 0; i < xmlFilePaths.Length; i++)
                //foreach (string xmlFilePath in xmlFilePaths)
                {
                    var xmlFilePath = xmlFilePaths[i];
                    bool wroteTechNotesResponse = false;
                    var sessionStart = sessions[xmlFilePath].First();

                    using (XmlReader reader = XmlReader.Create(xmlFilePath))
                    {
                        if (!wroteRoot)
                        {
                            ReadToElement(reader, "PatientStudy");
                            writer.WriteStartElement(reader.Prefix, reader.LocalName, reader.NamespaceURI);
                            writer.WriteAttributes(reader, true);

                            ReadToElement(reader, "TechNotes");
                            writer.WriteStartElement(reader.Prefix, reader.LocalName, reader.NamespaceURI);
                            writer.WriteAttributes(reader, true);
                            wroteRoot = true;

                            ReadToElement(reader, "TechNotesItems");
                            writer.WriteNode(reader, true);
                            reader.Read();
                        }
                        ReadToElement(reader, "TechNotesResponses");
                        if (reader.NodeType == XmlNodeType.Element)
                        {
                            if (!wroteAnyTechNotes)
                            {
                                writer.WriteStartElement(reader.Prefix, reader.LocalName, reader.NamespaceURI);
                                writer.WriteAttributes(reader, true);
                                wroteTechNotesResponse = true;
                                wroteAnyTechNotes = true;
                            }

                            bool wroteTechNotesComments = false;
                            while (ReadToElement(reader, "TechNotesResponse"))
                            {
                                if (!wroteTechNotesComments)
                                {
                                    writer.WriteComment($"Begin Stage: {Path.GetFileName(xmlFilePath)} - {sessionStart}");
                                    wroteTechNotesComments = true;
                                }
                                writer.WriteNode(reader, true);

                                reader.Read();

                            }
                        }

                    }
                }
                if (wroteAnyTechNotes)
                    writer.WriteEndElement();   //close the TechNotesResponses tag;
                writer.WriteEndElement();   //close the TechNotes tag;
                writer.WriteEndElement();   //close the PatientStudy tag;
                writer.WriteEndDocument();


            }
        }

        public class Session
        {
            public DateTime RecordingStart { get; set; }
            public int Duration { get; set; }
            public int LightsOff { get; set; }
            public int LightsOn { get; set; }
            public string XmlFilePath { get; set; }
            public List<Segment> Segments { get; set; } = new();
            public List<VideoSegment> VideoSegments { get; set; } = new();
            public string RmlFile { get; internal set; }
            public string CollectionFolder { get; internal set; }
            public string CollectionFolderPath { get; internal set; }
        }
        public class Segment
        {
            public DateTime StartTime { get; set; }
            public int Duration { get; set; }
        }
        public class VideoSegment : Segment
        {
            public string FileName { get; set; }
        }

        static bool ReadNextElement(XmlReader reader)
        {
            while (reader.Read())
            {
                if (reader.NodeType == XmlNodeType.Element)
                {
                    return true;
                }
            }
            return false;
        }
        static bool ReadToElement(XmlReader reader, string name)
        {
            while (ReadNextElement(reader))
            {
                if (reader.Name == name)
                    return true;
            }
            return false;
        }

        static DateTime ReadDate(XmlReader reader)
        {
            var value = reader.ReadElementContentAsString();
            DateTime parsed = DateTime.ParseExact(value, "yyyy-MM-ddTHH:mm:ss", CultureInfo.InvariantCulture);
            return parsed;
        }
        static int ReadInt(XmlReader reader)
        {
            var value = reader.ReadElementContentAsString();
            int parsed = int.Parse(value);
            return parsed;
        }
        private static void GetSessionDates(string[] xmlFilePaths, Dictionary<string, List<DateTime>> sessionDates, List<Session> sessions)
        {


            foreach (var file in xmlFilePaths)
            {
                var l = new List<DateTime>();

                sessionDates.Add(file, l);

                using (XmlReader reader = XmlReader.Create(file))
                {
                    var data = new Session();
                    sessions.Add(data);
                    data.XmlFilePath = file;
                    var fi = new FileInfo(file);
                    data.RmlFile = fi.Name;
                    data.CollectionFolder = fi.Directory.Name;
                    data.CollectionFolderPath = fi.Directory.FullName;

                    ReadToElement(reader, "Sessions");

                    while (ReadToElement(reader, nameof(Session)))
                    {
                        while (ReadNextElement(reader))
                        {
                            switch (reader.Name)
                            {
                                case nameof(Session.RecordingStart):
                                    data.RecordingStart = ReadDate(reader);
                                    l.Add(data.RecordingStart);
                                    break;

                                case nameof(Session.Duration):
                                    data.Duration = ReadInt(reader);
                                    break;

                                case nameof(Session.LightsOff):
                                    data.LightsOff = ReadInt(reader);
                                    break;
                                case nameof(Session.LightsOn):
                                    data.LightsOn = ReadInt(reader);
                                    break;
                                case nameof(Session.Segments):
                                    {
                                        while (ReadNextElement(reader))
                                        {
                                            if (reader.Name == nameof(Segment))
                                            {
                                                var segment = new Segment();
                                                data.Segments.Add(segment);

                                                ReadNextElement(reader);
                                                segment.StartTime = ReadDate(reader);
                                                ReadNextElement(reader);
                                                segment.Duration = ReadInt(reader);


                                            }
                                            else if (reader.Name == nameof(Session.VideoSegments))
                                            {
                                                while (ReadToElement(reader, nameof(VideoSegment)))
                                                {
                                                    var segment = new VideoSegment();
                                                    data.VideoSegments.Add(segment);

                                                    ReadNextElement(reader);
                                                    segment.StartTime = ReadDate(reader);
                                                    ReadNextElement(reader);
                                                    segment.Duration = ReadInt(reader);
                                                    ReadNextElement(reader);
                                                    segment.FileName = reader.ReadElementContentAsString();

                                                }
                                            }
                                            else
                                            {
                                                continue;
                                            }

                                        }
                                        break;
                                    }
                                default:
                                    break;
                            }
                        }
                    }
                }
            }
        }
    }
}
