﻿using EnsoLib;
using System.Net;

namespace EnsoClientCli
{
    internal class Program
    {

        static async Task Main(string[] args)
        {
            ApiAuthorization.Init();

            await TestApi();
            await TestClient();

        }
        static async Task TestApi()
        {
            var client = new WebClient();
            var url = "https://ensoapurl";

            client.Headers.Add("Authorization", ApiAuthorization.BearerToken);
            client.Headers.Add("Accept", "application/json");

            var result = client.DownloadString(url);

        }
        static async Task TestClient()
        {
            IClient eventClient = new Client();
            var res = eventClient.StudyGETAsync();
            var events = new Dictionary<string, Event>();
            var page = 1;
        }

    }
   
}
