using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Ethos.Model;
using Ethos.Model.Scheduling;
using Ethos.Workflows.Api;
using Ethos.Workflows.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class SchedulingConstraintController(DbContext dbContext)
    : EntityControllerBase<SchedulingConstraintDbo, CreateSchedulingConstraintDto, SchedulingConstraintDto, SchedulingConstraintQ>(dbContext)
{
    protected override SchedulingConstraintDto MapToDto(SchedulingConstraintDbo dbo)
    {
        return new SchedulingConstraintDto
        {
            Id = dbo.Id,
            Name = dbo.Name,
            Description = dbo.Description,
            Expression = dbo.Expression,
            IsHardConstraint = dbo.IsHardConstraint
        };
    }

    protected override SchedulingConstraintDbo CreateOrUpdateEntity(SchedulingConstraintDbo? entity, CreateSchedulingConstraintDto input,
        Guid? requiredId = null)
    {
        if (entity == null)
        {
            entity = new SchedulingConstraintDbo
            {
                Id = requiredId ?? Guid.NewGuid(),
                Name = input.Name,
                Description = input.Description,
                Expression = input.Expression,
                IsHardConstraint = input.IsHardConstraint,
                TopLevelVariables = input.Expression.GetTopLevelVars()
            };
        }
        else
        {
            entity.Name = input.Name;
            entity.Description = input.Description;
            entity.Expression = input.Expression;
            entity.TopLevelVariables = input.Expression.GetTopLevelVars();
            entity.IsHardConstraint = input.IsHardConstraint;
        }

        return entity;
    }
}