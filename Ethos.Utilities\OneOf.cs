using System.Runtime.InteropServices;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Ethos.Utilities;

[JsonConverter(typeof(OneOfConverterFactory))]
public struct OneOf<T1, T2>
{
    private byte _index;
    private object _item;
    
    public bool IsFirst => _index == 1;
    public bool IsSecond => _index == 2;
    public bool IsUndefined => _index == 0;
    
    // Getters
    public T1 Item1 => _index == 1 ? (T1) _item : throw new InvalidOperationException("Item1 is not set");
    public T2 Item2 => _index == 2 ? (T2) _item : throw new InvalidOperationException("Item2 is not set");

    public OneOf(T1 item1)
    {
        _index = 1;
        _item = item1;
    }

    public OneOf(T2 item2)
    {
        _index = 2;
        _item = item2;
    }
    
    public TResult Match<TResult>(Func<T1, TResult> f1, Func<T2, TResult> f2)
    {
        switch (_index)
        {
            case 1: return f1((T1) _item);
            case 2: return f2((T2) _item);
            default: throw new InvalidOperationException("OneOf must have one non-null value");
        }
    }

    public void Match(Action<T1> f1, Action<T2> f2)
    {
        switch (_index)
        {
            case 1: f1((T1) _item); break;
            case 2: f2((T2) _item); break;
        }
    }
}

[JsonConverter(typeof(OneOfConverterFactory))]
public class OneOf<T1, T2, T3>
{
    private int _index;
    private object _item;
    
    public bool IsFirst => _index == 1;
    public bool IsSecond => _index == 2;
    public bool IsThird => _index == 3;
    public bool IsUndefined => _index == 0;
    
    // Getters
    public T1 Item1 => _index == 1 ? (T1) _item : throw new InvalidOperationException("Item1 is not set");
    public T2 Item2 => _index == 2 ? (T2) _item : throw new InvalidOperationException("Item2 is not set");
    public T3 Item3 => _index == 3 ? (T3) _item : throw new InvalidOperationException("Item3 is not set");

    public OneOf(T1 item1)
    {
        _index = 1;
        _item = item1;
    }

    public OneOf(T2 item2)
    {
        _index = 2;
        _item = item2;
    }

    public OneOf(T3 item3)
    {
        _index = 3;
        _item = item3;
    }
    
    public TResult Match<TResult>(Func<T1, TResult> f1, Func<T2, TResult> f2, Func<T3, TResult> f3)
    {
        switch (_index)
        {
            case 1: return f1((T1) _item);
            case 2: return f2((T2) _item);
            case 3: return f3((T3) _item);
            default: throw new InvalidOperationException("OneOf must have one non-null value");
        }
    }

    public void Match(Action<T1> f1, Action<T2> f2, Action<T3> f3)
    {
        switch (_index)
        {
            case 1: f1((T1) _item); break;
            case 2: f2((T2) _item); break;
            case 3: f3((T3) _item); break;
        }
    }
}

public class OneOfConverterFactory : JsonConverterFactory
{
    public override bool CanConvert(Type typeToConvert)
    {
        if (!typeToConvert.IsGenericType)
            return false;

        Type genericTypeDef = typeToConvert.GetGenericTypeDefinition();
        return genericTypeDef == typeof(OneOf<,>)
               || genericTypeDef == typeof(OneOf<,,>);
    }

    public override JsonConverter CreateConverter(
        Type typeToConvert, 
        JsonSerializerOptions options)
    {
        Type[] typeArgs = typeToConvert.GetGenericArguments();
        if (typeArgs.Length == 2)
        {
            // closed generic for OneOf<T1,T2>
            Type converterType = typeof(OneOfConverter<,>).MakeGenericType(typeArgs);
            return (JsonConverter)Activator.CreateInstance(converterType);
        }
        else if (typeArgs.Length == 3)
        {
            // closed generic for OneOf<T1,T2,T3>
            Type converterType = typeof(OneOfConverter<,,>).MakeGenericType(typeArgs);
            return (JsonConverter)Activator.CreateInstance(converterType);
        }
        else
        {
            throw new NotSupportedException(
                $"Type {typeToConvert.FullName} is not supported by OneOfConverterFactory");
        }
    }
}

public class OneOfConverter<T1, T2> : JsonConverter<OneOf<T1, T2>>
{
    public override OneOf<T1, T2> Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        // Expect a JSON object with "index" and "value" properties.
        if (reader.TokenType != JsonTokenType.StartObject)
            throw new JsonException();

        int index = 0;
        object value = null;

        while (reader.Read())
        {
            if (reader.TokenType == JsonTokenType.EndObject)
                break;
                
            if (reader.TokenType != JsonTokenType.PropertyName)
                throw new JsonException();

            string propertyName = reader.GetString();
            reader.Read();

            if (propertyName == "index")
            {
                index = reader.GetInt32();
            }
            else if (propertyName == "value")
            {
                switch (index)
                {
                    case 1:
                        value = JsonSerializer.Deserialize<T1>(ref reader, options);
                        break;
                    case 2:
                        value = JsonSerializer.Deserialize<T2>(ref reader, options);
                        break;
                    default:
                        throw new JsonException("Invalid index value during deserialization");
                }
            }
        }

        return index switch
        {
            1 => new OneOf<T1, T2>((T1)value),
            2 => new OneOf<T1, T2>((T2)value),
            _ => throw new JsonException("No valid index found"),
        };
    }

    public override void Write(Utf8JsonWriter writer, OneOf<T1, T2> oneOf, JsonSerializerOptions options)
    {
        writer.WriteStartObject();

        if (oneOf.IsFirst)
        {
            writer.WriteNumber("index", 1);
            writer.WritePropertyName("value");
            JsonSerializer.Serialize(writer, oneOf.Item1, options);
        }
        else if (oneOf.IsSecond)
        {
            writer.WriteNumber("index", 2);
            writer.WritePropertyName("value");
            JsonSerializer.Serialize(writer, oneOf.Item2, options);
        }
        else
        {
            throw new JsonException("OneOf must have a non-null value");
        }

        writer.WriteEndObject();
    }
}

public class OneOfConverter<T1, T2, T3> : JsonConverter<OneOf<T1, T2, T3>>
{
    public override OneOf<T1, T2, T3> Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType != JsonTokenType.StartObject)
            throw new JsonException();

        int index = 0;
        object value = null;

        while (reader.Read())
        {
            if (reader.TokenType == JsonTokenType.EndObject)
                break;
                
            if (reader.TokenType != JsonTokenType.PropertyName)
                throw new JsonException();

            string propertyName = reader.GetString();
            reader.Read();

            if (propertyName == "index")
            {
                index = reader.GetInt32();
            }
            else if (propertyName == "value")
            {
                switch (index)
                {
                    case 1:
                        value = JsonSerializer.Deserialize<T1>(ref reader, options);
                        break;
                    case 2:
                        value = JsonSerializer.Deserialize<T2>(ref reader, options);
                        break;
                    case 3:
                        value = JsonSerializer.Deserialize<T3>(ref reader, options);
                        break;
                    default:
                        throw new JsonException("Invalid index value during deserialization");
                }
            }
        }

        return index switch
        {
            1 => new OneOf<T1, T2, T3>((T1)value),
            2 => new OneOf<T1, T2, T3>((T2)value),
            3 => new OneOf<T1, T2, T3>((T3)value),
            _ => throw new JsonException("No valid index found"),
        };
    }

    public override void Write(Utf8JsonWriter writer, OneOf<T1, T2, T3> oneOf, JsonSerializerOptions options)
    {
        writer.WriteStartObject();

        if (oneOf.IsFirst)
        {
            writer.WriteNumber("index", 1);
            writer.WritePropertyName("value");
            JsonSerializer.Serialize(writer, oneOf.Item1, options);
        }
        else if (oneOf.IsSecond)
        {
            writer.WriteNumber("index", 2);
            writer.WritePropertyName("value");
            JsonSerializer.Serialize(writer, oneOf.Item2, options);
        }
        else if (oneOf.IsThird)
        {
            writer.WriteNumber("index", 3);
            writer.WritePropertyName("value");
            JsonSerializer.Serialize(writer, oneOf.Item3, options);
        }
        else
        {
            throw new JsonException("OneOf must have a non-null value");
        }

        writer.WriteEndObject();
    }
}
