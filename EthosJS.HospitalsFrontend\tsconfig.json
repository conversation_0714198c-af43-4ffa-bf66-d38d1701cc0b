{"compilerOptions": {"noImplicitAny": true, "outDir": "./.dist/", "moduleResolution": "node", "skipLibCheck": true, "removeComments": true, "downlevelIteration": true, "strict": false, "strictPropertyInitialization": false, "allowJs": true, "jsx": "react-jsx", "sourceMap": true, "declaration": false, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "noImplicitReturns": true, "noUnusedLocals": false, "noUnusedParameters": false, "module": "esnext", "target": "es6", "lib": ["dom", "dom.iterable", "scripthost", "es2015", "es2016", "es2017", "esnext"], "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "baseUrl": "src", "typeRoots": ["./types", "./node_modules/@types"], "noFallthroughCasesInSwitch": true}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "test", "**/*spec.ts"]}