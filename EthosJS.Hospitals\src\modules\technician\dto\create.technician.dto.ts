import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsInt, IsOptional, IsPositive, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { TechnicianStandardScheduleDto } from '@app/modules/technician/dto/technician.standard.schedule.dto';
import { CreateTechnicianCredentialDto } from '@app/modules/technician/dto/create.technician.credential.dto';

export class CreateTechnicianDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsInt()
  @IsPositive()
  clinicId: number;

  @ApiProperty()
  @IsInt()
  @IsPositive()
  capacity: number;

  @ApiPropertyOptional({ isArray: true, type: CreateTechnicianCredentialDto })
  @IsOptional()
  @IsArray()
  @ValidateNested()
  @Type(() => CreateTechnicianCredentialDto)
  credentials?: CreateTechnicianCredentialDto[];

  @ApiPropertyOptional()
  @IsOptional()
  @ValidateNested()
  @Type(() => TechnicianStandardScheduleDto)
  standardSchedule: TechnicianStandardScheduleDto;
}
