﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Ethos.TenantConfig.Migrations
{
    /// <inheritdoc />
    public partial class DynamicMigration_638785330321879316 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_TenantConfig",
                table: "TenantConfig");

            migrationBuilder.DropIndex(
                name: "IX_TenantConfig_Name_TenantId",
                table: "TenantConfig");

            migrationBuilder.AddPrimaryKey(
                name: "PK_TenantConfig",
                table: "TenantConfig",
                columns: new[] { "Name", "TenantId" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_TenantConfig",
                table: "TenantConfig");

            migrationBuilder.AddPrimaryKey(
                name: "PK_TenantConfig",
                table: "TenantConfig",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_TenantConfig_Name_TenantId",
                table: "TenantConfig",
                columns: new[] { "Name", "TenantId" },
                unique: true);
        }
    }
}
