import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CredentialRepository } from '@app/modules/credential/credential.repository';
import { CredentialService } from '@app/modules/credential/credential.service';
import { CredentialController } from '@app/modules/credential/credential.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([CredentialRepository]),
  ],
  providers: [CredentialService],
  controllers: [CredentialController],
  exports: [CredentialService],
})
export class CredentialModule {}
