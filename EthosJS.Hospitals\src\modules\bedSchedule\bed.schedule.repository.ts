import moment from 'moment/moment';
import { BadRequestException } from '@nestjs/common';
import { EntityRepository, In } from 'typeorm';
import { IListResult } from '@app/common/types';
import { BedScheduleEntity } from '@app/modules/bedSchedule/bed.schedule.entity';
import { BedScheduleFiltersDto } from '@app/modules/bedSchedule/dto/bed.schedule.filters.dto';
import { BaseRepository } from '@app/common/base.repository';
import { CreateBedScheduleDto } from '@app/modules/bedSchedule/dto/create.bed.schedule.dto';
import { BedScheduleCollectionDto } from '@app/modules/bedSchedule/dto/bed.schedule.collection.dto';
import { IBedScheduleEquipment } from '@app/modules/bedSchedule/types';

@EntityRepository(BedScheduleEntity)
export class BedScheduleRepository extends BaseRepository<BedScheduleEntity> {
  collectionDto = BedScheduleCollectionDto;

  async list({ facilityIds, ...filters }: BedScheduleFiltersDto): Promise<IListResult<BedScheduleEntity>> {
    const where: any = { ...filters };

    if (facilityIds) {
      where.facilityId = In(facilityIds);
    }

    return super.list(where, ['facility']);
  }

  async addEquipmentToSchedules(facilityId: number, equipmentsToAdd: Record<number, IBedScheduleEquipment>): Promise<void> {
    const today = moment().startOf('day');

    await this.query('UPDATE bed_schedules SET equipments = equipments || $1 WHERE date >= $2 AND facility_id = $3 AND deleted_at IS NULL;', [equipmentsToAdd, today, facilityId]);
  }

  async checkHasSchedules({ facilityId, dayShiftBeds, nightShiftBeds, date }: Omit<CreateBedScheduleDto, 'equipments'>): Promise<void> {
    const [{ count: dayCount }] = await this.query('SELECT COUNT(*) as count FROM schedules WHERE schedules.date = $1 AND facility_id = $2 AND shift = \'day\' AND schedules.deleted_at IS NULL', [date, facilityId]);

    if (Number(dayCount) > dayShiftBeds) {
      throw new BadRequestException(`Too many schedules at ${date} - ${dayCount}. Increase dayShiftBeds ${dayShiftBeds}`);
    }

    const [{ count: nightCount }] = await this.query('SELECT COUNT(*) as count FROM schedules WHERE schedules.date = $1 AND facility_id = $2 AND shift = \'night\' AND schedules.deleted_at IS NULL', [date, facilityId]);

    if (Number(nightCount) > nightShiftBeds) {
      throw new BadRequestException(`Too many schedules at ${date} - ${nightCount}. Increase nightShiftBeds ${nightShiftBeds}`);
    }
  }

  async getQueryRunner() {
    return this.manager.connection.createQueryRunner();
  }
}
