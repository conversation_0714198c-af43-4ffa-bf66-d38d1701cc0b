using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.IdentityModel.Tokens;

namespace Ethos.Auth;

public interface IEthosLocalJWTBuilder
{
    string GenerateJwtToken(string username);
    
    public class LocalJwtNotEnabledException : InvalidOperationException
    {
        public LocalJwtNotEnabledException() : base("Local auth is not enabled.") { }
    }
}

public class EthosLocalJWTBuilder(IEthosAuthConfigProvider configProvider) : IEthosLocalJWTBuilder
{
    public string GenerateJwtToken(string username)
    {
        if (!configProvider.IsLocalAuthEnabled)
            throw new IEthosLocalJWTBuilder.LocalJwtNotEnabledException();
        
        var key = Encoding.UTF8.GetBytes(configProvider.LocalJwtKey!);
        var signingCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256);

        var claims = new[]
        {
            new Claim(JwtRegisteredClaimNames.Sub, username),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
        };

        var token = new JwtSecurityToken(
            issuer: configProvider.LocalJwtIssuer,
            audience: configProvider.LocalJwtAudience,
            claims: claims,
            expires: DateTime.UtcNow.AddHours(1),
            signingCredentials: signingCredentials
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }
}