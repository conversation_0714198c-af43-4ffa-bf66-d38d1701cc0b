using System.Collections.Concurrent;
using System.Diagnostics;
using System.Linq.Expressions;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;
using Ethos.ReferenceData.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class StringEnumConverter<T> : JsonConverter<T> where T : struct, Enum 
{
    public override T Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType != JsonTokenType.String)
            throw new JsonException("Expected string token for EntityType");

        var value = reader.GetString();
        if (value is null || !Enum.TryParse<T>(value, true, out var result))
            throw new JsonException($"Invalid EntityType value: {value}");

        return result;
    }

    public override void Write(Utf8JsonWriter writer, T value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value.ToString());
    }
}

[JsonConverter(typeof(StringEnumConverter<EntityType>))]
public enum EntityType {
    EditRecord,
    FileMetadata,
    WorkflowDraftTransition,
    WorkflowEntityLink,
    WorkflowInstance,
    WorkflowTransition,
    
    Draft,
    Note,
    Address,
    Insurance,
    InsuranceHolderData,
    PhoneNumberWithUseData,
    
    Patient,
    PatientGuardian,
    PatientAppointment,
    PatientAppointmentConfirmation,
    
    PersonalContactDetail,
    PersonalEmail,
    PersonalAddress,
    PersonalPhoneNumber,
    PersonalEmergencyContact,
    
    OrganizationContactDetail,
    OrganizationEmail,
    OrganizationAddress,
    OrganizationPhoneNumber,
    
    Order,
    Study,
    
    Provider,
    CareLocation,
    Room,
    Equipment,
    
    Physician,
    PhysicianCareLocationRelation,
    
    Technician,
    TechnicianRole,
    TechnicianShiftPreference,
    TechnicianCareLocationRelation,
    TechnicianAppointment,
    
    InsuranceVerification,
    SchedulingConstraint
}

public interface IEntity 
{
    public const string DefaultSchema = "ethos";
    
    public static EntityType GetEntityType<TEntity>()
    {
        return GetEntityType(typeof(TEntity));
    }
    
    public static EntityType GetEntityType(Type entityType)
    {
        var entityName = entityType.Name;
        if (!entityType.IsAssignableTo(typeof(IEntity)))
        {
            throw new ArgumentException($"Type {entityType.Name} is not an IEntity type.");
        }
        if (!entityName.EndsWith("Dbo"))
        {
            throw new ArgumentException($"Type {entityType.Name} does not end with 'Dbo'.");
        }
        entityName = entityName[..^"Dbo".Length];
        if (Enum.TryParse<EntityType>(entityName, out var entityTypeValue))
        {
            return entityTypeValue;
        }
        throw new ArgumentException($"Type {entityType.Name} is not a valid entity type.");
    }
}

public interface IEntity<TSelf> : IEntity where TSelf : class, IEntity<TSelf>
{
    public static abstract void Register(ModelBuilder modelBuilder);
    public static abstract void Register(EntityTypeBuilder<TSelf> entity);
}

[JsonConverter(typeof(StringEnumConverter<EditType>))]
public enum EditType { Create=1, Update=2, Delete=3 }

[JsonConverter(typeof(StringEnumConverter<EntityStatus>))]
public enum EntityStatus { Valid=1, Archived=2, Deleted=3 }

public class EditRecordDbo : IEntity, IEntity<EditRecordDbo>
{
    public long Id { get; set; }
    public required string? UserId { get; set; }             // Who made the change (null if system)
    public required DateTime Timestamp { get; set; }         // When the change happened
    public required EditType EditType { get; set; }          // Create, Update, Delete
    public required string EntityName { get; set; } = null!; // Which entity/table was changed
    public required string? PrimaryKey { get; set; }         // PK value of the entity as string
    public required Dictionary<string, object?> OldValues { get; set; } = new();
    public required Dictionary<string, object?> NewValues { get; set; } = new();
    public required List<string> ChangedColumns { get; set; } = new();
    
    public static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<EditRecordDbo>(Register);

    public static void Register(EntityTypeBuilder<EditRecordDbo> entity)
    {
        entity.ToTable("EditRecord");
        
        entity.HasKey(e => e.Id);
        entity.Property(e => e.Id)
            .HasColumnName("Id")
            .ValueGeneratedOnAdd()
            .IsRequired();
        
        entity.HasIndex(e => e.EntityName);
        entity.Property(e => e.EntityName).HasMaxLength(100).IsRequired();
        entity.Property(e => e.PrimaryKey).HasMaxLength(100);
        entity.Property(e => e.EditType).HasConversion<string>();
        entity.Property(e => e.ChangedColumns).HasConversion(
                // Serialize: Handle null JsonNode
                v => JsonSerializer.Serialize(v, JsonSerializerOptions.Default),
                // Deserialize: Handle null/empty string from DB
                v => JsonSerializer.Deserialize<List<string>>(v!, JsonSerializerOptions.Default)!
            )
            
            .HasColumnType("jsonb")
            .IsRequired(false)
            .Metadata.SetValueComparer(new Microsoft.EntityFrameworkCore.ChangeTracking.ValueComparer<List<string>>(
                            (c1, c2) => (c1 == null && c2 == null) || (c1 != null && c2 != null && c1.SequenceEqual(c2)), // How to check equality (order matters here)
                            c => c == null ? 0 : c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())), // How to generate a hash code
                            c => c == null ? null : c.ToList() // How to create a snapshot/deep copy
                        ));
        entity.Property(e => e.OldValues).HasConversion(
                // Serialize: Handle null JsonNode
                v => JsonSerializer.Serialize(v, JsonSerializerOptions.Default),
                // Deserialize: Handle null/empty string from DB
                v => JsonSerializer.Deserialize<Dictionary<string, object?>>(v!, JsonSerializerOptions.Default)!
            )
            .HasColumnType("jsonb")
            .IsRequired(false);
        entity.Property(e => e.NewValues).HasConversion(
                // Serialize: Handle null JsonNode
                v => JsonSerializer.Serialize(v, JsonSerializerOptions.Default),
                // Deserialize: Handle null/empty string from DB
                v => JsonSerializer.Deserialize<Dictionary<string, object?>>(v!, JsonSerializerOptions.Default)!
            )
            .HasColumnType("jsonb")
            .IsRequired(false);
    }
}

public interface IAuditableEntity
{
    public Guid Id { get; init; }
    public EntityStatus State { get; set; } // Default to Valid
    
    public long CreateEventId { get; set; } // references EditRecord.Id
    public long? UpdateEventId { get; set; } // references EditRecord.Id
    
    public EditRecordDbo CreateEvent { get; set; }
    public EditRecordDbo? UpdateEvent { get; set; }
}

public abstract class IAuditableEntity<TEntity> : IEntity<TEntity>, IAuditableEntity where TEntity : IAuditableEntity<TEntity>
{
    public Guid Id { get; init; }
    public EntityStatus State { get; set; } = EntityStatus.Valid; // Default to Valid
    
    public long CreateEventId { get; set; } // references EditRecord.Id
    public virtual EditRecordDbo CreateEvent { get; set; }
    
    public long? UpdateEventId { get; set; } // references EditRecord.Id
    public virtual EditRecordDbo? UpdateEvent { get; set; }
    
    public static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<TEntity>(Register);
    
    public static void Register(EntityTypeBuilder<TEntity> entity)
    {
        entity.ToTable(typeof(TEntity).Name);
        
        entity.HasKey(e => e.Id);
        entity.Property(e => e.Id)
            .HasColumnName("Id")
            .ValueGeneratedOnAdd()
            .IsRequired();
        
        entity.Property(e => e.State)
            .HasConversion<string>()
            .IsRequired();
        
        entity.HasOne<EditRecordDbo>(e => e.CreateEvent)
            .WithMany()
            .HasForeignKey(e => e.CreateEventId)
            .HasPrincipalKey(e => e.Id);
        
        entity.HasOne<EditRecordDbo>(e => e.UpdateEvent)
            .WithMany()
            .HasForeignKey(e => e.UpdateEventId)
            .HasPrincipalKey(e => e.Id);
    }
}

/// <summary>
/// 
/// </summary>
/// <typeparam name="TSelf"></typeparam>
public abstract class IOwnedEntity<TSelf> : IAuditableEntity<TSelf> where TSelf : IOwnedEntity<TSelf>;

/// <summary>
/// 
/// </summary>
public interface IReferenceDataEntity
{
    long Id { get; set; }
    string Name { get; set; }
    List<IReferenceDataEntity> GetDefaults();

}

/// <summary>
/// 
/// </summary>
/// <typeparam name="TSelf"></typeparam>
public abstract class ReferenceDataEntityBase<TSelf> : IReferenceDataEntity where TSelf : ReferenceDataEntityBase<TSelf>, new()
{
    const string __id = nameof(__id);

    /// <summary>
    /// 
    /// </summary>
    [ReferenceDataIgnore]
    [Newtonsoft.Json.JsonProperty(__id)]
    [System.Text.Json.Serialization.JsonPropertyName(__id)]
    public long Id { get; set; }

    public virtual string Name { get; set; } = null!;

    /// <summary>
    /// 
    /// </summary>
    public static void Register()
    {
        var values = Activator.CreateInstance<TSelf>().GetDefaults();
        if (values.Count > 0)
            ReferenceDataRepository.Register(typeof(TSelf), [.. values]);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="id"></param>
    /// <param name="service"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public static async Task<bool> ValidateId(IReferenceDataClient service, params long[] ids)
    {
        var setDetails = typeof(TSelf).GetReferenceDataSetDetail();
        if (setDetails is null || string.IsNullOrEmpty(setDetails.Name))
            throw new Exception($"Type {typeof(TSelf).Name} is not defined as reference data.");
        setDetails.Version ??= string.Empty;
        return await service.ValidateSetValues([.. ids.Select(i => new ReferenceDataValidationDto()
        {
            SetName = setDetails.Name,
            Version = setDetails.Version,
            Value = i,
            ValidationType = ReferenceDataValidationType.Id
        })]);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="value"></param>
    /// <param name="service"></param>
    /// <param name="propertyName"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public static async Task<bool> Validate(IReferenceDataClient service, object value, string? propertyName = null)
    {
        var setDetails = typeof(TSelf).GetReferenceDataSetDetail();
        if (setDetails is null || string.IsNullOrEmpty(setDetails.Name))
            throw new Exception($"Type {typeof(TSelf).Name} is not defined as reference data.");
        setDetails.Version ??= string.Empty;
        return await service.ValidateSetValues(new ReferenceDataValidationDto()
        {
            SetName = setDetails.Name,
            Version = setDetails.Version,
            Value = value,
            PropertyName = propertyName,
            ValidationType = !string.IsNullOrEmpty(propertyName) ? ReferenceDataValidationType.Value : ReferenceDataValidationType.Key,
        });
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="value"></param>
    /// <param name="service"></param>
    /// <param name="propertyName"></param>
    /// <returns></returns>
    public static async Task<bool> TryValidate(IReferenceDataClient service, object value, string? propertyName = null)
    {
        try
        {
            return await Validate(service, value, propertyName);
        }
        catch
        {
            return false;
        }
    }


    /// <summary>
    /// 
    /// </summary>
    /// <param name="id"></param>
    /// <param name="service"></param>
    /// <returns></returns>
    public static async Task<bool> TryValidateId(IReferenceDataClient service, params long[] ids)
    {
        try
        {
            return await ValidateId(service, ids);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="service"></param>
    /// <returns></returns>
    public static async Task<List<TSelf>> FromService(IReferenceDataClient service)
    {
        var setDetails = typeof(TSelf).GetReferenceDataSetDetail();
        if (setDetails is null || string.IsNullOrEmpty(setDetails.Name))
            throw new Exception($"Type {typeof(TSelf).Name} is not defined as reference data.");
        setDetails.Version ??= string.Empty;
        var setValues = await service.GetSetValues<TSelf>(setDetails.Name, setDetails.Version);
        return setValues.Select(x => x.Values).ToList();
    }

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    List<IReferenceDataEntity> IReferenceDataEntity.GetDefaults()
    {
        return GetDefaults().Cast<IReferenceDataEntity>().ToList();
    }

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    public virtual List<TSelf> GetDefaults()
    {
        return [];
    }
}

public static class ReferenceDataRepository
{
    public static ConcurrentDictionary<Type, object[]> Defaults { get; } = [];

    public static void Unregister<T>()
    {
        Unregister(typeof(T));
    }

    public static void Unregister(Type type)
    {
        object[]? existing;
        do
        {
            Defaults.TryRemove(type, out existing);
        }
        while (existing is not null);
    }

    public static bool IsRegistered(Type type)
    {
        return Defaults.ContainsKey(type);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="type"></param>
    /// <param name="values"></param>
    public static void Register<T>(params T[] values)
    {
        AddOrUpdateType(typeof(T), values);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public static void Register<T>()
    {
        Register(typeof(T));
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="type"></param>
    public static void Register(Type type)
    {
        if (!IsRegistered(type))
        {
            var typeInstance = Activator.CreateInstance(type);
            if (typeInstance is IReferenceDataEntity refData)
            {
                var defs = refData.GetDefaults();
                if (defs.Count > 0)
                    AddOrUpdateType(type, [.. defs]);
            }
        }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="type"></param>
    /// <param name="values"></param>
    public static void Register(Type type, params object[] values)
    {
        AddOrUpdateType(type, [.. values]);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="type"></param>
    /// <param name="typeValues"></param>
    /// <returns></returns>
    static bool AddOrUpdateType(Type type, params object[] typeValues)
    {
        if (typeValues.Length == 0)
            return false;

        if (Defaults.TryGetValue(type, out var existingValues))
        {
            var newValues = existingValues.Union(typeValues).ToArray();
            return Defaults.TryUpdate(type, newValues, existingValues);
        }
        else
        {
            return Defaults.TryAdd(type, typeValues);
        }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="httpClient"></param>
    public static async Task Bootstrap(IReferenceDataClient client)
    {
        if (Defaults.IsEmpty)
            throw new Exception("No reference data to bootstrap. Call the `Register` method to register data sets.");

        foreach (var kvp in Defaults)
        {
            try
            {
                if (kvp.Value.Length > 0)
                    await client.RemoveAndCreateSet(kvp.Key, kvp.Value);
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine(ex.Message);
                Debug.WriteLine(ex.Message);
            }
        }
    }
}

// Attribute to specify default values for reference data entities
[AttributeUsage(AttributeTargets.Class, Inherited = false)]
public class DefaultReferenceDataAttribute : Attribute
{
    readonly List<Dictionary<string, object?>> values = [];

    public List<Dictionary<string, object?>> Values { get => values; }

    public DefaultReferenceDataAttribute(string[] values)
    {
        foreach (var val in values)
            this.values.Add(new() { { "name", val } });
    }
}


//public abstract class ReferenceDataEntityBase<TSelf> : IAuditableEntity<TSelf> where TSelf : ReferenceDataEntityBase<TSelf>
//{
//    public required string Name { get; set; }
//    public new static void Register(ModelBuilder modelBuilder) => modelBuilder.Entity<TSelf>(Register);
    
//    public new static void Register(EntityTypeBuilder<TSelf> entity)
//    {
//        IAuditableEntity<TSelf>.Register(entity);
//        entity.Property(e => e.Name).HasMaxLength(200).IsRequired();
//        entity.HasIndex(e => e.Name)
//            .HasDatabaseName($"IX_{typeof(TSelf).Name}_Name");
//    }
//}

//// Attribute to specify default values for reference data entities
//[AttributeUsage(AttributeTargets.Class, Inherited = false)]
//public class DefaultReferenceDataAttribute : Attribute
//{
//    public string[]? Values { get; }
//    public DefaultReferenceDataAttribute(string[] values)
//    {
//        Values = values;
//    }
//    public DefaultReferenceDataAttribute(Dictionary<string, Dictionary<string, string>> values)
//    {
//    }
//}

public interface IPrimitiveQuery
{
    public Expression BuildPredicateBody(ParameterExpression self);
}

[System.Text.Json.Serialization.JsonConverter(typeof(QueryDtoConverterFactory))]
public abstract record QueryDto<TPrimitive> where TPrimitive : IPrimitiveQuery
{
    // Private ctor ensures we are still controlling the hierarchy
    private QueryDto() { }

    public sealed record All : QueryDto<TPrimitive>;
    public sealed record Literal(TPrimitive Value) : QueryDto<TPrimitive>;
    public sealed record Not(QueryDto<TPrimitive> Expr) : QueryDto<TPrimitive>;
    public sealed record And(List<QueryDto<TPrimitive>> Exprs) : QueryDto<TPrimitive>;
    public sealed record Or(List<QueryDto<TPrimitive>> Exprs) : QueryDto<TPrimitive>;
    
    public bool Any(Func<TPrimitive, bool> predicate) 
    {
        return this switch
        {
            Literal lit => predicate(lit.Value),
            Not not => not.Expr.Any(predicate),
            And and => and.Exprs.Any(expr => expr.Any(predicate)),
            Or or => or.Exprs.Any(expr => expr.Any(predicate)),
            All => true,
            _ => throw new NotSupportedException($"Unsupported query type: {this.GetType().Name}")
        };
    }
    
    // This method is fully recursive. It converts our Query tree into an Expression.
    public Expression BuildPredicateBody(ParameterExpression self, Func<TPrimitive, ParameterExpression, Expression> primBuilder)
    {
        return this switch
        {
            // For literal queries, we switch on the actual type of PatientQ.
            Literal lit => primBuilder(lit.Value, self),
            // For a NOT query, simply negate the sub-expression:
            Not not => Expression.Not(not.Expr.BuildPredicateBody(self, primBuilder)),
            // For an AND query, combine the left and right expressions with &&:
            And and => and.Exprs.Any() 
                ? and.Exprs.Select(expr => expr.BuildPredicateBody(self, primBuilder))
                    .Aggregate(Expression.AndAlso)
                : Expression.Constant(true),
            // For an OR query, combine the left and right expressions with ||:
            Or or => or.Exprs.Any() 
                ? or.Exprs.Select(expr => expr.BuildPredicateBody(self, primBuilder))
                    .Aggregate(Expression.OrElse)
                : Expression.Constant(false),
            All => Expression.Constant(true),
            _ => throw new NotSupportedException($"Unsupported query type: {this.GetType().Name}")
        };
    }
    
    public Expression<Func<TEntity, bool>> BuildPredicate<TEntity>()
    {
        // Create the parameter "p" which represents a PatientModel in the lambda
        var parameter = Expression.Parameter(typeof(TEntity), "p");
        // Recursively build the expression body for the query:
        var body = this.BuildPredicateBody(parameter, (q, s) => q.BuildPredicateBody(s));
        return Expression.Lambda<Func<TEntity, bool>>(body, parameter);
    }
}

public class QueryDtoConverterFactory : JsonConverterFactory
{
    public override bool CanConvert(Type typeToConvert)
    {
        if (!typeToConvert.IsGenericType)
            return false;
        Type genericTypeDef = typeToConvert.GetGenericTypeDefinition();
        return genericTypeDef == typeof(QueryDto<>);
    }

    public override JsonConverter CreateConverter(
        Type typeToConvert, 
        JsonSerializerOptions options)
    {
        Type[] typeArgs = typeToConvert.GetGenericArguments();
        if (typeArgs.Length == 1)
        {
            // closed generic for OneOf<T1,T2>
            Type converterType = typeof(QueryDtoConverter<>).MakeGenericType(typeArgs);
            return (JsonConverter)Activator.CreateInstance(converterType)!;
        }

        throw new NotSupportedException(
            $"Type {typeToConvert.FullName} is not supported by OneOfConverterFactory");
    }
}

public class QueryDtoConverter<TPrimitive> : JsonConverter<QueryDto<TPrimitive>> where TPrimitive : IPrimitiveQuery
{
    // $type
    // value, expr, exprs
    
    public override void Write(Utf8JsonWriter writer, QueryDto<TPrimitive> query, JsonSerializerOptions options)
    {
        writer.WriteStartObject();

        switch(query) {
            case QueryDto<TPrimitive>.All all:
                writer.WriteString("$type", "All");
                break;
            case QueryDto<TPrimitive>.Literal literal:
                writer.WriteString("$type", "Literal");
                writer.WritePropertyName("value");
                JsonSerializer.Serialize(writer, literal.Value, options);
                break;
            case QueryDto<TPrimitive>.Not not:
                writer.WriteString("$type", "Not");
                writer.WritePropertyName("expr");
                JsonSerializer.Serialize(writer, not.Expr, options);
                break;
            case QueryDto<TPrimitive>.And and:
                writer.WriteString("$type", "And");
                writer.WritePropertyName("exprs");
                JsonSerializer.Serialize(writer, and.Exprs, options);
                break;
            case QueryDto<TPrimitive>.Or or:
                writer.WriteString("$type", "Or");
                writer.WritePropertyName("exprs");
                JsonSerializer.Serialize(writer, or.Exprs, options);
                break;
            default:
                throw new NotSupportedException($"Unsupported query type: {query.GetType().Name}");
        }

        writer.WriteEndObject();
    }
    
    public override QueryDto<TPrimitive> Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        // Expect a JSON object with "index" and "value" properties.
        if (reader.TokenType != JsonTokenType.StartObject)
            throw new JsonException();

        string? typeName = null;
        object? data = null;

        while (reader.Read())
        {
            if (reader.TokenType == JsonTokenType.EndObject)
                break;
                
            if (reader.TokenType != JsonTokenType.PropertyName)
                throw new JsonException();

            string? propertyName = reader.GetString();
            reader.Read();

            switch (propertyName)
            {
                case "$type":
                    typeName = reader.GetString();
                    break;
                case "value" or "Value":
                    data = JsonSerializer.Deserialize<TPrimitive>(ref reader, options);
                    break;
                case "exprs" or "Exprs":
                    data = JsonSerializer.Deserialize<List<QueryDto<TPrimitive>>>(ref reader, options);
                    break;
                case "expr" or "Expr":
                    data = JsonSerializer.Deserialize<QueryDto<TPrimitive>>(ref reader, options);
                    break;
                default:
                    throw new JsonException($"Unexpected property: {propertyName}");
            }
        }

        switch(typeName) {
            case "all" or "All":
                return new QueryDto<TPrimitive>.All();
            case "literal" or "Literal":
                return new QueryDto<TPrimitive>.Literal((TPrimitive)data!);
            case "not" or "Not":
                return new QueryDto<TPrimitive>.Not((QueryDto<TPrimitive>)data!);
            case "and" or "And":
                return new QueryDto<TPrimitive>.And((List<QueryDto<TPrimitive>>)data!);
            case "or" or "Or":
                return new QueryDto<TPrimitive>.Or((List<QueryDto<TPrimitive>>)data!);
            default:
                throw new JsonException($"Unsupported query type: {typeName}");
        }
    }
}
