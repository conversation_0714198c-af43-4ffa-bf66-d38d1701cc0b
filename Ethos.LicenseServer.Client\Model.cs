﻿
using System.Diagnostics;
using System.Text.Json;

namespace Ethos.LicenseServer.Client
{
    /// <summary>
    /// 
    /// </summary>
    public interface ILicenseServerEntity
    {
        string? Href { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public abstract class LicenseServerEntity : ILicenseServerEntity
    {
        public string? Href { get; set; }
    }

    public class LicenseServerUser : LicenseServerEntity
    {
        public Guid Id { get; set; }
    }

    public class LicenseServerTenant : LicenseServerEntity
    {
        public Guid Id { get; set; }
        public LicenseServerChangeLog? ChangeLog { get; set; }
    }

    /// <summary>
    /// Represents a user assigned to a specific tenant/account.
    /// </summary>
    public class LicenseServerUserAccount : LicenseServerEntity, ILicenseServerEntity
    {
        public Guid Id { get; set; }
        public string State { get; set; } = null!;
        public LicenseServerTenant? Account { get; set; }
        public LicenseServerUser? User { get; set; }
        public LicenseServerChangeLog? ChangeLog { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class LicenseServerChangeLog
    {
        public DateTimeOffset? LastUpdatedDateTime { get; set; }
        public DateTimeOffset? CreatedDateTime { get; set; }
    }

    /// <summary>
    /// Represents a user from an external directory or Azure AD B2C.
    /// </summary>
    public class LicenseServerPreTokenIssuanceResponse
    {
        readonly static JsonSerializerOptions jsonSerializerOptions = new() { AllowTrailingCommas = true, PropertyNameCaseInsensitive = true };
        public string Version { get; set; } = null!;
        public string Action { get; set; } = null!;

        [System.Text.Json.Serialization.JsonPropertyName("extension_products")]
        public string? ExtensionProducts { get; set; }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public List<ProductsExtension> GetProductsExtension()
        {
            if (string.IsNullOrEmpty(ExtensionProducts?.Trim()))
                return [];

            try
            {
                return JsonSerializer.Deserialize<List<ProductsExtension>>(ExtensionProducts, jsonSerializerOptions) ?? [];
            }
            catch (Exception ex)
            {
                Debug.WriteLine(ex);
                return [];
            }
        }
    }

    public class ProductsExtension
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = null!;
        public Guid AccountId { get; set; }
        public Guid LicenseId { get; set; }
        public IList<string> Permissions { get; set; } = [];
        public IList<string> Features { get; set; } = [];
    }

    /// <summary>
    /// 
    /// </summary>
    public class LicenseServerLicense : LicenseServerEntity, ILicenseServerEntity
    {
        public Guid Id { get; set; }
        public string Type { get; set; } = "LICENSE";
        public string State { get; set; } = null!;
        public long StartDate { get; set; }
        public long ExpirationDate { get; set; }
        public LicenseServerTenant Account { get; set; } = null!;
        public LicenseServerChangeLog? ChangeLog { get; set; }
    }

    /// <summary>
    /// Represents the assignment of a product feature to a specific license in a tenant.
    /// </summary>
    public class LicenseServerFeature : LicenseServerEntity, ILicenseServerEntity
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = null!;
        public string? Description { get; set; }
        public string State { get; set; } = null!;
        public LicenseServerProduct Product { get; set; } = null!;
        public IList<LicenseServerEligibilityRule> EligibilityRules { get; set; } = [];
        public LicenseServerChangeLog? ChangeLog { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class LicenseServerEligibilityRule : LicenseServerEntity, ILicenseServerEntity
    {
        public virtual string Type { get; set; } = null!;
        public string? ErrorMessage { get; set; }
        public LicenseServerEligibilityPredefinedConstraint? PredefinedConstraint { get; set; }
        public LicenseServerEligibilityCustomConstraint? CustomConstraint { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class LicenseServerEligibilityConstraint : LicenseServerEntity, ILicenseServerEntity
    {
    }

    /// <summary>
    /// 
    /// </summary>
    public class LicenseServerEligibilityPredefinedConstraint : LicenseServerEligibilityConstraint, ILicenseServerEntity
    {
        public string Name { get; set; } = null!;
        public object Value { get; set; } = null!;
    }

    /// <summary>
    /// 
    /// </summary>
    public class LicenseServerEligibilityCustomConstraint : LicenseServerEligibilityConstraint, ILicenseServerEntity
    {
        public string Expression { get; set; } = null!;
    }

    /// <summary>
    /// Represents the assignment of a product to a specific license.
    /// </summary>
    public class LicenseServerLicensedProduct : LicenseServerEntity, ILicenseServerEntity
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = null!;
        public IList<string> Permissions { get; set; } = [];
        public LicenseServerProduct Product { get; set; } = null!;
        public LicenseServerLicense License { get; set; } = null!;
        public LicenseServerTenant Account { get; set; } = null!;
        public LicenseServerChangeLog? ChangeLog { get; set; }
    }

    /// <summary>
    /// Represents an external product definition.
    /// </summary>
    public class LicenseServerProduct : LicenseServerEntity, ILicenseServerEntity
    {
        public Guid Id { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <typeparam name="T"></typeparam>
    internal class LicensePagedResult<T> where T : ILicenseServerEntity
    {
        public LicenseServerPagination Pagination { get; set; } = null!;
        public ICollection<T> Data { get; set; } = [];
    }

    /// <summary>
    /// 
    /// </summary>
    internal class LicenseServerPagination
    {
        public int Offset { get; set; }
        public int Total { get; set; }
        public int Next { get; set; }
        public int Previous { get; set; }
    }
}
