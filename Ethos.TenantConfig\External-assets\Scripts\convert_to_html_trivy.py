import json
import sys
from jinja2 import Template

# Define HTML template
html_template = """
<!DOCTYPE html>
<html>
<head>
    <title>{{ report_type }} Scan Report</title>
    <style>
        body { font-family: Arial, sans-serif; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; }
        th { background-color: #f2f2f2; }
        .summary-table th { background-color: #e0e0e0; }
        .critical { background-color: #f8d7da; }
        .high { background-color: #fff3cd; }
        .medium { background-color: #d4edda; }
        .low { background-color: #cce5ff; }
        .unknown { background-color: #eeeeee; }
    </style>
</head>
<body>
    <h1>{{ report_type }} Report - EthosTenantConfig</h1>

    <h2>Summary of Vulnerabilities</h2>
    <table class="summary-table">
        <thead>
            <tr>
                <th>Severity</th>
                <th>Count</th>
            </tr>
        </thead>
        <tbody>
            <tr class="critical">
                <td>Critical</td>
                <td>{{ count['Critical'] }}</td>
            </tr>
            <tr class="high">
                <td>High</td>
                <td>{{ count['High'] }}</td>
            </tr>
            <tr class="medium">
                <td>Medium</td>
                <td>{{ count['Medium'] }}</td>
            </tr>
            <tr class="low">
                <td>Low</td>
                <td>{{ count['Low'] }}</td>
            </tr>
            <tr class="unknown">
                <td>Unknown</td>
                <td>{{ count['Unknown'] }}</td>
            </tr>
        </tbody>
    </table>

    <h2>Details of Vulnerabilities</h2>
    <table>
        <thead>
            <tr>
                <th>Serial No.</th>
                <th>Vulnerability ID</th>
                <th>Package Name</th>
                <th>Installed Version</th>
                <th>Fixed Version</th>
                <th>Severity</th>
                <th>Description</th>
                <th>References</th>
                <th>Published Date</th>
                <th>Last Modified Date</th>
                <th>CVSS Score</th>
                <th>CVSS Vector</th>
                <th>Image Name</th>
            </tr>
        </thead>
        <tbody>
            {% for vuln in vulnerabilities %}
            <tr>
                <td>{{ loop.index }}</td> <!-- Serial Number -->
                <td>{{ vuln['vulnerability_id'] }}</td>
                <td>{{ vuln['package'] }}</td>
                <td>{{ vuln['version'] }}</td>
                <td>{{ vuln['fixed_version'] }}</td>
                <td>{{ vuln['severity'] }}</td>
                <td>{{ vuln['description'] }}</td>
                <td>{{ vuln['references'] }}</td>
                <td>{{ vuln['published_date'] }}</td>
                <td>{{ vuln['last_modified_date'] }}</td>
                <td>{{ vuln['cvss_score'] }}</td>
                <td>{{ vuln['cvss_vector'] }}</td>
                <td>{{ image_name }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</body>
</html>
"""

# Define severity ranking for sorting and severity colors
severity_order = {'Critical': 1, 'High': 2, 'Medium': 3, 'Low': 4, 'Unknown': 5}

def generate_html(json_file, output_file, report_type):
    with open(json_file, 'r') as f:
        data = json.load(f)

    vulnerabilities = []
    severity_count = {'Critical': 0, 'High': 0, 'Medium': 0, 'Low': 0, 'Unknown': 0}

    for result in data.get('Results', []):
        for vuln in result.get('Vulnerabilities', []):
            # Normalize severity to title case
            severity = vuln.get('Severity', 'Unknown').capitalize()
            if severity not in severity_count:
                severity = 'Unknown'

            severity_count[severity] += 1

            vulnerabilities.append({
                'vulnerability_id': vuln.get('VulnerabilityID', 'N/A'),
                'package': vuln.get('PkgName', 'N/A'),
                'version': vuln.get('InstalledVersion', 'N/A'),
                'fixed_version': vuln.get('FixedVersion', 'N/A'),
                'severity': severity,
                'description': vuln.get('Description', 'N/A'),
                'references': ', '.join(vuln.get('References', [])),
                'published_date': vuln.get('PublishedDate', 'N/A'),
                'last_modified_date': vuln.get('LastModifiedDate', 'N/A'),
                'cvss_score': vuln.get('CVSS', {}).get('nvd', {}).get('V3Score', 'N/A'),
                'cvss_vector': vuln.get('CVSS', {}).get('nvd', {}).get('V3Vector', 'N/A')
            })

    # Sort vulnerabilities by severity (Critical > High > Medium > Low > Unknown)
    vulnerabilities.sort(key=lambda x: severity_order.get(x['severity'], 5))

    # Render the HTML template
    template = Template(html_template)
    html_content = template.render(vulnerabilities=vulnerabilities, report_type=report_type, count=severity_count)

    # Write the HTML content to the output file
    with open(output_file, 'w') as f:
        f.write(html_content)

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: python3 convert_to_html.py <json_file> <output_file> <report_type>")
        sys.exit(1)

    json_file = sys.argv[1]
    output_file = sys.argv[2]
    report_type = sys.argv[3]

    generate_html(json_file, output_file, report_type)