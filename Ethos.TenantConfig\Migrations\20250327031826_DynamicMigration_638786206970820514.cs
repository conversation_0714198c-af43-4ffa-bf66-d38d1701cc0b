﻿using System.Collections.Generic;
using Ethos.TenantConfig;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Ethos.TenantConfig.Migrations
{
    /// <inheritdoc />
    public partial class DynamicMigration_638786206970820514 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<Dictionary<string, TenantConfigValue>>(
                name: "Values",
                table: "TenantConfig",
                type: "jsonb",
                nullable: true,
                oldClrType: typeof(Dictionary<string, TenantConfigValue>),
                oldType: "jsonb");

            migrationBuilder.AlterColumn<Dictionary<string, TenantConfigStorageValue>>(
                name: "Storage",
                table: "TenantConfig",
                type: "jsonb",
                nullable: true,
                oldClrType: typeof(Dictionary<string, TenantConfigStorageValue>),
                oldType: "jsonb");

            migrationBuilder.AlterColumn<Dictionary<string, TenantConfigSecretValue>>(
                name: "Secrets",
                table: "TenantConfig",
                type: "jsonb",
                nullable: true,
                oldClrType: typeof(Dictionary<string, TenantConfigSecretValue>),
                oldType: "jsonb");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<Dictionary<string, TenantConfigValue>>(
                name: "Values",
                table: "TenantConfig",
                type: "jsonb",
                nullable: false,
                oldClrType: typeof(Dictionary<string, TenantConfigValue>),
                oldType: "jsonb",
                oldNullable: true);

            migrationBuilder.AlterColumn<Dictionary<string, TenantConfigStorageValue>>(
                name: "Storage",
                table: "TenantConfig",
                type: "jsonb",
                nullable: false,
                oldClrType: typeof(Dictionary<string, TenantConfigStorageValue>),
                oldType: "jsonb",
                oldNullable: true);

            migrationBuilder.AlterColumn<Dictionary<string, TenantConfigSecretValue>>(
                name: "Secrets",
                table: "TenantConfig",
                type: "jsonb",
                nullable: false,
                oldClrType: typeof(Dictionary<string, TenantConfigSecretValue>),
                oldType: "jsonb",
                oldNullable: true);
        }
    }
}
