using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq.Expressions;
using System.Text.Json;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;


public class InsuranceVerificationDbo : IAuditableEntity<InsuranceVerificationDbo>
{
    public Guid StudyId { get; set; }
    public StudyDbo Study { get; set; } = null!;
    
    public Guid ServiceId { get; set; }
    public DateTime CreatedAt { get; set; }
    
    public string StateName { get; set; } = null!;
    [Column(TypeName = "jsonb")] public string StateJson { get; set; }
    [NotMapped] private InsuranceVerificationJobState? _currentState;
    [NotMapped] public InsuranceVerificationJobState CurrentState
    {
        get
        {
            _currentState ??= JsonSerializer.Deserialize<InsuranceVerificationJobState>(StateJson, JsonOptions);
            if (_currentState != null)
            {
                if (StateName != _currentState.GetType().Name)
                {
                    throw new InvalidOperationException($"StateName '{StateName}' does not match the deserialized state type '{_currentState.GetType().Name}'.");
                }
            }
            return _currentState ?? throw new InvalidOperationException("Failed to deserialize job state.");
        }
        set
        {
            _currentState = value;
            StateJson = JsonSerializer.Serialize(value, JsonOptions);
            StateName = value.GetType().Name;
        }
    }

    // Job lifecycle & processing control
    [Required] [StringLength(50)] public string CoarseStateDiscriminator { get; set; } // e.g., "Pending", "Processing"
    public DateTime? LastCoarseStateUpdate { get; set; }
    
    // IF CoarseJobState == Processing
    public Guid? ProcessingServiceInstanceId { get; set; }
    public DateTime? ProcessingTimeoutTimestamp { get; set; }
    public DateTime? WaitingUntil { get; set; }
    
    // IF CoarseJobState == WaitingForExternalAction
    public DateTime? ExternalActionTimeoutTimestamp { get; set; }
    
    // IF CoarseJobState == WaitingForManualAction
    public string? CoarseStateMessage { get; set; }
    public bool? CoarseStateSucceeded { get; set; }
    
    // Convenience method to update state and map to coarse state
    public void UpdateFineGrainedState(InsuranceVerificationJobState newState)
    {
        CurrentState = newState; // This also updates StateJson and StateHash via setter

        // Map to coarse state and update flattened properties
        var newCoarseState = newState.CoarseJobState;
        CoarseStateDiscriminator = newCoarseState.GetType().Name; // Simple discriminator
        LastCoarseStateUpdate = DateTime.UtcNow;

        // Reset all optional coarse state fields before setting new ones
        ProcessingServiceInstanceId = null;
        ProcessingTimeoutTimestamp = null;
        WaitingUntil = null;
        ExternalActionTimeoutTimestamp = null;

        switch (newCoarseState)
        {
            case CoarseJobState.Processing proc:
                throw new InvalidOperationException("CoarseJobState.Processing should not be returned by MapCoarseState.");
            
            case CoarseJobState.Pending p:
                WaitingUntil = p.NextRetryTimestamp;
                break;
            case CoarseJobState.WaitingForExternalAction wa:
                ExternalActionTimeoutTimestamp = wa.Timeout;
                break;
            case CoarseJobState.WaitingForManualAction wm:
                CoarseStateMessage = wm.Reason;
                break;
            case CoarseJobState.Completed c:
                CoarseStateSucceeded = c.Succeeded;
                CoarseStateMessage = c.OutcomeMessage;
                break;
            case CoarseJobState.Error e:
                CoarseStateMessage = e.ErrorMessage;
                break;
        }
    }
    
    private static JsonSerializerOptions JsonOptions { get; } = new JsonSerializerOptions
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase, // Or whatever you prefer
        // Add converters if necessary
    };
    
    public new static void Register(ModelBuilder modelBuilder) =>
            modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<InsuranceVerificationDbo>(Register);

    /// <summary>
    /// Configures the InsuranceVerificationEntity.
    /// </summary>
    public new static void Register(EntityTypeBuilder<InsuranceVerificationDbo> entity)
    {
        // Call IAuditableEntity's Register method if it provides base configurations (like Id, CreatedAt, UpdatedAt)
        // This assumes IAuditableEntity has a static Register method or similar mechanism.
        // If IAuditableEntity<T> is just an interface, common properties like Id should be configured here.
        // For this example, we'll configure Id explicitly as primary key.
        // If IAuditableEntity.Register exists and configures Id, this line might be redundant or handled by that call.
        
        // Assuming IAuditableEntity<InsuranceVerificationEntity>.Register(entity); would be called if it exists and handles common fields.
        // If not, configure primary key and auditable fields here:
        entity.HasKey(e => e.Id);
        entity.Property(e => e.CreatedAt).IsRequired(); // Assuming CreatedAt is part of this entity's direct responsibility or IAuditableEntity contract

        // Configure properties
        entity.Property(e => e.StudyId).IsRequired();
        entity.Property(e => e.ServiceId).IsRequired();

        entity.Property(e => e.StateName)
              .IsRequired()
              .HasMaxLength(255); // Max length for state name discriminator

        entity.Property(e => e.StateJson)
              .HasColumnType("jsonb") // Specific to PostgreSQL, use appropriate type for other DBs
              .IsRequired();

        entity.Property(e => e.CoarseStateDiscriminator)
              .IsRequired()
              .HasMaxLength(50); // Consistent with data annotation

        entity.Property(e => e.LastCoarseStateUpdate);
        entity.Property(e => e.ProcessingServiceInstanceId);
        entity.Property(e => e.ProcessingTimeoutTimestamp);
        entity.Property(e => e.WaitingUntil);
        entity.Property(e => e.ExternalActionTimeoutTimestamp);
        entity.Property(e => e.CoarseStateMessage).HasMaxLength(1000); // Optional: Max length for messages
        entity.Property(e => e.CoarseStateSucceeded);

        // Relationships
        entity.HasOne(e => e.Study)
              .WithMany() // If StudyEntity has a collection of InsuranceVerificationEntity, specify it here: .WithMany(s => s.InsuranceVerifications)
              .HasForeignKey(e => e.StudyId)
              .IsRequired()
              .OnDelete(DeleteBehavior.Restrict); // Or Cascade / SetNull based on business rules. Restrict is safer.

        // Ignored properties (NotMapped)
        // The [NotMapped] attribute on _currentState and CurrentState is usually sufficient.
        // If explicit configuration is needed: entity.Ignore(e => e.CurrentState);

        // Indexes for performance on frequently queried columns
        entity.HasIndex(e => e.StudyId).HasDatabaseName("IX_InsuranceVerification_StudyId");
        entity.HasIndex(e => e.CoarseStateDiscriminator).HasDatabaseName("IX_InsuranceVerification_CoarseState");
        entity.HasIndex(e => e.StateName).HasDatabaseName("IX_InsuranceVerification_StateName");
        entity.HasIndex(e => e.WaitingUntil).HasDatabaseName("IX_InsuranceVerification_WaitingUntil");
        entity.HasIndex(e => e.ExternalActionTimeoutTimestamp).HasDatabaseName("IX_InsuranceVerification_ExtActionTimeout");
        entity.HasIndex(e => new { e.ProcessingServiceInstanceId, e.ProcessingTimeoutTimestamp }).HasDatabaseName("IX_InsuranceVerification_ProcessingStatus");
    }
}

/// <summary>
/// Defines primitive query types for InsuranceVerificationEntity.
/// </summary>
[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(InsuranceVerificationQ.WithId), "WithId")]
[JsonDerivedType(typeof(InsuranceVerificationQ.WithStudyId), "WithStudyId")]
[JsonDerivedType(typeof(InsuranceVerificationQ.WithOrderId), "WithOrderId")]
[JsonDerivedType(typeof(InsuranceVerificationQ.WithPatientId), "WithPatientId")]
public abstract record InsuranceVerificationQ : IPrimitiveQuery
{
    /// <summary>
    /// Queries by the exact ID of the insurance verification job.
    /// </summary>
    public sealed record WithId(Guid Id) : InsuranceVerificationQ;

    /// <summary>
    /// Queries for jobs associated with a specific Study ID.
    /// </summary>
    public sealed record WithStudyId(Guid StudyId) : InsuranceVerificationQ;

    /// <summary>
    /// Queries for jobs associated with a specific Order ID.
    /// This requires navigating through StudyEntity.
    /// </summary>
    public sealed record WithOrderId(Guid OrderId) : InsuranceVerificationQ;

    /// <summary>
    /// Queries for jobs associated with a specific Patient ID.
    /// This requires navigating through StudyEntity and OrderEntity.
    /// </summary>
    public sealed record WithPatientId(Guid PatientId) : InsuranceVerificationQ;

    /// <summary>
    /// Builds the body of the predicate expression for the current query type.
    /// </summary>
    /// <param name="self">The parameter expression representing the InsuranceVerificationEntity.</param>
    /// <returns>An Expression representing the predicate body.</returns>
    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithId wid => Expression.Equal(
                Expression.Property(self, nameof(InsuranceVerificationDbo.Id)),
                Expression.Constant(wid.Id)),

            WithStudyId wsid => Expression.Equal(
                Expression.Property(self, nameof(InsuranceVerificationDbo.StudyId)),
                Expression.Constant(wsid.StudyId)),

            WithOrderId woid => BuildRelatedEntityPredicate(self, woid.OrderId,
                nameof(InsuranceVerificationDbo.Study), nameof(StudyDbo.OrderId)),

            WithPatientId wpid => BuildRelatedEntityPredicate(self, wpid.PatientId,
                nameof(InsuranceVerificationDbo.Study), nameof(StudyDbo.Order), nameof(OrderDbo.PatientId)),

            _ => throw new NotSupportedException($"Unsupported InsuranceVerificationQ type: {GetType().Name}")
        };
    }

    /// <summary>
    /// Helper to build predicates for related entities.
    /// Example: job.Study.OrderId == value
    /// Example: job.Study.Order.PatientId == value
    /// </summary>
    private static Expression BuildRelatedEntityPredicate(ParameterExpression baseParam, Guid valueToMatch, params string[] navigationProperties)
    {
        Expression currentPropertyAccess = baseParam;
        Expression? nullChecks = null;

        foreach (var propName in navigationProperties.Take(navigationProperties.Length -1) ) // All but the last property which is the ID
        {
            currentPropertyAccess = Expression.Property(currentPropertyAccess, propName);
            var notNullCheck = Expression.NotEqual(currentPropertyAccess, Expression.Constant(null, currentPropertyAccess.Type));
            nullChecks = nullChecks == null ? notNullCheck : Expression.AndAlso(nullChecks, notNullCheck);
        }

        // Final property to compare (e.g., OrderId, PatientId)
        var idProperty = Expression.Property(currentPropertyAccess, navigationProperties.Last());
        var valueConstant = Expression.Constant(valueToMatch, idProperty.Type); // Ensure correct type for Guid or Guid?
        var matchExpression = Expression.Equal(idProperty, valueConstant);

        return nullChecks == null ? matchExpression : Expression.AndAlso(nullChecks, matchExpression);
    }


    // Static factory methods for convenience
    public static InsuranceVerificationQ HasId(Guid id) => new WithId(id);
    public static InsuranceVerificationQ HasStudyId(Guid studyId) => new WithStudyId(studyId);
    public static InsuranceVerificationQ HasOrderId(Guid orderId) => new WithOrderId(orderId);
    public static InsuranceVerificationQ HasPatientId(Guid patientId) => new WithPatientId(patientId);
}