using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class OrganizationContactDetailDbo : IOwnedEntity<OrganizationContactDetailDbo>
{
    public ICollection<OrganizationEmailDbo> Emails { get; set; } = new List<OrganizationEmailDbo>();
    public ICollection<OrganizationPhoneNumberDbo> PhoneNumbers { get; set; } = new List<OrganizationPhoneNumberDbo>();
    public ICollection<OrganizationAddressDbo> Addresses { get; set; } = new List<OrganizationAddressDbo>();
    public ICollection<OrganizationContactPersonDbo> ContactPersons { get; set; } = new List<OrganizationContactPersonDbo>();
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<OrganizationContactDetailDbo>(Register);

    public new static void Register(EntityTypeBuilder<OrganizationContactDetailDbo> entity)
    {
        IOwnedEntity<OrganizationContactDetailDbo>.Register(entity);
        
        entity.HasMany(p => p.Emails)
            .WithOne(e => e.Parent)
            .HasForeignKey(e => e.ParentId)
            .OnDelete(DeleteBehavior.Cascade);

        entity.HasMany(p => p.PhoneNumbers)
            .WithOne(e => e.Parent)
            .HasForeignKey(e => e.ParentId)
            .OnDelete(DeleteBehavior.Cascade);

        entity.HasMany(p => p.Addresses)
            .WithOne(e => e.Parent)
            .HasForeignKey(e => e.ParentId)
            .OnDelete(DeleteBehavior.Cascade);

        entity.HasMany(p => p.ContactPersons)
            .WithOne(e => e.Parent)
            .HasForeignKey(e => e.ParentId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}