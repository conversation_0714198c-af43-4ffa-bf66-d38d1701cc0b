import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsInt, IsOptional, IsPositive } from 'class-validator';
import { IsDateOnly } from '@app/common/decorators/validators.decorator';

export class CreateTechnicianCredentialDto {
  @ApiProperty()
  @IsInt()
  @IsPositive()
  credentialId: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsDateOnly()
  validUntil?: string;
}
