using Ethos.Auth;
using Ethos.Events.Client;
using Ethos.Roles.Model;
using Ethos.Utilities.Pagination;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Roles.Controllers
{
    [ApiController]
    [Route("api/roles")]
    [Authorize]
    [EthosAuthFeature(Name = "Core")]
    public class RoleController : ControllerBase
    {
        readonly ILogger<RoleController> logger;
        readonly IServiceScopeFactory scopeFactory;
        readonly AppDbContext dbContext;
        readonly IConfiguration configuration;
        readonly IEthosEventClient eventClient;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="scopeFactory"></param>
        public RoleController(ILogger<RoleController> logger,
                              IServiceScopeFactory scopeFactory,
                              AppDbContext dbContext,
                              IConfiguration configuration,
                              IEthosEventClient eventClient)
        {
            this.logger = logger;
            this.scopeFactory = scopeFactory;
            this.dbContext = dbContext;
            this.configuration = configuration;
            this.eventClient = eventClient;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [EthosAuthScope(ScopeDefinitions.RoleWrite)]
        public async Task<IActionResult> CreateRole(EthosRoleDto role)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(CreateRole), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var isRoleAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.RoleAdmin);
                var effectiveTenantId = this.GetRequiredTenantId();

                if (string.IsNullOrEmpty(role.Name))
                    return BadRequest();

                // handle builtin role functionality
                if (BuiltinRole.IsBuiltinName(role.Name))
                {
                    if (!isRoleAdmin)
                        return Forbid();
                    if (!role.Id.HasValue)
                        return BadRequest();
                    var id = BuiltinRole.GetBuiltinId(role.Name);
                    if (!id.HasValue || role.Id.Value != id.Value)
                        return Conflict();
                }

                if (!role.Id.HasValue || role.Id.Value == Guid.Empty)
                    role.Id = Guid.NewGuid();

                if (dbContext.Roles.Any(r => r.TenantId == effectiveTenantId && (r.Id == role.Id.Value || string.Equals(r.Name.ToLower(), role.Name.ToLower()))))
                    return Conflict();

                var newRole = new EthosRole()
                {
                    Name = role.Name,
                    Id = role.Id.Value,
                    TenantId = effectiveTenantId
                };

                var scopes = new List<EthosRoleScope>();
                var messages = new List<object>();

                var isScopeAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.ScopeAdmin);

                foreach (var scope in role.Scopes ?? []) 
                {
                    if (!EthosScope.TryParse(scope, null, out var _scope) || !_scope.IsFullyQualified())
                    {
                        messages.Add(new { scope, message = "Invalid scope." });
                        continue;
                    }

                    var scopeDef = dbContext.Scopes.FirstOrDefault(s => string.Equals(s.Name.ToLower(), _scope.ToString().ToLower()));

                    if (scopeDef is null)
                    {
                        logger.LogWarning("Scope does not exist: {Scope}", scope);
                        messages.Add(new { scope, message = "No such scope." });
                        continue;
                    }

                    if (!scopeDef.Assignable && scopeDef.Privileged && !isScopeAdmin)
                    {
                        logger.LogWarning("Skipping non-assignable scope: {Scope}", scope);
                        messages.Add(new { scope, message = "Scope cannot be added to the role because it is globally privileged." });
                        continue;
                    }

                    scopes.Add(new EthosRoleScope()
                    {
                        TenantId = effectiveTenantId,
                        RoleId = newRole.Id,
                        Scope = _scope.ToString(),
                        DisplayName = _scope.ToString(),
                    });   
                }

                using var trans = dbContext.Database.BeginTransaction();
                try
                {
                    dbContext.Roles.Add(newRole);

                    if (scopes.Count > 0)
                    {
                        foreach (var scope in scopes)
                            dbContext.RoleScopes.Add(scope);
                    }

                    await dbContext.SaveChangesAsync();
                    trans.Commit();

                    return CreatedAtAction(nameof(GetRole), new { roleId = newRole.Id }, new EthosRoleDto()
                    {
                        Id = newRole.Id,
                        Name = newRole.Name,
                        Scopes = [.. newRole.Scopes.Select(scope => scope.Scope)],
                    });
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error creating new role.");
                    trans.Rollback();
                    await eventClient.CreateExceptionEvent(ex);
                    return Problem("Unexpected server error while creating role.", null, 500);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="pagingParameters"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("builtin")]
        [EthosAuthScope(ScopeDefinitions.RoleRead)]
        public IActionResult GetBuiltinRoles()
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetBuiltinRoles), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                return Ok(BuiltinRole.All.Select(r => new EthosRoleSummaryDto()
                {
                    Id = r.Id,
                    Name = r.Name,
                }));
            }          
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [Route("{roleId}")]
        [EthosAuthScope(ScopeDefinitions.RoleWrite)]
        public async Task<IActionResult> UpdateRole([FromRoute] Guid roleId, [FromBody] EthosRoleDto role)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(UpdateRole), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (roleId == Guid.Empty)
                    return BadRequest();

                var isRoleAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.RoleAdmin);
                var tenantId = this.GetRequiredTenantId();

                var existingRole = dbContext.Roles.FirstOrDefault(r => r.Id == roleId && r.TenantId == tenantId);

                if (existingRole is null)
                    return NotFound();

                // handle builtin role functionality
                if (BuiltinRole.IsBuiltinName(role.Name))
                {
                    if (!isRoleAdmin)
                        return Forbid();
                    var id = BuiltinRole.GetBuiltinId(role.Name);
                    if (!id.HasValue || roleId != id.Value)
                        return Conflict();
                }

                if (dbContext.Roles.Any(r => r.TenantId == tenantId && string.Equals(r.Name.ToLower(), role.Name.ToLower()) && r.Id != roleId))
                    return Conflict();

                existingRole.Name = role.Name;
                
                var messages = new List<object>();

                var scopes = new List<EthosRoleScope>();
                foreach (var scope in role.Scopes ?? [])
                {
                    if (!EthosScope.TryParse(scope, null, out var _scope) || !_scope.IsFullyQualified())
                    {
                        messages.Add(new { scope, message = "Invalid scope." });
                        continue;
                    }

                    var scopeDef = dbContext.Scopes.FirstOrDefault(s => string.Equals(s.Name.ToLower(), _scope.ToString().ToLower()));

                    if (scopeDef is null)
                    {
                        logger.LogWarning("Scope does not exist: {Scope}", scope);
                        messages.Add(new { scope, message = "No such scope." });
                        continue;
                    }

                    if (!scopeDef.Assignable && scopeDef.Privileged)
                    {
                        if (!isRoleAdmin)
                        {
                            logger.LogWarning("Skipping non-assignable scope: {Scope}", scope);
                            messages.Add(new { scope, message = "Scope cannot be added to the role because it is globally privileged." });
                            continue;
                        }
                    }

                    scopes.Add(new EthosRoleScope()
                    {
                        TenantId = existingRole.TenantId,
                        RoleId = roleId,
                        Scope = _scope.ToString(),
                        DisplayName = scopeDef.Description ?? _scope.ToString()
                    });
                }

                using var trans = dbContext.Database.BeginTransaction();
                try
                {
                    dbContext.Roles.Update(existingRole);

                    if (scopes.Count > 0)
                    {
                        await dbContext.RoleScopes.Where(s => s.RoleId == roleId).ExecuteDeleteAsync();
                        foreach (var scope in scopes)
                            dbContext.RoleScopes.Add(scope);
                    }

                    await dbContext.SaveChangesAsync();
                    trans.Commit();
                    return messages.Count == 0 ? NoContent() : StatusCode(207, messages);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error updating role.");
                    trans.Rollback();
                    await eventClient.CreateExceptionEvent(ex);
                    return Problem("Unexpected server error while updating role.", null, 500);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        [Route("{roleId}")]
        [EthosAuthScope(ScopeDefinitions.RoleDelete)]
        public async Task<IActionResult> DeleteRole([FromRoute] Guid roleId)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(DeleteRole), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (roleId == Guid.Empty)
                    return BadRequest();

                var tenantId = this.GetRequiredTenantId();

                if (BuiltinRole.IsBuiltin(roleId) && !HttpContext.User.IsAllowedScope(ScopeDefinitions.RoleAdmin))
                    return Forbid();

                var delRole = dbContext.Roles.FirstOrDefault(r => r.Id == roleId && r.TenantId == tenantId);

                if (delRole is null)
                    return NotFound();

                dbContext.Roles.Remove(delRole);
                await dbContext.SaveChangesAsync();
                return NoContent();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("{roleId}/assign/{userId}")]
        [EthosAuthScope(ScopeDefinitions.RoleAssignmentWrite)]
        public async Task<IActionResult> AssignRole([FromRoute] Guid roleId, [FromRoute] Guid userId)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(AssignRole), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (roleId == Guid.Empty || userId == Guid.Empty)
                    return BadRequest();

                var isRoleAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.RoleAdmin);
                var tenantId = this.GetRequiredTenantId();

                var role = dbContext.Roles.FirstOrDefault(r => r.Id == roleId && tenantId == r.TenantId);

                if (role is null)
                    return NotFound();

                if (role.TenantId != tenantId && !isRoleAdmin)
                    return BadRequest();

                // does the user already have the role?
                var assignment = dbContext.RoleAssignments.FirstOrDefault(ra => ra.TenantId == tenantId && ra.RoleId == roleId && ra.UserId == userId);

                if (assignment is not null)
                    return NoContent();

                dbContext.RoleAssignments.Add(new EthosRoleAssignment()
                {
                    UserId = userId,
                    TenantId = tenantId,
                    RoleId = roleId,
                });

                await dbContext.SaveChangesAsync();
                return NoContent();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="roleId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{roleId}/assign/{userId}")]
        [EthosAuthScope(ScopeDefinitions.RoleAssignmentDelete)]
        public async Task<IActionResult> UnssignRole([FromRoute] Guid roleId, [FromRoute] Guid userId)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(UnssignRole), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (roleId == Guid.Empty || userId == Guid.Empty)
                    return BadRequest();

                var tenantId = this.GetRequiredTenantId();

                var role = dbContext.Roles.FirstOrDefault(r => r.Id == roleId && tenantId == r.TenantId);

                if (role is null)
                    return NotFound($"No such role with ID {roleId}");

                // does the user have the role?
                var assignment = dbContext.RoleAssignments.FirstOrDefault(ra => ra.TenantId == tenantId && ra.RoleId == roleId && ra.UserId == userId);

                if (assignment is null)
                    return NotFound($"User is not assigned to role '{role.Name}'");

                dbContext.RoleAssignments.Remove(assignment);
                await dbContext.SaveChangesAsync();
                return NoContent();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="roleId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("assignments/{userId}")]
        [EthosAuthScope(ScopeDefinitions.RoleAssignmentRead)]
        public IActionResult GetRoleAssignments([FromRoute] Guid userId)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetRoleAssignments), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (userId == Guid.Empty)
                    return BadRequest();

                var isRoleAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.RoleAdmin);
                var tenantId = this.GetRequiredTenantId();

                var assignments = dbContext.RoleAssignments.Include(ra => ra.Role)
                                                           .ThenInclude(r => r.Scopes)
                                                           .Where(ra => ra.TenantId == tenantId && ra.UserId == userId)
                                                           .OrderBy(ra => ra.RoleId);

                return Ok(new EthosUserRoleAssignmentDto
                {
                    UserId = userId,
                    Roles = [.. assignments.Select(a => new EthosRoleAssignmentSummaryDto()
                    {
                        Id = a.RoleId,
                        Name = a.Role.Name,
                        TenantId = a.TenantId,
                    }).Distinct()],

                    Scopes = [.. assignments.GroupBy(a => a.TenantId).Select(a => new EthosScopeAssignmentSummaryDto() {
                        TenantId = a.Key,
                        EffectiveScopes = a.SelectMany(r => r.Role.Scopes.Select(s => s.ToString()).Cast<string>().Distinct()).ToArray()
                    })]

                    //Filters = [.. assignments.SelectMany(a => a.Role.Filters).Distinct()]
                });
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("scopes")]
        [EthosAuthScope(ScopeDefinitions.ScopeWrite)]
        public async Task<IActionResult> CreateScope([FromBody] List<EthosScopeDto> scopes)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(CreateScope), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (scopes is null || scopes.Count < 1)
                    return BadRequest("At least one scope is required.");

                var messages = new List<object>();

                using var trans = dbContext.Database.BeginTransaction();

                try
                {
                    foreach (var scope in scopes)
                    {
                        if (scope is null)
                            continue;

                        if (string.IsNullOrEmpty(scope.Name))
                        {
                            logger.LogWarning("Scope name is required.");
                            messages.Add(new { scope = "Unknown", message = "Scope name is required." });
                            continue;
                        }

                        if (!EthosScope.TryParse(scope.Name, null, out var _scope) || !_scope.IsFullyQualified())
                        {
                            logger.LogWarning("Skipping invalid scope: {Scope}", scope);
                            messages.Add(new { scope = scope.Name, message = "Invalid scope." });
                            continue;
                        }

                        var hasScopeAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.ScopeAdmin);

                        if (scope.Privileged && !scope.Assignable && !hasScopeAdmin)
                        {
                            logger.LogWarning("Current user does not have permission to create globally privileged scope: {Scope}", scope);
                            messages.Add(new { scope = scope.Name, message = "User does not have permission to create privileged scopes." });
                            continue;
                        }

                        var scopeExists = dbContext.Scopes.FirstOrDefault(s => string.Equals(s.Name.ToLower(), _scope.ToString().ToLower())) != null;

                        if (scopeExists)
                        {
                            logger.LogInformation("Scope {Scope} already exists", scope);
                            messages.Add(new { scope = _scope.ToString(), message = "Scope already exists." });
                            continue;
                        }

                        dbContext.Scopes.Add(new EthosDbScope()
                        {
                            Name = _scope.ToString(),
                            Description = scope.Description,
                            Assignable = scope.Assignable,
                            Privileged = scope.Privileged,
                        });

                        await dbContext.SaveChangesAsync();

                        messages.Add(new { scope = _scope.ToString(), message = "Successfully created scope." });
                    }

                    await trans.CommitAsync();
                    return messages.Count == 0 ? NoContent() : StatusCode(207, messages);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error creating scope(s)");
                    await trans.RollbackAsync();
                    await eventClient.CreateExceptionEvent(ex);
                    return Problem($"Error creating scope(s): {ex.Message}", null, 500);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="scope"></param>
        /// <param name="scopeDto"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("scopes/{scope}")]
        [EthosAuthScope(ScopeDefinitions.ScopeWrite)]
        public async Task<IActionResult> UpdateScope([FromRoute] string scope, [FromBody] EthosScopeDto scopeDto)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(UpdateScope), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (!EthosScope.TryParse(scope, null, out var routeScope))
                    return NotFound();

                if (scopeDto is null)
                    return BadRequest();

                if (string.IsNullOrEmpty(scopeDto.Name))
                    scopeDto.Name = routeScope.ToString();

                if (!EthosScope.TryParse(scopeDto.Name, null, out var dtoScope))
                    return BadRequest();

                using var trans = dbContext.Database.BeginTransaction();

                try
                {
                    var existingScope = dbContext.Scopes.FirstOrDefault(s => string.Equals(s.Name.ToLower(), routeScope.ToString().ToLower()));

                    if (existingScope is null)
                        return NotFound();

                    var hasScopeAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.ScopeAdmin);
                    if (scopeDto.Privileged && !scopeDto.Assignable && !hasScopeAdmin)
                    {
                        logger.LogWarning("Current user does not have permission to update globally privileged scope: {Scope}", routeScope.ToString());
                        //return (new { scope = routeScope.ToString(), message = "User does not have permission to create privileged scopes." });
                        return Forbid();
                    }

                    existingScope.Name = dtoScope.ToString();
                    existingScope.Description = scopeDto.Description;
                    existingScope.Privileged = scopeDto.Privileged;
                    existingScope.Assignable = scopeDto.Assignable;

                    dbContext.Scopes.Update(existingScope);

                    if (!string.Equals(routeScope.ToString(), dtoScope.ToString(), StringComparison.OrdinalIgnoreCase))
                    {
                        // scope was renamed
                        var roleScopes = dbContext.RoleScopes.Where(rs => string.Equals(rs.Scope.ToLower(), routeScope.ToString().ToLower()));

                        if (roleScopes.Any())
                        {
                            await roleScopes.ForEachAsync(rs =>
                            {
                                if (rs.DisplayName.Contains(routeScope.ToString(), StringComparison.OrdinalIgnoreCase))
                                    rs.DisplayName = rs.DisplayName.Replace(routeScope.ToString(), dtoScope.ToString(), StringComparison.OrdinalIgnoreCase);

                                rs.Scope = dtoScope.ToString();
                            });
                        }
                    }

                    await dbContext.SaveChangesAsync();
                    await trans.CommitAsync();
                    return NoContent();
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error updating scope: {Scope}", routeScope.ToString());
                    await trans.RollbackAsync();
                    await eventClient.CreateExceptionEvent(ex);
                    return Problem($"Error updating scope {routeScope}: {ex.Message}", null, 500);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="scopes"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("scopes")]
        [EthosAuthScope(ScopeDefinitions.ScopeDelete)]
        public async Task<IActionResult> DeleteScope([FromBody] string[] scopes)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(DeleteScope), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (scopes is null || scopes.Length < 1)
                    return NoContent();

                var messages = new List<object>();

                using var trans = dbContext.Database.BeginTransaction();

                try
                {
                    var isScopeAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.ScopeAdmin);

                    foreach (var scope in scopes)
                    {
                        if (string.IsNullOrEmpty(scope))
                            continue;

                        if (!EthosScope.TryParse(scope, null, out var _scope) || !_scope.IsFullyQualified())
                        {
                            logger.LogWarning("Skipping invalid scope: {Scope}", scope);
                            messages.Add(new { scope, message = "Invalid scope." });
                            continue;
                        }

                        var existingScope = dbContext.Scopes.FirstOrDefault(s => string.Equals(s.Name.ToLower(), _scope.ToString().ToLower()));

                        if (existingScope is null)
                        {
                            logger.LogInformation("Scope {Scope} does not exist", scope);
                            messages.Add(new { scope = _scope.ToString(), message = "Scope does not exist." });
                            continue;
                        }

                        if (existingScope.Privileged && !existingScope.Assignable && !isScopeAdmin)
                        {
                            logger.LogInformation("Privileged scope {Scope} cannot be deleted by the current user.", scope);
                            messages.Add(new { scope = _scope.ToString(), message = "Globally privileged scope cannot be deleted by the current user." });
                            continue;
                        }

                        dbContext.Scopes.Remove(existingScope);
                        await dbContext.RoleScopes.Where(rs => string.Equals(rs.Scope.ToLower(), _scope.ToString().ToLower())).ExecuteDeleteAsync();
                        await dbContext.SaveChangesAsync();
                    }

                    await trans.CommitAsync();
                    return messages.Count == 0 ? NoContent() : StatusCode(207, messages);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error deleting scope(s)");
                    await trans.RollbackAsync();
                    await eventClient.CreateExceptionEvent(ex);
                    return Problem($"Error deleting scope(s): {ex.Message}", null, 500);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("{roleId}/scopes")]
        [EthosAuthScope(ScopeDefinitions.RoleWrite)]
        public async Task<IActionResult> AddScopesToRole([FromRoute] Guid roleId, [FromBody] string[] scopes)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(AddScopesToRole), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (roleId == Guid.Empty)
                    return BadRequest();

                var isScopeAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.ScopeAdmin);
                var isRoleAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.RoleAdmin);
                var tenantId = this.GetRequiredTenantId();

                var role = dbContext.Roles.FirstOrDefault(r => r.Id == roleId && tenantId == r.TenantId);

                if (role is null)
                    return NotFound();

                if (BuiltinRole.IsBuiltin(roleId) && !isRoleAdmin)
                    return Forbid();

                var messages = new List<object>();

                using var trans = dbContext.Database.BeginTransaction();

                try
                {
                    foreach (var scope in scopes)
                    {
                        if (!EthosScope.TryParse(scope, null, out var _scope) || !_scope.IsFullyQualified())
                        {
                            logger.LogWarning("Skipping invalid scope: {Scope}", scope);
                            messages.Add(new { scope, message = "Invalid scope." });
                            continue;
                        }

                        var scopeDef = dbContext.Scopes.FirstOrDefault(s => string.Equals(s.Name.ToLower(), _scope.ToString().ToLower()));

                        if (scopeDef is null)
                        {
                            logger.LogWarning("Scope does not exist: {Scope}", scope);
                            messages.Add(new { scope, message = "No such scope." });
                            continue;
                        }

                        if (!scopeDef.Assignable && scopeDef.Privileged)
                        {
                            if (!isScopeAdmin)
                            {
                                logger.LogWarning("Skipping non-assignable scope: {Scope}", scope);
                                messages.Add(new { scope, message = "Scope cannot be added to the role because it is globally privileged." });
                                continue;
                            }
                        }
                        else if (!scopeDef.Assignable)
                        {
                            if (!isRoleAdmin)
                            {
                                logger.LogWarning("Skipping non-assignable scope: {Scope}", scope);
                                messages.Add(new { scope, message = "Scope cannot be added to the role because it is not assignable." });
                                continue;
                            }
                        }

                        var scopeExists = dbContext.RoleScopes.FirstOrDefault(rs => rs.RoleId == roleId &&
                                                                              string.Equals(rs.Scope.ToLower(), _scope.ToString().ToLower())) != null;

                        if (scopeExists)
                        {
                            logger.LogInformation("Scope {Scope} already exists for role {RoleId}", scope, roleId);
                            messages.Add(new { scope = _scope.ToString(), message = "Scope already exists on role." });
                            continue;
                        }

                        dbContext.RoleScopes.Add(new EthosRoleScope()
                        {
                            Scope = _scope.ToString(),
                            RoleId = roleId,
                            TenantId = role.TenantId,
                            DisplayName = _scope.ToString(),
                        });

                        await dbContext.SaveChangesAsync();
                    }

                    await trans.CommitAsync();
                    return messages.Count == 0 ? NoContent() : StatusCode(207, messages);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error adding scope(s) to role {RoleId} for tenant {TenantId}", roleId, tenantId);
                    await trans.RollbackAsync();
                    await eventClient.CreateExceptionEvent(ex);
                    return Problem($"Error adding scope(s) to role: {ex.Message}", null, 500);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="roleId"></param>
        /// <param name="scopes"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{roleId}/scopes")]
        [EthosAuthScope(ScopeDefinitions.RoleWrite)]
        public async Task<IActionResult> RemoveScopesFromRole([FromRoute] Guid roleId, [FromBody] string[] scopes, [FromQuery] bool? removeAll = false)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(RemoveScopesFromRole), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (roleId == Guid.Empty)
                    return BadRequest();

                var isScopeAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.ScopeAdmin);
                var isRoleAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.RoleAdmin);
                var tenantId = this.GetRequiredTenantId();

                if (BuiltinRole.IsBuiltin(roleId) && !isRoleAdmin)
                    return Forbid();

                var role = dbContext.Roles.FirstOrDefault(r => r.Id == roleId && tenantId == r.TenantId);

                if (role is null)
                    return NotFound();

                var messages = new List<object>();

                using var trans = dbContext.Database.BeginTransaction();

                try
                {
                    if (removeAll.HasValue && removeAll.Value)
                    {
                        if (!isScopeAdmin)
                            return BadRequest();

                        await dbContext.RoleScopes.Where(rs => rs.RoleId == roleId).ExecuteDeleteAsync();
                    }
                    else
                    {
                        foreach (var scope in scopes)
                        {
                            if (!EthosScope.TryParse(scope, null, out var _scope) || !_scope.IsFullyQualified())
                            {
                                logger.LogWarning("Skipping invalid scope: {Scope}", scope);
                                messages.Add(new { scope, message = "Invalid scope." });
                                continue;
                            }

                            var scopeDef = dbContext.Scopes.FirstOrDefault(s => string.Equals(s.Name.ToLower(), _scope.ToString().ToLower()));

                            var existingScope = dbContext.RoleScopes.FirstOrDefault(rs => rs.RoleId == roleId &&
                                                                                          string.Equals(rs.Scope.ToLower(), _scope.ToString().ToLower()));

                            if (existingScope is null)
                            {
                                if (scopeDef is not null)
                                {
                                    logger.LogInformation("Scope {Scope} does not exist for role {RoleId}", scope, roleId);
                                    messages.Add(new { scope = _scope.ToString(), message = "Scope does not exist on role." });
                                    continue;
                                }
                                else
                                {
                                    logger.LogWarning("Skipping non-existent scope: {Scope}", scope);
                                    messages.Add(new { scope, message = "No such scope." });
                                    continue;
                                }
                            }
                            else if (scopeDef is not null)
                            {
                                if (!scopeDef.Assignable)
                                {
                                    if (scopeDef.Privileged)
                                    {
                                        if (!isScopeAdmin)
                                        {
                                            logger.LogWarning("Current user cannot remove globally privileged scope from role: {Scope}", scope);
                                            messages.Add(new { scope, message = "Globally privileged scope cannot be removed from the role by the current user." });
                                            continue;
                                        }
                                    }
                                    else
                                    {
                                        if (!isRoleAdmin)
                                        {
                                            logger.LogWarning("Current user cannot remove scope from role: {Scope}", scope);
                                            messages.Add(new { scope, message = "Non-assignable scope cannot be removed from the role by the current user." });
                                            continue;
                                        }
                                    }
                                }
                            }
                            dbContext.RoleScopes.Remove(existingScope);
                        }
                    }

                    await dbContext.SaveChangesAsync();
                    await trans.CommitAsync();
                    return messages.Count == 0 ? NoContent() : StatusCode(207, messages);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error removing scope(s) from role {RoleId} for tenant {TenantId}", roleId, tenantId);
                    await trans.RollbackAsync();
                    await eventClient.CreateExceptionEvent(ex);
                    return Problem($"Error removing scope(s) from role: {ex.Message}", null, 500);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.RoleRead)]
        public async Task<ActionResult<PagedResponse<EthosRoleDto>>> GetRoles([FromQuery] PagingParameters pagingParameters, [FromQuery] string? name = null)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetRoles), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var tenantId = this.GetRequiredTenantId();

                var roles = dbContext.Roles.Include(r => r.Scopes).Where(r => r.TenantId == tenantId &&
                                                                              (!string.IsNullOrEmpty(name) && r.Name.ToLower().StartsWith(name.ToLower()) ||
                                                                              string.IsNullOrEmpty(name)))
                                     .OrderBy(r => r.Name);

                return Ok(await roles.PaginateWithLinksAsync(this, (roles) =>
                {
                    var _roles = new List<EthosRoleDto>();
                    _roles.AddRange(roles.Select(r => new EthosRoleDto()
                    {
                         Id = r.Id,
                         Name = r.Name,
                         Scopes = HttpContext.User.IsAllowedScope(ScopeDefinitions.ScopeRead) ? [.. r.Scopes.Select(s => s.Scope)] : [],
                    }));
                    return _roles;
                }, pagingParameters.limit, pagingParameters.offset));
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("{roleId}")]
        [EthosAuthScope(ScopeDefinitions.RoleRead)]
        public async Task<IActionResult> GetRole([FromRoute] Guid roleId)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetRole), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var tenantId = this.GetRequiredTenantId();
                var role = await dbContext.Roles.Include(r => r.Scopes)
                                                .FirstOrDefaultAsync(r => r.TenantId == tenantId && r.Id == roleId);

                if (role is null)
                    return NotFound();

                return Ok(new EthosRoleDto()
                {
                    Id = role.Id,
                    Name = role.Name,
                    Scopes = HttpContext.User.IsAllowedScope(ScopeDefinitions.ScopeRead) ? [.. role.Scopes.Select(s => s.Scope)] : []
                });
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="pagingParameters"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("scopes")]
        [EthosAuthScope(ScopeDefinitions.ScopeRead)]
        public async Task<IActionResult> GetScopes([FromQuery] PagingParameters pagingParameters)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetScopes), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var hasScopeAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.ScopeAdmin);

                var scopes = dbContext.Scopes.Where(s => hasScopeAdmin || (s.Assignable && !s.Privileged))
                                             .OrderBy(s => s.Name);

                return Ok(await scopes.PaginateWithLinksAsync<EthosDbScope, EthosScopeDto>(this, (sps) =>
                {
                    return [.. sps.Select(s => new EthosScopeDto()
                    {
                        Name = s.Name,
                        Description = s.Description,
                        Assignable = s.Assignable,
                        Privileged = s.Privileged,
                    })];
                }, pagingParameters.limit, pagingParameters.offset));
            }
        }
    }
}
