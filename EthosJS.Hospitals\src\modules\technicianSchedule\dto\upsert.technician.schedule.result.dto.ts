import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { TechnicianScheduleDto } from '@app/modules/technicianSchedule/dto/technician.schedule.dto';
import { UpsertTechnicianScheduleItemDto } from '@app/modules/technicianSchedule/dto/upsert.technician.schedule.dto';

class UpsertTechnicianScheduleResultItemDto {
  @ApiPropertyOptional({ type: TechnicianScheduleDto })
  @Expose()
  item?: TechnicianScheduleDto;

  @ApiPropertyOptional({ type: UpsertTechnicianScheduleItemDto })
  @Expose()
  sourceItem?: UpsertTechnicianScheduleItemDto;

  @ApiPropertyOptional()
  @Expose()
  error?: string;
}

export class UpsertTechnicianScheduleResultDto {
  @ApiProperty({ type: UpsertTechnicianScheduleResultItemDto, isArray: true })
  @Expose()
  items: UpsertTechnicianScheduleResultItemDto[];
}