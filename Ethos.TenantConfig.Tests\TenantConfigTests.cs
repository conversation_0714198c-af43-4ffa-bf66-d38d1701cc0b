﻿using Xunit;
using System.Text.Json;
using System.Net.Mime;
using FluentAssertions;
using System.Net.Http.Json;
using Microsoft.AspNetCore.JsonPatch;
using System.Diagnostics;
using System.Net.Http.Headers;

namespace Ethos.TenantConfig.Tests
{
    /// <summary>
    /// 
    /// </summary>
    public class TenantConfigTests : IDisposable
    {
        readonly static string baseUri = "https://localhost:5066";
        readonly static string tenantIdStr = "257650A2-FD84-4CEB-8526-CF0A722723B3";
        readonly static Guid tenantId = new(tenantIdStr);
        readonly static JsonSerializerOptions options = new()
        {
            WriteIndented = true,
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        };
        static bool apiRunning = false;
        Process? _apiProcess;
        readonly HttpClient client = new(new HttpClientHandler() { 
             ClientCertificateOptions = ClientCertificateOption.Manual,
            ServerCertificateCustomValidationCallback = (msg, cert, chain, errors) => { return true; } });

        /// <summary>
        /// 
        /// </summary>
        public TenantConfigTests()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        public void Dispose()
        {
            // Stop the API process
            if (_apiProcess != null && !_apiProcess.HasExited)
            {
                _apiProcess.Kill();
                _apiProcess.Dispose();
                apiRunning = false;
                GC.SuppressFinalize(this);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Exception"></exception>
        void StartApi()
        {
            // Start the API project
            _apiProcess = new Process();
            _apiProcess.StartInfo.FileName = "dotnet";
            _apiProcess.StartInfo.Arguments = @"run --project Z:\tenant-config-service\Ethos.TenantConfig\Ethos.TenantConfig.csproj"; // Adjust the path.
            _apiProcess.StartInfo.UseShellExecute = false;
            _apiProcess.StartInfo.CreateNoWindow = false; 
            _apiProcess.Start();

            Task.Delay(30000).Wait();

            if (_apiProcess.HasExited)
                throw new Exception("Cannot start API: Invalid API");

            apiRunning = true;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [Fact]
        public async Task Create_New_Config_And_Delete_Success()
        {
            if (!apiRunning)
                StartApi();

            var sectionName = "new_config";
            var tenantConfig = new TenantConfigSection()
            {
                TenantId = tenantId,
                Name = sectionName,
                Values = new Dictionary<string, TenantConfigValue>() {
                    { "test", new TenantConfigValue() {
                         Value = "test_string_1",
                    }}
                }
            };

            var req = new HttpRequestMessage(HttpMethod.Post, $"{baseUri}/tenants/{tenantId}/config");
            req.Content = new StringContent(JsonSerializer.Serialize(tenantConfig), new MediaTypeHeaderValue(MediaTypeNames.Application.Json));
            var result = await client.SendAsync(req);
            var response = await result.Content.ReadFromJsonAsync<TenantConfigSection>();

            result.Should().NotBeNull();
            result.Content.Should().NotBeNull();
            //response.Should().BeEquivalentTo(tenantConfig);

            req = new HttpRequestMessage(HttpMethod.Delete, $"{baseUri}/tenants/{tenantId}/config/{sectionName}");
            result = await client.SendAsync(req);

            result.Should().NotBeNull();
            result.StatusCode.Should().Be(System.Net.HttpStatusCode.NoContent);
            return;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [Fact]
        public async Task Replace_Config_And_Delete_Success()
        {
            if (!apiRunning)
                StartApi();
            var sectionName = "replace_config";
            var tenantConfig = new TenantConfigSection()
            {
                TenantId = tenantId,
                Name = sectionName,
                Values = new Dictionary<string, TenantConfigValue>() {
                    { "test", new TenantConfigValue() {
                         Value = "test_string_1",
                    }}
                }
            };

            var req = new HttpRequestMessage(HttpMethod.Post, $"{baseUri}/tenants/{tenantId}/config");
            req.Content = new StringContent(JsonSerializer.Serialize(tenantConfig), new MediaTypeHeaderValue(MediaTypeNames.Application.Json));
            var result = await client.SendAsync(req);
            var response = await result.Content.ReadFromJsonAsync<TenantConfigSection>();

            result.Should().NotBeNull();
            result.Content.Should().NotBeNull();
            response.Should().BeEquivalentTo(tenantConfig);

            tenantConfig = new TenantConfigSection()
            {
                TenantId = tenantId,
                Name = sectionName,
                Values = new Dictionary<string, TenantConfigValue>() {
                    { "replace", new TenantConfigValue() {
                         Value = "replace_string_1",
                    }}
                }
            };

            req = new HttpRequestMessage(HttpMethod.Put, $"{baseUri}/tenants/{tenantId}/config/{sectionName}");
            result = await client.SendAsync(req);

            result.Should().NotBeNull();
            result.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            response = await result.Content.ReadFromJsonAsync<TenantConfigSection>();
            response.Should().BeEquivalentTo(tenantConfig);

            req = new HttpRequestMessage(HttpMethod.Delete, $"{baseUri}/tenants/{tenantId}/config/{sectionName}");
            result = await client.SendAsync(req);

            result.Should().NotBeNull();
            result.StatusCode.Should().Be(System.Net.HttpStatusCode.NoContent);
            return;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="patchDocument"></param>
        /// <returns></returns>
        private static string SerializePatchDocument<T>(JsonPatchDocument<T> patchDocument) where T : class
        {
            var operations = new List<object>();

            foreach (var operation in patchDocument.Operations)
            {
                var jsonOperation = new Dictionary<string, object>
            {
                { "op", operation.OperationType.ToString().ToLower() },
                { "path", operation.path }
            };

                if (operation.value != null)
                    jsonOperation["value"] = operation.value;

                if (operation.from != null)
                    jsonOperation["from"] = operation.from;

                operations.Add(jsonOperation);
            }
            return JsonSerializer.Serialize(operations, options);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [Fact]
        public async Task Patch_Config_And_Delete_Success()
        {
            if (!apiRunning)
                StartApi();
            var sectionName = "patch_config";
            var tenantConfig = new TenantConfigSection()
            {
                //TenantId = tenantId,
                Name = sectionName,
                Values = new Dictionary<string, TenantConfigValue>() {
                    { "test", new TenantConfigValue() {
                         Value = "test_string_1",
                    }
                    },
                    { "test1", new TenantConfigValue() {
                         Value = "test_string_2",
                    }
                    },
                    { "test2", new TenantConfigValue() {
                         Value = "test_string_3",
                    }
                    }
                }
            };

            var req = new HttpRequestMessage(HttpMethod.Post, $"{baseUri}/tenants/{tenantId}/config")
            {
                Content = new StringContent(JsonSerializer.Serialize(tenantConfig), new MediaTypeHeaderValue(MediaTypeNames.Application.Json))
            };
            var result = await client.SendAsync(req);
            var response = await result.Content.ReadFromJsonAsync<TenantConfigSection>();

            result.Should().NotBeNull();
            result.Content.Should().NotBeNull();
            //response.Should().BeEquivalentTo(tenantConfig);

            var patch = new JsonPatchDocument<TenantConfigSection>();
            patch.Replace(t => t.Values["test2"], new TenantConfigValue()
            {
                Value = "patched_string_3"
            });

            patch.Remove(t => t.Values["test"]);

            patch.Add(t => t.Values["test3"], new TenantConfigValue()
            {
                Value = "patched_string_4"
            });

            var tmpConfig = tenantConfig;
            tmpConfig.Values["test2"] = new TenantConfigValue()
            {
                Value = "patched_string_3"
            };
            tmpConfig.Values.Remove("test");
            tmpConfig.Values.Add("test3", new TenantConfigValue()
            {
                Value = "patched_string_4"
            });

            req = new HttpRequestMessage(HttpMethod.Patch, $"{baseUri}/tenants/{tenantId}/config/{sectionName}");
           
            var contentStsr = SerializePatchDocument( patch );
            req.Content = new StringContent(contentStsr, new MediaTypeHeaderValue("application/json-patch+json"));
            result = await client.SendAsync(req);
            response = await result.Content.ReadFromJsonAsync<TenantConfigSection>();

            result.Should().NotBeNull();
            result.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            req = new HttpRequestMessage(HttpMethod.Delete, $"{baseUri}/tenants/{tenantId}/config/{sectionName}");
            result = await client.SendAsync(req);

            result.Should().NotBeNull();
            result.StatusCode.Should().Be(System.Net.HttpStatusCode.NoContent);
            return;
        }
    }
}
