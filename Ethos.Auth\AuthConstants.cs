﻿
using System.IdentityModel.Tokens.Jwt;

namespace Ethos.Auth
{
    /// <summary>
    /// 
    /// </summary>
    public static class PermissionConstants
    {
        //public const string Create = nameof(EthosPermission.Create);
        public const string Read = nameof(EthosPermission.Read);
        public const string ReadAll = nameof(EthosPermission.ReadAll);
        public const string Write = nameof(EthosPermission.Write);
        public const string Delete = nameof(EthosPermission.Delete);
        public const string Audit = nameof(EthosPermission.Audit);
        public const string Admin = nameof(EthosPermission.Admin);
        public const string All = EthosScope.All;
        public const string None = EthosScope.None;
    }

    /// <summary>
    /// 
    /// </summary>
    public static class FeatureConstants
    {
        public const string ReferenceData = nameof(ReferenceData);
        public const string Core = nameof(Core);
        public const string InsuranceVerification = nameof(InsuranceVerification);
        public const string All = "*";
    }

    /// <summary>
    /// 
    /// </summary>
    public static class EntityConstants
    {
        public const string All = "*";

        /* 
         * Reference Data
         */
        public const string Set = nameof(Set);
        public const string SetValue = nameof(SetValue);
        public const string List = nameof(List);

        /*
         * Roles
         */
        public const string Role = nameof(Role);
        public const string RoleAssignment = nameof(RoleAssignment);

        /*
         * Events
         */
        public const string Event = nameof(Event);

        /*
         * Licenses/Products/Tenants
         */
        public const string License = nameof(License);
        public const string Product = nameof(Product);
        public const string Feature = nameof(Feature);
        public const string Tenant = nameof(Tenant);
        public const string Scope = nameof(Scope);
        public const string User = nameof(User);
    }

    /// <summary>
    /// 
    /// </summary>
    public static class ScopeDefinitions
    {
        public const string RefDataSetWrite = $"{EntityConstants.Set}.{PermissionConstants.Write}";
        public const string RefDataSetValueWrite = $"{EntityConstants.SetValue}.{PermissionConstants.Write}";
        public const string RefDataListWrite = $"{EntityConstants.List}.{PermissionConstants.Write}";
        public const string RefDataSetRead = $"{EntityConstants.Set}.{PermissionConstants.Read}";
        public const string RefDataSetValueRead = $"{EntityConstants.SetValue}.{PermissionConstants.Read}";
        public const string RefDataListRead = $"{EntityConstants.List}.{PermissionConstants.Read}";
        public const string PlatformAdministrator = $"{FeatureConstants.All}.{EntityConstants.All}.{PermissionConstants.Admin}";
        public const string RoleWrite = $"{EntityConstants.Role}.{PermissionConstants.Write}";
        public const string RoleAdmin = $"{FeatureConstants.Core}.{EntityConstants.Role}.{PermissionConstants.Admin}";
        public const string RoleDelete = $"{EntityConstants.Role}.{PermissionConstants.Delete}";
        public const string RoleRead = $"{EntityConstants.Role}.{PermissionConstants.Read}";
        public const string RoleAssignmentWrite = $"{EntityConstants.RoleAssignment}.{PermissionConstants.Write}";
        public const string RoleAssignmentDelete = $"{EntityConstants.RoleAssignment}.{PermissionConstants.Delete}";
        public const string RoleAssignmentRead = $"{EntityConstants.RoleAssignment}.{PermissionConstants.Read}";
        public const string RoleAssignmentReadAll = $"{EntityConstants.RoleAssignment}.{PermissionConstants.ReadAll}";
        public const string EventWrite = $"{FeatureConstants.Core}.{EntityConstants.Event}.{PermissionConstants.Write}";
        public const string EventRead = $"{FeatureConstants.Core}.{EntityConstants.Event}.{PermissionConstants.Read}";
        public const string LicenseAdministrator = $"{FeatureConstants.Core}.{EntityConstants.License}.{PermissionConstants.Admin}";
        public const string LicenseWrite = $"{FeatureConstants.Core}.{EntityConstants.License}.{PermissionConstants.Write}";
        public const string LicenseRead = $"{FeatureConstants.Core}.{EntityConstants.License}.{PermissionConstants.Read}";
        public const string ProductWrite = $"{FeatureConstants.Core}.{EntityConstants.Product}.{PermissionConstants.Write}";
        public const string FeatureWrite = $"{FeatureConstants.Core}.{EntityConstants.Feature}.{PermissionConstants.Write}";
        public const string FeatureDelete = $"{FeatureConstants.Core}.{EntityConstants.Feature}.{PermissionConstants.Delete}";
        public const string FeatureRead = $"{FeatureConstants.Core}.{EntityConstants.Feature}.{PermissionConstants.Read}";
        public const string ProductRead = $"{FeatureConstants.Core}.{EntityConstants.Product}.{PermissionConstants.Read}";
        public const string ScopeRead = $"{FeatureConstants.Core}.{EntityConstants.Scope}.{PermissionConstants.Read}";
        public const string ScopeAdmin = $"{FeatureConstants.Core}.{EntityConstants.Scope}.{PermissionConstants.Admin}";
        public const string ScopeDelete = $"{FeatureConstants.Core}.{EntityConstants.Scope}.{PermissionConstants.Delete}";
        public const string ScopeWrite = $"{FeatureConstants.Core}.{EntityConstants.Scope}.{PermissionConstants.Write}";
        public const string ProductAdministrator = $"{FeatureConstants.Core}.{EntityConstants.Product}.{PermissionConstants.Admin}";
        public const string TenantWrite = $"{FeatureConstants.Core}.{EntityConstants.Tenant}.{PermissionConstants.Write}";
        public const string TenantRead = $"{FeatureConstants.Core}.{EntityConstants.Tenant}.{PermissionConstants.Read}";
        public const string TenantReadAll = $"{FeatureConstants.Core}.{EntityConstants.Tenant}.{PermissionConstants.ReadAll}";
        public const string TenantAdministrator = $"{FeatureConstants.Core}.{EntityConstants.Tenant}.{PermissionConstants.Admin}";
        public const string UserAdmin = $"{FeatureConstants.Core}.{EntityConstants.User}.{PermissionConstants.Admin}";
        public const string UserWrite = $"{FeatureConstants.Core}.{EntityConstants.User}.{PermissionConstants.Write}";
        public const string UserDelete = $"{FeatureConstants.Core}.{EntityConstants.User}.{PermissionConstants.Delete}";
        public const string UserRead = $"{FeatureConstants.Core}.{EntityConstants.User}.{PermissionConstants.Read}";
        public const string UserReadAll = $"{FeatureConstants.Core}.{EntityConstants.User}.{PermissionConstants.ReadAll}";
    }

    /// <summary>
    /// 
    /// </summary>
    public static class EthosClaimNames
    {
        public const string ExtensionProducts = "extension_products";
        public const string TenantId = "extension_ethosTenantId";
        public const string MicrosoftScope = "http://schemas.microsoft.com/identity/claims/scope";
        public const string Scopes = "extension_ethosScopes";
        public const string Emails = "emails";
        public const string JobTitle = "jobTitle";
        public const string Country = "country";
        public const string GivenName = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname";
        public const string Surname = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname";
        public const string MicrosoftObjectIdentifier = "http://schemas.microsoft.com/identity/claims/objectidentifier";
        public const string MicrosoftNameIdentifier = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier";
    }
}
