using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class PhysicianCareLocationRelationDbo : IAuditableEntity<PhysicianCareLocationRelationDbo>
{
    public Guid PhysicianId { get; set; }
    public PhysicianDbo Physician { get; set; } = null!;
    
    public Guid CareLocationId { get; set; }
    public CareLocationDbo CareLocation { get; set; } = null!;
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<PhysicianCareLocationRelationDbo>(Register);
    
    public new static void Register(EntityTypeBuilder<PhysicianCareLocationRelationDbo> entity)
    {
        IAuditableEntity<PhysicianCareLocationRelationDbo>.Register(entity);
        
        entity.HasOne(e => e.Physician)
            .WithMany()
            .HasForeign<PERSON>ey(e => e.PhysicianId)
            .OnDelete(DeleteBehavior.Cascade);
        
        entity.HasOne(e => e.CareLocation)
            .WithMany()
            .HasForeignKey(e => e.CareLocationId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}