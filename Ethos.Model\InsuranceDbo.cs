using System.Linq.Expressions;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class InsuranceDbo : IOwnedEntity<InsuranceDbo>
{
    public required long? InsuranceCarrier { get; set; }
    public required string? InsuranceId { get; set; }
    public required string PolicyId { get; set; }
    public required string GroupNumber { get; set; }
    public required string? MemberId { get; set; }
    public required InsuranceHolderDataDbo? InsuranceHolder { get; set; }
    public required PhoneNumberWithUseDataDbo? PhoneNumber { get; set; }
    
    public Guid PatientId { get; set; }
    public virtual PatientDbo? Patient { get; set; }
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<InsuranceDbo>(Register);

    public new static void Register(EntityTypeBuilder<InsuranceDbo> entity)
    {
        IAuditableEntity<InsuranceDbo>.Register(entity);
        
        entity.Property(e => e.InsuranceId).HasMaxLength(200).IsRequired(false);
        entity.Property(e => e.PolicyId).HasMaxLength(200).IsRequired();
        entity.Property(e => e.GroupNumber).HasMaxLength(200).IsRequired();
        entity.Property(e => e.MemberId).HasMaxLength(200).IsRequired(false);
        entity.Property(e => e.InsuranceCarrier).IsRequired(false);
        
        entity.HasOne(s => s.Patient)
            .WithMany(p => p.Insurances)
            .HasForeignKey(s => s.PatientId)
            .HasPrincipalKey(p => p.Id)
            .OnDelete(DeleteBehavior.Cascade);
        
        entity.HasOne(e => e.InsuranceHolder)
            .WithOne()
            .HasPrincipalKey<InsuranceHolderDataDbo>(e => e.Id);
        
        entity.HasOne(e => e.PhoneNumber)
            .WithOne()
            .HasPrincipalKey<PhoneNumberWithUseDataDbo>(e => e.Id);
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(InsuranceQ.WithId), "WithId")]
[JsonDerivedType(typeof(InsuranceQ.WithPatientId), "WithPatientId")]
public abstract record InsuranceQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : InsuranceQ;
    public sealed record WithPatientId(Guid PatientId) : InsuranceQ;

    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithId id => Expression.Equal(Expression.Property(self, nameof(InsuranceDbo.Id)), Expression.Constant(id.Id)),
            WithPatientId patientId => Expression.Equal(Expression.Property(self, nameof(InsuranceDbo.PatientId)), Expression.Constant(patientId.PatientId)),
            _ => throw new NotImplementedException()
        };
    }
}