// THIS FILE IS AUTO-GENERATED
// DO NOT EDIT

using System.Collections.Immutable;
using System.Text.RegularExpressions;
using Ethos.Utilities;

namespace Ethos.Model.Types;

public sealed record PersonName(
    IReadOnlyList<string> FirstNames,
    IReadOnlyList<string> LastNames,
    IReadOnlyList<string> MiddleInitials,
    IReadOnlyList<string> MiddleNames,
    IReadOnlyList<string> Prefixes,
    IReadOnlyList<string> Suffixes);
