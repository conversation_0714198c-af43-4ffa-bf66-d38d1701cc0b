﻿using Microsoft.AspNetCore.WebUtilities;
using System.Text.Json;
using System.Reflection;
using System.Text;
using System.Net.Mime;
using System.Net.Http.Headers;
using System.Text.Json.Serialization;
using System.Net.Http.Json;
using System.Collections;
using System.Collections.Generic;

namespace Ethos.ReferenceData.Client
{
    /// <summary>
    /// 
    /// </summary>
    public interface IReferenceDataClient : IWebClient, IDisposable
    {
        Task<List<ReferenceDataSetDto>> ListSets(string? name = null, string? version = null, bool fuzzyMatch = true);
        Task<ReferenceDataSetDto?> GetSet(long setId);
        Task<ReferenceDataSetDto?> GetSet(string setName, string? version);
        Task<List<ReferenceDataSetValue<TRefData>>> GetSetValues<TRefData>(long setId) where TRefData : class, new();
        Task<List<ReferenceDataSetValue<TRefData>>> GetSetValues<TRefData>(string setName, string? version) where TRefData : class, new();
        Task<ReferenceDataSetValue<TRefData>?> GetSetValueById<TRefData>(long id) where TRefData : class, new();
        Task<ReferenceDataSetValue<TRefData>?> GetSetValuesByIds<TRefData>(params long[] ids) where TRefData : class, new();
        Task<long> RemoveAndCreateSet<TRefData>(List<TRefData> items) where TRefData : class, new();
        Task<long> RemoveAndCreateSet(Type type, params object[] items);
        Task<long> GetOrCreateSet(string setName,
                                  string? version,
                                  string keyName,
                                  string? source,
                                  string? authority,
                                  params object[] items);
        Task<long> AddObjectToSet<TRefData>(TRefData value,
                                           string setName,
                                           string? version) where TRefData : class, new();
        Task<long> AddObjectToSet(object value, long setId);
        Task<long> AddObjectToSet(object value, string setName, string? version);
        Task<long> AddValueToSet(IDictionary<string, object> value, long setId);
        Task<ReferenceDataSetValue<TAltRefData>?> AddAlternateKeyToSet<TAltRefData>(long setId, ReferenceDataSetAlternateKeyDto alternateKeyDefinition);
        Task<ReferenceDataSetValue<TRefData>?> ProcessAlternateKey<TRefData>(long setId, object selectedAlternateKey, params ReferenceDataSetValueDefinitionDto[] newFieldValues);
        Task<List<ReferenceDataSetAlternateKeyDto<TAltRefData>>> GetAlternateSetKeys<TAltRefData>(long setId) where TAltRefData : class, new();
        Task<long> AddValueToSet(IDictionary<string, object> value,
                                 string setName,
                                 string? version);
        Task<long> AddObjectToSet<TRefData>(TRefData value, long setId) where TRefData : class, new();
        Task<long> ImportCsv(string csvFileWithPath,
                             string setName,
                             string? version,
                             string keyFieldName,
                             string? source = null,
                             string? authority = null,
                             Dictionary<string, string>? typeMappings = null);
        Task<bool> ValidateSetValues(params ReferenceDataValidationDto[] values);
        Task DeleteSet(string setName, string? version);
        Task DeleteSet(long setId);
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataClient : WebClient, IWebClient, IReferenceDataClient, IDisposable
    {
        readonly JsonSerializerOptions jsonOptions = new()
        {
            AllowTrailingCommas = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
        };

        /// <summary>
        /// 
        /// </summary>
        /// <param name="httpClient"></param>
        /// <param name="logger"></param>
        /// <exception cref="ArgumentNullException"></exception>
        public ReferenceDataClient(HttpClient httpClient) : base(httpClient)
        {
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="baseUri"></param>
        /// <param name="path"></param>
        /// <param name="queryParams"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        Uri CreateUri(string path, Dictionary<string, string>? queryParams = null)
        {
            if (string.IsNullOrEmpty(BaseUri))
                throw new Exception("No base URI found for reference data service.");

            var uriBuilder = new UriBuilder(BaseUri)
            {
                Path = path
            };

            if (queryParams != null && queryParams.Count > 0)
                uriBuilder.Query = QueryHelpers.AddQueryString(uriBuilder.Query, queryParams);

            return uriBuilder.Uri;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="se"></param>
        /// <param name="version"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<List<ReferenceDataSetDto>> ListSets(string? setName = null, string? version = null, bool fuzzyMatch = true)
        {
            var queryParams = new Dictionary<string, string>();

            if (!string.IsNullOrEmpty(setName))
                queryParams.Add(nameof(setName), setName);

            if (version != null)
                queryParams.Add(nameof(version), version);

            var results = await GetPagedResult<ReferenceDataSetDto>("/api/reference/sets", queryParams).ConfigureAwait(false);

            if (!string.IsNullOrEmpty(setName) && !fuzzyMatch)
                results = results.Where(s => string.Equals(s.Name, setName, StringComparison.OrdinalIgnoreCase)).ToList();

            if (version != null && !fuzzyMatch)
                results = results.Where(s => string.Equals(s.Version ?? string.Empty, version, StringComparison.OrdinalIgnoreCase)).ToList();

            return results;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="values"></param>
        /// <returns></returns>
        public async Task<bool> ValidateSetValues(params ReferenceDataValidationDto[] values)
        {
            if (values is null || values.Length == 0)
                throw new ArgumentException("One or more validation requests are required.", nameof(values));

            var result = await PostJsonAsync(CreateUri("/api/reference/validate"), values).ConfigureAwait(false);

            if (result.IsSuccessStatusCode)
                return true;

            if (result.Content is not null)
            {
                var objResult = await result.Content.ReadFromJsonAsync<ReferenceDataValidationError>(jsonOptions).ConfigureAwait(false);

                if (objResult?.Errors.Count > 0)
                    throw new AggregateException(objResult.Errors.Select(e => new Exception(string.Join(' ', e.Value))));
            }
            throw new Exception("Unknown error while validating reference data: response content was null.");
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setName"></param>
        /// <param name="version"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        /// <exception cref="Exception"></exception>
        public async Task<ReferenceDataSetDto?> GetSet(string setName, string? version)
        {
            if (string.IsNullOrEmpty(setName))
                throw new ArgumentException("Set name is required.", nameof(setName));

            version ??= string.Empty;

            var sets = await ListSets(setName, version, false).ConfigureAwait(false);

            if (sets.Count == 0)
                return null;

            if (sets.Count > 1)
                throw new Exception($"More than one set matched name '{setName}' and version '{version}'");

            return sets[0];
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setId"></param>
        /// <returns></returns>
        public async Task<ReferenceDataSetDto?> GetSet(long setId)
        {
            return await GetJsonAsync<ReferenceDataSetDto>(CreateUri($"/api/reference/sets/{setId}", [])).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TRefData"></typeparam>
        /// <param name="setId"></param>
        /// <returns></returns>
        public async Task<List<ReferenceDataSetValue<TRefData>>> GetSetValues<TRefData>(long setId) where TRefData : class, new()
        {
            var set = await GetSet(setId).ConfigureAwait(false) ?? throw new Exception("Did not find set.");
            var results = await GetPagedResult<ReferenceDataSetValue<TRefData>>($"/api/reference/sets/{setId}/values", []).ConfigureAwait(false);
            results.ForEach(v => { v.SetId = setId; v.SetName = set.Name; });
            return results;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TRefData"></typeparam>
        /// <param name="setName"></param>
        /// <param name="version"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<List<ReferenceDataSetValue<TRefData>>> GetSetValues<TRefData>(string setName, string? version) where TRefData : class, new()
        {
            var set = await GetSet(setName, version ?? string.Empty).ConfigureAwait(false) ?? 
                throw new Exception($"Set not found with name '{setName}' and version '{version}'");

            if (!set.SetId.HasValue)
                return [];

            return await GetSetValues<TRefData>(set.SetId.Value);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TAltRefData"></typeparam>
        /// <param name="setId"></param>
        /// <returns></returns>
        public async Task<List<ReferenceDataSetAlternateKeyDto<TAltRefData>>> GetAlternateSetKeys<TAltRefData>(long setId) where TAltRefData : class, new()
        {
            var results = await GetPagedResult<ReferenceDataSetAlternateKeyDto<TAltRefData>>($"/api/reference/sets/{setId}/alternates", []).ConfigureAwait(false);
            return results;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TRefData"></typeparam>
        /// <param name="setId"></param>
        /// <param name="selectedAlternateKey"></param>
        /// <param name="newFieldValues"></param>
        /// <returns></returns>
        public async Task<ReferenceDataSetValue<TRefData>?> ProcessAlternateKey<TRefData>(long setId, object selectedAlternateKey, params ReferenceDataSetValueDefinitionDto[] newFieldValues)
        {
            var result = await PostJsonAsync(CreateUri($"/api/reference/sets/{setId}/alternates/process", []), 
                                                new { selectedValue = selectedAlternateKey, schema = newFieldValues }).ConfigureAwait(false);

            return await result.Content.ReadFromJsonAsync<ReferenceDataSetValue<TRefData>>(jsonOptions);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TAltRefData"></typeparam>
        /// <param name="setId"></param>
        /// <param name="alternateKeyDefinition"></param>
        /// <returns></returns>
        public async Task<ReferenceDataSetValue<TAltRefData>?> AddAlternateKeyToSet<TAltRefData>(long setId, ReferenceDataSetAlternateKeyDto alternateKeyDefinition)
        {
            var result = await PostJsonAsync(CreateUri($"/api/reference/sets/{setId}/alternates", []),
                                             alternateKeyDefinition)
                              .ConfigureAwait(false);
            return await result.Content.ReadFromJsonAsync<ReferenceDataSetValue<TAltRefData>>(jsonOptions);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TRefData"></typeparam>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ReferenceDataSetValue<TRefData>?> GetSetValueById<TRefData>(long id) where TRefData : class, new()
        {
            return await GetJsonAsync<ReferenceDataSetValue<TRefData>>(CreateUri($"/api/reference/keys/{id}", [])).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TRefData"></typeparam>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<ReferenceDataSetValue<TRefData>?> GetSetValuesByIds<TRefData>(params long[] ids) where TRefData : class, new()
        {
            if (ids.Length == 0) 
                return default;
            return await GetJsonAsync<ReferenceDataSetValue<TRefData>>(CreateUri($"/api/reference/keys?{string.Join('&', ids.Select(i => $"ids={i}"))}", []))
                        .ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setName"></param>
        /// <param name="version"></param>
        /// <param name="keyFieldName"></param>
        /// <param name="items"></param>
        /// <param name="source"></param>
        /// <param name="authority"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        /// <exception cref="Exception"></exception>
        async Task<long> InternalGetOrCreateSet(string setName,
                                                string version,
                                                string keyFieldName,
                                                List<object> items,
                                                string? source = null,
                                                string? authority = null)
        {
            if (string.IsNullOrEmpty(setName))
                throw new ArgumentException("Set name is required.", nameof(setName));

            if (string.IsNullOrEmpty(keyFieldName))
                throw new ArgumentException("Key field name is required.", nameof(setName));

            if (items.Count == 0)
                throw new ArgumentException("Item is required.", nameof(items));

            if (string.IsNullOrEmpty(version))
                version = string.Empty;

            var set = await GetSet(setName, version).ConfigureAwait(false);

            if (set is not null && set.SetId.HasValue)
                return set.SetId.Value;

            var list = new List<List<ReferenceDataSetValueDefinitionDto>>();

            if (items.All(o => o is IList<ReferenceDataSetValueDefinitionDto>))
            {
                list.AddRange([.. items.Cast<List<ReferenceDataSetValueDefinitionDto>>()]);
            }
            else
            {
                var properties = items.First().GetType().GetProperties(BindingFlags.Public |
                                                                       BindingFlags.Instance |
                                                                       BindingFlags.FlattenHierarchy);

                foreach (var item in items)
                {
                    var valueDef = await GetValueSet(item, properties).ConfigureAwait(false);
                    list.Add(valueDef);
                }
            }

            var result = await PostJsonAsync(CreateUri("/api/reference/internal/intake", []),
                                             new { name = setName, version, source, authority, key = keyFieldName, data = list })
                               .ConfigureAwait(false);

            var jobResult = await result.Content.ReadFromJsonAsync<ReferenceDataImportJobDto>(jsonOptions) ??
                throw new Exception("Could not import reference data set.");

            return await AwaitDataImportJob(jobResult.JobId, GetTimeout(list.Count * 256));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dataSizeBytes"></param>
        /// <param name="baseTimeoutSecs"></param>
        /// <param name="maxTimeoutSecs"></param>
        /// <returns></returns>
        int GetTimeout(int dataSizeBytes, int baseTimeoutSecs = 5, int maxTimeoutSecs = 240)
        {
            int baseTimeoutMilliseconds = baseTimeoutSecs * 1000;

            // Assume 2sec per KB of data
            double scalingFactorMillisecondsPerKB = 2000;

            // Convert to KB
            double dataSizeKB = dataSizeBytes / 1024.0;

            // Linear scaling of timeout
            int timeoutMilliseconds = (int)(baseTimeoutMilliseconds + (dataSizeKB * scalingFactorMillisecondsPerKB));

            int bufferMilliseconds = 2000;
            int totalTimeoutMilliseconds = timeoutMilliseconds + bufferMilliseconds;

            int maxTimeoutMilliseconds = maxTimeoutSecs * 1000;
            return Math.Min(totalTimeoutMilliseconds, maxTimeoutMilliseconds) / 1000;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="csvFileWithPath"></param>
        /// <param name="setName"></param>
        /// <param name="version"></param>
        /// <param name="keyFieldName"></param>
        /// <param name="source"></param>
        /// <param name="authority"></param>
        /// <param name="typeMappings"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        /// <exception cref="FileNotFoundException"></exception>
        /// <exception cref="Exception"></exception>
        public async Task<long> ImportCsv(string csvFileWithPath,
                                          string setName,
                                          string? version,
                                          string keyFieldName,
                                          string? source = null,
                                          string? authority = null,
                                          Dictionary<string, string>? typeMappings = null)
        {
            if (string.IsNullOrEmpty(setName))
                throw new ArgumentException("Set name is required.", nameof(setName));

            if (string.IsNullOrEmpty(keyFieldName))
                throw new ArgumentException("Set key field name is required.", nameof(keyFieldName));

            if (string.IsNullOrEmpty(csvFileWithPath))
                throw new ArgumentException("CSV file path is required.", nameof(csvFileWithPath));

            csvFileWithPath = Path.GetFullPath(csvFileWithPath);

            var fi = new FileInfo(csvFileWithPath);

            if (!fi.Exists)
                throw new FileNotFoundException($"CSV file '{csvFileWithPath}' does not exist.");

            if (string.IsNullOrEmpty(version))
                version = string.Empty;

            using var stream = fi.OpenRead();
            using var reader = new StreamReader(stream);
            var csvContents = reader.ReadToEnd();

            if (string.IsNullOrEmpty(csvContents))
                throw new ArgumentException("CSV file is empty.", nameof(csvFileWithPath));

            var csvBytes = Encoding.UTF8.GetBytes(csvContents);

            const string setKey = nameof(setKey);

            var byteArrayContent = new ByteArrayContent(csvBytes, 0, csvBytes.Length);
            byteArrayContent.Headers.ContentType = new MediaTypeHeaderValue(MediaTypeNames.Text.Csv);

            var formData = new MultipartFormDataContent
                {
                    { byteArrayContent, "csvFile", fi.Name },
                };

            var additionalParameters = new Dictionary<string, string>
            {
                { nameof(setName), setName },
                { nameof(version), version },
                { setKey, keyFieldName },
            };

            if (!string.IsNullOrEmpty(source))
                additionalParameters.Add(nameof(source), source);

            if (!string.IsNullOrEmpty(authority))
                additionalParameters.Add(nameof(authority), authority);

            foreach (var typeMapping in typeMappings ?? [])
                additionalParameters.Add(typeMapping.Key, typeMapping.Value);

            foreach (var parameter in additionalParameters)
                formData.Add(new StringContent(parameter.Value), parameter.Key);

            var response = await PostContentAsync(CreateUri("/api/reference/internal/importCsv", []), formData);
            var jobResult = await response.Content.ReadFromJsonAsync<ReferenceDataImportJobDto>(jsonOptions).ConfigureAwait(false) ??
                            throw new Exception("Could not import reference data CSV.");

            return await AwaitDataImportJob(jobResult.JobId, GetTimeout(csvBytes.Length));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="jobId"></param>
        /// <param name="timeoutSeconds"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        /// <exception cref="Exception"></exception>
        async Task<long> AwaitDataImportJob(Guid jobId, int timeoutSeconds = 240, int delaySeconds = 5)
        {
            if (jobId == Guid.Empty)
                throw new ArgumentException("Import job ID is invalid.", nameof(jobId));

            if (timeoutSeconds < 0)
                timeoutSeconds = 0;

            if (delaySeconds < 0)
                delaySeconds = 0;

            if (delaySeconds > timeoutSeconds)
                delaySeconds = delaySeconds - timeoutSeconds;

            var timeout = TimeSpan.FromSeconds(timeoutSeconds);
            var jobUri = CreateUri($"/api/reference/jobs/{jobId}", []);
            var startingTime = DateTime.UtcNow;
            bool complete;
            long? setId;
            string? error;

            do
            {
                var jobResult = await GetJsonAsync<ReferenceDataImportJobDto>(jobUri).ConfigureAwait(false);
                complete = jobResult?.IsComplete ?? false;
                error = jobResult?.Error;
                setId = jobResult?.Set?.SetId;

                if (!complete)
                    await Task.Delay(delaySeconds * 1000).ConfigureAwait(false);

            } while (!complete && (DateTime.UtcNow - startingTime) < timeout);

            if (!complete)
                throw new Exception($"Reference data import did not complete in {timeoutSeconds} second(s).");

            if (complete && !string.IsNullOrEmpty(error))
                throw new Exception($"Error creating reference data set: {error}");

            return setId.HasValue && setId.Value > 0 ? setId.Value : throw new Exception("Unknown error while importing reference data.");
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TRefData"></typeparam>
        /// <param name="items"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<long> RemoveAndCreateSet<TRefData>(List<TRefData> items) where TRefData : class, new()
        {
            var refDataInfo = typeof(TRefData).GetReferenceDataSetDetail() ?? 
                throw new Exception($"No reference data set definition found for type '{typeof(TRefData).Name}'.");

            var keyValue = typeof(TRefData).GetReferenceDataSetKey();

            if (string.IsNullOrEmpty(keyValue))
                throw new Exception($"No reference data key found for type '{typeof(TRefData).Name}'.");

            await DeleteSet(refDataInfo.Name, refDataInfo.Version).ConfigureAwait(false);

            return await InternalGetOrCreateSet(refDataInfo.Name, refDataInfo.Version ?? string.Empty, keyValue, 
                                        [.. items.Cast<object>()], refDataInfo.Source, refDataInfo.Authority).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="type"></param>
        /// <param name="items"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<long> RemoveAndCreateSet(Type type, params object[] items)
        {
            var refDataInfo = type.GetReferenceDataSetDetail() ??
                throw new Exception($"No reference data set definition found for type '{type.Name}'.");

            var keyValue = type.GetReferenceDataSetKey();

            if (string.IsNullOrEmpty(keyValue))
                throw new Exception($"No reference data key found for type '{type.Name}'.");

            await DeleteSet(refDataInfo.Name, refDataInfo.Version).ConfigureAwait(false);

            return await InternalGetOrCreateSet(refDataInfo.Name, refDataInfo.Version ?? string.Empty, keyValue,
                                                [.. items], refDataInfo.Source, refDataInfo.Authority).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setName"></param>
        /// <param name="version"></param>
        /// <param name="keyName"></param>
        /// <param name="items"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<long> GetOrCreateSet(string setName,
                                               string? version,
                                               string keyName,
                                               string? source,
                                               string? authority,
                                               params object[] items)
        {
            if (string.IsNullOrEmpty(setName))
                throw new ArgumentException($"Set name is required.", nameof(setName));

            if (string.IsNullOrEmpty(keyName))
                throw new ArgumentException($"Key is required.", nameof(keyName));

            if (items is null || items.Length == 0)
                throw new ArgumentException("One or more items are required.", nameof(items));

            var dtos = items.Select(async i => await GetValueSet(i));

            return await InternalGetOrCreateSet(setName, version ?? string.Empty, keyName,
                                                [.. items], source, authority).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TRefData"></typeparam>
        /// <param name="value"></param>
        /// <param name="setName"></param>
        /// <param name="version"></param>
        /// <returns></returns>
        public async Task<long> AddObjectToSet<TRefData>(TRefData value,
                                                        string setName, 
                                                        string? version) where TRefData : class, new()
        {
            var set = await GetSet(setName, version) ?? throw new Exception("No matching sets found.");
            return await AddObjectToSet<TRefData>(value, set.SetId ?? 0);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TRefData"></typeparam>
        /// <param name="setId"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public async Task<long> AddObjectToSet<TRefData>(TRefData value, long setId) where TRefData : class, new()
        {
            var data = await PostJsonAsync(CreateUri($"/api/reference/sets/{setId}/values"),
                                           await GetValueSet(value)).ConfigureAwait(false);
            var result = await data.Content.ReadFromJsonAsync<ReferenceDataSetValue<TRefData>>(jsonOptions);
            return result?.Id ?? 0;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="value"></param>
        /// <param name="setId"></param>
        /// <returns></returns>
        public async Task<long> AddObjectToSet(object value, long setId)
        {
            var data = await PostJsonAsync(CreateUri($"/api/reference/sets/{setId}/values"),
                                           await GetValueSet(value)).ConfigureAwait(false);
            var result = await data.Content.ReadFromJsonAsync<ReferenceDataSetValue>(jsonOptions);
            return result?.Id ?? 0;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="value"></param>
        /// <param name="setName"></param>
        /// <param name="version"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<long> AddObjectToSet(object value,
                                               string setName,
                                               string? version)
        {
            var set = await GetSet(setName, version) ?? throw new Exception("No matching sets found.");
            return await AddObjectToSet(value, set.SetId ?? 0);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TRefData"></typeparam>
        /// <param name="value"></param>
        /// <param name="setName"></param>
        /// <param name="version"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<long> AddValueToSet(IDictionary<string, object> value, long setId)
        {
            var list = new List<ReferenceDataSetValueDefinitionDto>();
            foreach (var kvp in value)
                list.Add(new ReferenceDataSetValueDefinitionDto()
                {
                    Name = kvp.Key,
                    Value = kvp.Value,
                });

            var data = await PostJsonAsync(CreateUri($"/api/reference/sets/{setId}/values"),
                                           list).ConfigureAwait(false);
            var result = await data.Content.ReadFromJsonAsync<ReferenceDataSetValue>(jsonOptions);
            return result?.Id ?? 0;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setName"></param>
        /// <param name="version"></param>
        /// <returns></returns>
        public async Task DeleteSet(string setName, string? version)
        {
            await DeleteAsync(CreateUri("/api/reference/sets", new() { { nameof(setName), setName }, { nameof(version), version ?? string.Empty }})).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setId"></param>
        /// <returns></returns>
        public async Task DeleteSet(long setId)
        {
            await DeleteAsync(CreateUri($"/api/reference/sets/{setId}")).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setId"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public async Task<long> AddValueToSet(IDictionary<string, object> value,
                                              string setName,
                                              string? version)
        {
            var set = await GetSet(setName, version) ?? throw new Exception("No matching sets found.");
            return await AddValueToSet(value, set.SetId ?? 0);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="data"></param>
        /// <param name="properties"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        async Task<List<ReferenceDataSetValueDefinitionDto>> GetValueSet(object data, params PropertyInfo[] properties)
        {
            if (data is null)
                throw new ArgumentNullException(nameof(data), "Cannot generate reference data set value: input object was null.");

            if (properties is null || properties.Length == 0)
                properties = data.GetType().GetProperties(BindingFlags.Public |
                                                          BindingFlags.Instance |
                                                          BindingFlags.FlattenHierarchy);

            if (properties.Length == 0)
                return [];

            var localList = new List<ReferenceDataSetValueDefinitionDto>();

            foreach (var property in properties ?? [])
            {
                if (property.ReferenceDataWillIgnore())
                    continue;

                var items = new List<ReferenceDataSetValueDefinitionDto>();

                var value = property.GetValue(data);
                if (value is not null)
                {
                    ReferenceDataSetMappingDto? inSet = null;
                    var externalSetMapping = property.GetReferenceDataExternalSetDetail();
                    if (externalSetMapping != null)
                    {
                        var extSet = await GetSet(externalSetMapping.SetName, externalSetMapping.Version).ConfigureAwait(false) ??
                            throw new Exception($"External set '{externalSetMapping.SetName}' not found with version '{externalSetMapping.Version}'.");
                        inSet = new ReferenceDataSetMappingDto()
                        {
                            SetId = extSet.SetId ?? 0,
                        };
                    }

                    var cleanName = property.Name.CleanName();

                    if (!property.PropertyType.IsArray || !typeof(IEnumerable).IsAssignableFrom(property.PropertyType) || typeof(string) == property.PropertyType)
                    {
                        items.Add(new ReferenceDataSetValueDefinitionDto()
                        {
                            Name = cleanName,
                            Value = value,
                            InSet = inSet
                        });
                    }
                    else if (property.PropertyType.IsArray || typeof(IEnumerable).IsAssignableFrom(property.PropertyType))
                    {
                        if (value is IEnumerable enu)
                        {
                            foreach (var i in enu)
                            {
                                items.Add(new ReferenceDataSetValueDefinitionDto()
                                {
                                    Name = cleanName,
                                    Value = i,
                                    InSet = inSet
                                });
                            }
                        }    
                    }
                }

                if (items.Count > 0)
                    localList.AddRange(items);
            }
            return localList;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="uriPath"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        async Task<List<T>> GetPagedResult<T>(string uriPath, Dictionary<string, string>? query = null) where T : class, new()
        {
            var list = new List<T>();
            query ??= [];

            var limit = 250;
            var offset = 0;

            if (!query.TryGetValue(nameof(limit), out _))
                query.Add(nameof(limit), limit.ToString());

            if (!query.TryGetValue(nameof(offset), out _))
                query.Add(nameof(offset), offset.ToString());

            var totalCount = 0;
            var uri = CreateUri(uriPath, query);

            do
            {
                var result = await GetJsonAsync<ReferenceDataPagedResult<T>>(uri).ConfigureAwait(false);

                if (result != null)
                {
                    totalCount = result.TotalCount;
                    list.AddRange(result.Items);

                    if (!string.IsNullOrEmpty(result.Next))
                        uri = new Uri(result.Next);
                    else
                        uri = null;

                }
            } while (list.Count < totalCount && uri != null);

            return list;
        }

        /// <summary>
        /// 
        /// </summary>
        ~ReferenceDataClient()
        {
            Dispose(false);
        }
    }
}
