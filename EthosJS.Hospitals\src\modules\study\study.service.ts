import { <PERSON>Null } from 'typeorm';
import { BadRequestException, Injectable } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional-cls-hooked';
import { IListResult } from '@app/common/types';
import {
  StudyRepository,
  StudyCredentialRepository,
} from '@app/modules/study/study.repository';
import { StudyFiltersDto } from '@app/modules/study/dto/study.filters.dto';
import { StudyEntity } from '@app/modules/study/study.entity';
import { CreateStudyDto } from '@app/modules/study/dto/create.study.dto';
import { UpdateStudyDto } from '@app/modules/study/dto/update.study.dto';
import { EquipmentService } from '@app/modules/equipment/equipment.service';
import { StudyDto } from '@app/modules/study/dto/study.dto';
import { StudyEquipmentDto } from '@app/modules/study/dto/study.equipment.dto';
import { StudyCredentialDto, StudyCredentialItemDto } from '@app/modules/study/dto/study.credential.dto';
import { CredentialService } from '@app/modules/credential/credential.service';
import { StudyCredentialEntity } from '@app/modules/study/study.credential.entity';
import { CredentialEntity } from '@app/modules/credential/credential.entity';
import { IStudyEquipment } from '@app/modules/study/types';
import { CreateStudyEquipmentDto } from '@app/modules/study/dto/create.study.equipment.dto';

@Injectable()
export class StudyService {
  constructor(
    private readonly studyRepository: StudyRepository,
    private readonly studyCredentialRepository: StudyCredentialRepository,
    private readonly equipmentService: EquipmentService,
    private readonly credentialService: CredentialService,
  ) {}

  async list(filters: StudyFiltersDto): Promise<IListResult<StudyEntity>> {
    return this.studyRepository.list(filters);
  }

  async getAll(): Promise<StudyEntity[]> {
    return this.studyRepository.find({
      relations: ['credentials'],
    });
  }

  async getByIdOrFail(studyId: number): Promise<[StudyEntity, CredentialEntity[]]> {
    const study = await this.studyRepository.findOne({
      where: {
        id: studyId,
      },
      relations: ['credentials', 'credentials.state'],
    });

    if (!study) {
      throw new BadRequestException(`Study ${studyId} not found`);
    }
    const credentialIds: number[] = [];
    study.credentials.forEach(({ credentials }) => credentialIds.push(...credentials));
    const credentials = await this.credentialService.getByIds(credentialIds);

    return [study, credentials];
  }

  @Transactional()
  async create({ equipments, ...study }: CreateStudyDto): Promise<[StudyEntity, CredentialEntity[]]> {
    if (study.credentials?.length) {
      const credentialIds: number[] = [];
      study.credentials.forEach(({ credentials }) => credentialIds.push(...credentials));
      await this.credentialService.checkExistence(credentialIds);
    }

    const entity = this.studyRepository.create(study);

    if (equipments) {
      entity.equipments = await this.getEquipments(equipments);
    }

    const created = await this.studyRepository.save(entity);

    if (study.credentials?.length) {
      const studyCredentials = this.studyCredentialRepository.create(study.credentials.map((item) => ({ ...item, studyId: created.id })));
      await this.studyCredentialRepository.save(studyCredentials);
    }

    return this.getByIdOrFail(created.id);
  }

  @Transactional()
  async update({ id, credentials, equipments, ...update }: UpdateStudyDto): Promise<[StudyEntity, CredentialEntity[]]> {
    if (credentials?.length) {
      const credentialIds: number[] = [];
      credentials.forEach(({ credentials }) => credentialIds.push(...credentials));
      await this.credentialService.checkExistence(credentialIds);
    }

    const study = await this.studyRepository.findOne({ where: { id } });

    if (!study) {
      throw new BadRequestException(`Study ${id} not found`);
    }

    const entity = this.studyRepository.merge(study, update);

    if (equipments) {
      entity.equipments = await this.getEquipments(equipments);
    }

    if (credentials) {
      await this.studyCredentialRepository.delete({ studyId: id });

      if (credentials.length) {
        const studyCredentials = this.studyCredentialRepository.create(credentials.map((item) => ({ ...item, studyId: entity.id })));
        await this.studyCredentialRepository.save(studyCredentials);
      }
    }

    await this.studyRepository.save(entity);

    if (update.name) {
      await this.studyRepository.updateStudyName(entity.id, update.name);
    }

    return this.getByIdOrFail(study.id);
  }

  async getCredentialsForStudy(studyId: number, stateId: number): Promise<StudyCredentialEntity[]> {
    let studyCredentials = await this.studyCredentialRepository.find({
      where: {
        studyId,
        stateId,
      },
    });

    if (!studyCredentials.length) {
      studyCredentials = await this.studyCredentialRepository.find({
        where: {
          studyId,
          stateId: IsNull(),
        },
      });
    }

    return studyCredentials;
  }

  @Transactional()
  async delete(studyId: number): Promise<[StudyEntity, CredentialEntity[]]> {
    const [study, credentials] = await this.getByIdOrFail(studyId);

    await this.studyRepository.checkHasSchedules(studyId);
    await this.studyRepository.softRemove(study);

    return [study, credentials];
  }

  private async getEquipments(equipments: CreateStudyEquipmentDto[]): Promise<Record<number, IStudyEquipment>> {
    if (!equipments.length) {
      return {};
    }

    const equipmentIds = equipments.map(({ equipmentId }) => equipmentId);
    const existingEquipmentsMap = await this.equipmentService.checkExistence(equipmentIds);

    return equipments.reduce((acc, item) => {
      const equipment = existingEquipmentsMap[item.equipmentId];

      acc[item.equipmentId] = {
        count: item.count,
        equipmentId: item.equipmentId,
        equipmentName: equipment.name,
      };

      return acc;
    }, {} as Record<number, IStudyEquipment>);
  }

  static mapToDto(entity: StudyEntity, credentials: CredentialEntity[]): StudyDto {
    const credObj = credentials.reduce((acc, item) => {
      acc[item.id] = item;

      return acc;
    }, {} as Record<number, CredentialEntity>);

    const dto = new StudyDto();
    dto.id = entity.id;
    dto.createdAt = entity.createdAt;
    dto.updatedAt = entity.updatedAt;
    dto.name = entity.name;
    dto.equipments = Object.values(entity.equipments).map(({ equipmentId, equipmentName, count }) => {
      const dto = new StudyEquipmentDto();

      dto.studyId = entity.id;
      dto.equipmentId = Number(equipmentId);
      dto.equipmentName = equipmentName;
      dto.count = count;

      return dto;
    });

    if (entity.credentials?.length) {
      dto.credentials = entity.credentials.map((credential) => {
        const dto = new StudyCredentialDto();

        dto.stateId = credential.stateId;
        dto.stateName = credential?.state?.name;
        dto.credentials = credential.credentials.map((credential) => {
          const dto = new StudyCredentialItemDto();
          dto.credentialId = credential;

          const cred = credObj[credential];
          dto.credentialName = cred?.name;
          dto.credentialCode = cred?.code;

          return dto;
        });

        return dto;
      });
    }

    return dto;
  }
}
