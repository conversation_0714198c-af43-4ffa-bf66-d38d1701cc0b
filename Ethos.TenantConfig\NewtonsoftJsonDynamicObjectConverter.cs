﻿using Newtonsoft.Json;

namespace Ethos.TenantConfig
{
    /// <summary>
    /// 
    /// </summary>
    public class NewtonsoftJsonDynamicObjectConverter : JsonConverter
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="reader"></param>
        /// <param name="objectType"></param>
        /// <param name="existingValue"></param>
        /// <param name="serializer"></param>
        /// <returns></returns>
        /// <exception cref="JsonException"></exception>
        public override object? ReadJson(JsonReader reader, Type objectType, object? existingValue, JsonSerializer serializer)
        {
            switch (reader.TokenType)
            {
                case JsonToken.String:
                    return reader.Value?.ToString();
                case JsonToken.Integer:
                    return Convert.ToInt64(reader.Value);
                case JsonToken.Float:
                    return Convert.ToDouble(reader.Value);
                case JsonToken.Boolean:
                    return Convert.ToBoolean(reader.Value);
                case JsonToken.Null:
                    return null;
                case JsonToken.StartObject:
                    return ReadDictionary(reader, serializer);
                case JsonToken.StartArray:
                    return ReadList(reader, serializer);
                default:
                    throw new JsonException($"Unexpected token type {reader.TokenType}");
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="reader"></param>
        /// <param name="serializer"></param>
        /// <returns></returns>
        /// <exception cref="JsonException"></exception>
        private Dictionary<string, object?> ReadDictionary(JsonReader reader, JsonSerializer serializer)
        {
            var dictionary = new Dictionary<string, object?>();

            while (reader.Read())
            {
                if (reader.TokenType == JsonToken.EndObject)
                {
                    return dictionary;
                }

                if (reader.TokenType != JsonToken.PropertyName)
                {
                    throw new JsonException($"Expected property name, but got {reader.TokenType}");
                }

                var propertyName = reader.Value?.ToString();
                if (propertyName == null)
                    continue;

                reader.Read();
                dictionary[propertyName] = ReadJson(reader, typeof(object), null, serializer);
            }

            throw new JsonException("Expected end object");
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="reader"></param>
        /// <param name="serializer"></param>
        /// <returns></returns>
        private List<object?> ReadList(JsonReader reader, JsonSerializer serializer)
        {
            var list = new List<object?>();

            while (reader.Read() && reader.TokenType != JsonToken.EndArray)
            {
                list.Add(ReadJson(reader, typeof(object), null, serializer));
            }

            return list;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="writer"></param>
        /// <param name="value"></param>
        /// <param name="serializer"></param>
        public override void WriteJson(JsonWriter writer, object? value, JsonSerializer serializer)
        {
            if (value == null)
            {
                writer.WriteNull();
            }
            else if (value is string stringValue)
            {
                writer.WriteValue(stringValue);
            }
            else if (value is long longValue)
            {
                writer.WriteValue(longValue);
            }
            else if (value is double doubleValue)
            {
                writer.WriteValue(doubleValue);
            }
            else if (value is bool boolValue)
            {
                writer.WriteValue(boolValue);
            }
            else if (value is Dictionary<string, object?> dictionaryValue)
            {
                WriteDictionary(writer, dictionaryValue, serializer);
            }
            else if (value is List<object?> listValue)
            {
                WriteList(writer, listValue, serializer);
            }
            else
            {
                serializer.Serialize(writer, value);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="writer"></param>
        /// <param name="value"></param>
        /// <param name="serializer"></param>
        private void WriteDictionary(JsonWriter writer, Dictionary<string, object?> value, JsonSerializer serializer)
        {
            writer.WriteStartObject();

            foreach (var kvp in value)
            {
                writer.WritePropertyName(kvp.Key);
                WriteJson(writer, kvp.Value, serializer);
            }

            writer.WriteEndObject();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="writer"></param>
        /// <param name="value"></param>
        /// <param name="serializer"></param>
        private void WriteList(JsonWriter writer, List<object?> value, JsonSerializer serializer)
        {
            writer.WriteStartArray();
            foreach (var item in value)
            {
                WriteJson(writer, item, serializer);
            }
            writer.WriteEndArray();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="objectType"></param>
        /// <returns></returns>
        public override bool CanConvert(Type objectType)
        {
            return objectType == typeof(object);
        }
    }
}


