import _ from 'lodash';
import { Transactional } from 'typeorm-transactional-cls-hooked';
import { BadRequestException, forwardRef, Inject, Injectable } from '@nestjs/common';
import { IListResult } from '@app/common/types';
import { FacilityFiltersDto } from '@app/modules/facility/dto/facility.filters.dto';
import { FacilityRepository } from '@app/modules/facility/facility.repository';
import { FacilityEntity } from '@app/modules/facility/facility.entity';
import { CreateFacilityDto } from '@app/modules/facility/dto/create.facility.dto';
import { UpdateFacilityDto } from '@app/modules/facility/dto/update.facility.dto';
import { ClinicService } from '@app/modules/clinic/clinic.service';
import { CityService } from '@app/modules/city/city.service';
import { EquipmentService } from '@app/modules/equipment/equipment.service';
import { BedScheduleService } from '@app/modules/bedSchedule/bed.schedule.service';
import { FacilityDto } from '@app/modules/facility/dto/facility.dto';
import { FacilityEquipmentDto } from '@app/modules/facility/dto/facility.equipment.dto';
import { IFacilityEquipment } from '@app/modules/facility/types';
import { CreateFacilityEquipmentDto } from '@app/modules/facility/dto/create.facility.equipment.dto';
import { ScheduleDto } from '@app/modules/schedule/dto/schedule.dto';
import { GetConflictsFacilityDto } from '@app/modules/facility/dto/getConflicts.facility.dto';
import { ISchedule } from '@app/modules/schedule/types';
import { mapToScheduleDto } from '@app/modules/schedule/helpers';

@Injectable()
export class FacilityService {
  constructor(
    private readonly repository: FacilityRepository,
    private readonly clinicService: ClinicService,
    private readonly cityService: CityService,
    private readonly equipmentService: EquipmentService,
    @Inject(forwardRef(() => BedScheduleService)) private readonly bedScheduleService: BedScheduleService,
  ) {}

  async list(filters: FacilityFiltersDto): Promise<IListResult<FacilityDto>> {
    const { data, count } = await this.repository.list(filters);

    return {
      data: data.map((item) => FacilityService.mapToDto(item)),
      count,
    };
  }

  async getByIdOrFail(facilityId: number): Promise<FacilityEntity> {
    const facility = await this.repository.findOne({
      where: {
        id: facilityId,
      },
      relations: ['city', 'clinic'],
    });

    if (!facility) {
      throw new BadRequestException(`Facility ${facilityId} is not found`);
    }

    return facility;
  }

  @Transactional()
  async create({ equipments, ...facility }: CreateFacilityDto): Promise<FacilityEntity> {
    await this.clinicService.getByIdOrFail(facility.clinicId);
    await this.cityService.getByIdOrFail(facility.cityId);

    const entity = this.repository.create(facility);

    if (equipments) {
      entity.equipments = await this.getEquipments(equipments);
    }

    const created = await this.repository.save(entity);
    return this.getByIdOrFail(created.id);
  }

  @Transactional()
  async update({ id, ...update }: UpdateFacilityDto): Promise<FacilityEntity> {
    const facility = await this.getByIdOrFail(id);

    if (update.cityId) {
      const city = await this.cityService.getByIdOrFail(update.cityId);

      if (city.stateId !== facility.city?.stateId) {
        throw new BadRequestException('City must be from the same state');
      }

      facility.city = city;
    }

    const conflicts = await this.getConflicts({ id, ...update });

    if (conflicts.length) {
      throw new BadRequestException(`${conflicts.length} conflicts in schedule. Check it through special method`);
    }

    const entity = this.repository.merge(facility, update);

    if (update.equipments) {
      const equipmentsToAdd = update.equipments.filter((item) => !facility.equipments[item.equipmentId]);
      const equipmentsToAddMap = await this.getEquipments(equipmentsToAdd);

      await this.bedScheduleService.addEquipmentToSchedules(entity.id, equipmentsToAddMap);
      entity.equipments = await this.getEquipments(update.equipments);
    }

    if (update.capacity) {
      await this.repository.updateSchedulesCapacity(facility.id, update.capacity);
    }

    if (update.name) {
      await this.repository.updateFacilityName(facility.id, update.name);
    }

    await entity.save();

    return this.getByIdOrFail(entity.id);
  }

  async getConflicts({ id, equipments, capacity }: GetConflictsFacilityDto, facility?: FacilityEntity): Promise<ScheduleDto[]> {
    if (!facility) {
      facility = await this.getByIdOrFail(id);
    }
    const conflicts: ISchedule[] = [];
    const schedules = await this.repository.getFacilitySchedules(facility.id);
    const schedulesByDates = schedules.reduce((acc, item) => {
      if (!acc[item.date]) {
        acc[item.date] = [];
      }
      acc[item.date].push(item);

      return acc;
    }, {} as Record<string, ISchedule[]>);

    if (capacity && capacity < facility.capacity) {
      Object.values(schedulesByDates).forEach((item) => {
        if (schedules.length > capacity) {
          conflicts.push(...item);
        }
      });
    }

    if (equipments) {
      const newEquipments = await this.getEquipments(equipments);

      Object.values(schedulesByDates).forEach((item) => {
        const itemsWithEquipments = item.filter((schedule) => !_.isEmpty(schedule.equipments));
        const scheduledEquipments = itemsWithEquipments.reduce((acc, schedule) => {
          Object.values(schedule.equipments).forEach(({ equipmentId, count }) => {
            if (!acc[equipmentId]) {
              acc[equipmentId] = 0;
            }
            acc[equipmentId] += count;
          });

          return acc;
        }, {} as Record<string, number>);

        let hasConflicts = false;

        Object.entries(scheduledEquipments).forEach(([equipmentId, count]) => {
          const equipment = newEquipments[equipmentId as any];

          if (!equipment || equipment.count < count) {
            hasConflicts = true;
          }
        });

        if (hasConflicts) {
          conflicts.push(...itemsWithEquipments);
        }
      });
    }

    return conflicts.map(mapToScheduleDto);
  }

  @Transactional()
  async delete(facilityId: number): Promise<FacilityEntity> {
    const facility = await this.getByIdOrFail(facilityId);

    await this.repository.checkHasSchedules(facilityId);
    await facility.softRemove();
    await this.repository.softRemoveRelations(facilityId);

    return facility;
  }

  private async getEquipments(equipments: CreateFacilityEquipmentDto[]): Promise<Record<number, IFacilityEquipment>> {
    if (!equipments.length) {
      return {};
    }
    const equipmentIds = equipments.map(({ equipmentId }) => equipmentId);
    const equipmentsMap = await this.equipmentService.checkExistence(equipmentIds);

    return equipments.reduce((acc, item) => {
      const equipment = equipmentsMap[item.equipmentId];

      acc[item.equipmentId] = {
        count: item.count,
        equipmentId: item.equipmentId,
        equipmentName: equipment?.name,
      };

      return acc;
    }, {} as Record<number, IFacilityEquipment>);
  }

  static mapToDto(entity: FacilityEntity): FacilityDto {
    const dto = new FacilityDto();

    dto.id = entity.id;
    dto.createdAt = entity.createdAt;
    dto.updatedAt = entity.updatedAt;
    dto.name = entity.name;
    dto.addressLine1 = entity.addressLine1;
    dto.addressLine2 = entity.addressLine2;
    dto.zip = entity.zip;
    dto.phone = entity.phone;
    dto.fax = entity.fax;
    dto.capacity = entity.capacity;
    dto.cityId = entity.cityId;
    dto.cityName = entity.city.name;
    dto.clinicId = entity.clinicId;
    dto.clinicName = entity.clinic.name;
    dto.equipments = Object.values(entity.equipments).map(({ count, equipmentName, equipmentId }) => {
      const dto = new FacilityEquipmentDto();

      dto.equipmentId = Number(equipmentId);
      dto.equipmentName = equipmentName;
      dto.facilityId = entity.id;
      dto.count = count;

      return dto;
    });

    return dto;
  }
}
