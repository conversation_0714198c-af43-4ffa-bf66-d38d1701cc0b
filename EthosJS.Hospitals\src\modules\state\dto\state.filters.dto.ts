import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { BaseFiltersDto } from '@app/common/dto/base.filters.dto';
import { EStateSort } from '@app/modules/state/enums';

export class StateFiltersDto extends BaseFiltersDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ enum: EStateSort })
  @IsOptional()
  @IsEnum(EStateSort)
  orderField: EStateSort = EStateSort.CreatedAt;
}
