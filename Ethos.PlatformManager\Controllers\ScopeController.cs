using Ethos.Auth;
using Ethos.Events.Client;
using Ethos.Utilities.Filtering;
using Ethos.Utilities.Pagination;
using Ethos.Utilities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.PlatformManager.Controllers
{
    [ApiController]
    [Route("api/scopes")]
    [Authorize]
    [EthosAuthFeature(Name = "Core")]
    public class ScopeController : ControllerBase
    {
        readonly ILogger<ScopeController> logger;
        readonly IServiceScopeFactory scopeFactory;
        readonly PlatformManagerDbContext dbContext;
        readonly IConfiguration configuration;
        readonly IEthosEventClient eventClient;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="scopeFactory"></param>
        public ScopeController(ILogger<ScopeController> logger,
                              IServiceScopeFactory scopeFactory,
                              PlatformManagerDbContext dbContext,
                              IConfiguration configuration,
                              IEthosEventClient eventClient)
        {
            this.logger = logger;
            this.scopeFactory = scopeFactory;
            this.dbContext = dbContext;
            this.configuration = configuration;
            this.eventClient = eventClient;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [EthosAuthScope(ScopeDefinitions.ScopeWrite)]
        public async Task<IActionResult> CreateScope([FromBody] List<EthosScopeDto> scopes)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(CreateScope), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (scopes is null || scopes.Count < 1)
                    return this.EthosErrorBadRequest("At least one scope is required.");

                var messages = new List<object>();
                bool hasError = false;

                using var trans = dbContext.Database.BeginTransaction();

                try
                {
                    foreach (var scope in scopes)
                    {
                        if (scope is null)
                            continue;

                        if (string.IsNullOrEmpty(scope.Name))
                        {
                            logger.LogWarning("Scope name is required.");
                            messages.Add(new { scope = "Unknown", message = "Scope name is required." });
                            hasError = true;
                            continue;
                        }

                        if (!EthosScope.TryParse(scope.Name, null, out var _scope) || !_scope.IsFullyQualified())
                        {
                            logger.LogWarning("Skipping invalid scope: {Scope}", scope);
                            messages.Add(new { scope = scope.Name, message = "Invalid scope." });
                            hasError = true;
                            continue;
                        }

                        var hasScopeAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.ScopeAdmin);

                        if (scope.Privileged && !scope.Assignable && !hasScopeAdmin)
                        {
                            logger.LogWarning("Current user does not have permission to create globally privileged scope: {Scope}", scope);
                            messages.Add(new { scope = scope.Name, message = "User does not have permission to create privileged scopes." });
                            hasError = true;
                            continue;
                        }

                        if (!hasScopeAdmin && !scope.Assignable)
                        {
                            logger.LogInformation("Current user cannot create non-assignable scope: {Scope}", scope);
                            messages.Add(new { scope = _scope.ToString(), message = "Current user cannot create non-assignable scope: setting assignable flag to true." });
                            hasError = true;
                            scope.Assignable = true;
                        }

                        var scopeExists = dbContext.Scopes.FirstOrDefault(s => Equals(s.Name.ToLower(), _scope.ToString().ToLower())) != null;

                        if (scopeExists)
                        {
                            logger.LogInformation("Scope {Scope} already exists", scope);
                            messages.Add(new { scope = _scope.ToString(), message = "Scope already exists." });
                            hasError = true;
                            continue;
                        }

                        dbContext.Scopes.Add(new EthosDbScope()
                        {
                            Name = _scope.ToString(),
                            Description = scope.Description,
                            Assignable = scope.Assignable,
                            Privileged = scope.Privileged,
                        });

                        await dbContext.SaveChangesAsync();

                        messages.Add(new { scope = _scope.ToString(), message = "Successfully created scope." });
                    }

                    await trans.CommitAsync();
                    return !hasError ? Ok(messages) : StatusCode(207, messages);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error creating scope(s)");
                    await trans.RollbackAsync();
                    await eventClient.CreateExceptionEvent(ex);
                    return Problem($"Error creating scope(s): {ex.Message}", null, 500);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="scope"></param>
        /// <param name="scopeDto"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{scope}")]
        [EthosAuthScope(ScopeDefinitions.ScopeWrite)]
        public async Task<IActionResult> UpdateScope([FromRoute] string scope, [FromBody] EthosScopeDto scopeDto)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(UpdateScope), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (!EthosScope.TryParse(scope, null, out var routeScope))
                    return this.EthosErrorNotFound($"Scope {scope} was not found.");

                if (scopeDto is null)
                    return this.EthosErrorBadRequest("Scope entity is required.");

                if (string.IsNullOrEmpty(scopeDto.Name))
                    scopeDto.Name = routeScope.ToString();

                if (!EthosScope.TryParse(scopeDto.Name, null, out var dtoScope))
                    return this.EthosErrorBadRequest($"Scope {scopeDto.Name} is invalid.");

                using var trans = dbContext.Database.BeginTransaction();

                try
                {
                    var existingScope = dbContext.Scopes.FirstOrDefault(s => Equals(s.Name.ToLower(), routeScope.ToString().ToLower()));

                    if (existingScope is null)
                        return this.EthosErrorBadRequest($"Scope {routeScope} was not found.");

                    var hasScopeAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.ScopeAdmin);
                    if (!hasScopeAdmin && ((scopeDto.Privileged && !scopeDto.Assignable) || (existingScope.Privileged && !existingScope.Assignable)))
                    {
                        logger.LogWarning("Current user does not have permission to update globally privileged scope: {Scope}", routeScope.ToString());
                        return this.EthosErrorForbidden($"Globally privileged scope {routeScope} cannot be updated by the current user.");
                    }

                    if (scopeDto.Assignable != existingScope.Assignable && !hasScopeAdmin)
                    {
                        logger.LogWarning("Current user cannot change assignable flag on scope: {Scope}", routeScope.ToString());
                        return this.EthosErrorForbidden($"Assignable flag cannot be updated by the current user.");
                    }

                    existingScope.Name = dtoScope.ToString();
                    existingScope.Description = scopeDto.Description;
                    existingScope.Privileged = scopeDto.Privileged;
                    existingScope.Assignable = scopeDto.Assignable;

                    dbContext.Scopes.Update(existingScope);

                    if (!string.Equals(routeScope.ToString(), dtoScope.ToString(), StringComparison.OrdinalIgnoreCase))
                    {
                        // scope was renamed
                        var roleScopes = dbContext.RoleScopes.Where(rs => Equals(rs.Scope.ToLower(), routeScope.ToString().ToLower()));

                        if (roleScopes.Any())
                        {
                            await roleScopes.ForEachAsync(rs =>
                            {
                                if (rs.DisplayName.Contains(routeScope.ToString(), StringComparison.OrdinalIgnoreCase))
                                    rs.DisplayName = rs.DisplayName.Replace(routeScope.ToString(), dtoScope.ToString(), StringComparison.OrdinalIgnoreCase);

                                rs.Scope = dtoScope.ToString();
                            });
                        }
                    }

                    await dbContext.SaveChangesAsync();
                    await trans.CommitAsync();
                    return NoContent();
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error updating scope: {Scope}", routeScope.ToString());
                    await trans.RollbackAsync();
                    await eventClient.CreateExceptionEvent(ex);
                    return Problem($"Error updating scope {routeScope}: {ex.Message}", null, 500);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="scopes"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{scope}")]
        [EthosAuthScope(ScopeDefinitions.ScopeDelete, ScopeDefinitions.RoleWrite)]
        public async Task<IActionResult> DeleteScope([FromRoute] string scope)
        {
            if (string.IsNullOrEmpty(scope))
                return this.EthosErrorBadRequest("Invalid scope.");

            if (!EthosScope.TryParse(scope, null, out var _scope) || !_scope.IsFullyQualified())
            {
                logger.LogWarning("Cannot delete invalid scope: {Scope}", scope);
                return this.EthosErrorBadRequest($"Invalid scope: {scope}");
            }
            return await DeleteScope([scope]);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="scopes"></param>
        /// <returns></returns>
        [HttpDelete]
        [EthosAuthScope(ScopeDefinitions.ScopeDelete, ScopeDefinitions.RoleWrite)]
        public async Task<IActionResult> DeleteScope([FromBody] string[] scopes)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(DeleteScope), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (scopes is null || scopes.Length < 1)
                    return NoContent();

                var messages = new List<object>();
                bool hasError = false;

                using var trans = dbContext.Database.BeginTransaction();

                try
                {
                    var isScopeAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.ScopeAdmin);

                    foreach (var scope in scopes)
                    {
                        if (string.IsNullOrEmpty(scope))
                            continue;

                        if (!EthosScope.TryParse(scope, null, out var _scope) || !_scope.IsFullyQualified())
                        {
                            logger.LogWarning("Skipping invalid scope: {Scope}", scope);
                            messages.Add(new { scope, message = "Invalid scope." });
                            hasError = true;
                            continue;
                        }

                        var existingScope = dbContext.Scopes.FirstOrDefault(s => Equals(s.Name.ToLower(), _scope.ToString().ToLower()));

                        if (existingScope is null)
                        {
                            logger.LogInformation("Scope {Scope} does not exist", scope);
                            messages.Add(new { scope = _scope.ToString(), message = "Scope does not exist." });
                            hasError = true;
                            continue;
                        }

                        if (existingScope.Privileged && !existingScope.Assignable && !isScopeAdmin)
                        {
                            logger.LogInformation("Globally privileged scope {Scope} cannot be deleted by the current user.", scope);
                            messages.Add(new { scope = _scope.ToString(), message = "Globally privileged scope cannot be deleted by the current user." });
                            hasError = true;
                            continue;
                        }

                        if (!existingScope.Assignable && !isScopeAdmin)
                        {
                            logger.LogInformation("Non-assignable scope {Scope} cannot be deleted by the current user.", scope);
                            messages.Add(new { scope = _scope.ToString(), message = "Non-assignable scope cannot be deleted by the current user." });
                            hasError = true;
                            continue;
                        }

                        dbContext.Scopes.Remove(existingScope);
                        await dbContext.RoleScopes.Where(rs => Equals(rs.Scope.ToLower(), _scope.ToString().ToLower())).ExecuteDeleteAsync();
                        await dbContext.SaveChangesAsync();
                    }

                    await trans.CommitAsync();
                    return !hasError ? NoContent() : StatusCode(207, messages);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error deleting scope(s)");
                    await trans.RollbackAsync();
                    await eventClient.CreateExceptionEvent(ex);
                    return Problem($"Error deleting scope(s): {ex.Message}", null, 500);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="pagingParameters"></param>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.ScopeRead)]
        public async Task<IActionResult> GetScopes([FromQuery] PagingParameters pagingParameters, [FromQuery] string? filter = null)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetScopes), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var hasScopeAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.ScopeAdmin);

                var scopes = dbContext.Scopes.Where(s => hasScopeAdmin || !(!s.Assignable && s.Privileged))
                                             .Filter(filter)
                                             .OrderBy(s => s.Name);

                return Ok(await scopes.PaginateWithLinksAsync<EthosDbScope, EthosScopeDto>(this, (sps) =>
                {
                    return [.. sps.Select(s => new EthosScopeDto()
                    {
                        Name = s.Name,
                        Description = s.Description,
                        Assignable = s.Assignable,
                        Privileged = s.Privileged,
                    })];
                }, pagingParameters.limit, pagingParameters.offset));
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="scope"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{scope}")]
        [EthosAuthScope(ScopeDefinitions.ScopeRead)]
        public async Task<IActionResult> GetScope([FromRoute] string scope)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetScope), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var hasScopeAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.ScopeAdmin);

                var scopeObj = await dbContext.Scopes.FirstOrDefaultAsync(s => string.Equals(scope.ToLower(), s.Name.ToLower()) && 
                                                                               (hasScopeAdmin || !(!s.Assignable && s.Privileged)));

                if (scopeObj is null)
                    return this.EthosErrorNotFound($"Scope not found: {scope}");

                return Ok(new EthosScopeDto()
                {
                    Name = scopeObj.Name,
                    Description = scopeObj.Description,
                    Assignable = scopeObj.Assignable,
                    Privileged = scopeObj.Privileged
                });
            }
        }
    }
}
