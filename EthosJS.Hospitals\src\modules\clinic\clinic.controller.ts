import { AuthGuard } from '@app/common/guards/auth.guard';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { Api<PERSON><PERSON>erAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { ClinicService } from '@app/modules/clinic/clinic.service';
import { ClinicFiltersDto } from '@app/modules/clinic/dto/clinic.filters.dto';
import { ClinicCollectionDto } from '@app/modules/clinic/dto/clinic.collection.dto';
import { CreateClinicDto } from '@app/modules/clinic/dto/create.clinic.dto';
import { UpdateClinicDto } from '@app/modules/clinic/dto/update.clinic.dto';
import { ClinicEntity } from '@app/modules/clinic/clinic.entity';
import { DeleteClinicDto } from '@app/modules/clinic/dto/delete.clinic.dto';

@Controller('clinic')
@ApiTags('Clinics')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class ClinicController {
  constructor(private readonly service: ClinicService) {
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: ClinicCollectionDto,
    description: 'Get list of clinics',
  })
  async list(@Query() filters: ClinicFiltersDto): Promise<ClinicCollectionDto> {
    return this.service.list(filters);
  }

  @Get('/:clinicId')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: ClinicEntity,
    description: 'Get clinic by id',
  })
  async getById(@Param('clinicId', new ParseIntPipe()) clinicId: number): Promise<ClinicEntity> {
    return this.service.getByIdOrFail(clinicId);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOkResponse({
    type: ClinicEntity,
    description: 'Create clinic',
  })
  async create(@Body() clinic: CreateClinicDto): Promise<ClinicEntity> {
    return this.service.create(clinic);
  }

  @Put()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: ClinicEntity,
    description: 'Update clinic',
  })
  async update(@Body() update: UpdateClinicDto): Promise<ClinicEntity> {
    return this.service.update(update);
  }

  @Delete()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: ClinicEntity,
    description: 'Delete clinic',
  })
  async delete(@Body() { id }: DeleteClinicDto): Promise<ClinicEntity> {
    return this.service.delete(id);
  }
}
