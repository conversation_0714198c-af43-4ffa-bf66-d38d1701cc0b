using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Ethos.Model;
using Ethos.Workflows.Api;
using Ethos.Workflows.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class StudyController(DbContext dbContext)
    : EntityControllerBase<StudyDbo, CreateStudyDto, StudyDto, StudyQ>(dbContext)
{
    protected override StudyDto MapToDto(StudyDbo dbo)
    {
        return new StudyDto
        {
            Id = dbo.Id,
            OrderId = dbo.OrderId,
            
            EncounterType = dbo.EncounterType,
            StudyType = dbo.StudyType,
            StudyAttributes = dbo.StudyAttributes,
            Insurances = dbo.Insurances?.Select(i => i.Id).ToList(),
        };
    }

    protected override StudyDbo CreateOrUpdateEntity(StudyDbo? entity, CreateStudyDto input, Guid? requiredId = null)
    {
        var createdNew = entity == null;
        entity ??= new StudyDbo { Id = requiredId ?? Guid.NewGuid() };

        if (createdNew)
        {
            var order = _dbContext.Set<OrderDbo>()
                .FirstOrDefault(o => o.Id == input.OrderId);
            if (order == null)
                throw new RequiredEntityDoesNotExistException(EntityType.Order, input.OrderId);
            entity.OrderId = order.Id;
            entity.Order = order;
        }
        else
        {
            entity.OrderId = input.OrderId;
        }
        
        if (input.Insurances != null)
        {
            entity.Insurances = _dbContext.Set<InsuranceDbo>()
                .Where(i => input.Insurances.Contains(i.Id))
                .ToList();
        }
        
        if (input.EncounterType != null) 
            entity.EncounterType = input.EncounterType.Value;
        
        if (input.StudyType != null)
            entity.StudyType = input.StudyType.Value;
        
        if (input.StudyAttributes != null)
            entity.StudyAttributes = input.StudyAttributes;

        if (createdNew) _dbSet.Add(entity);
        
        return entity;
    }

    protected override IQueryable<StudyDbo> ApplyIncludes(IQueryable<StudyDbo> query)
    {
        // Include insurance data when fetching studies
        return query.Include(s => s.Insurances);
    }
}
