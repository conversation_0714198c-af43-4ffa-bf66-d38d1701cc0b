using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class PhoneNumberWithUseDataDbo : IOwnedEntity<PhoneNumberWithUseDataDbo>
{
    public required string PhoneNumber { get; set; }
    public required long PhoneNumberTypeId { get; set; }
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<PhoneNumberWithUseDataDbo>(Register);

    public new static void Register(EntityTypeBuilder<PhoneNumberWithUseDataDbo> entity)
    {
        IOwnedEntity<PhoneNumberWithUseDataDbo>.Register(entity);
        
        entity.Property(p => p.PhoneNumber).HasMaxLength(20).IsRequired();
        entity.Property(p => p.PhoneNumberTypeId).IsRequired();
    }
}