using System.Collections.Immutable;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;
using Microsoft.Extensions.Configuration;

namespace Ethos.Model.Scheduling;

public class EvaluationException : Exception
{
    public EvaluationException(string message) : base(message) { }
    public EvaluationException(string message, Exception innerException) : base(message, innerException) { }
}

public sealed record ExprEvalContext
{
    
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(Expr.VarName), "VarName")]
[JsonDerivedType(typeof(Expr.LitInt), "LitInt")]
[JsonDerivedType(typeof(Expr.LitString), "LitString")]
[JsonDerivedType(typeof(Expr.Eq), "Eq")]
[JsonDerivedType(typeof(Expr.Neq), "Neq")]
[JsonDerivedType(typeof(Expr.Gt), "Gt")]
[JsonDerivedType(typeof(Expr.Gte), "Gte")]
[JsonDerivedType(typeof(Expr.Lt), "Lt")]
[JsonDerivedType(typeof(Expr.Lte), "Lte")]
[JsonDerivedType(typeof(Expr.And), "And")]
[JsonDerivedType(typeof(Expr.Or), "Or")]
[JsonDerivedType(typeof(Expr.Exists), "Exists")]
[JsonDerivedType(typeof(Expr.Forall), "Forall")]
[JsonDerivedType(typeof(Expr.Not), "Not")]
[JsonDerivedType(typeof(Expr.Implies), "Implies")]
[JsonDerivedType(typeof(Expr.Contains), "Contains")]
public abstract record Expr
{
    public sealed record VarName(string Name) : Expr;
    public sealed record LitInt(int Value) : Expr;
    public sealed record LitString(string Value) : Expr;
    public sealed record Eq(Expr Left, Expr Right) : Expr;
    public sealed record Neq(Expr Left, Expr Right) : Expr;
    public sealed record Gt(Expr Left, Expr Right) : Expr;
    public sealed record Gte(Expr Left, Expr Right) : Expr;
    public sealed record Lt(Expr Left, Expr Right) : Expr;
    public sealed record Lte(Expr Left, Expr Right) : Expr;
    public sealed record And(Expr Left, Expr Right) : Expr;
    public sealed record Or(Expr Left, Expr Right) : Expr;
    public sealed record Exists(string Field, string BoundVarName, Expr Inner) : Expr;
    public sealed record Forall(string Field, string BoundVarName, Expr Inner) : Expr;
    public sealed record Not(Expr Inner) : Expr;
    public sealed record Implies(Expr Left, Expr Right) : Expr;
    public sealed record Contains(Expr Left, Expr Right) : Expr;

    public static Expr operator &(Expr left, Expr right) => new And(left, right);
    public static Expr operator |(Expr left, Expr right) => new Or(left, right);
    public static Expr operator !(Expr inner) => new Not(inner);
    public static Expr operator >(Expr left, Expr right) => new Gt(left, right);
    public static Expr operator <(Expr left, Expr right) => new Lt(left, right);
    public static Expr operator >=(Expr left, Expr right) => new Gte(left, right);
    public static Expr operator <=(Expr left, Expr right) => new Lte(left, right);
    public virtual Expr EqualTo(Expr right) => new Eq(this, right);
    public virtual Expr NotEqualTo(Expr right) => new Neq(this, right);
    
    public static Expr Var(string name) => new VarName(name);
    public static Expr Lit(int value) => new LitInt(value);
    public static Expr Lit(string value) => new LitString(value);

    public ImmutableHashSet<string> GetTopLevelVars() => this switch
    {
        VarName varName => ImmutableHashSet.Create(varName.Name.Split('.')[0]),
        LitInt _ => ImmutableHashSet<string>.Empty,
        LitString _ => ImmutableHashSet<string>.Empty,
        Eq eq => eq.Left.GetTopLevelVars().Union(eq.Right.GetTopLevelVars()),
        Neq neq => neq.Left.GetTopLevelVars().Union(neq.Right.GetTopLevelVars()),
        Gt gt => gt.Left.GetTopLevelVars().Union(gt.Right.GetTopLevelVars()),
        Gte gte => gte.Left.GetTopLevelVars().Union(gte.Right.GetTopLevelVars()),
        Lt lt => lt.Left.GetTopLevelVars().Union(lt.Right.GetTopLevelVars()),
        Lte lte => lte.Left.GetTopLevelVars().Union(lte.Right.GetTopLevelVars()),
        And and => and.Left.GetTopLevelVars().Union(and.Right.GetTopLevelVars()),
        Or or => or.Left.GetTopLevelVars().Union(or.Right.GetTopLevelVars()),
        Not not => not.Inner.GetTopLevelVars(),
        Exists exists => ImmutableHashSet.Create(exists.Field.Split('.')[0])
            .Union(exists.Inner.GetTopLevelVars().Remove(exists.BoundVarName)),
        Forall forall => ImmutableHashSet.Create(forall.Field.Split('.')[0])
            .Union(forall.Inner.GetTopLevelVars().Remove(forall.BoundVarName)),
        Implies implies => implies.Left.GetTopLevelVars().Union(implies.Right.GetTopLevelVars()),
        Contains contains => contains.Left.GetTopLevelVars().Union(contains.Right.GetTopLevelVars()),
    };
    
    public async Task<Value> Eval(ExprEvalContext ctx, Value.Object root)
    {
        switch (this)
        {
            case VarName var:
                var parts = var.Name.Split('.');
                var current = root;
                foreach (var part in parts)
                {
                    var r = await current.GetField(ctx, var.Name);
                    if (r is Value.Null) return Value.NullValue;
                    current = r as Value.Object;
                    if (current == null)
                    {
                        // If we reach a point where the object is null, we return NullValue.
                        return Value.NullValue;
                    }
                }
                return current;
            
            case LitInt lit:    return Value.Of(lit.Value);
            case LitString lit: return Value.Of(lit.Value);
            case Eq eq:
            {
                var left = await eq.Left.Eval(ctx, root);
                var right = await eq.Right.Eval(ctx, root);
                return Value.Of(await left.IsEqual(right));
            }
            case Neq neq:
            {
                var left = await neq.Left.Eval(ctx, root);
                var right = await neq.Right.Eval(ctx, root);
                return Value.Of(await left.IsEqual(right));
            }
            case Gt gt:
            {
                var left = await gt.Left.Eval(ctx, root);
                var right = await gt.Right.Eval(ctx, root);
                if (left is Value.Integer leftInt && right is Value.Integer rightInt)
                    return Value.Of(leftInt.Value > rightInt.Value);
                if (left is Value.Real leftReal && right is Value.Real rightReal)
                    return Value.Of(leftReal.Value > rightReal.Value);
                if (left is Value.DateOnly leftDate && right is Value.DateOnly rightDate)
                    return Value.Of(leftDate.Value > rightDate.Value);
                if (left is Value.DateTime leftDateTime && right is Value.DateTime rightDateTime)
                    return Value.Of(leftDateTime.Value > rightDateTime.Value);
                if (left is Value.DateTime leftDateTime2 && right is Value.DateOnly rightDate2)
                    return Value.Of(leftDateTime2.Value > rightDate2.Value.ToDateTime(TimeOnly.MinValue));
                if (left is Value.DateOnly leftDate2 && right is Value.DateTime rightDateTime2)
                    return Value.Of(leftDate2.Value.ToDateTime(TimeOnly.MinValue) > rightDateTime2.Value);
                throw new EvaluationException($"Cannot compare {left.GetType().Name} and {right.GetType().Name}");
            }
            case Gte gte:
            {
                var left = await gte.Left.Eval(ctx, root);
                var right = await gte.Right.Eval(ctx, root);
                if (left is Value.Integer leftInt && right is Value.Integer rightInt)
                    return Value.Of(leftInt.Value >= rightInt.Value);
                if (left is Value.Real leftReal && right is Value.Real rightReal)
                    return Value.Of(leftReal.Value >= rightReal.Value);
                if (left is Value.DateOnly leftDate && right is Value.DateOnly rightDate)
                    return Value.Of(leftDate.Value >= rightDate.Value);
                if (left is Value.DateTime leftDateTime && right is Value.DateTime rightDateTime)
                    return Value.Of(leftDateTime.Value >= rightDateTime.Value);
                if (left is Value.DateTime leftDateTime2 && right is Value.DateOnly rightDate2)
                    return Value.Of(leftDateTime2.Value >= rightDate2.Value.ToDateTime(TimeOnly.MinValue));
                if (left is Value.DateOnly leftDate2 && right is Value.DateTime rightDateTime2)
                    return Value.Of(leftDate2.Value.ToDateTime(TimeOnly.MinValue) >= rightDateTime2.Value);
                throw new EvaluationException($"Cannot compare {left.GetType().Name} and {right.GetType().Name}");
            }
            case Lt lt:
            {
                var left = await lt.Left.Eval(ctx, root);
                var right = await lt.Right.Eval(ctx, root);
                if (left is Value.Integer leftInt && right is Value.Integer rightInt)
                    return Value.Of(leftInt.Value < rightInt.Value);
                if (left is Value.Real leftReal && right is Value.Real rightReal)
                    return Value.Of(leftReal.Value < rightReal.Value);
                if (left is Value.DateOnly leftDate && right is Value.DateOnly rightDate)
                    return Value.Of(leftDate.Value < rightDate.Value);
                if (left is Value.DateTime leftDateTime && right is Value.DateTime rightDateTime)
                    return Value.Of(leftDateTime.Value < rightDateTime.Value);
                if (left is Value.DateTime leftDateTime2 && right is Value.DateOnly rightDate2)
                    return Value.Of(leftDateTime2.Value < rightDate2.Value.ToDateTime(TimeOnly.MinValue));
                if (left is Value.DateOnly leftDate2 && right is Value.DateTime rightDateTime2)
                    return Value.Of(leftDate2.Value.ToDateTime(TimeOnly.MinValue) < rightDateTime2.Value);
                throw new EvaluationException($"Cannot compare {left.GetType().Name} and {right.GetType().Name}");
            }
            case Lte lte:
            {
                var left = await lte.Left.Eval(ctx, root);
                var right = await lte.Right.Eval(ctx, root);
                if (left is Value.Integer leftInt && right is Value.Integer rightInt)
                    return Value.Of(leftInt.Value <= rightInt.Value);
                if (left is Value.Real leftReal && right is Value.Real rightReal)
                    return Value.Of(leftReal.Value <= rightReal.Value);
                if (left is Value.DateOnly leftDate && right is Value.DateOnly rightDate)
                    return Value.Of(leftDate.Value <= rightDate.Value);
                if (left is Value.DateTime leftDateTime && right is Value.DateTime rightDateTime)
                    return Value.Of(leftDateTime.Value <= rightDateTime.Value);
                if (left is Value.DateTime leftDateTime2 && right is Value.DateOnly rightDate2)
                    return Value.Of(leftDateTime2.Value <= rightDate2.Value.ToDateTime(TimeOnly.MinValue));
                if (left is Value.DateOnly leftDate2 && right is Value.DateTime rightDateTime2)
                    return Value.Of(leftDate2.Value.ToDateTime(TimeOnly.MinValue) <= rightDateTime2.Value);
                throw new EvaluationException($"Cannot compare {left.GetType().Name} and {right.GetType().Name}");
            }
            case And and:
            {
                var left = await and.Left.Eval(ctx, root);
                var right = await and.Right.Eval(ctx, root);
                if (left is Value.Boolean leftBool && right is Value.Boolean rightBool)
                    return Value.Of(leftBool.Value && rightBool.Value);
                throw new EvaluationException($"Cannot evaluate AND operation on {left.GetType().Name} and {right.GetType().Name}");
            }
            case Or or:
            {
                var left = await or.Left.Eval(ctx, root);
                var right = await or.Right.Eval(ctx, root);
                if (left is Value.Boolean leftBool && right is Value.Boolean rightBool)
                    return Value.Of(leftBool.Value || rightBool.Value);
                throw new EvaluationException($"Cannot evaluate OR operation on {left.GetType().Name} and {right.GetType().Name}");
            }
            case Not not:
            {
                var inner = await not.Inner.Eval(ctx, root);
                if (inner is Value.Boolean boolean)
                    return Value.Of(!boolean.Value);
                throw new EvaluationException($"Cannot evaluate NOT operation on {inner.GetType().Name}");
            }
            case Exists exists:
            {
                var fieldValue = await new VarName(exists.Field).Eval(ctx, root);
                if (fieldValue is not Value.List list)
                    throw new EvaluationException($"Expected a list for field '{exists.Field}', but got {fieldValue.GetType().Name}");
                var newRoot = new Value.Object();
                foreach (var key in root.Properties.Keys)
                {
                    newRoot[key] = root[key];
                }
                foreach (var item in list.Values)
                {
                    newRoot[exists.BoundVarName] = item;
                    var innerValue = await exists.Inner.Eval(ctx, newRoot);
                    if (innerValue is Value.Boolean boolean && boolean.Value)
                    {
                        return Value.Of(true);
                    }
                }
                return Value.Of(false);
            }
            case Forall forall:
            {
                var fieldValue = await new VarName(forall.Field).Eval(ctx, root);
                if (fieldValue is not Value.List list)
                    throw new EvaluationException($"Expected a list for field '{forall.Field}', but got {fieldValue.GetType().Name}");
                var newRoot = new Value.Object();
                foreach (var key in root.Properties.Keys)
                {
                    newRoot[key] = root[key];
                }
                foreach (var item in list.Values)
                {
                    newRoot[forall.BoundVarName] = item;
                    var innerValue = await forall.Inner.Eval(ctx, newRoot);
                    if (innerValue is Value.Boolean boolean && !boolean.Value)
                    {
                        return Value.Of(false);
                    }
                }
                return Value.Of(true);
            }
            case Implies implies:
            {
                var left = await implies.Left.Eval(ctx, root);
                var right = await implies.Right.Eval(ctx, root);
                if (left is Value.Boolean leftBool && right is Value.Boolean rightBool)
                    return Value.Of(!leftBool.Value || rightBool.Value);
                throw new EvaluationException($"Cannot evaluate IMPLIES operation on {left.GetType().Name} and {right.GetType().Name}");
            }
            case Contains contains:
            {
                var left = await contains.Left.Eval(ctx, root);
                var right = await contains.Right.Eval(ctx, root);
                if (right is Value.List rightObj)
                {
                    var found = false;
                    foreach (var item in rightObj.Values)
                    {
                        if (await left.IsEqual(item))
                        {
                            found = true;
                            break;
                        }
                    }
                    return Value.Of(found);
                }
                if (left is Value.String leftObj && right is Value.String rightStr)
                {
                    return Value.Of(leftObj.Value.Contains(rightStr.Value, StringComparison.OrdinalIgnoreCase));
                }
                throw new EvaluationException($"Cannot evaluate CONTAINS operation on {left.GetType().Name} and {right.GetType().Name}");
            }
        };
        throw new EvaluationException($"Unknown expression type: {this.GetType().Name}");
    }
}

public abstract record Value
{
    public sealed record Object(Dictionary<string, Value> Properties) : Value
    {
        public Object() : this(new Dictionary<string, Value>()) { }
        
        public Value this[string key]
        {
            get => Properties.TryGetValue(key, out var value) ? value : Value.NullValue;
            set => Properties[key] = value;
        }

        public bool TryGetValue(string key, out Value value)
        {
            var parts = key.Split('.');
            if (parts.Length == 1)
            {
                return Properties.TryGetValue(key, out value);
            }
            Value current = this;
            foreach (var part in parts)
            {
                if (current is Object obj && obj.Properties.TryGetValue(part, out var next))
                {
                    current = next;
                }
                else
                {
                    value = Value.NullValue;
                    return false;
                }
            }
            value = current;
            return true;
        }
    }
    public sealed record List(System.Collections.Generic.List<Value> Values) : Value;
    
    public sealed record Null() : Value;
    public sealed record Integer(long Value) : Value;
    public sealed record Boolean(bool Value) : Value;
    public sealed record Real(double Value) : Value;
    public sealed record String(string Value) : Value;
    public sealed record DateOnly(System.DateOnly Value) : Value;
    public sealed record DateTime(System.DateTime Value) : Value;

    public abstract record IReferenceData : Value
    {
        public abstract Task<IReferenceDataEntity?> GetRawValue();
    }

    public sealed record ReferenceData<TRefData>(LazyTask<TRefData?> Value)
        : IReferenceData where TRefData : class, IReferenceDataEntity
    {
        public override async Task<IReferenceDataEntity?> GetRawValue()
        {
            return await Value.Get();
        }
    }
    
    public async Task<Value> GetField(ExprEvalContext ctx, string fieldName)
    {
        if (this is Object obj && obj.Properties.TryGetValue(fieldName, out var value))
        {
            return value;
        }
        if (this is IReferenceData refData)
        {
            var refEntity = await refData.GetRawValue();
            if (refEntity == null) return NullValue;
            if (fieldName == "Name") return new String(refEntity.Name);
            if (fieldName == "Id") return new Integer(refEntity.Id);
            // Return the fields via reflection.
            var prop = refEntity.GetType().GetProperty(fieldName);
            if (prop != null)
            {
                var propValue = prop.GetValue(refEntity);
                if (propValue is System.DateOnly dateOnly) return new DateOnly(dateOnly);
                if (propValue is System.DateTime dateTime) return new DateTime(dateTime);
                if (propValue is int intValue) return new Integer(intValue);
                if (propValue is long longValue) return new Integer(longValue);
                if (propValue is double doubleValue) return new Real(doubleValue);
                if (propValue is bool boolValue) return new Boolean(boolValue);
                if (propValue is string strValue) return new String(strValue);
            }
        }
        return NullValue;
    }

    public async Task<bool> IsEqual(Value that) => that switch
    {
        Object obj => await IsEqual(obj),
        List value => await IsEqual(value),
        String value => await IsEqual(value),
        IReferenceData value => await IsEqual(value),
        Null _ => this is Null,
        Integer value => (this is Integer self && self.Value == value.Value) ||
                         (this is Real real && real.Value == value.Value),
        Real value => (this is Real self && self.Value == value.Value) ||
                      (this is Integer integer && integer.Value == value.Value),
        Boolean value => this is Boolean self && self.Value == value.Value,
        DateOnly value => this is DateOnly self && self.Value == value.Value,
        DateTime value => this is DateTime self && self.Value == value.Value,
    };
    
    public async Task<bool> IsEqual(Value.Object that)
    {
        if (this is not Value.Object self) return false;
        if (self.Properties.Count != that.Properties.Count) return false;
        foreach (var kvp in self.Properties)
        {
            if (!that.Properties.TryGetValue(kvp.Key, out var otherValue)) return false;
            if (!await kvp.Value.IsEqual(otherValue)) return false;
        }
        return true;
    }
    public async Task<bool> IsEqual(Value.List that)
    {
        if (this is not Value.List self) return false;
        if (self.Values.Count != that.Values.Count) return false;
        for (int i = 0; i < self.Values.Count; i++)
        {
            if (!await self.Values[i].IsEqual(that.Values[i])) return false;
        }
        return true;
    }
    public async Task<bool> IsEqual(Value.IReferenceData that)
    {
        // We special-case strings.
        if (this is Value.String thisStr)
        {
            var thatValue0 = await that.GetRawValue();
            return thatValue0?.Name == thisStr.Value;
        }
        if (this is not Value.IReferenceData self) return false;
        // For reference data, we compare the underlying Lazy<TRefData?> values.
        var selfValue = await self.GetRawValue();
        var thatValue = await that.GetRawValue();
        if (selfValue == null && thatValue == null) return true;
        if (selfValue == null || thatValue == null) return false;
        // If both are non-null, we compare the actual entities.
        return selfValue.Equals(thatValue);
    }
    public async Task<bool> IsEqual(Value.String that)
    {
        if (this is Value.String self) return self.Value == that.Value;
        if (this is Value.IReferenceData refData)
        {
            // If this is reference data, we check if the name matches.
            return (await refData.GetRawValue())?.Name == that.Value;
        }
        return false;
    }
    
    public static Null NullValue { get; } = new Null();
    public static Value OfRef<TRefData>(LazyTask<TRefData?>? value) where TRefData : class, IReferenceDataEntity
    {
        if (value == null) return NullValue;
        return new ReferenceData<TRefData>(value);
    }
    public static Value Of(System.DateOnly? date) => date == null ? NullValue : new DateOnly(date.Value);
    public static Value Of(System.DateTime? dateTime) => dateTime == null ? NullValue : new DateTime(dateTime.Value);
    public static Value Of(int? value) => value == null ? NullValue : new Integer(value.Value);
    public static Value Of(double? value) => value == null ? NullValue : new Real(value.Value);
    public static Value Of(decimal? value) => value == null ? NullValue : new Real((double) value.Value);
    public static Value Of(long? value) => value == null ? NullValue : new Integer(value.Value);
    public static Value Of(bool? value) => value == null ? NullValue : new Boolean(value.Value);
    public static Value Of(string? value) => value == null ? NullValue : new String(value);
    public static Value Of(System.Collections.Generic.List<Value>? values)
    {
        if (values == null || values.Count == 0) return NullValue;
        return new List(values);
    }
    public static Value Of(Dictionary<string, Value>? properties)
    {
        if (properties == null || properties.Count == 0) return NullValue;
        return new Object(properties);
    }

    public static Value Of(JsonNode? json)
    {
        if (json == null) return NullValue;
        if (json is JsonObject jsonObject)
        {
            var dict = new Dictionary<string, Value>();
            foreach (var kvp in jsonObject)
            {
                dict[kvp.Key] = Of(kvp.Value);
            }
            return new Object(dict);
        }
        if (json is JsonArray jsonArray)
        {
            var list = new System.Collections.Generic.List<Value>();
            foreach (var item in jsonArray)
            {
                list.Add(Of(item));
            }
            return new List(list);
        }
        if (json is JsonValue jsonValue)
        {
            if (jsonValue.TryGetValue(out int intValue))
            {
                return new Integer(intValue);
            }
            if (jsonValue.TryGetValue(out long longValue))
            {
                return new Integer(longValue);
            }
            if (jsonValue.TryGetValue(out double doubleValue))
            {
                return new Real(doubleValue);
            }
            if (jsonValue.TryGetValue(out string? stringValue))
            {
                return new String(stringValue);
            }
            if (jsonValue.TryGetValue(out bool boolValue))
            {
                return new Boolean(boolValue);
            }
        }
        throw new EvaluationException($"Unsupported JSON node type: {json.GetType().Name}");
    }
}

public sealed record Constraint(string Id, bool IsHard, Expr Expr)
{
    public static Constraint Hard(string id, Expr expr) => new(id, true, expr);
    public static Constraint Soft(string id, Expr expr) => new(id, false, expr);
    
    public ImmutableHashSet<string> GetTopLevelVars() => Expr.GetTopLevelVars();
}

public class LazyTask<T>
{
    private Func<Task<T>>? _task;
    private T _value;

    public LazyTask(Func<Task<T>> factory)
    {
        _task = factory;
    }

    public async Task<T> Get()
    {
        if (_task == null) return _value;
        _value = await _task();
        _task = null; // Clear the task to prevent re-execution
        return _value;
    }
}

public interface IReferenceData
{
    public Task<TRefData?> GetById<TRefData>(long? id) where TRefData : class, IReferenceDataEntity, new();
    
    Value GetLazyById<TRefData>(long? id) where TRefData : class, IReferenceDataEntity, new()
    {
        if (id == null) return Value.NullValue;
        return new Value.ReferenceData<TRefData>(
            new LazyTask<TRefData?>(() => GetById<TRefData>(id)));
    }
}

public static class SchedulerConfig
{
    // Technician
    //   Gender
    //   BirthSex
    //   Qualification
    // CareLocation
    // Facility
    // Room
    // Patient
    // CareLocationShift
    // Equipment

    private static int Age(DateOnly date)
    {
        var today = DateOnly.FromDateTime(DateTime.UtcNow);
        var age = today.Year - date.Year;
        if (today < date.AddYears(age)) age--;
        return age;
    }
    private static int? Age(DateOnly? date)
    {
        if (date == null) return null;
        return Age(date.Value);
    }
    
    public static Value.Object GetAttributes(PatientDbo patient, IReferenceData referenceData)
    {
        var result = new Value.Object();

        result["Gender"] = referenceData.GetLazyById<GenderEntity>(patient.Demographics?.GenderId);
        result["BirthSex"] = referenceData.GetLazyById<SexEntity>(patient.Demographics?.SexId);
        result["MaritalStatus"] = referenceData.GetLazyById<GenderEntity>(patient.Demographics?.MaritalStatusId);
        result["Race"] = referenceData.GetLazyById<RaceEntity>(patient.Demographics?.RaceId);
        result["Ethnicity"] = referenceData.GetLazyById<EthnicityEntity>(patient.Demographics?.EthnicityId);
        result["DateOfBirth"] = Value.Of(patient.Demographics?.DateOfBirth);
        if (patient.PhysicalMeasurements != null)
        {
            result["NeckSize"] = Value.Of(patient.PhysicalMeasurements.NeckSize);
            result["Height"] = Value.Of(patient.PhysicalMeasurements.HeightInches);
            result["Weight"] = Value.Of(patient.PhysicalMeasurements.WeightPounds);
            result["BMI"] = Value.Of(patient.PhysicalMeasurements.Bmi);
        }
        
        result["TechnicianPreference"] = referenceData.GetLazyById<TechnicianPreferenceEntity>(patient.TechnicianPreference);
        result["ClinicalConsiderations"] = Value.Of(patient.ClinicalConsiderations
            .Select(c => referenceData.GetLazyById<ClinicalConsiderationEntity>(c))
            .ToList());
        result["PreferredWeekdays"] = Value.Of(patient.PreferredWeekdays
            .Select(d => referenceData.GetLazyById<WeekDayEntity>(d))
            .ToList());

        return result;
    }
    
    public static Value.Object GetAttributes(ProviderDbo dbo, IReferenceData referenceData)
    {
        var result = new Value.Object();
        return result;
    }
    
    public static Value.Object GetAttributes(CareLocationDbo dbo, IReferenceData referenceData)
    {
        var result = new Value.Object();
        result["Name"] = Value.Of(dbo.Name);
        result["SupportedEncounterTypes"] = Value.Of(dbo.SupportedEncounterTypes
            .Select(e => referenceData.GetLazyById<EncounterTypeEntity>(e))
            .ToList());
        result["SupportedStudyTypes"] = Value.Of(dbo.SupportedStudyTypes
            .Select(e => referenceData.GetLazyById<StudyTypeEntity>(e))
            .ToList());
        result["Equipment"] = Value.Of(dbo.Equipment
            .Select(Value (e) => GetAttributes(e, referenceData))
            .ToList());
        return result;
    }
    
    public static Value.Object GetAttributes(TechnicianDbo dbo, IReferenceData referenceData)
    {
        var result = new Value.Object();
        result["GenderId"] = referenceData.GetLazyById<GenderEntity>(dbo.Demographics?.GenderId);
        result["BirthSexId"] = referenceData.GetLazyById<SexEntity>(dbo.Demographics?.SexId);
        return result;
    }

    public static Value.Object GetAttributes(EquipmentDbo dbo, IReferenceData referenceData)
    {
        return new Value.Object(new Dictionary<string, Value>
        {
            // ["Type"] = referenceData.GetLazyById<Eq>(e.TypeId),
            ["Data"] = Value.Of(dbo.EquipmentData)
        });
    }
    
    public static Value.Object GetAttributes(RoomDbo dbo, IReferenceData referenceData)
    {
        var result = new Value.Object();
        result["Name"] = Value.Of(dbo.Name);
        result["SupportedStudyTypes"] = Value.Of(dbo.SupportedStudyTypes?
            .Select(e => referenceData.GetLazyById<StudyTypeEntity>(e))
            .ToList());
        result["Equipment"] = Value.Of(dbo.Equipment.Select(Value (e) => GetAttributes(e, referenceData)).ToList());
        return result;
    }
    
    public static Value.Object GetAttributes(CareLocationShiftDbo dbo, IReferenceData referenceData)
    {
        var result = new Value.Object();
        return result;
    }
    
    // public static List<Constraint> AllConstraints = new List<Constraint>
    // {
    //     Constraint.Soft("Technician Gender Preference", 
    //         Expr.Var("Technician.Gender").EqualTo(Expr.Var("Patient.TechnicianPreference"))),
    // };

    // public static HashSet<Constraint> RoomConstraints = AllConstraints
    //     .Where(e => !e.GetTopLevelVars().Contains("Technician") && !e.GetTopLevelVars().Contains("Shift"))
    //     .ToHashSet();
    //
    // public static HashSet<Constraint> RoomShiftConstraints = AllConstraints
    //     .Where(e => !e.GetTopLevelVars().Contains("Technician"))
    //     .Where(e => !RoomConstraints.Contains(e))
    //     .ToHashSet();
}