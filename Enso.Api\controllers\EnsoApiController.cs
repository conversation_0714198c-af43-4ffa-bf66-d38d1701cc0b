﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace EnsoApi.controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EnsoApiController : ControllerBase
    {
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public interface IController
    {

        /// <summary>
        /// Create an access token
        /// </summary>

        /// <remarks>
        /// Create an access token by including a refresh token in the `Authorization` header as `Bear<PERSON> &lt;refresh_token&gt;`.
        /// </remarks>

        /// <param name="authorization">Refresh token in the format `<PERSON><PERSON> &lt;refresh_token&gt;`.</param>

        /// <returns>Successful response</returns>

        System.Threading.Tasks.Task<CreateAccessTokenResponseSchema> RefreshAsync(string authorization);

        /// <summary>
        /// Retrieve a list of studies.
        /// </summary>

        /// <remarks>
        /// Studies can be queried by their `created`, `state`, and `status` fields. Results are paginated and ordered by creation date in descending order.
        /// </remarks>

        /// <param name="id__in">Retrieve studies in bulk by `id`.</param>

        /// <param name="created__gte">Retrieve studies created after or on the specified ISO 8601 datetime (UTC).</param>

        /// <param name="created__gt">Retrieve studies created strictly after the specified ISO 8601 datetime (UTC).</param>

        /// <param name="created__lte">Retrieve studies created before or on the specified ISO 8601 datetime (UTC).</param>

        /// <param name="created__lt">Retrieve studies created strictly before the specified ISO 8601 datetime (UTC).</param>

        /// <param name="pid">Retrieve studies by `pid`.</param>

        /// <param name="pid__in">Retrieve studies in bulk by `pid`.</param>

        /// <param name="state">Limit results to studies in the specified state.</param>

        /// <param name="state__in">Limit results to studies in the specified states.</param>

        /// <param name="status">Limit results to studies in the specified status.</param>

        /// <param name="status__in">Limit results to studies in the specified statuses.</param>

        /// <param name="limit">Limit the number of results returned.</param>

        /// <param name="skip">A cursor to use for pagination.  The (zero-based) offset of the first item in the collection to return.</param>

        /// <returns>Successful response</returns>

        System.Threading.Tasks.Task<PaginatedStudyResponseSchema> StudyGETAsync(System.Collections.Generic.IEnumerable<string> id__in, System.DateTimeOffset? created__gte, System.DateTimeOffset? created__gt, System.DateTimeOffset? created__lte, System.DateTimeOffset? created__lt, string pid, System.Collections.Generic.IEnumerable<string> pid__in, State? state, System.Collections.Generic.IEnumerable<Anonymous> state__in, Status? status, System.Collections.Generic.IEnumerable<Anonymous2> status__in, int? limit, int? skip);

        /// <summary>
        /// Create a study.
        /// </summary>

        /// <remarks>
        /// Most service accounts are configured to provide default values for `hardware`, `software`, and `desaturation` when you create a study, so only provide these values if instructed to or you need to override the defaults.
        /// </remarks>

        /// <returns>Successful response</returns>

        System.Threading.Tasks.Task<StudyResponseSchema> StudyPOSTAsync(CreateStudy body);

        /// <summary>
        /// Retrieve a study by ID.
        /// </summary>

        /// <param name="study_id">The unique ID of the study.</param>

        /// <returns>Successful response</returns>

        System.Threading.Tasks.Task<StudyResponseSchema> StudyGET2Async(string study_id);

        /// <summary>
        /// Update a study by ID.
        /// </summary>

        /// <param name="study_id">The unique ID of the study.</param>


        /// <returns>Successful response</returns>

        System.Threading.Tasks.Task<StudyResponseSchema> StudyPATCHAsync(string study_id, UpdateStudy body);

        /// <summary>
        /// Retrieve a list of study files.
        /// </summary>

        /// <remarks>
        /// Retrieves study file metadata and a URL to download the study. Note that, even if the `content_encoding` of a file is `gzip`, Google Cloud Storage will automatically &lt;a href="https://cloud.google.com/storage/docs/transcoding"&gt;decompress the file&lt;/a&gt; for you upon download.
        /// </remarks>

        /// <param name="study_id">The unique ID of the study.</param>

        /// <param name="version">The version of the study. New studies should upload to the `CURRENT` version and grab EnsoSleep scoring from the `SCORED` version. A backup of the study is made to `ORIGINAL`.</param>

        /// <returns>Successful response</returns>

        System.Threading.Tasks.Task<ListRetrieveFileResponseSchema> FileGETAsync(string study_id, Version version);

        /// <summary>
        /// Create a study file.
        /// </summary>

        /// <remarks>
        /// Creating a study file will return an `upload_url`. You can upload the file to this URL by following the directions from &lt;a href="https://cloud.google.com/storage/docs/performing-resumable-uploads#upload-file"&gt;Google Cloud Storage - Performing Resumable Uploads&lt;/a&gt;. For large files or slower networks, we recommend the Multiple Chunk Upload method.  If you receive a timeout error, you need to reduce your chunk size to a smaller value that your network can tolerate.
        /// </remarks>

        /// <param name="study_id">The unique ID of the study.</param>

        /// <param name="version">The version of the study. New studies should upload to the `CURRENT` version and grab EnsoSleep scoring from the `SCORED` version. A backup of the study is made to `ORIGINAL`.</param>


        /// <returns>Successful response</returns>

        System.Threading.Tasks.Task<CreateFileResponseSchema> FilePOSTAsync(string study_id, Version2 version, CreateFile body);

        /// <summary>
        /// Retrieve a study file by name.
        /// </summary>

        /// <remarks>
        /// Retrieves study file metadata and a URL to download the study. Note that, even if the `content_encoding` of a file is `gzip`, Google Cloud Storage will automatically &lt;a href="https://cloud.google.com/storage/docs/transcoding"&gt;decompress the file&lt;/a&gt; for you upon download.
        /// </remarks>

        /// <param name="study_id">The unique ID of the study.</param>

        /// <param name="version">The version of the study. New studies should upload to the `CURRENT` version and grab EnsoSleep scoring from the `SCORED` version. A backup of the study is made to `ORIGINAL`.</param>

        /// <param name="filename">The name of the file.</param>

        /// <returns>Successful response</returns>

        System.Threading.Tasks.Task<RetrieveFileResponseSchema> FileGET2Async(string study_id, Version3 version, string filename);

        /// <summary>
        /// Retrieve a list of score sets.
        /// </summary>

        /// <remarks>
        /// Score sets can be queried by their `patient_id` and `name`. Results are paginated. To retreive EnsoSleep scoring, use the querystring `?patient_id=&lt;patient_id&gt;&amp;name=prepared`.
        /// </remarks>

        /// <param name="name">The name of the score set.</param>


        /// <param name="patient_id">The ID the study.</param>

        /// <returns>Successful response</returns>

        System.Threading.Tasks.Task<PaginatedScoreSetResponseSchema> ScoresetAsync(string name, System.Collections.Generic.IEnumerable<string> id__in, string patient_id);

        /// <summary>
        /// Retrieve a score set by ID.
        /// </summary>

        /// <param name="scoreset_id">The unique ID of the score set.</param>

        /// <returns>Successful response</returns>

        System.Threading.Tasks.Task<ScoreSetResponseSchema> Scoreset2Async(string scoreset_id);

        /// <summary>
        /// Retrieve a list of events for a given score set.
        /// </summary>

        /// <remarks>
        /// Use the querystring `?group_by=name` to group events by name for easier parsing.
        /// </remarks>

        /// <param name="group_by">Grouping events will return them as `{"hypopnea": [&lt;event_1&gt;, &lt;event_2&gt;], "desat": ...}`</param>

        /// <returns>Successful response</returns>

        System.Threading.Tasks.Task<ListScoreEventResponseSchema> EventAsync(Group_by? group_by);

        /// <summary>
        /// Retrieve a report by ID.
        /// </summary>

        /// <param name="report_id">The unique ID of the report.</param>

        /// <returns>Successful response</returns>

        System.Threading.Tasks.Task<ReportResponseSchema> ReportGETAsync(string report_id);

        /// <summary>
        /// Update a report's notes.
        /// </summary>

        /// <param name="report_id">The unique ID of the report.</param>


        /// <returns>Successful response</returns>

        System.Threading.Tasks.Task<ReportResponseSchema> ReportPATCHAsync(string report_id, UpdateReport body);

    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]

    public partial class Controller : Microsoft.AspNetCore.Mvc.ControllerBase
    {
        private IController _implementation;

        public Controller(IController implementation)
        {
            _implementation = implementation;
        }

        /// <summary>
        /// Create an access token
        /// </summary>
        /// <remarks>
        /// Create an access token by including a refresh token in the `Authorization` header as `Bearer &lt;refresh_token&gt;`.
        /// </remarks>
        /// <param name="authorization">Refresh token in the format `Bearer &lt;refresh_token&gt;`.</param>
        /// <returns>Successful response</returns>
        [Microsoft.AspNetCore.Mvc.HttpPost, Microsoft.AspNetCore.Mvc.Route("v2/auth/refresh")]
        public System.Threading.Tasks.Task<CreateAccessTokenResponseSchema> Refresh([Microsoft.AspNetCore.Mvc.FromHeader] string authorization)
        {

            return _implementation.RefreshAsync(authorization);
        }

        /// <summary>
        /// Retrieve a list of studies.
        /// </summary>
        /// <remarks>
        /// Studies can be queried by their `created`, `state`, and `status` fields. Results are paginated and ordered by creation date in descending order.
        /// </remarks>
        /// <param name="id__in">Retrieve studies in bulk by `id`.</param>
        /// <param name="created__gte">Retrieve studies created after or on the specified ISO 8601 datetime (UTC).</param>
        /// <param name="created__gt">Retrieve studies created strictly after the specified ISO 8601 datetime (UTC).</param>
        /// <param name="created__lte">Retrieve studies created before or on the specified ISO 8601 datetime (UTC).</param>
        /// <param name="created__lt">Retrieve studies created strictly before the specified ISO 8601 datetime (UTC).</param>
        /// <param name="pid">Retrieve studies by `pid`.</param>
        /// <param name="pid__in">Retrieve studies in bulk by `pid`.</param>
        /// <param name="state">Limit results to studies in the specified state.</param>
        /// <param name="state__in">Limit results to studies in the specified states.</param>
        /// <param name="status">Limit results to studies in the specified status.</param>
        /// <param name="status__in">Limit results to studies in the specified statuses.</param>
        /// <param name="limit">Limit the number of results returned.</param>
        /// <param name="skip">A cursor to use for pagination.  The (zero-based) offset of the first item in the collection to return.</param>
        /// <returns>Successful response</returns>
        [Microsoft.AspNetCore.Mvc.HttpGet, Microsoft.AspNetCore.Mvc.Route("v2/study")]
        public System.Threading.Tasks.Task<PaginatedStudyResponseSchema> StudyGET([Microsoft.AspNetCore.Mvc.FromQuery] System.Collections.Generic.IEnumerable<string> id__in, [Microsoft.AspNetCore.Mvc.FromQuery] System.DateTimeOffset? created__gte, [Microsoft.AspNetCore.Mvc.FromQuery] System.DateTimeOffset? created__gt, [Microsoft.AspNetCore.Mvc.FromQuery] System.DateTimeOffset? created__lte, [Microsoft.AspNetCore.Mvc.FromQuery] System.DateTimeOffset? created__lt, [Microsoft.AspNetCore.Mvc.FromQuery] string pid, [Microsoft.AspNetCore.Mvc.FromQuery] System.Collections.Generic.IEnumerable<string> pid__in, [Microsoft.AspNetCore.Mvc.FromQuery] State? state, [Microsoft.AspNetCore.Mvc.FromQuery] System.Collections.Generic.IEnumerable<Anonymous> state__in, [Microsoft.AspNetCore.Mvc.FromQuery] Status? status, [Microsoft.AspNetCore.Mvc.FromQuery] System.Collections.Generic.IEnumerable<Anonymous2> status__in, [Microsoft.AspNetCore.Mvc.FromQuery] int? limit, [Microsoft.AspNetCore.Mvc.FromQuery] int? skip)
        {

            return _implementation.StudyGETAsync(id__in, created__gte, created__gt, created__lte, created__lt, pid, pid__in, state, state__in, status, status__in, limit, skip);
        }

        /// <summary>
        /// Create a study.
        /// </summary>
        /// <remarks>
        /// Most service accounts are configured to provide default values for `hardware`, `software`, and `desaturation` when you create a study, so only provide these values if instructed to or you need to override the defaults.
        /// </remarks>
        /// <returns>Successful response</returns>
        [Microsoft.AspNetCore.Mvc.HttpPost, Microsoft.AspNetCore.Mvc.Route("v2/study")]
        public System.Threading.Tasks.Task<StudyResponseSchema> StudyPOST([Microsoft.AspNetCore.Mvc.FromBody] CreateStudy body)
        {

            return _implementation.StudyPOSTAsync(body);
        }

        /// <summary>
        /// Retrieve a study by ID.
        /// </summary>
        /// <param name="study_id">The unique ID of the study.</param>
        /// <returns>Successful response</returns>
        [Microsoft.AspNetCore.Mvc.HttpGet, Microsoft.AspNetCore.Mvc.Route("v2/study/{study_id}")]
        public System.Threading.Tasks.Task<StudyResponseSchema> StudyGET2(string study_id)
        {

            return _implementation.StudyGET2Async(study_id);
        }

        /// <summary>
        /// Update a study by ID.
        /// </summary>
        /// <param name="study_id">The unique ID of the study.</param>
        /// <returns>Successful response</returns>
        [Microsoft.AspNetCore.Mvc.HttpPatch, Microsoft.AspNetCore.Mvc.Route("v2/study/{study_id}")]
        public System.Threading.Tasks.Task<StudyResponseSchema> StudyPATCH(string study_id, [Microsoft.AspNetCore.Mvc.FromBody] UpdateStudy body)
        {

            return _implementation.StudyPATCHAsync(study_id, body);
        }

        /// <summary>
        /// Retrieve a list of study files.
        /// </summary>
        /// <remarks>
        /// Retrieves study file metadata and a URL to download the study. Note that, even if the `content_encoding` of a file is `gzip`, Google Cloud Storage will automatically &lt;a href="https://cloud.google.com/storage/docs/transcoding"&gt;decompress the file&lt;/a&gt; for you upon download.
        /// </remarks>
        /// <param name="study_id">The unique ID of the study.</param>
        /// <param name="version">The version of the study. New studies should upload to the `CURRENT` version and grab EnsoSleep scoring from the `SCORED` version. A backup of the study is made to `ORIGINAL`.</param>
        /// <returns>Successful response</returns>
        [Microsoft.AspNetCore.Mvc.HttpGet, Microsoft.AspNetCore.Mvc.Route("v2/study/{study_id}/version/{version}/file")]
        public System.Threading.Tasks.Task<ListRetrieveFileResponseSchema> FileGET(string study_id, Version version)
        {

            return _implementation.FileGETAsync(study_id, version);
        }

        /// <summary>
        /// Create a study file.
        /// </summary>
        /// <remarks>
        /// Creating a study file will return an `upload_url`. You can upload the file to this URL by following the directions from &lt;a href="https://cloud.google.com/storage/docs/performing-resumable-uploads#upload-file"&gt;Google Cloud Storage - Performing Resumable Uploads&lt;/a&gt;. For large files or slower networks, we recommend the Multiple Chunk Upload method.  If you receive a timeout error, you need to reduce your chunk size to a smaller value that your network can tolerate.
        /// </remarks>
        /// <param name="study_id">The unique ID of the study.</param>
        /// <param name="version">The version of the study. New studies should upload to the `CURRENT` version and grab EnsoSleep scoring from the `SCORED` version. A backup of the study is made to `ORIGINAL`.</param>
        /// <returns>Successful response</returns>
        [Microsoft.AspNetCore.Mvc.HttpPost, Microsoft.AspNetCore.Mvc.Route("v2/study/{study_id}/version/{version}/file")]
        public System.Threading.Tasks.Task<CreateFileResponseSchema> FilePOST(string study_id, Version2 version, [Microsoft.AspNetCore.Mvc.FromBody] CreateFile body)
        {

            return _implementation.FilePOSTAsync(study_id, version, body);
        }

        /// <summary>
        /// Retrieve a study file by name.
        /// </summary>
        /// <remarks>
        /// Retrieves study file metadata and a URL to download the study. Note that, even if the `content_encoding` of a file is `gzip`, Google Cloud Storage will automatically &lt;a href="https://cloud.google.com/storage/docs/transcoding"&gt;decompress the file&lt;/a&gt; for you upon download.
        /// </remarks>
        /// <param name="study_id">The unique ID of the study.</param>
        /// <param name="version">The version of the study. New studies should upload to the `CURRENT` version and grab EnsoSleep scoring from the `SCORED` version. A backup of the study is made to `ORIGINAL`.</param>
        /// <param name="filename">The name of the file.</param>
        /// <returns>Successful response</returns>
        [Microsoft.AspNetCore.Mvc.HttpGet, Microsoft.AspNetCore.Mvc.Route("v2/study/{study_id}/version/{version}/file/{filename}")]
        public System.Threading.Tasks.Task<RetrieveFileResponseSchema> FileGET2(string study_id, Version3 version, string filename)
        {

            return _implementation.FileGET2Async(study_id, version, filename);
        }

        /// <summary>
        /// Retrieve a list of score sets.
        /// </summary>
        /// <remarks>
        /// Score sets can be queried by their `patient_id` and `name`. Results are paginated. To retreive EnsoSleep scoring, use the querystring `?patient_id=&lt;patient_id&gt;&amp;name=prepared`.
        /// </remarks>
        /// <param name="name">The name of the score set.</param>
        /// <param name="patient_id">The ID the study.</param>
        /// <returns>Successful response</returns>
        [Microsoft.AspNetCore.Mvc.HttpGet, Microsoft.AspNetCore.Mvc.Route("v2/scoreset")]
        public System.Threading.Tasks.Task<PaginatedScoreSetResponseSchema> Scoreset([Microsoft.AspNetCore.Mvc.FromQuery] string name, [Microsoft.AspNetCore.Mvc.FromQuery] System.Collections.Generic.IEnumerable<string> id__in, [Microsoft.AspNetCore.Mvc.FromQuery] string patient_id)
        {

            return _implementation.ScoresetAsync(name, id__in, patient_id);
        }

        /// <summary>
        /// Retrieve a score set by ID.
        /// </summary>
        /// <param name="scoreset_id">The unique ID of the score set.</param>
        /// <returns>Successful response</returns>
        [Microsoft.AspNetCore.Mvc.HttpGet, Microsoft.AspNetCore.Mvc.Route("v2/scoreset/{scoreset_id}")]
        public System.Threading.Tasks.Task<ScoreSetResponseSchema> Scoreset2(string scoreset_id)
        {

            return _implementation.Scoreset2Async(scoreset_id);
        }

        /// <summary>
        /// Retrieve a list of events for a given score set.
        /// </summary>
        /// <remarks>
        /// Use the querystring `?group_by=name` to group events by name for easier parsing.
        /// </remarks>
        /// <param name="group_by">Grouping events will return them as `{"hypopnea": [&lt;event_1&gt;, &lt;event_2&gt;], "desat": ...}`</param>
        /// <returns>Successful response</returns>
        [Microsoft.AspNetCore.Mvc.HttpGet, Microsoft.AspNetCore.Mvc.Route("v2/scoreset/{scoreset_id}/event")]
        public System.Threading.Tasks.Task<ListScoreEventResponseSchema> Event([Microsoft.AspNetCore.Mvc.FromQuery] Group_by? group_by)
        {

            return _implementation.EventAsync(group_by);
        }

        /// <summary>
        /// Retrieve a report by ID.
        /// </summary>
        /// <param name="report_id">The unique ID of the report.</param>
        /// <returns>Successful response</returns>
        [Microsoft.AspNetCore.Mvc.HttpGet, Microsoft.AspNetCore.Mvc.Route("v2/report/{report_id}")]
        public System.Threading.Tasks.Task<ReportResponseSchema> ReportGET(string report_id)
        {

            return _implementation.ReportGETAsync(report_id);
        }

        /// <summary>
        /// Update a report's notes.
        /// </summary>
        /// <param name="report_id">The unique ID of the report.</param>
        /// <returns>Successful response</returns>
        [Microsoft.AspNetCore.Mvc.HttpPatch, Microsoft.AspNetCore.Mvc.Route("v2/report/{report_id}")]
        public System.Threading.Tasks.Task<ReportResponseSchema> ReportPATCH(string report_id, [Microsoft.AspNetCore.Mvc.FromBody] UpdateReport body)
        {

            return _implementation.ReportPATCHAsync(report_id, body);
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public interface IGenerateController
    {

        /// <summary>
        /// Generate a link that allows external users with the link to access a resource.
        /// </summary>

        /// <remarks>
        /// Generates an expiring signed url that contains information required
        /// <br/>to access a resource within EnsoSleep,  available at an unprotected route.
        /// <br/>The link expiration defaults to 90 minutes, but is configurable.
        /// </remarks>

        /// <param name="expiring_link_type">Type of resource the expiring link grants access to</param>


        /// <returns>Successful response</returns>

        System.Threading.Tasks.Task<GenerateLinkResponseSchema> LinkAsync(Expiring_link_type expiring_link_type, ExpiringLinkStudy body);

    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]

    public partial class GenerateController : Microsoft.AspNetCore.Mvc.ControllerBase
    {
        private IGenerateController _implementation;

        public GenerateController(IGenerateController implementation)
        {
            _implementation = implementation;
        }

        /// <summary>
        /// Generate a link that allows external users with the link to access a resource.
        /// </summary>
        /// <remarks>
        /// Generates an expiring signed url that contains information required
        /// <br/>to access a resource within EnsoSleep,  available at an unprotected route.
        /// <br/>The link expiration defaults to 90 minutes, but is configurable.
        /// </remarks>
        /// <param name="expiring_link_type">Type of resource the expiring link grants access to</param>
        /// <returns>Successful response</returns>
        [Microsoft.AspNetCore.Mvc.HttpPost, Microsoft.AspNetCore.Mvc.Route("v2/expiring_link/{expiring_link_type}/generate_link")]
        public System.Threading.Tasks.Task<GenerateLinkResponseSchema> Link(Expiring_link_type expiring_link_type, [Microsoft.AspNetCore.Mvc.FromBody] ExpiringLinkStudy body)
        {

            return _implementation.LinkAsync(expiring_link_type, body);
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GenerateLink
    {
        /// <summary>
        /// Signed Expiring URL that grants an external user access to a study.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("url", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Url { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GenerateLinkResponseSchema
    {
        [Newtonsoft.Json.JsonProperty("message", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Message { get; set; }

        [Newtonsoft.Json.JsonProperty("data", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public GenerateLink Data { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CreateAccessToken
    {
        /// <summary>
        /// An access token that can be used to access protected endpoints.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("access_token", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Access_token { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CreateAccessTokenResponseSchema
    {
        [Newtonsoft.Json.JsonProperty("message", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Message { get; set; }

        [Newtonsoft.Json.JsonProperty("data", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public CreateAccessToken Data { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Export
    {
        /// <summary>
        /// Scoreset ID
        /// </summary>
        [Newtonsoft.Json.JsonProperty("scoreset_id", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Scoreset_id { get; set; }

        /// <summary>
        /// Report ID
        /// </summary>
        [Newtonsoft.Json.JsonProperty("report_id", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Report_id { get; set; }

        /// <summary>
        /// Patient ID
        /// </summary>
        [Newtonsoft.Json.JsonProperty("patient_id", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Patient_id { get; set; }

        /// <summary>
        /// Return as HTML instead of PDF
        /// </summary>
        [Newtonsoft.Json.JsonProperty("export_html", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool Export_html { get; set; }

        /// <summary>
        /// Used to override the header/footer report text
        /// </summary>
        [Newtonsoft.Json.JsonProperty("report_name", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Report_name { get; set; }

        /// <summary>
        /// Template ID
        /// </summary>
        [Newtonsoft.Json.JsonProperty("template_id", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Template_id { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ExportSchema
    {
        [Newtonsoft.Json.JsonProperty("message", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Message { get; set; }

        [Newtonsoft.Json.JsonProperty("data", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Export Data { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Event
    {
        [Newtonsoft.Json.JsonProperty("count", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Count { get; set; }

        [Newtonsoft.Json.JsonProperty("total_duration", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Total_duration { get; set; }

        [Newtonsoft.Json.JsonProperty("median_duration", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Median_duration { get; set; }

        [Newtonsoft.Json.JsonProperty("percent", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double Percent { get; set; }

        [Newtonsoft.Json.JsonProperty("min_duration", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Min_duration { get; set; }

        [Newtonsoft.Json.JsonProperty("max_duration", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Max_duration { get; set; }

        [Newtonsoft.Json.JsonProperty("mean_duration", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Mean_duration { get; set; }

        [Newtonsoft.Json.JsonProperty("index", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double Index { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SimpleEvent
    {
        [Newtonsoft.Json.JsonProperty("count", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Count { get; set; }

        [Newtonsoft.Json.JsonProperty("index", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double Index { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Associations
    {
        [Newtonsoft.Json.JsonProperty("arousal", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.IDictionary<string, SimpleEvent> Arousal { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Axis
    {
        [Newtonsoft.Json.JsonProperty("events", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.IDictionary<string, Event> Events { get; set; }

        [Newtonsoft.Json.JsonProperty("percent", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double Percent { get; set; }

        [Newtonsoft.Json.JsonProperty("sleep_time", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Sleep_time { get; set; }

        [Newtonsoft.Json.JsonProperty("seconds", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Seconds { get; set; }

        [Newtonsoft.Json.JsonProperty("associations", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Associations Associations { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Staging
    {
        [Newtonsoft.Json.JsonProperty("n1", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Axis N1 { get; set; }

        [Newtonsoft.Json.JsonProperty("non_rem", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Axis Non_rem { get; set; }

        [Newtonsoft.Json.JsonProperty("n2", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Axis N2 { get; set; }

        [Newtonsoft.Json.JsonProperty("sleep", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Axis Sleep { get; set; }

        [Newtonsoft.Json.JsonProperty("n3", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Axis N3 { get; set; }

        [Newtonsoft.Json.JsonProperty("rem_supine", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Axis Rem_supine { get; set; }

        [Newtonsoft.Json.JsonProperty("rem", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Axis Rem { get; set; }

        [Newtonsoft.Json.JsonProperty("wake", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Axis Wake { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Position
    {
        [Newtonsoft.Json.JsonProperty("right", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Axis Right { get; set; }

        [Newtonsoft.Json.JsonProperty("left", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Axis Left { get; set; }

        [Newtonsoft.Json.JsonProperty("prone", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Axis Prone { get; set; }

        [Newtonsoft.Json.JsonProperty("supine", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Axis Supine { get; set; }

        [Newtonsoft.Json.JsonProperty("non_supine", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Axis Non_supine { get; set; }

        [Newtonsoft.Json.JsonProperty("upright", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Axis Upright { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class TimeInRange
    {
        [Newtonsoft.Json.JsonProperty("duration", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Duration { get; set; }

        [Newtonsoft.Json.JsonProperty("upper_bound", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Upper_bound { get; set; }

        [Newtonsoft.Json.JsonProperty("percent", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double Percent { get; set; }

        [Newtonsoft.Json.JsonProperty("lower_bound", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Lower_bound { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Signal
    {
        [Newtonsoft.Json.JsonProperty("min", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Min { get; set; }

        [Newtonsoft.Json.JsonProperty("time_in_range", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<TimeInRange> Time_in_range { get; set; }

        [Newtonsoft.Json.JsonProperty("max", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Max { get; set; }

        [Newtonsoft.Json.JsonProperty("mean", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Mean { get; set; }

        [Newtonsoft.Json.JsonProperty("median", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Median { get; set; }

        [Newtonsoft.Json.JsonProperty("percent_bad_data", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double Percent_bad_data { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Signals
    {
        [Newtonsoft.Json.JsonProperty("spo2", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.IDictionary<string, Signal> Spo2 { get; set; }

        [Newtonsoft.Json.JsonProperty("heart_rate", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.IDictionary<string, Signal> Heart_rate { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Titration
    {
        [Newtonsoft.Json.JsonProperty("recording_time", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Recording_time { get; set; }

        [Newtonsoft.Json.JsonProperty("awakenings", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Awakenings { get; set; }

        [Newtonsoft.Json.JsonProperty("staging", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Staging Staging { get; set; }

        [Newtonsoft.Json.JsonProperty("end", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int End { get; set; }

        [Newtonsoft.Json.JsonProperty("start_time", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTimeOffset Start_time { get; set; }

        [Newtonsoft.Json.JsonProperty("position", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Position Position { get; set; }

        [Newtonsoft.Json.JsonProperty("associations", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Associations Associations { get; set; }

        [Newtonsoft.Json.JsonProperty("duration", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Duration { get; set; }

        [Newtonsoft.Json.JsonProperty("sleep_latency", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Sleep_latency { get; set; }

        [Newtonsoft.Json.JsonProperty("end_time", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTimeOffset End_time { get; set; }

        [Newtonsoft.Json.JsonProperty("wake_after_sleep_onset", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Wake_after_sleep_onset { get; set; }

        [Newtonsoft.Json.JsonProperty("sleep_fragmentation", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Sleep_fragmentation { get; set; }

        [Newtonsoft.Json.JsonProperty("monitoring_time", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Monitoring_time { get; set; }

        [Newtonsoft.Json.JsonProperty("start", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Start { get; set; }

        [Newtonsoft.Json.JsonProperty("events", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.IDictionary<string, Event> Events { get; set; }

        [Newtonsoft.Json.JsonProperty("sleep_time", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Sleep_time { get; set; }

        [Newtonsoft.Json.JsonProperty("sleep_efficiency", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double Sleep_efficiency { get; set; }

        [Newtonsoft.Json.JsonProperty("treatment", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Treatment { get; set; }

        [Newtonsoft.Json.JsonProperty("signals", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Signals Signals { get; set; }

        [Newtonsoft.Json.JsonProperty("settings", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.IDictionary<string, double> Settings { get; set; }

        [Newtonsoft.Json.JsonProperty("rem_latency", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Rem_latency { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SubReport
    {
        [Newtonsoft.Json.JsonProperty("recording_time", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Recording_time { get; set; }

        [Newtonsoft.Json.JsonProperty("awakenings", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Awakenings { get; set; }

        [Newtonsoft.Json.JsonProperty("staging", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Staging Staging { get; set; }

        [Newtonsoft.Json.JsonProperty("end", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int End { get; set; }

        [Newtonsoft.Json.JsonProperty("start_time", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTimeOffset Start_time { get; set; }

        [Newtonsoft.Json.JsonProperty("position", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Position Position { get; set; }

        [Newtonsoft.Json.JsonProperty("associations", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Associations Associations { get; set; }

        [Newtonsoft.Json.JsonProperty("duration", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Duration { get; set; }

        [Newtonsoft.Json.JsonProperty("sleep_latency", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Sleep_latency { get; set; }

        [Newtonsoft.Json.JsonProperty("end_time", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTimeOffset End_time { get; set; }

        [Newtonsoft.Json.JsonProperty("wake_after_sleep_onset", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Wake_after_sleep_onset { get; set; }

        [Newtonsoft.Json.JsonProperty("sleep_fragmentation", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Sleep_fragmentation { get; set; }

        [Newtonsoft.Json.JsonProperty("monitoring_time", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Monitoring_time { get; set; }

        [Newtonsoft.Json.JsonProperty("start", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Start { get; set; }

        [Newtonsoft.Json.JsonProperty("events", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.IDictionary<string, Event> Events { get; set; }

        [Newtonsoft.Json.JsonProperty("sleep_time", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Sleep_time { get; set; }

        [Newtonsoft.Json.JsonProperty("sleep_efficiency", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double Sleep_efficiency { get; set; }

        [Newtonsoft.Json.JsonProperty("signals", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Signals Signals { get; set; }

        [Newtonsoft.Json.JsonProperty("rem_latency", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Rem_latency { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class StudyMetrics
    {
        [Newtonsoft.Json.JsonProperty("recording_time", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Recording_time { get; set; }

        [Newtonsoft.Json.JsonProperty("awakenings", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Awakenings { get; set; }

        [Newtonsoft.Json.JsonProperty("staging", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Staging Staging { get; set; }

        [Newtonsoft.Json.JsonProperty("end", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int End { get; set; }

        [Newtonsoft.Json.JsonProperty("total_recording_time", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Total_recording_time { get; set; }

        [Newtonsoft.Json.JsonProperty("start_time", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTimeOffset Start_time { get; set; }

        [Newtonsoft.Json.JsonProperty("position", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Position Position { get; set; }

        [Newtonsoft.Json.JsonProperty("associations", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Associations Associations { get; set; }

        [Newtonsoft.Json.JsonProperty("duration", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Duration { get; set; }

        [Newtonsoft.Json.JsonProperty("sleep_latency", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Sleep_latency { get; set; }

        [Newtonsoft.Json.JsonProperty("alerts_count", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Alerts_count { get; set; }

        [Newtonsoft.Json.JsonProperty("end_time", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTimeOffset End_time { get; set; }

        [Newtonsoft.Json.JsonProperty("wake_after_sleep_onset", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Wake_after_sleep_onset { get; set; }

        [Newtonsoft.Json.JsonProperty("sleep_fragmentation", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Sleep_fragmentation { get; set; }

        [Newtonsoft.Json.JsonProperty("monitoring_time", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Monitoring_time { get; set; }

        [Newtonsoft.Json.JsonProperty("total_sleep_time", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Total_sleep_time { get; set; }

        [Newtonsoft.Json.JsonProperty("lights_on_time", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTimeOffset Lights_on_time { get; set; }

        [Newtonsoft.Json.JsonProperty("start", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Start { get; set; }

        [Newtonsoft.Json.JsonProperty("events", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.IDictionary<string, Event> Events { get; set; }

        [Newtonsoft.Json.JsonProperty("lights_off_time", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTimeOffset Lights_off_time { get; set; }

        [Newtonsoft.Json.JsonProperty("sleep_time", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Sleep_time { get; set; }

        [Newtonsoft.Json.JsonProperty("lights_on", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<int> Lights_on { get; set; }

        [Newtonsoft.Json.JsonProperty("lights_off", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<int> Lights_off { get; set; }

        [Newtonsoft.Json.JsonProperty("sleep_efficiency", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double Sleep_efficiency { get; set; }

        [Newtonsoft.Json.JsonProperty("signals", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Signals Signals { get; set; }

        [Newtonsoft.Json.JsonProperty("rem_latency", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Rem_latency { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Report
    {
        [Newtonsoft.Json.JsonProperty("titration", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<Titration> Titration { get; set; }

        [Newtonsoft.Json.JsonProperty("split", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public SubReport Split { get; set; }

        [Newtonsoft.Json.JsonProperty("notes", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Notes { get; set; }

        [Newtonsoft.Json.JsonProperty("study", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public StudyMetrics Study { get; set; }

        [Newtonsoft.Json.JsonProperty("warnings", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public object Warnings { get; set; }

        [Newtonsoft.Json.JsonProperty("mslt", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<SubReport> Mslt { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ReportResponseSchema
    {
        [Newtonsoft.Json.JsonProperty("message", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Message { get; set; }

        [Newtonsoft.Json.JsonProperty("data", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Report Data { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ExportReport
    {
        /// <summary>
        /// Template ID
        /// </summary>
        [Newtonsoft.Json.JsonProperty("template_id", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Template_id { get; set; }

        /// <summary>
        /// Used to override the header/footer report text
        /// </summary>
        [Newtonsoft.Json.JsonProperty("report_name", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Report_name { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ExportReportSchema
    {
        [Newtonsoft.Json.JsonProperty("message", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Message { get; set; }

        [Newtonsoft.Json.JsonProperty("data", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExportReport Data { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ScoreSet
    {
        /// <summary>
        /// The unique ID of the score set.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Id { get; set; }

        /// <summary>
        /// The ID the study.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("patient_id", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Patient_id { get; set; }

        /// <summary>
        /// The ID of the clinic
        /// </summary>
        [Newtonsoft.Json.JsonProperty("clinic_id", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Clinic_id { get; set; }

        /// <summary>
        /// The name of the score set.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("name", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Name { get; set; }

        /// <summary>
        /// ISO 8601 datetime (UTC) when the score set was created.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("created", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTimeOffset Created { get; set; }

        /// <summary>
        /// ISO 8601 datetime (UTC) when the score set was last modified.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("last_modified", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTimeOffset Last_modified { get; set; }

        /// <summary>
        /// ID of the user who created the score set.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("user_id", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string User_id { get; set; }

        /// <summary>
        /// Name of the parent score set.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("copy_from", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Copy_from { get; set; }

        /// <summary>
        /// Alias name for the score set.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("alias", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Alias { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PaginatedScoreSetResponseSchema
    {
        [Newtonsoft.Json.JsonProperty("message", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Message { get; set; }

        [Newtonsoft.Json.JsonProperty("data", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<ScoreSet> Data { get; set; }

        /// <summary>
        /// Total number of documents returned.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("returned", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Returned { get; set; }

        /// <summary>
        /// Total number of documents matching the query, including those not returned in the response.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("count", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Count { get; set; }

        /// <summary>
        /// Whether or not this list has another page of items after this one that can be fetched.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("has_next", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool Has_next { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ScoreSetResponseSchema
    {
        [Newtonsoft.Json.JsonProperty("message", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Message { get; set; }

        [Newtonsoft.Json.JsonProperty("data", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ScoreSet Data { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ScoreEvent
    {
        /// <summary>
        /// The unique UUID of the event.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Id { get; set; }

        /// <summary>
        /// Name of the event.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("name", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ScoreEventName Name { get; set; }

        /// <summary>
        /// The start time in seconds of the event.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("start", Required = Newtonsoft.Json.Required.Always)]
        public double Start { get; set; }

        /// <summary>
        /// The duration in seconds of the event.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("duration", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double? Duration { get; set; }

        /// <summary>
        /// Heart rate before the event.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("hr_before", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double Hr_before { get; set; }

        /// <summary>
        /// Heart rate extremum during the event.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("hr_extreme", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double Hr_extreme { get; set; }

        /// <summary>
        /// Oxygen level before the event.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("o2_before", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double O2_before { get; set; }

        /// <summary>
        /// Minimum oxygen level during the event.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("o2_min", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double O2_min { get; set; }

        /// <summary>
        /// The association for `arousal` events.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("association", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ScoreEventAssociation Association { get; set; }

        /// <summary>
        /// Event channel.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("channel", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Channel { get; set; }

        /// <summary>
        /// The titration settings and value for any titration event.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("titration", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.IDictionary<string, double> Titration { get; set; }

        /// <summary>
        /// User comments on the event.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("comment", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Comment { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ListScoreEventResponseSchema
    {
        [Newtonsoft.Json.JsonProperty("message", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Message { get; set; }

        [Newtonsoft.Json.JsonProperty("data", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<ScoreEvent> Data { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Management
    {
        /// <summary>
        /// The current state of the study in the management workflow.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("state", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string State { get; set; }

        /// <summary>
        /// The time the management state was last changed.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("last_changed", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTimeOffset? Last_changed { get; set; }

        /// <summary>
        /// Whether or not the study has been locked.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("locked", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool Locked { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class StudyPatient
    {
        /// <summary>
        /// ISO 8601 date in the format `YYYY-MM-DD`.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("birth_date", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset? Birth_date { get; set; }

        /// <summary>
        /// Body mass index.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("bmi", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double? Bmi { get; set; }

        /// <summary>
        /// Patient's first name.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("first_name", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string First_name { get; set; }

        /// <summary>
        /// Patient's gender.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("gender", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public StudyPatientGender? Gender { get; set; }

        /// <summary>
        /// Patient's height in inches.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("height", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double? Height { get; set; }

        /// <summary>
        /// Patient's last name.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("last_name", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Last_name { get; set; }

        /// <summary>
        /// Array of medications the patient takes.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("medication", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<string> Medication { get; set; }

        /// <summary>
        /// Medical record number of the Patient
        /// </summary>
        [Newtonsoft.Json.JsonProperty("mrn", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mrn { get; set; }

        /// <summary>
        /// Patient's neck size in inches.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("neck_size", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Neck_size { get; set; }

        /// <summary>
        /// Order number for the specific Study
        /// </summary>
        [Newtonsoft.Json.JsonProperty("order_number", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Order_number { get; set; }

        /// <summary>
        /// Patient's weight in lbs.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("weight", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double? Weight { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class StudyOptions
    {
        /// <summary>
        /// Desaturation criterion for scoring. Use `3` to score at least 3% desaturations. Use `4` to score at least 4% desaturations.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("desaturation", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public StudyOptionsDesaturation Desaturation { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Realtime
    {
        /// <summary>
        /// Total seconds of data scored during realtime scoring
        /// </summary>
        [Newtonsoft.Json.JsonProperty("scored_seconds", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Scored_seconds { get; set; }

        /// <summary>
        /// State of realtime scoring
        /// </summary>
        [Newtonsoft.Json.JsonProperty("enabled", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool Enabled { get; set; }

        /// <summary>
        /// Total seconds of data read and saved in hdf5 files
        /// </summary>
        [Newtonsoft.Json.JsonProperty("read_seconds", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Read_seconds { get; set; }

        /// <summary>
        /// Completed state for realtime scoring
        /// </summary>
        [Newtonsoft.Json.JsonProperty("complete", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool Complete { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Advice
    {
        /// <summary>
        /// Minimum total sleep time before alert is triggered
        /// </summary>
        [Newtonsoft.Json.JsonProperty("min_tst", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Min_tst { get; set; }

        /// <summary>
        /// Minimum apnea-hypopnea index before alert is triggered
        /// </summary>
        [Newtonsoft.Json.JsonProperty("min_ahi", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double Min_ahi { get; set; }

        /// <summary>
        /// Minimum SPO2 value before alert is triggered
        /// </summary>
        [Newtonsoft.Json.JsonProperty("min_spo2", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Min_spo2 { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Study
    {
        /// <summary>
        /// The unique identifier of the study.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Id { get; set; }

        /// <summary>
        /// ISO 8601 datetime (UTC) when study was created.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("created", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTimeOffset Created { get; set; }

        /// <summary>
        /// The ID of the clinic to which the study belongs.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("clinic_id", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Clinic_id { get; set; }

        /// <summary>
        /// The ID of the computer from which the study was uploaded.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("computer_id", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Computer_id { get; set; }

        /// <summary>
        /// Software used to encode study data.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("software", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public StudySoftware Software { get; set; }

        /// <summary>
        /// Hardware used to collect signal data.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("hardware", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Hardware { get; set; }

        [Newtonsoft.Json.JsonProperty("management", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Management Management { get; set; }

        [Newtonsoft.Json.JsonProperty("patient", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public StudyPatient Patient { get; set; }

        /// <summary>
        /// External Patient ID.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("pid", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Pid { get; set; }

        [Newtonsoft.Json.JsonProperty("options", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public StudyOptions Options { get; set; }

        /// <summary>
        /// The type of the study.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("study_type", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Study_type { get; set; }

        /// <summary>
        /// The state of the study.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("state", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public StudyState State { get; set; }

        /// <summary>
        /// The status of the study
        /// </summary>
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public StudyStatus Status { get; set; }

        [Newtonsoft.Json.JsonProperty("realtime", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Realtime Realtime { get; set; }

        [Newtonsoft.Json.JsonProperty("advice", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Advice Advice { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class StudyResponseSchema
    {
        [Newtonsoft.Json.JsonProperty("message", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Message { get; set; }

        [Newtonsoft.Json.JsonProperty("data", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Study Data { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PaginatedStudyResponseSchema
    {
        [Newtonsoft.Json.JsonProperty("message", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Message { get; set; }

        [Newtonsoft.Json.JsonProperty("data", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<Study> Data { get; set; }

        /// <summary>
        /// Total number of documents returned.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("returned", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Returned { get; set; }

        /// <summary>
        /// Total number of documents matching the query, including those not returned in the response.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("count", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Count { get; set; }

        /// <summary>
        /// Whether or not this list has another page of items after this one that can be fetched.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("has_next", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool Has_next { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CreateFile
    {
        /// <summary>
        /// Name of the file. Do not include this field if this is an EDF study.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("filename", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Filename { get; set; }

        /// <summary>
        /// Content encoding of the file. Use `gzip` if you plan to gzip compress your uploads.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("content_encoding", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Content_encoding { get; set; }

        /// <summary>
        /// URL to upload the file.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("upload_url", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Upload_url { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CreateFileResponseSchema
    {
        [Newtonsoft.Json.JsonProperty("message", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Message { get; set; }

        [Newtonsoft.Json.JsonProperty("data", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public CreateFile Data { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class RetrieveFile
    {
        /// <summary>
        /// Name of the file. Do not include this field if this is an EDF study.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("filename", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Filename { get; set; }

        /// <summary>
        /// Content encoding of the file. Use `gzip` if you plan to gzip compress your uploads.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("content_encoding", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Content_encoding { get; set; }

        /// <summary>
        /// URL to download the file.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("url", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Url { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class RetrieveFileResponseSchema
    {
        [Newtonsoft.Json.JsonProperty("message", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Message { get; set; }

        [Newtonsoft.Json.JsonProperty("data", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public RetrieveFile Data { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ListRetrieveFileResponseSchema
    {
        [Newtonsoft.Json.JsonProperty("message", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Message { get; set; }

        [Newtonsoft.Json.JsonProperty("data", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<RetrieveFile> Data { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ExpiringLinkStudy
    {
        /// <summary>
        /// External user's email address.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("email", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Email { get; set; }

        /// <summary>
        /// External user's first name.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("first_name", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string First_name { get; set; }

        /// <summary>
        /// External user's last name.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("last_name", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Last_name { get; set; }

        /// <summary>
        /// The unique ID of the study.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("study_id", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Study_id { get; set; }

        /// <summary>
        /// The unique ID of the scoreset that should be initially displayed when the external user accesses the study.              Exclude from the request to bring external user to the scoreset selection window.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("initial_score_set_id", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Initial_score_set_id { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CreateStudy
    {
        /// <summary>
        /// The unique identifier of the study.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Id { get; set; }

        /// <summary>
        /// ISO 8601 datetime (UTC) when study was created.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("created", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTimeOffset Created { get; set; }

        /// <summary>
        /// The ID of the clinic to which the study belongs.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("clinic_id", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Clinic_id { get; set; }

        /// <summary>
        /// The ID of the computer from which the study was uploaded.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("computer_id", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Computer_id { get; set; }

        /// <summary>
        /// Software used to encode study data.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("software", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public CreateStudySoftware Software { get; set; }

        /// <summary>
        /// Hardware used to collect signal data.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("hardware", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Hardware { get; set; }

        [Newtonsoft.Json.JsonProperty("management", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Management Management { get; set; }

        [Newtonsoft.Json.JsonProperty("patient", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public StudyPatient Patient { get; set; }

        /// <summary>
        /// External Patient ID.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("pid", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Pid { get; set; }

        [Newtonsoft.Json.JsonProperty("options", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public StudyOptions Options { get; set; }

        /// <summary>
        /// The type of the study.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("study_type", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Study_type { get; set; }

        /// <summary>
        /// The state of the study.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("state", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public CreateStudyState State { get; set; }

        /// <summary>
        /// The status of the study
        /// </summary>
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public CreateStudyStatus Status { get; set; }

        [Newtonsoft.Json.JsonProperty("realtime", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Realtime Realtime { get; set; }

        [Newtonsoft.Json.JsonProperty("advice", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Advice Advice { get; set; }

        /// <summary>
        /// The upload root folder on the computer in which the study resides.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("root", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Root { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class UpdateStudy
    {
        /// <summary>
        /// The unique identifier of the study.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Id { get; set; }

        /// <summary>
        /// ISO 8601 datetime (UTC) when study was created.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("created", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTimeOffset Created { get; set; }

        /// <summary>
        /// The ID of the clinic to which the study belongs.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("clinic_id", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Clinic_id { get; set; }

        /// <summary>
        /// The ID of the computer from which the study was uploaded.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("computer_id", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Computer_id { get; set; }

        /// <summary>
        /// Software used to encode study data.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("software", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public UpdateStudySoftware Software { get; set; }

        /// <summary>
        /// Hardware used to collect signal data.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("hardware", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Hardware { get; set; }

        [Newtonsoft.Json.JsonProperty("management", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Management Management { get; set; }

        [Newtonsoft.Json.JsonProperty("patient", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public StudyPatient Patient { get; set; }

        /// <summary>
        /// External Patient ID.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("pid", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Pid { get; set; }

        [Newtonsoft.Json.JsonProperty("options", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public StudyOptions Options { get; set; }

        /// <summary>
        /// The type of the study.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("study_type", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Study_type { get; set; }

        /// <summary>
        /// The state of the study.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("state", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public UpdateStudyState State { get; set; }

        /// <summary>
        /// The status of the study
        /// </summary>
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public UpdateStudyStatus Status { get; set; }

        [Newtonsoft.Json.JsonProperty("realtime", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Realtime Realtime { get; set; }

        [Newtonsoft.Json.JsonProperty("advice", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Advice Advice { get; set; }

        /// <summary>
        /// ISO 8601 date in the format `YYYY-MM-DD`.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("patient__birth_date", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset? Patient__birth_date { get; set; }

        /// <summary>
        /// Body mass index.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("patient__bmi", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double? Patient__bmi { get; set; }

        /// <summary>
        /// Patient's first name.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("patient__first_name", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Patient__first_name { get; set; }

        /// <summary>
        /// Patient's gender.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("patient__gender", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public UpdateStudyPatient__gender? Patient__gender { get; set; }

        /// <summary>
        /// Patient's height in inches.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("patient__height", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double? Patient__height { get; set; }

        /// <summary>
        /// Patient's last name.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("patient__last_name", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Patient__last_name { get; set; }

        /// <summary>
        /// Patient medical record number.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("patient__mrn", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Patient__mrn { get; set; }

        /// <summary>
        /// Name of a medication to append to the array of medications.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("push__patient__medication", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Push__patient__medication { get; set; }

        /// <summary>
        /// Array of medications to add to the end of the array of all medications.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("push_all__patient__medication", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<string> Push_all__patient__medication { get; set; }

        /// <summary>
        /// Remove the specified medication from the array of all medications.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("pull__patient__medication", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Pull__patient__medication { get; set; }

        /// <summary>
        /// Array of medications to be removed from the array of all medications.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("pull_all__patient__medication", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<string> Pull_all__patient__medication { get; set; }

        /// <summary>
        /// Add the specified medication to the list of all medications if it does not already exist.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("add_to_set__patient__medication", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Add_to_set__patient__medication { get; set; }

        /// <summary>
        /// Patient's neck size in inches.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("patient__neck_size", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Patient__neck_size { get; set; }

        /// <summary>
        /// Order number for Study.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("patient__order_number", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Patient__order_number { get; set; }

        /// <summary>
        /// Patient's weight in lbs.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("patient__weight", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double? Patient__weight { get; set; }

        /// <summary>
        /// Desaturation criterion for scoring. Use `3` to score at least 3% desaturations. Use `4` to score at least 4% desaturations.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("options__desaturation", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public UpdateStudyOptions__desaturation Options__desaturation { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class UpdateReport
    {
        [Newtonsoft.Json.JsonProperty("notes", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Notes { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum Expiring_link_type
    {

        [System.Runtime.Serialization.EnumMember(Value = @"study")]
        Study = 0,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum State
    {

        [System.Runtime.Serialization.EnumMember(Value = @"New")]
        New = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Processing")]
        Processing = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"Done")]
        Done = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"Skipped")]
        Skipped = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"Error")]
        Error = 4,

    }

    /// <summary>
    /// Limit results to studies in the specified state.
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum Anonymous
    {

        [System.Runtime.Serialization.EnumMember(Value = @"New")]
        New = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Processing")]
        Processing = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"Done")]
        Done = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"Skipped")]
        Skipped = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"Error")]
        Error = 4,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum Status
    {

        [System.Runtime.Serialization.EnumMember(Value = @"DEBUG")]
        DEBUG = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"CLOUDPAT_CONVERT_ERR")]
        CLOUDPAT_CONVERT_ERR = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"MISSING_CHANNELS_ERR")]
        MISSING_CHANNELS_ERR = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"MALFORMED_LIGHTS_ERR")]
        MALFORMED_LIGHTS_ERR = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"FAIL_ERR")]
        FAIL_ERR = 4,

        [System.Runtime.Serialization.EnumMember(Value = @"NO_LONGER_EXISTS")]
        NO_LONGER_EXISTS = 5,

        [System.Runtime.Serialization.EnumMember(Value = @"PEDIATRIC")]
        PEDIATRIC = 6,

        [System.Runtime.Serialization.EnumMember(Value = @"API_ERR")]
        API_ERR = 7,

        [System.Runtime.Serialization.EnumMember(Value = @"REPORT_ERR")]
        REPORT_ERR = 8,

        [System.Runtime.Serialization.EnumMember(Value = @"MSLT_ERR")]
        MSLT_ERR = 9,

        [System.Runtime.Serialization.EnumMember(Value = @"NATUS_CONV_ERR")]
        NATUS_CONV_ERR = 10,

        [System.Runtime.Serialization.EnumMember(Value = @"SKIP_ERR")]
        SKIP_ERR = 11,

        [System.Runtime.Serialization.EnumMember(Value = @"EDF_ERR")]
        EDF_ERR = 12,

        [System.Runtime.Serialization.EnumMember(Value = @"SMALL_EDF_ERR")]
        SMALL_EDF_ERR = 13,

        [System.Runtime.Serialization.EnumMember(Value = @"BACKEND_ERR")]
        BACKEND_ERR = 14,

        [System.Runtime.Serialization.EnumMember(Value = @"WEB_ERR")]
        WEB_ERR = 15,

        [System.Runtime.Serialization.EnumMember(Value = @"INTEGRATION_SIGNAL_ERR")]
        INTEGRATION_SIGNAL_ERR = 16,

        [System.Runtime.Serialization.EnumMember(Value = @"INTEGRATION_WRITE_ERR")]
        INTEGRATION_WRITE_ERR = 17,

        [System.Runtime.Serialization.EnumMember(Value = @"INTEGRATION_READ_ERR")]
        INTEGRATION_READ_ERR = 18,

        [System.Runtime.Serialization.EnumMember(Value = @"MALFORMED_ERR")]
        MALFORMED_ERR = 19,

        [System.Runtime.Serialization.EnumMember(Value = @"HOLD_ERR")]
        HOLD_ERR = 20,

        [System.Runtime.Serialization.EnumMember(Value = @"DID_ERR")]
        DID_ERR = 21,

        [System.Runtime.Serialization.EnumMember(Value = @"SVC_ERR")]
        SVC_ERR = 22,

        [System.Runtime.Serialization.EnumMember(Value = @"ERROR")]
        ERROR = 23,

        [System.Runtime.Serialization.EnumMember(Value = @"UPLOADING")]
        UPLOADING = 24,

        [System.Runtime.Serialization.EnumMember(Value = @"UPLOADED")]
        UPLOADED = 25,

        [System.Runtime.Serialization.EnumMember(Value = @"DEIDENTIFYING")]
        DEIDENTIFYING = 26,

        [System.Runtime.Serialization.EnumMember(Value = @"TO_CONVERT")]
        TO_CONVERT = 27,

        [System.Runtime.Serialization.EnumMember(Value = @"TO_PROCESS")]
        TO_PROCESS = 28,

        [System.Runtime.Serialization.EnumMember(Value = @"DOWNLOADING")]
        DOWNLOADING = 29,

        [System.Runtime.Serialization.EnumMember(Value = @"COMPLETE")]
        COMPLETE = 30,

        [System.Runtime.Serialization.EnumMember(Value = @"REVERT")]
        REVERT = 31,

        [System.Runtime.Serialization.EnumMember(Value = @"HISTORICAL")]
        HISTORICAL = 32,

        [System.Runtime.Serialization.EnumMember(Value = @"HISTORICAL_DID")]
        HISTORICAL_DID = 33,

        [System.Runtime.Serialization.EnumMember(Value = @"PATIENT_OBJ")]
        PATIENT_OBJ = 34,

        [System.Runtime.Serialization.EnumMember(Value = @"EXTRACT")]
        EXTRACT = 35,

        [System.Runtime.Serialization.EnumMember(Value = @"CLASSIFY")]
        CLASSIFY = 36,

        [System.Runtime.Serialization.EnumMember(Value = @"POST_PROCESS")]
        POST_PROCESS = 37,

        [System.Runtime.Serialization.EnumMember(Value = @"PROCESSED")]
        PROCESSED = 38,

        [System.Runtime.Serialization.EnumMember(Value = @"REPORTING")]
        REPORTING = 39,

        [System.Runtime.Serialization.EnumMember(Value = @"NATUS_CONVERT")]
        NATUS_CONVERT = 40,

        [System.Runtime.Serialization.EnumMember(Value = @"READY_FOR_TRANSFER")]
        READY_FOR_TRANSFER = 41,

        [System.Runtime.Serialization.EnumMember(Value = @"TRANSFER_IN_PROGRESS")]
        TRANSFER_IN_PROGRESS = 42,

        [System.Runtime.Serialization.EnumMember(Value = @"TRANSFER_DOWNLOAD")]
        TRANSFER_DOWNLOAD = 43,

        [System.Runtime.Serialization.EnumMember(Value = @"TRANSFER_DOWNLOADING")]
        TRANSFER_DOWNLOADING = 44,

        [System.Runtime.Serialization.EnumMember(Value = @"HISTORICAL_TO_CONVERT")]
        HISTORICAL_TO_CONVERT = 45,

        [System.Runtime.Serialization.EnumMember(Value = @"REUPLOAD")]
        REUPLOAD = 46,

        [System.Runtime.Serialization.EnumMember(Value = @"DEIDENTIFIED")]
        DEIDENTIFIED = 47,

        [System.Runtime.Serialization.EnumMember(Value = @"READ_EVENTS")]
        READ_EVENTS = 48,

        [System.Runtime.Serialization.EnumMember(Value = @"READING_EVENTS")]
        READING_EVENTS = 49,

        [System.Runtime.Serialization.EnumMember(Value = @"READ_SIGNALS")]
        READ_SIGNALS = 50,

        [System.Runtime.Serialization.EnumMember(Value = @"READING_SIGNALS")]
        READING_SIGNALS = 51,

        [System.Runtime.Serialization.EnumMember(Value = @"WRITE_EVENTS")]
        WRITE_EVENTS = 52,

        [System.Runtime.Serialization.EnumMember(Value = @"WRITING_EVENTS")]
        WRITING_EVENTS = 53,

        [System.Runtime.Serialization.EnumMember(Value = @"DEIDENTIFY")]
        DEIDENTIFY = 54,

        [System.Runtime.Serialization.EnumMember(Value = @"CREATED")]
        CREATED = 55,

        [System.Runtime.Serialization.EnumMember(Value = @"REVERTED")]
        REVERTED = 56,

        [System.Runtime.Serialization.EnumMember(Value = @"UPLOAD_HOLD")]
        UPLOAD_HOLD = 57,

        [System.Runtime.Serialization.EnumMember(Value = @"TO_CLOUDPAT_CONVERT")]
        TO_CLOUDPAT_CONVERT = 58,

        [System.Runtime.Serialization.EnumMember(Value = @"CLOUDPAT_CONVERT")]
        CLOUDPAT_CONVERT = 59,

        [System.Runtime.Serialization.EnumMember(Value = @"WEB_CREATED")]
        WEB_CREATED = 60,

        [System.Runtime.Serialization.EnumMember(Value = @"WEB_UPLOADING")]
        WEB_UPLOADING = 61,

        [System.Runtime.Serialization.EnumMember(Value = @"ACQUIRING")]
        ACQUIRING = 62,

    }

    /// <summary>
    /// Limit results to studies in the specified status.
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum Anonymous2
    {

        [System.Runtime.Serialization.EnumMember(Value = @"DEBUG")]
        DEBUG = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"CLOUDPAT_CONVERT_ERR")]
        CLOUDPAT_CONVERT_ERR = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"MISSING_CHANNELS_ERR")]
        MISSING_CHANNELS_ERR = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"MALFORMED_LIGHTS_ERR")]
        MALFORMED_LIGHTS_ERR = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"FAIL_ERR")]
        FAIL_ERR = 4,

        [System.Runtime.Serialization.EnumMember(Value = @"NO_LONGER_EXISTS")]
        NO_LONGER_EXISTS = 5,

        [System.Runtime.Serialization.EnumMember(Value = @"PEDIATRIC")]
        PEDIATRIC = 6,

        [System.Runtime.Serialization.EnumMember(Value = @"API_ERR")]
        API_ERR = 7,

        [System.Runtime.Serialization.EnumMember(Value = @"REPORT_ERR")]
        REPORT_ERR = 8,

        [System.Runtime.Serialization.EnumMember(Value = @"MSLT_ERR")]
        MSLT_ERR = 9,

        [System.Runtime.Serialization.EnumMember(Value = @"NATUS_CONV_ERR")]
        NATUS_CONV_ERR = 10,

        [System.Runtime.Serialization.EnumMember(Value = @"SKIP_ERR")]
        SKIP_ERR = 11,

        [System.Runtime.Serialization.EnumMember(Value = @"EDF_ERR")]
        EDF_ERR = 12,

        [System.Runtime.Serialization.EnumMember(Value = @"SMALL_EDF_ERR")]
        SMALL_EDF_ERR = 13,

        [System.Runtime.Serialization.EnumMember(Value = @"BACKEND_ERR")]
        BACKEND_ERR = 14,

        [System.Runtime.Serialization.EnumMember(Value = @"WEB_ERR")]
        WEB_ERR = 15,

        [System.Runtime.Serialization.EnumMember(Value = @"INTEGRATION_SIGNAL_ERR")]
        INTEGRATION_SIGNAL_ERR = 16,

        [System.Runtime.Serialization.EnumMember(Value = @"INTEGRATION_WRITE_ERR")]
        INTEGRATION_WRITE_ERR = 17,

        [System.Runtime.Serialization.EnumMember(Value = @"INTEGRATION_READ_ERR")]
        INTEGRATION_READ_ERR = 18,

        [System.Runtime.Serialization.EnumMember(Value = @"MALFORMED_ERR")]
        MALFORMED_ERR = 19,

        [System.Runtime.Serialization.EnumMember(Value = @"HOLD_ERR")]
        HOLD_ERR = 20,

        [System.Runtime.Serialization.EnumMember(Value = @"DID_ERR")]
        DID_ERR = 21,

        [System.Runtime.Serialization.EnumMember(Value = @"SVC_ERR")]
        SVC_ERR = 22,

        [System.Runtime.Serialization.EnumMember(Value = @"ERROR")]
        ERROR = 23,

        [System.Runtime.Serialization.EnumMember(Value = @"UPLOADING")]
        UPLOADING = 24,

        [System.Runtime.Serialization.EnumMember(Value = @"UPLOADED")]
        UPLOADED = 25,

        [System.Runtime.Serialization.EnumMember(Value = @"DEIDENTIFYING")]
        DEIDENTIFYING = 26,

        [System.Runtime.Serialization.EnumMember(Value = @"TO_CONVERT")]
        TO_CONVERT = 27,

        [System.Runtime.Serialization.EnumMember(Value = @"TO_PROCESS")]
        TO_PROCESS = 28,

        [System.Runtime.Serialization.EnumMember(Value = @"DOWNLOADING")]
        DOWNLOADING = 29,

        [System.Runtime.Serialization.EnumMember(Value = @"COMPLETE")]
        COMPLETE = 30,

        [System.Runtime.Serialization.EnumMember(Value = @"REVERT")]
        REVERT = 31,

        [System.Runtime.Serialization.EnumMember(Value = @"HISTORICAL")]
        HISTORICAL = 32,

        [System.Runtime.Serialization.EnumMember(Value = @"HISTORICAL_DID")]
        HISTORICAL_DID = 33,

        [System.Runtime.Serialization.EnumMember(Value = @"PATIENT_OBJ")]
        PATIENT_OBJ = 34,

        [System.Runtime.Serialization.EnumMember(Value = @"EXTRACT")]
        EXTRACT = 35,

        [System.Runtime.Serialization.EnumMember(Value = @"CLASSIFY")]
        CLASSIFY = 36,

        [System.Runtime.Serialization.EnumMember(Value = @"POST_PROCESS")]
        POST_PROCESS = 37,

        [System.Runtime.Serialization.EnumMember(Value = @"PROCESSED")]
        PROCESSED = 38,

        [System.Runtime.Serialization.EnumMember(Value = @"REPORTING")]
        REPORTING = 39,

        [System.Runtime.Serialization.EnumMember(Value = @"NATUS_CONVERT")]
        NATUS_CONVERT = 40,

        [System.Runtime.Serialization.EnumMember(Value = @"READY_FOR_TRANSFER")]
        READY_FOR_TRANSFER = 41,

        [System.Runtime.Serialization.EnumMember(Value = @"TRANSFER_IN_PROGRESS")]
        TRANSFER_IN_PROGRESS = 42,

        [System.Runtime.Serialization.EnumMember(Value = @"TRANSFER_DOWNLOAD")]
        TRANSFER_DOWNLOAD = 43,

        [System.Runtime.Serialization.EnumMember(Value = @"TRANSFER_DOWNLOADING")]
        TRANSFER_DOWNLOADING = 44,

        [System.Runtime.Serialization.EnumMember(Value = @"HISTORICAL_TO_CONVERT")]
        HISTORICAL_TO_CONVERT = 45,

        [System.Runtime.Serialization.EnumMember(Value = @"REUPLOAD")]
        REUPLOAD = 46,

        [System.Runtime.Serialization.EnumMember(Value = @"DEIDENTIFIED")]
        DEIDENTIFIED = 47,

        [System.Runtime.Serialization.EnumMember(Value = @"READ_EVENTS")]
        READ_EVENTS = 48,

        [System.Runtime.Serialization.EnumMember(Value = @"READING_EVENTS")]
        READING_EVENTS = 49,

        [System.Runtime.Serialization.EnumMember(Value = @"READ_SIGNALS")]
        READ_SIGNALS = 50,

        [System.Runtime.Serialization.EnumMember(Value = @"READING_SIGNALS")]
        READING_SIGNALS = 51,

        [System.Runtime.Serialization.EnumMember(Value = @"WRITE_EVENTS")]
        WRITE_EVENTS = 52,

        [System.Runtime.Serialization.EnumMember(Value = @"WRITING_EVENTS")]
        WRITING_EVENTS = 53,

        [System.Runtime.Serialization.EnumMember(Value = @"DEIDENTIFY")]
        DEIDENTIFY = 54,

        [System.Runtime.Serialization.EnumMember(Value = @"CREATED")]
        CREATED = 55,

        [System.Runtime.Serialization.EnumMember(Value = @"REVERTED")]
        REVERTED = 56,

        [System.Runtime.Serialization.EnumMember(Value = @"UPLOAD_HOLD")]
        UPLOAD_HOLD = 57,

        [System.Runtime.Serialization.EnumMember(Value = @"TO_CLOUDPAT_CONVERT")]
        TO_CLOUDPAT_CONVERT = 58,

        [System.Runtime.Serialization.EnumMember(Value = @"CLOUDPAT_CONVERT")]
        CLOUDPAT_CONVERT = 59,

        [System.Runtime.Serialization.EnumMember(Value = @"WEB_CREATED")]
        WEB_CREATED = 60,

        [System.Runtime.Serialization.EnumMember(Value = @"WEB_UPLOADING")]
        WEB_UPLOADING = 61,

        [System.Runtime.Serialization.EnumMember(Value = @"ACQUIRING")]
        ACQUIRING = 62,

    }

    /// <summary>
    /// The version of the study. New studies should upload to the `CURRENT` version and grab EnsoSleep scoring from the `SCORED` version. A backup of the study is made to `ORIGINAL`.
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum Version
    {

        [System.Runtime.Serialization.EnumMember(Value = @"ORIGINAL")]
        ORIGINAL = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"CURRENT")]
        CURRENT = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"SCORED")]
        SCORED = 2,

    }

    /// <summary>
    /// The version of the study. New studies should upload to the `CURRENT` version and grab EnsoSleep scoring from the `SCORED` version. A backup of the study is made to `ORIGINAL`.
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum Version2
    {

        [System.Runtime.Serialization.EnumMember(Value = @"ORIGINAL")]
        ORIGINAL = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"CURRENT")]
        CURRENT = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"SCORED")]
        SCORED = 2,

    }

    /// <summary>
    /// The version of the study. New studies should upload to the `CURRENT` version and grab EnsoSleep scoring from the `SCORED` version. A backup of the study is made to `ORIGINAL`.
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum Version3
    {

        [System.Runtime.Serialization.EnumMember(Value = @"ORIGINAL")]
        ORIGINAL = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"CURRENT")]
        CURRENT = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"SCORED")]
        SCORED = 2,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum Group_by
    {

        [System.Runtime.Serialization.EnumMember(Value = @"type")]
        Type = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"name")]
        Name = 1,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum ScoreEventName
    {

        [System.Runtime.Serialization.EnumMember(Value = @"central_apnea")]
        Central_apnea = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"obstructive_apnea")]
        Obstructive_apnea = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"mixed_apnea")]
        Mixed_apnea = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"central_hypopnea")]
        Central_hypopnea = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"mixed_hypopnea")]
        Mixed_hypopnea = 4,

        [System.Runtime.Serialization.EnumMember(Value = @"obstructive_hypopnea")]
        Obstructive_hypopnea = 5,

        [System.Runtime.Serialization.EnumMember(Value = @"central_respiratory")]
        Central_respiratory = 6,

        [System.Runtime.Serialization.EnumMember(Value = @"obstructive_respiratory")]
        Obstructive_respiratory = 7,

        [System.Runtime.Serialization.EnumMember(Value = @"unclassified_respiratory")]
        Unclassified_respiratory = 8,

        [System.Runtime.Serialization.EnumMember(Value = @"periodic_breathing")]
        Periodic_breathing = 9,

        [System.Runtime.Serialization.EnumMember(Value = @"cheyne_stokes_breathing")]
        Cheyne_stokes_breathing = 10,

        [System.Runtime.Serialization.EnumMember(Value = @"rera")]
        Rera = 11,

        [System.Runtime.Serialization.EnumMember(Value = @"hypoventilation")]
        Hypoventilation = 12,

        [System.Runtime.Serialization.EnumMember(Value = @"hypercapnia")]
        Hypercapnia = 13,

        [System.Runtime.Serialization.EnumMember(Value = @"hypopnea")]
        Hypopnea = 14,

        [System.Runtime.Serialization.EnumMember(Value = @"respiratory_event")]
        Respiratory_event = 15,

        [System.Runtime.Serialization.EnumMember(Value = @"bradycardia")]
        Bradycardia = 16,

        [System.Runtime.Serialization.EnumMember(Value = @"tachycardia")]
        Tachycardia = 17,

        [System.Runtime.Serialization.EnumMember(Value = @"sinus_tachycardia")]
        Sinus_tachycardia = 18,

        [System.Runtime.Serialization.EnumMember(Value = @"narrow_complex_tachycardia")]
        Narrow_complex_tachycardia = 19,

        [System.Runtime.Serialization.EnumMember(Value = @"wide_complex_tachycardia")]
        Wide_complex_tachycardia = 20,

        [System.Runtime.Serialization.EnumMember(Value = @"atrial_fibrillation")]
        Atrial_fibrillation = 21,

        [System.Runtime.Serialization.EnumMember(Value = @"asystole")]
        Asystole = 22,

        [System.Runtime.Serialization.EnumMember(Value = @"premature_atrial_contraction")]
        Premature_atrial_contraction = 23,

        [System.Runtime.Serialization.EnumMember(Value = @"premature_ventricular_contraction")]
        Premature_ventricular_contraction = 24,

        [System.Runtime.Serialization.EnumMember(Value = @"desaturation")]
        Desaturation = 25,

        [System.Runtime.Serialization.EnumMember(Value = @"sustained_desaturation")]
        Sustained_desaturation = 26,

        [System.Runtime.Serialization.EnumMember(Value = @"snore")]
        Snore = 27,

        [System.Runtime.Serialization.EnumMember(Value = @"arousal")]
        Arousal = 28,

        [System.Runtime.Serialization.EnumMember(Value = @"rem_behavior_disorder")]
        Rem_behavior_disorder = 29,

        [System.Runtime.Serialization.EnumMember(Value = @"bruxism")]
        Bruxism = 30,

        [System.Runtime.Serialization.EnumMember(Value = @"leg_movement")]
        Leg_movement = 31,

        [System.Runtime.Serialization.EnumMember(Value = @"periodic_leg_movement")]
        Periodic_leg_movement = 32,

        [System.Runtime.Serialization.EnumMember(Value = @"alternating_leg_muscle_activation")]
        Alternating_leg_muscle_activation = 33,

        [System.Runtime.Serialization.EnumMember(Value = @"hypnagogic_foot_tremor")]
        Hypnagogic_foot_tremor = 34,

        [System.Runtime.Serialization.EnumMember(Value = @"excessive_fragmentary_myoclonus")]
        Excessive_fragmentary_myoclonus = 35,

        [System.Runtime.Serialization.EnumMember(Value = @"rhythmic_movement_disorder")]
        Rhythmic_movement_disorder = 36,

        [System.Runtime.Serialization.EnumMember(Value = @"left_leg_movement")]
        Left_leg_movement = 37,

        [System.Runtime.Serialization.EnumMember(Value = @"right_leg_movement")]
        Right_leg_movement = 38,

        [System.Runtime.Serialization.EnumMember(Value = @"unstaged")]
        Unstaged = 39,

        [System.Runtime.Serialization.EnumMember(Value = @"wake")]
        Wake = 40,

        [System.Runtime.Serialization.EnumMember(Value = @"n1")]
        N1 = 41,

        [System.Runtime.Serialization.EnumMember(Value = @"n2")]
        N2 = 42,

        [System.Runtime.Serialization.EnumMember(Value = @"n3")]
        N3 = 43,

        [System.Runtime.Serialization.EnumMember(Value = @"rem")]
        Rem = 44,

        [System.Runtime.Serialization.EnumMember(Value = @"bad_data")]
        Bad_data = 45,

        [System.Runtime.Serialization.EnumMember(Value = @"cpap")]
        Cpap = 46,

        [System.Runtime.Serialization.EnumMember(Value = @"bpap")]
        Bpap = 47,

        [System.Runtime.Serialization.EnumMember(Value = @"apap")]
        Apap = 48,

        [System.Runtime.Serialization.EnumMember(Value = @"adaptive_servo_ventilation")]
        Adaptive_servo_ventilation = 49,

        [System.Runtime.Serialization.EnumMember(Value = @"auto_ventilation")]
        Auto_ventilation = 50,

        [System.Runtime.Serialization.EnumMember(Value = @"oxygen")]
        Oxygen = 51,

        [System.Runtime.Serialization.EnumMember(Value = @"phrenic_nerve_stimulator")]
        Phrenic_nerve_stimulator = 52,

        [System.Runtime.Serialization.EnumMember(Value = @"npap")]
        Npap = 53,

        [System.Runtime.Serialization.EnumMember(Value = @"vpap")]
        Vpap = 54,

        [System.Runtime.Serialization.EnumMember(Value = @"oral_appliance")]
        Oral_appliance = 55,

        [System.Runtime.Serialization.EnumMember(Value = @"hypoglossal_nerve_stimulator")]
        Hypoglossal_nerve_stimulator = 56,

        [System.Runtime.Serialization.EnumMember(Value = @"upright")]
        Upright = 57,

        [System.Runtime.Serialization.EnumMember(Value = @"supine")]
        Supine = 58,

        [System.Runtime.Serialization.EnumMember(Value = @"prone")]
        Prone = 59,

        [System.Runtime.Serialization.EnumMember(Value = @"right")]
        Right = 60,

        [System.Runtime.Serialization.EnumMember(Value = @"left")]
        Left = 61,

        [System.Runtime.Serialization.EnumMember(Value = @"supine_left")]
        Supine_left = 62,

        [System.Runtime.Serialization.EnumMember(Value = @"supine_right")]
        Supine_right = 63,

        [System.Runtime.Serialization.EnumMember(Value = @"prone_left")]
        Prone_left = 64,

        [System.Runtime.Serialization.EnumMember(Value = @"prone_right")]
        Prone_right = 65,

        [System.Runtime.Serialization.EnumMember(Value = @"lights_on")]
        Lights_on = 66,

        [System.Runtime.Serialization.EnumMember(Value = @"lights_off")]
        Lights_off = 67,

        [System.Runtime.Serialization.EnumMember(Value = @"comment")]
        Comment = 68,

        [System.Runtime.Serialization.EnumMember(Value = @"inadequate")]
        Inadequate = 69,

        [System.Runtime.Serialization.EnumMember(Value = @"adequate")]
        Adequate = 70,

        [System.Runtime.Serialization.EnumMember(Value = @"alert")]
        Alert = 71,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum ScoreEventAssociation
    {

        [System.Runtime.Serialization.EnumMember(Value = @"resp")]
        Resp = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"spont")]
        Spont = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"limb")]
        Limb = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"rera")]
        Rera = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"awakening")]
        Awakening = 4,

        [System.Runtime.Serialization.EnumMember(Value = @"plm")]
        Plm = 5,

        [System.Runtime.Serialization.EnumMember(Value = @"snore")]
        Snore = 6,

        [System.Runtime.Serialization.EnumMember(Value = @"bruxism")]
        Bruxism = 7,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum StudyPatientGender
    {

        [System.Runtime.Serialization.EnumMember(Value = @"male")]
        Male = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"female")]
        Female = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"other")]
        Other = 2,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum StudyOptionsDesaturation
    {

        _3 = 3,

        _4 = 4,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum StudySoftware
    {

        [System.Runtime.Serialization.EnumMember(Value = @"AirView")]
        AirView = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Alice")]
        Alice = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"Cadwell")]
        Cadwell = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"Carefusion")]
        Carefusion = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"Compumedics")]
        Compumedics = 4,

        [System.Runtime.Serialization.EnumMember(Value = @"EDF")]
        EDF = 5,

        [System.Runtime.Serialization.EnumMember(Value = @"Natus Sandman Elite")]
        Natus_Sandman_Elite = 6,

        [System.Runtime.Serialization.EnumMember(Value = @"NeuroVirtual")]
        NeuroVirtual = 7,

        [System.Runtime.Serialization.EnumMember(Value = @"NihonKohden")]
        NihonKohden = 8,

        [System.Runtime.Serialization.EnumMember(Value = @"Nox")]
        Nox = 9,

        [System.Runtime.Serialization.EnumMember(Value = @"RemLogic")]
        RemLogic = 10,

        [System.Runtime.Serialization.EnumMember(Value = @"Sleepware G3")]
        Sleepware_G3 = 11,

        [System.Runtime.Serialization.EnumMember(Value = @"Snap")]
        Snap = 12,

        [System.Runtime.Serialization.EnumMember(Value = @"Xltek")]
        Xltek = 13,

        [System.Runtime.Serialization.EnumMember(Value = @"DreamClear")]
        DreamClear = 14,

        [System.Runtime.Serialization.EnumMember(Value = @"WatchPAT")]
        WatchPAT = 15,

        [System.Runtime.Serialization.EnumMember(Value = @"Viatom")]
        Viatom = 16,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum StudyState
    {

        [System.Runtime.Serialization.EnumMember(Value = @"New")]
        New = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Processing")]
        Processing = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"Done")]
        Done = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"Skipped")]
        Skipped = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"Error")]
        Error = 4,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum StudyStatus
    {

        [System.Runtime.Serialization.EnumMember(Value = @"DEBUG")]
        DEBUG = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"CLOUDPAT_CONVERT_ERR")]
        CLOUDPAT_CONVERT_ERR = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"MISSING_CHANNELS_ERR")]
        MISSING_CHANNELS_ERR = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"MALFORMED_LIGHTS_ERR")]
        MALFORMED_LIGHTS_ERR = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"FAIL_ERR")]
        FAIL_ERR = 4,

        [System.Runtime.Serialization.EnumMember(Value = @"NO_LONGER_EXISTS")]
        NO_LONGER_EXISTS = 5,

        [System.Runtime.Serialization.EnumMember(Value = @"PEDIATRIC")]
        PEDIATRIC = 6,

        [System.Runtime.Serialization.EnumMember(Value = @"API_ERR")]
        API_ERR = 7,

        [System.Runtime.Serialization.EnumMember(Value = @"REPORT_ERR")]
        REPORT_ERR = 8,

        [System.Runtime.Serialization.EnumMember(Value = @"MSLT_ERR")]
        MSLT_ERR = 9,

        [System.Runtime.Serialization.EnumMember(Value = @"NATUS_CONV_ERR")]
        NATUS_CONV_ERR = 10,

        [System.Runtime.Serialization.EnumMember(Value = @"SKIP_ERR")]
        SKIP_ERR = 11,

        [System.Runtime.Serialization.EnumMember(Value = @"EDF_ERR")]
        EDF_ERR = 12,

        [System.Runtime.Serialization.EnumMember(Value = @"SMALL_EDF_ERR")]
        SMALL_EDF_ERR = 13,

        [System.Runtime.Serialization.EnumMember(Value = @"BACKEND_ERR")]
        BACKEND_ERR = 14,

        [System.Runtime.Serialization.EnumMember(Value = @"WEB_ERR")]
        WEB_ERR = 15,

        [System.Runtime.Serialization.EnumMember(Value = @"INTEGRATION_SIGNAL_ERR")]
        INTEGRATION_SIGNAL_ERR = 16,

        [System.Runtime.Serialization.EnumMember(Value = @"INTEGRATION_WRITE_ERR")]
        INTEGRATION_WRITE_ERR = 17,

        [System.Runtime.Serialization.EnumMember(Value = @"INTEGRATION_READ_ERR")]
        INTEGRATION_READ_ERR = 18,

        [System.Runtime.Serialization.EnumMember(Value = @"MALFORMED_ERR")]
        MALFORMED_ERR = 19,

        [System.Runtime.Serialization.EnumMember(Value = @"HOLD_ERR")]
        HOLD_ERR = 20,

        [System.Runtime.Serialization.EnumMember(Value = @"DID_ERR")]
        DID_ERR = 21,

        [System.Runtime.Serialization.EnumMember(Value = @"SVC_ERR")]
        SVC_ERR = 22,

        [System.Runtime.Serialization.EnumMember(Value = @"ERROR")]
        ERROR = 23,

        [System.Runtime.Serialization.EnumMember(Value = @"UPLOADING")]
        UPLOADING = 24,

        [System.Runtime.Serialization.EnumMember(Value = @"UPLOADED")]
        UPLOADED = 25,

        [System.Runtime.Serialization.EnumMember(Value = @"DEIDENTIFYING")]
        DEIDENTIFYING = 26,

        [System.Runtime.Serialization.EnumMember(Value = @"TO_CONVERT")]
        TO_CONVERT = 27,

        [System.Runtime.Serialization.EnumMember(Value = @"TO_PROCESS")]
        TO_PROCESS = 28,

        [System.Runtime.Serialization.EnumMember(Value = @"DOWNLOADING")]
        DOWNLOADING = 29,

        [System.Runtime.Serialization.EnumMember(Value = @"COMPLETE")]
        COMPLETE = 30,

        [System.Runtime.Serialization.EnumMember(Value = @"REVERT")]
        REVERT = 31,

        [System.Runtime.Serialization.EnumMember(Value = @"HISTORICAL")]
        HISTORICAL = 32,

        [System.Runtime.Serialization.EnumMember(Value = @"HISTORICAL_DID")]
        HISTORICAL_DID = 33,

        [System.Runtime.Serialization.EnumMember(Value = @"PATIENT_OBJ")]
        PATIENT_OBJ = 34,

        [System.Runtime.Serialization.EnumMember(Value = @"EXTRACT")]
        EXTRACT = 35,

        [System.Runtime.Serialization.EnumMember(Value = @"CLASSIFY")]
        CLASSIFY = 36,

        [System.Runtime.Serialization.EnumMember(Value = @"POST_PROCESS")]
        POST_PROCESS = 37,

        [System.Runtime.Serialization.EnumMember(Value = @"PROCESSED")]
        PROCESSED = 38,

        [System.Runtime.Serialization.EnumMember(Value = @"REPORTING")]
        REPORTING = 39,

        [System.Runtime.Serialization.EnumMember(Value = @"NATUS_CONVERT")]
        NATUS_CONVERT = 40,

        [System.Runtime.Serialization.EnumMember(Value = @"READY_FOR_TRANSFER")]
        READY_FOR_TRANSFER = 41,

        [System.Runtime.Serialization.EnumMember(Value = @"TRANSFER_IN_PROGRESS")]
        TRANSFER_IN_PROGRESS = 42,

        [System.Runtime.Serialization.EnumMember(Value = @"TRANSFER_DOWNLOAD")]
        TRANSFER_DOWNLOAD = 43,

        [System.Runtime.Serialization.EnumMember(Value = @"TRANSFER_DOWNLOADING")]
        TRANSFER_DOWNLOADING = 44,

        [System.Runtime.Serialization.EnumMember(Value = @"HISTORICAL_TO_CONVERT")]
        HISTORICAL_TO_CONVERT = 45,

        [System.Runtime.Serialization.EnumMember(Value = @"REUPLOAD")]
        REUPLOAD = 46,

        [System.Runtime.Serialization.EnumMember(Value = @"DEIDENTIFIED")]
        DEIDENTIFIED = 47,

        [System.Runtime.Serialization.EnumMember(Value = @"READ_EVENTS")]
        READ_EVENTS = 48,

        [System.Runtime.Serialization.EnumMember(Value = @"READING_EVENTS")]
        READING_EVENTS = 49,

        [System.Runtime.Serialization.EnumMember(Value = @"READ_SIGNALS")]
        READ_SIGNALS = 50,

        [System.Runtime.Serialization.EnumMember(Value = @"READING_SIGNALS")]
        READING_SIGNALS = 51,

        [System.Runtime.Serialization.EnumMember(Value = @"WRITE_EVENTS")]
        WRITE_EVENTS = 52,

        [System.Runtime.Serialization.EnumMember(Value = @"WRITING_EVENTS")]
        WRITING_EVENTS = 53,

        [System.Runtime.Serialization.EnumMember(Value = @"DEIDENTIFY")]
        DEIDENTIFY = 54,

        [System.Runtime.Serialization.EnumMember(Value = @"CREATED")]
        CREATED = 55,

        [System.Runtime.Serialization.EnumMember(Value = @"REVERTED")]
        REVERTED = 56,

        [System.Runtime.Serialization.EnumMember(Value = @"UPLOAD_HOLD")]
        UPLOAD_HOLD = 57,

        [System.Runtime.Serialization.EnumMember(Value = @"TO_CLOUDPAT_CONVERT")]
        TO_CLOUDPAT_CONVERT = 58,

        [System.Runtime.Serialization.EnumMember(Value = @"CLOUDPAT_CONVERT")]
        CLOUDPAT_CONVERT = 59,

        [System.Runtime.Serialization.EnumMember(Value = @"WEB_CREATED")]
        WEB_CREATED = 60,

        [System.Runtime.Serialization.EnumMember(Value = @"WEB_UPLOADING")]
        WEB_UPLOADING = 61,

        [System.Runtime.Serialization.EnumMember(Value = @"ACQUIRING")]
        ACQUIRING = 62,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum CreateStudySoftware
    {

        [System.Runtime.Serialization.EnumMember(Value = @"AirView")]
        AirView = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Alice")]
        Alice = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"Cadwell")]
        Cadwell = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"Carefusion")]
        Carefusion = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"Compumedics")]
        Compumedics = 4,

        [System.Runtime.Serialization.EnumMember(Value = @"EDF")]
        EDF = 5,

        [System.Runtime.Serialization.EnumMember(Value = @"Natus Sandman Elite")]
        Natus_Sandman_Elite = 6,

        [System.Runtime.Serialization.EnumMember(Value = @"NeuroVirtual")]
        NeuroVirtual = 7,

        [System.Runtime.Serialization.EnumMember(Value = @"NihonKohden")]
        NihonKohden = 8,

        [System.Runtime.Serialization.EnumMember(Value = @"Nox")]
        Nox = 9,

        [System.Runtime.Serialization.EnumMember(Value = @"RemLogic")]
        RemLogic = 10,

        [System.Runtime.Serialization.EnumMember(Value = @"Sleepware G3")]
        Sleepware_G3 = 11,

        [System.Runtime.Serialization.EnumMember(Value = @"Snap")]
        Snap = 12,

        [System.Runtime.Serialization.EnumMember(Value = @"Xltek")]
        Xltek = 13,

        [System.Runtime.Serialization.EnumMember(Value = @"DreamClear")]
        DreamClear = 14,

        [System.Runtime.Serialization.EnumMember(Value = @"WatchPAT")]
        WatchPAT = 15,

        [System.Runtime.Serialization.EnumMember(Value = @"Viatom")]
        Viatom = 16,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum CreateStudyState
    {

        [System.Runtime.Serialization.EnumMember(Value = @"New")]
        New = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Processing")]
        Processing = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"Done")]
        Done = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"Skipped")]
        Skipped = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"Error")]
        Error = 4,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum CreateStudyStatus
    {

        [System.Runtime.Serialization.EnumMember(Value = @"DEBUG")]
        DEBUG = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"CLOUDPAT_CONVERT_ERR")]
        CLOUDPAT_CONVERT_ERR = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"MISSING_CHANNELS_ERR")]
        MISSING_CHANNELS_ERR = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"MALFORMED_LIGHTS_ERR")]
        MALFORMED_LIGHTS_ERR = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"FAIL_ERR")]
        FAIL_ERR = 4,

        [System.Runtime.Serialization.EnumMember(Value = @"NO_LONGER_EXISTS")]
        NO_LONGER_EXISTS = 5,

        [System.Runtime.Serialization.EnumMember(Value = @"PEDIATRIC")]
        PEDIATRIC = 6,

        [System.Runtime.Serialization.EnumMember(Value = @"API_ERR")]
        API_ERR = 7,

        [System.Runtime.Serialization.EnumMember(Value = @"REPORT_ERR")]
        REPORT_ERR = 8,

        [System.Runtime.Serialization.EnumMember(Value = @"MSLT_ERR")]
        MSLT_ERR = 9,

        [System.Runtime.Serialization.EnumMember(Value = @"NATUS_CONV_ERR")]
        NATUS_CONV_ERR = 10,

        [System.Runtime.Serialization.EnumMember(Value = @"SKIP_ERR")]
        SKIP_ERR = 11,

        [System.Runtime.Serialization.EnumMember(Value = @"EDF_ERR")]
        EDF_ERR = 12,

        [System.Runtime.Serialization.EnumMember(Value = @"SMALL_EDF_ERR")]
        SMALL_EDF_ERR = 13,

        [System.Runtime.Serialization.EnumMember(Value = @"BACKEND_ERR")]
        BACKEND_ERR = 14,

        [System.Runtime.Serialization.EnumMember(Value = @"WEB_ERR")]
        WEB_ERR = 15,

        [System.Runtime.Serialization.EnumMember(Value = @"INTEGRATION_SIGNAL_ERR")]
        INTEGRATION_SIGNAL_ERR = 16,

        [System.Runtime.Serialization.EnumMember(Value = @"INTEGRATION_WRITE_ERR")]
        INTEGRATION_WRITE_ERR = 17,

        [System.Runtime.Serialization.EnumMember(Value = @"INTEGRATION_READ_ERR")]
        INTEGRATION_READ_ERR = 18,

        [System.Runtime.Serialization.EnumMember(Value = @"MALFORMED_ERR")]
        MALFORMED_ERR = 19,

        [System.Runtime.Serialization.EnumMember(Value = @"HOLD_ERR")]
        HOLD_ERR = 20,

        [System.Runtime.Serialization.EnumMember(Value = @"DID_ERR")]
        DID_ERR = 21,

        [System.Runtime.Serialization.EnumMember(Value = @"SVC_ERR")]
        SVC_ERR = 22,

        [System.Runtime.Serialization.EnumMember(Value = @"ERROR")]
        ERROR = 23,

        [System.Runtime.Serialization.EnumMember(Value = @"UPLOADING")]
        UPLOADING = 24,

        [System.Runtime.Serialization.EnumMember(Value = @"UPLOADED")]
        UPLOADED = 25,

        [System.Runtime.Serialization.EnumMember(Value = @"DEIDENTIFYING")]
        DEIDENTIFYING = 26,

        [System.Runtime.Serialization.EnumMember(Value = @"TO_CONVERT")]
        TO_CONVERT = 27,

        [System.Runtime.Serialization.EnumMember(Value = @"TO_PROCESS")]
        TO_PROCESS = 28,

        [System.Runtime.Serialization.EnumMember(Value = @"DOWNLOADING")]
        DOWNLOADING = 29,

        [System.Runtime.Serialization.EnumMember(Value = @"COMPLETE")]
        COMPLETE = 30,

        [System.Runtime.Serialization.EnumMember(Value = @"REVERT")]
        REVERT = 31,

        [System.Runtime.Serialization.EnumMember(Value = @"HISTORICAL")]
        HISTORICAL = 32,

        [System.Runtime.Serialization.EnumMember(Value = @"HISTORICAL_DID")]
        HISTORICAL_DID = 33,

        [System.Runtime.Serialization.EnumMember(Value = @"PATIENT_OBJ")]
        PATIENT_OBJ = 34,

        [System.Runtime.Serialization.EnumMember(Value = @"EXTRACT")]
        EXTRACT = 35,

        [System.Runtime.Serialization.EnumMember(Value = @"CLASSIFY")]
        CLASSIFY = 36,

        [System.Runtime.Serialization.EnumMember(Value = @"POST_PROCESS")]
        POST_PROCESS = 37,

        [System.Runtime.Serialization.EnumMember(Value = @"PROCESSED")]
        PROCESSED = 38,

        [System.Runtime.Serialization.EnumMember(Value = @"REPORTING")]
        REPORTING = 39,

        [System.Runtime.Serialization.EnumMember(Value = @"NATUS_CONVERT")]
        NATUS_CONVERT = 40,

        [System.Runtime.Serialization.EnumMember(Value = @"READY_FOR_TRANSFER")]
        READY_FOR_TRANSFER = 41,

        [System.Runtime.Serialization.EnumMember(Value = @"TRANSFER_IN_PROGRESS")]
        TRANSFER_IN_PROGRESS = 42,

        [System.Runtime.Serialization.EnumMember(Value = @"TRANSFER_DOWNLOAD")]
        TRANSFER_DOWNLOAD = 43,

        [System.Runtime.Serialization.EnumMember(Value = @"TRANSFER_DOWNLOADING")]
        TRANSFER_DOWNLOADING = 44,

        [System.Runtime.Serialization.EnumMember(Value = @"HISTORICAL_TO_CONVERT")]
        HISTORICAL_TO_CONVERT = 45,

        [System.Runtime.Serialization.EnumMember(Value = @"REUPLOAD")]
        REUPLOAD = 46,

        [System.Runtime.Serialization.EnumMember(Value = @"DEIDENTIFIED")]
        DEIDENTIFIED = 47,

        [System.Runtime.Serialization.EnumMember(Value = @"READ_EVENTS")]
        READ_EVENTS = 48,

        [System.Runtime.Serialization.EnumMember(Value = @"READING_EVENTS")]
        READING_EVENTS = 49,

        [System.Runtime.Serialization.EnumMember(Value = @"READ_SIGNALS")]
        READ_SIGNALS = 50,

        [System.Runtime.Serialization.EnumMember(Value = @"READING_SIGNALS")]
        READING_SIGNALS = 51,

        [System.Runtime.Serialization.EnumMember(Value = @"WRITE_EVENTS")]
        WRITE_EVENTS = 52,

        [System.Runtime.Serialization.EnumMember(Value = @"WRITING_EVENTS")]
        WRITING_EVENTS = 53,

        [System.Runtime.Serialization.EnumMember(Value = @"DEIDENTIFY")]
        DEIDENTIFY = 54,

        [System.Runtime.Serialization.EnumMember(Value = @"CREATED")]
        CREATED = 55,

        [System.Runtime.Serialization.EnumMember(Value = @"REVERTED")]
        REVERTED = 56,

        [System.Runtime.Serialization.EnumMember(Value = @"UPLOAD_HOLD")]
        UPLOAD_HOLD = 57,

        [System.Runtime.Serialization.EnumMember(Value = @"TO_CLOUDPAT_CONVERT")]
        TO_CLOUDPAT_CONVERT = 58,

        [System.Runtime.Serialization.EnumMember(Value = @"CLOUDPAT_CONVERT")]
        CLOUDPAT_CONVERT = 59,

        [System.Runtime.Serialization.EnumMember(Value = @"WEB_CREATED")]
        WEB_CREATED = 60,

        [System.Runtime.Serialization.EnumMember(Value = @"WEB_UPLOADING")]
        WEB_UPLOADING = 61,

        [System.Runtime.Serialization.EnumMember(Value = @"ACQUIRING")]
        ACQUIRING = 62,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum UpdateStudySoftware
    {

        [System.Runtime.Serialization.EnumMember(Value = @"AirView")]
        AirView = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Alice")]
        Alice = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"Cadwell")]
        Cadwell = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"Carefusion")]
        Carefusion = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"Compumedics")]
        Compumedics = 4,

        [System.Runtime.Serialization.EnumMember(Value = @"EDF")]
        EDF = 5,

        [System.Runtime.Serialization.EnumMember(Value = @"Natus Sandman Elite")]
        Natus_Sandman_Elite = 6,

        [System.Runtime.Serialization.EnumMember(Value = @"NeuroVirtual")]
        NeuroVirtual = 7,

        [System.Runtime.Serialization.EnumMember(Value = @"NihonKohden")]
        NihonKohden = 8,

        [System.Runtime.Serialization.EnumMember(Value = @"Nox")]
        Nox = 9,

        [System.Runtime.Serialization.EnumMember(Value = @"RemLogic")]
        RemLogic = 10,

        [System.Runtime.Serialization.EnumMember(Value = @"Sleepware G3")]
        Sleepware_G3 = 11,

        [System.Runtime.Serialization.EnumMember(Value = @"Snap")]
        Snap = 12,

        [System.Runtime.Serialization.EnumMember(Value = @"Xltek")]
        Xltek = 13,

        [System.Runtime.Serialization.EnumMember(Value = @"DreamClear")]
        DreamClear = 14,

        [System.Runtime.Serialization.EnumMember(Value = @"WatchPAT")]
        WatchPAT = 15,

        [System.Runtime.Serialization.EnumMember(Value = @"Viatom")]
        Viatom = 16,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum UpdateStudyState
    {

        [System.Runtime.Serialization.EnumMember(Value = @"New")]
        New = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Processing")]
        Processing = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"Done")]
        Done = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"Skipped")]
        Skipped = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"Error")]
        Error = 4,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum UpdateStudyStatus
    {

        [System.Runtime.Serialization.EnumMember(Value = @"DEBUG")]
        DEBUG = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"CLOUDPAT_CONVERT_ERR")]
        CLOUDPAT_CONVERT_ERR = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"MISSING_CHANNELS_ERR")]
        MISSING_CHANNELS_ERR = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"MALFORMED_LIGHTS_ERR")]
        MALFORMED_LIGHTS_ERR = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"FAIL_ERR")]
        FAIL_ERR = 4,

        [System.Runtime.Serialization.EnumMember(Value = @"NO_LONGER_EXISTS")]
        NO_LONGER_EXISTS = 5,

        [System.Runtime.Serialization.EnumMember(Value = @"PEDIATRIC")]
        PEDIATRIC = 6,

        [System.Runtime.Serialization.EnumMember(Value = @"API_ERR")]
        API_ERR = 7,

        [System.Runtime.Serialization.EnumMember(Value = @"REPORT_ERR")]
        REPORT_ERR = 8,

        [System.Runtime.Serialization.EnumMember(Value = @"MSLT_ERR")]
        MSLT_ERR = 9,

        [System.Runtime.Serialization.EnumMember(Value = @"NATUS_CONV_ERR")]
        NATUS_CONV_ERR = 10,

        [System.Runtime.Serialization.EnumMember(Value = @"SKIP_ERR")]
        SKIP_ERR = 11,

        [System.Runtime.Serialization.EnumMember(Value = @"EDF_ERR")]
        EDF_ERR = 12,

        [System.Runtime.Serialization.EnumMember(Value = @"SMALL_EDF_ERR")]
        SMALL_EDF_ERR = 13,

        [System.Runtime.Serialization.EnumMember(Value = @"BACKEND_ERR")]
        BACKEND_ERR = 14,

        [System.Runtime.Serialization.EnumMember(Value = @"WEB_ERR")]
        WEB_ERR = 15,

        [System.Runtime.Serialization.EnumMember(Value = @"INTEGRATION_SIGNAL_ERR")]
        INTEGRATION_SIGNAL_ERR = 16,

        [System.Runtime.Serialization.EnumMember(Value = @"INTEGRATION_WRITE_ERR")]
        INTEGRATION_WRITE_ERR = 17,

        [System.Runtime.Serialization.EnumMember(Value = @"INTEGRATION_READ_ERR")]
        INTEGRATION_READ_ERR = 18,

        [System.Runtime.Serialization.EnumMember(Value = @"MALFORMED_ERR")]
        MALFORMED_ERR = 19,

        [System.Runtime.Serialization.EnumMember(Value = @"HOLD_ERR")]
        HOLD_ERR = 20,

        [System.Runtime.Serialization.EnumMember(Value = @"DID_ERR")]
        DID_ERR = 21,

        [System.Runtime.Serialization.EnumMember(Value = @"SVC_ERR")]
        SVC_ERR = 22,

        [System.Runtime.Serialization.EnumMember(Value = @"ERROR")]
        ERROR = 23,

        [System.Runtime.Serialization.EnumMember(Value = @"UPLOADING")]
        UPLOADING = 24,

        [System.Runtime.Serialization.EnumMember(Value = @"UPLOADED")]
        UPLOADED = 25,

        [System.Runtime.Serialization.EnumMember(Value = @"DEIDENTIFYING")]
        DEIDENTIFYING = 26,

        [System.Runtime.Serialization.EnumMember(Value = @"TO_CONVERT")]
        TO_CONVERT = 27,

        [System.Runtime.Serialization.EnumMember(Value = @"TO_PROCESS")]
        TO_PROCESS = 28,

        [System.Runtime.Serialization.EnumMember(Value = @"DOWNLOADING")]
        DOWNLOADING = 29,

        [System.Runtime.Serialization.EnumMember(Value = @"COMPLETE")]
        COMPLETE = 30,

        [System.Runtime.Serialization.EnumMember(Value = @"REVERT")]
        REVERT = 31,

        [System.Runtime.Serialization.EnumMember(Value = @"HISTORICAL")]
        HISTORICAL = 32,

        [System.Runtime.Serialization.EnumMember(Value = @"HISTORICAL_DID")]
        HISTORICAL_DID = 33,

        [System.Runtime.Serialization.EnumMember(Value = @"PATIENT_OBJ")]
        PATIENT_OBJ = 34,

        [System.Runtime.Serialization.EnumMember(Value = @"EXTRACT")]
        EXTRACT = 35,

        [System.Runtime.Serialization.EnumMember(Value = @"CLASSIFY")]
        CLASSIFY = 36,

        [System.Runtime.Serialization.EnumMember(Value = @"POST_PROCESS")]
        POST_PROCESS = 37,

        [System.Runtime.Serialization.EnumMember(Value = @"PROCESSED")]
        PROCESSED = 38,

        [System.Runtime.Serialization.EnumMember(Value = @"REPORTING")]
        REPORTING = 39,

        [System.Runtime.Serialization.EnumMember(Value = @"NATUS_CONVERT")]
        NATUS_CONVERT = 40,

        [System.Runtime.Serialization.EnumMember(Value = @"READY_FOR_TRANSFER")]
        READY_FOR_TRANSFER = 41,

        [System.Runtime.Serialization.EnumMember(Value = @"TRANSFER_IN_PROGRESS")]
        TRANSFER_IN_PROGRESS = 42,

        [System.Runtime.Serialization.EnumMember(Value = @"TRANSFER_DOWNLOAD")]
        TRANSFER_DOWNLOAD = 43,

        [System.Runtime.Serialization.EnumMember(Value = @"TRANSFER_DOWNLOADING")]
        TRANSFER_DOWNLOADING = 44,

        [System.Runtime.Serialization.EnumMember(Value = @"HISTORICAL_TO_CONVERT")]
        HISTORICAL_TO_CONVERT = 45,

        [System.Runtime.Serialization.EnumMember(Value = @"REUPLOAD")]
        REUPLOAD = 46,

        [System.Runtime.Serialization.EnumMember(Value = @"DEIDENTIFIED")]
        DEIDENTIFIED = 47,

        [System.Runtime.Serialization.EnumMember(Value = @"READ_EVENTS")]
        READ_EVENTS = 48,

        [System.Runtime.Serialization.EnumMember(Value = @"READING_EVENTS")]
        READING_EVENTS = 49,

        [System.Runtime.Serialization.EnumMember(Value = @"READ_SIGNALS")]
        READ_SIGNALS = 50,

        [System.Runtime.Serialization.EnumMember(Value = @"READING_SIGNALS")]
        READING_SIGNALS = 51,

        [System.Runtime.Serialization.EnumMember(Value = @"WRITE_EVENTS")]
        WRITE_EVENTS = 52,

        [System.Runtime.Serialization.EnumMember(Value = @"WRITING_EVENTS")]
        WRITING_EVENTS = 53,

        [System.Runtime.Serialization.EnumMember(Value = @"DEIDENTIFY")]
        DEIDENTIFY = 54,

        [System.Runtime.Serialization.EnumMember(Value = @"CREATED")]
        CREATED = 55,

        [System.Runtime.Serialization.EnumMember(Value = @"REVERTED")]
        REVERTED = 56,

        [System.Runtime.Serialization.EnumMember(Value = @"UPLOAD_HOLD")]
        UPLOAD_HOLD = 57,

        [System.Runtime.Serialization.EnumMember(Value = @"TO_CLOUDPAT_CONVERT")]
        TO_CLOUDPAT_CONVERT = 58,

        [System.Runtime.Serialization.EnumMember(Value = @"CLOUDPAT_CONVERT")]
        CLOUDPAT_CONVERT = 59,

        [System.Runtime.Serialization.EnumMember(Value = @"WEB_CREATED")]
        WEB_CREATED = 60,

        [System.Runtime.Serialization.EnumMember(Value = @"WEB_UPLOADING")]
        WEB_UPLOADING = 61,

        [System.Runtime.Serialization.EnumMember(Value = @"ACQUIRING")]
        ACQUIRING = 62,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum UpdateStudyPatient__gender
    {

        [System.Runtime.Serialization.EnumMember(Value = @"male")]
        Male = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"female")]
        Female = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"other")]
        Other = 2,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum UpdateStudyOptions__desaturation
    {

        _3 = 3,

        _4 = 4,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    internal class DateFormatConverter : Newtonsoft.Json.Converters.IsoDateTimeConverter
    {
        public DateFormatConverter()
        {
            DateTimeFormat = "yyyy-MM-dd";
        }
    }

}
