import { Injectable } from '@nestjs/common';
import { WsException } from '@nestjs/websockets';
import { UserService } from '@app/modules/user/user.service';
import { UserEntity } from '@app/modules/user/user.entity';

@Injectable()
export class NotificationsService {
  constructor(
      private readonly userService: UserService,
  ) {}

  async getUserFromSocket(authorization: string | undefined): Promise<UserEntity> {
    if (!authorization) {
      throw new WsException('Invalid authorization header.');
    }

    const token = authorization.split(' ')[1];
    const user = await this.userService.findByToken(token);

    if (!user) {
      throw new WsException('Invalid token verification.');
    }

    return user;
  }
}
