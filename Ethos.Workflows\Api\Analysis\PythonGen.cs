using System.Text;
using System.Text.RegularExpressions;

namespace Ethos.Workflows.Api.Analysis;

public static class PythonGen
{
    public static Dictionary<string, string> Build(ApiModel apiModel)
    {
        var result = new Dictionary<string, string>();
        result["ethos_common.py"] = ZipBuilder.GetEmbedded(typeof(PythonGen).Assembly, "/py/ethos_common.py");
        result["login_api_v0.py"] = ZipBuilder.GetEmbedded(typeof(PythonGen).Assembly, "/py/login_api_v0.py");
        result["file_api_v0.py"] = ZipBuilder.GetEmbedded(typeof(PythonGen).Assembly, "/py/file_api_v0.py");
        result["test_script.py"] = ZipBuilder.GetEmbedded(typeof(PythonGen).Assembly, "/py/test_script.py");
        result["ethos_common_types.py"] = GenerateCommonTypes(apiModel);
        foreach (var controller in apiModel.EntityControllers.Values)
        {
            var apiFile = GenerateEntityApi(controller, apiModel);
            result[$"{controller.EntityName.ToLowerInvariant()}_api.py"] = apiFile;
        }
        
        // NEW: generate thin clients for non-entity controllers
        foreach (var ctrl in apiModel.Controllers.Values)
        {
            var fileName = $"{Naming.Snake(ctrl.Name)}_api.py";
            if (result.ContainsKey(fileName))
            {
                throw new InvalidOperationException($"Duplicate API file name: {fileName}. Please ensure unique controller names.");
            }
            result[fileName] = GenerateServiceApi(ctrl, apiModel);
        }

        return result;
    }

    public static string GenerateCommonTypes(ApiModel apiModel)
    {
        var entityControllerSpecificTypes = apiModel.EntityControllers.Values
            .SelectMany(c => new[] { c.InputDto, c.OutputDto, c.QueryType })
            .Distinct()
            .Select(c => (c as TypeRef.Custom)?.Name)
            .ToList().ToHashSet();
        var controllerSpecificTypes = apiModel.Controllers.Values
            .SelectMany(c => c.Endpoints)
            .SelectMany(e => new[] { e.BodyInputType, e.OutputType }
                .Concat(e.QueryParameters.Values)
                .Concat(e.OtherParameters.Values))
            .OfType<TypeRef.Custom>()
            .Select(t => t.Name)
            .ToList().ToHashSet();
        
        var commonTypesBuffer = new StringBuilder(16_384);
        commonTypesBuffer.AppendLine("from __future__ import annotations");
        commonTypesBuffer.AppendLine("from typing import Any, Dict, List, Set, Union, Optional");
        commonTypesBuffer.AppendLine("from dataclasses import dataclass, field");
        commonTypesBuffer.AppendLine("from abc import ABC, abstractmethod");
        commonTypesBuffer.AppendLine("from enum import Enum");
        commonTypesBuffer.AppendLine("from ethos_common import *");
        commonTypesBuffer.AppendLine();
        
        foreach (var pair in apiModel.AllTypes)
        {
            var typeName = pair.Key;
            var typeDef = pair.Value;

            if (entityControllerSpecificTypes.Contains(typeName)) continue;
            if (controllerSpecificTypes.Contains(typeName)) continue;
            
            AppendTypeDefinition(commonTypesBuffer, typeName, typeDef);
        }
        
        return commonTypesBuffer.ToString();
    }

    private static void AppendTypeDefinition(StringBuilder sb, string typeName, TypeDefinition typeDef)
    {
        if (typeDef is DataClassDefinition dataClass)
        {
            AppendDataClass(sb, typeName, dataClass);
        }
        else if (typeDef is AdtDefinition adt)
        {
            AppendAdt(sb, typeName, adt);
        }
        else if (typeDef is EnumDefinition enumDef)
        {
            AppendEnum(sb, typeName, enumDef);
        }
        else
        {
            throw new NotSupportedException($"Unsupported type definition: {typeDef.GetType().Name}");
        }
    }
    
    private static void AppendAdt(StringBuilder sb, string typeName, AdtDefinition adt)
    {
        sb.AppendLine($"class {typeName}(ABC):");
        sb.AppendLine("    \"\"\"Autogenerated abstract data type.\"\"\"");
        sb.AppendLine("    # Total variants: " + adt.Variants.Count);
        sb.AppendLine("    @abstractmethod");
        sb.AppendLine("    def to_json(self):");
        sb.AppendLine("        \"\"\"Convert to JSON serializable dict.\"\"\"");
        sb.AppendLine("        raise NotImplementedError(\"This is an abstract ADT.\")");
        sb.AppendLine();
        sb.AppendLine("    @classmethod");
        sb.AppendLine("    def from_json(cls, data: Any):");
        sb.AppendLine("        \"\"\"Create an instance from a JSON serializable dict.\"\"\"");
        sb.AppendLine("        if not isinstance(data, dict):");
        sb.AppendLine("            raise TypeError(\"Expected a dict for deserialization.\")");
        sb.AppendLine("        if '$type' not in data:");
        sb.AppendLine("            raise ValueError(\"Missing '$type' key in data.\")");
        sb.AppendLine("        type_str = data['$type']");
        sb.AppendLine("        if not isinstance(type_str, str):");
        sb.AppendLine("            raise TypeError(\"'$type' must be a string.\")");
        sb.AppendLine("        match type_str.lower():");
        foreach (var variant in adt.Variants)
        {
            sb.AppendLine($"            case '{variant.Name.ToLower()}': return {typeName}_{variant.Name}.from_json(data)");
        }
        sb.AppendLine("            case _: raise ValueError(f\"Unknown type: {data['$type']}\")");
        sb.AppendLine();
        foreach (var variant in adt.Variants)
        {
            sb.AppendLine($"    {variant.Name}: type['{typeName}_{variant.Name}'] = None # type: ignore");
        }
        sb.AppendLine();
        
        foreach (var variant in adt.Variants)
        {
            sb.AppendLine($"@dataclass(slots=True)");
            sb.AppendLine($"class {typeName}_{variant.Name}({typeName}):");
            foreach (var prop in variant.Properties)
            {
                var pyType = prop.Type.ToPython();
                sb.AppendLine($"    {Naming.PascalToCamel(prop.Name)}: {pyType}");
            }
            if (!variant.Properties.Any())
                sb.AppendLine("    pass");
            
            sb.AppendLine();
            sb.AppendLine("    def to_json(self):");
            sb.AppendLine("        \"\"\"Convert to JSON serializable dict.\"\"\"");
            sb.AppendLine("        return {");
            sb.AppendLine("            '$type': '" + variant.Name + "',");
            foreach (var prop in variant.Properties)
            {
                // Handle non-primitive (e.g. ADTs or data classes) by converting to JSON carefully.
                sb.AppendLine($"            '{Naming.PascalToCamel(prop.Name)}': {PythonJsonSerializer(prop.Type, "self." + Naming.PascalToCamel(prop.Name))},");
            }
            sb.AppendLine("        }");
            sb.AppendLine();
            
            sb.AppendLine("    @classmethod");
            sb.AppendLine($"    def from_json(cls, data: Dict[str, Any]) -> {typeName}_{variant.Name}:");
            sb.AppendLine("        \"\"\"Create an instance from a JSON serializable dict.\"\"\"");
            sb.AppendLine("        if not isinstance(data, dict):");
            sb.AppendLine("            raise TypeError(\"Expected a dict for deserialization.\")");
            sb.AppendLine("        return cls(");
            foreach (var prop in variant.Properties)
            {
                // Handle non-primitive (e.g. ADTs or data classes) by converting from JSON carefully.
                sb.AppendLine($"            {Naming.PascalToCamel(prop.Name)}={PythonJsonDeserializer(prop.Type, "data['" + Naming.PascalToCamel(prop.Name) + "']")},");
            }
            sb.AppendLine("        )");
            sb.AppendLine();
            
            sb.AppendLine($"{typeName}.{variant.Name} = {typeName}_{variant.Name}");
            sb.AppendLine();
        }
        
        sb.AppendLine();
    }
    
    private static void AppendDataClass(StringBuilder sb, string typeName, DataClassDefinition dataClass)
    {
        sb.AppendLine("@dataclass(slots=True)");
        sb.AppendLine($"class {typeName}:");
        
        foreach (var prop in dataClass.Properties)
        {
            var pyType = prop.Type.ToPython();
            sb.AppendLine($"    {Naming.PascalToCamel(prop.Name)}: {pyType}");
        }

        if (!dataClass.Properties.Any())
            sb.AppendLine("    pass");
        
        sb.AppendLine();
        
        sb.AppendLine("    def to_json(self) -> Dict[str, Any]:");
        sb.AppendLine("        \"\"\"Convert to JSON serializable dict.\"\"\"");
        sb.AppendLine("        return {");
        foreach (var prop in dataClass.Properties)
        {
            // Handle non-primitive (e.g. ADTs or data classes) by converting to JSON carefully.
            sb.AppendLine($"            '{Naming.PascalToCamel(prop.Name)}': {PythonJsonSerializer(prop.Type, "self." + Naming.PascalToCamel(prop.Name))},");
        }
        sb.AppendLine("        }");
        sb.AppendLine();
        sb.AppendLine("    @classmethod");
        sb.AppendLine($"    def from_json(cls, data: Dict[str, Any]) -> {typeName}:");
        sb.AppendLine("        \"\"\"Create an instance from a JSON serializable dict.\"\"\"");
        sb.AppendLine("        if not isinstance(data, dict):");
        sb.AppendLine("            raise TypeError(\"Expected a dict for deserialization.\")");
        sb.AppendLine("        return cls(");
        foreach (var prop in dataClass.Properties)
        {
            // Handle non-primitive (e.g. ADTs or data classes) by converting from JSON carefully.
            sb.AppendLine($"            {Naming.PascalToCamel(prop.Name)}={PythonJsonDeserializer(prop.Type, "data['" + Naming.PascalToCamel(prop.Name) + "']")},");
        }
        sb.AppendLine("        )");
        sb.AppendLine();
    }

    private static void AppendEnum(StringBuilder sb, string typeName, EnumDefinition enumDef)
    {
        sb.AppendLine($"class {typeName}(Enum):");
        foreach (var member in enumDef.Members)
        {
            sb.AppendLine($"    {member} = '{member}'");
        }
        sb.AppendLine();
        
        sb.AppendLine($"    def to_json(self):");
        sb.AppendLine("        \"\"\"Convert to JSON serializable value.\"\"\"");
        sb.AppendLine("        return self.value");
        sb.AppendLine();
        sb.AppendLine($"    @classmethod");
        sb.AppendLine($"    def from_json(cls, value: Any) -> {typeName}:");
        sb.AppendLine("        \"\"\"Create an instance from a JSON serializable value.\"\"\"");
        sb.AppendLine("        if not isinstance(value, str):");
        sb.AppendLine("            raise TypeError(\"Expected a string for deserialization.\")");
        sb.AppendLine("        try:");
        sb.AppendLine($"            return cls(value)");
        sb.AppendLine("        except ValueError:");
        sb.AppendLine("            raise ValueError(f\"Unknown enum value: {value}\")");
        sb.AppendLine();
    }
    
    private static string ToPython(this TypeRef type) => type switch
    {
        TypeRef.Primitive(var kind) => kind switch
            {
                PrimitiveKind.Bool => "bool",
                PrimitiveKind.Int => "int",
                PrimitiveKind.Float => "float",
                PrimitiveKind.String => "str",
                PrimitiveKind.Guid => "UUID | str",
                PrimitiveKind.TimeOnly => "TimeOnly",
                PrimitiveKind.DateOnly => "DateOnly",
                PrimitiveKind.DateTime => "DateTime",
                PrimitiveKind.DateTimeOffset => "DateTimeOffset",
                PrimitiveKind.Json => "Json",
                PrimitiveKind.JsonDict => "JsonDict",
            },
        TypeRef.Nullable(var innerType) => $"{innerType.ToPython()} | None",
        TypeRef.List(var elementType) => $"list[{elementType.ToPython()}]",
        TypeRef.Dict(var keyType, var valueType) => $"dict[{keyType.ToPython()}, {valueType.ToPython()}]",
        TypeRef.Set(var elementType) => $"set[{elementType.ToPython()}]",
        TypeRef.Custom(var name) => name
    };
    
    private static string PythonJsonSerializer(this TypeRef type, string varName) => type switch
    {
        TypeRef.Primitive(var kind) => varName,
        TypeRef.Nullable(var innerType) => $"None if {varName} is None else ({PythonJsonSerializer(innerType, varName)})",
        TypeRef.List(var elementType) => $"[{PythonJsonSerializer(elementType, "x")} for x in {varName}]",
        TypeRef.Set(var elementType) => $"[{PythonJsonSerializer(elementType, "x")} for x in {varName}]",
        TypeRef.Dict(var keyType, var valueType) => 
            $"{{{PythonJsonSerializer(keyType, "k")}: {PythonJsonSerializer(valueType, "v")} for k, v in {varName}.items()}}",
        TypeRef.Custom(var name) => $"{varName}.to_json()"
    };

    private static string PythonJsonDeserializer(this TypeRef type, string varName) => type switch
    {
        TypeRef.Primitive(var kind) => varName,
        TypeRef.Nullable(var innerType) => $"None if {varName} is None else ({PythonJsonDeserializer(innerType, varName)})",
        TypeRef.List(var elementType) => $"[{PythonJsonDeserializer(elementType, "x")} for x in {varName}]",
        TypeRef.Set(var elementType) => $"set([{PythonJsonDeserializer(elementType, "x")} for x in {varName}])",
        TypeRef.Dict(var keyType, var valueType) => 
            $"{{{PythonJsonDeserializer(keyType, "k")}: {PythonJsonDeserializer(valueType, "v")} for k, v in {varName}.items()}}",
        TypeRef.Custom(var name) => $"{name}.from_json({varName})"
    };
    
    public static string GenerateEntityApi(EntityControllerDefinition iface, ApiModel apiModel)
    {
        // Get the client class name and route segment
        var entitySnake = Naming.PascalToSnake(iface.EntityName);
        
        // Build the client-specific file
        var apiFileBuffer = new StringBuilder(16_384);
        apiFileBuffer.AppendLine("from __future__ import annotations"); apiFileBuffer.AppendLine();
        apiFileBuffer.AppendLine("from ethos_common import *"); apiFileBuffer.AppendLine();
        apiFileBuffer.AppendLine("from ethos_common_types import *"); apiFileBuffer.AppendLine();

        var inputDto = (iface.InputDto as TypeRef.Custom)!;
        var outputDto = (iface.OutputDto as TypeRef.Custom)!;
        var queryType = (iface.QueryType as TypeRef.Custom)!;
        
        // Append type declarations.
        AppendTypeDefinition(apiFileBuffer, inputDto.Name, apiModel.AllTypes[inputDto.Name]);
        AppendTypeDefinition(apiFileBuffer, outputDto.Name, apiModel.AllTypes[outputDto.Name]);
        AppendTypeDefinition(apiFileBuffer, queryType.Name, apiModel.AllTypes[queryType.Name]);

        apiFileBuffer.AppendLine($"class {iface.EntityName}Api(EntityHttpClientBase):");
        apiFileBuffer.AppendLine("    \"\"\"Asyncio client auto-generated from IEntityHttpClient.\"\"\"");
        apiFileBuffer.AppendLine("    def __init__(self, session):");
        apiFileBuffer.AppendLine($"        super().__init__(session, '{iface.EntityName}', {outputDto.Name})");
        apiFileBuffer.AppendLine();
        
        return apiFileBuffer.ToString();
    }
    
    private static string GenerateServiceApi(ControllerDefinition ctrl, ApiModel model)
    {
        var sb = new StringBuilder(16_384);
        sb.AppendLine("from __future__ import annotations\n");
        sb.AppendLine("from typing import Any, Dict\n");
        sb.AppendLine("from ethos_common import *");
        sb.AppendLine("from ethos_common_types import *\n");

        // emit any DTOs specific to this controller
        var referencedTypes = ctrl.Endpoints
            .SelectMany(e => new[] { e.BodyInputType, e.OutputType }
                .Concat(e.QueryParameters.Values)
                .Concat(e.OtherParameters.Values))
            .OfType<TypeRef.Custom>()
            .Select(t => t.Name)
            .Distinct();

        foreach (var tName in referencedTypes)
            AppendTypeDefinition(sb, tName, model.AllTypes[tName]);

        // client name = last segment of route + 'Api'
        var className = Naming.Pascal(ctrl.Route.Split('/').Last()) + "Api";
        sb.AppendLine($"class {className}(HttpClientBase):");
        sb.AppendLine("    \"\"\"Thin client generated from ControllerDefinition.\"\"\"");
        sb.AppendLine("    def __init__(self, session):");
        sb.AppendLine($"        super().__init__(session, '{ctrl.Route}', None)\n");

        foreach (var ep in ctrl.Endpoints)
            AppendEndpointMethod(sb, ep);

        return sb.ToString();
    }
    
    private static void AppendEndpointMethod(StringBuilder sb, EndpointDefinition ep)
    {
        // Use a more descriptive name for the function, e.g., 'create_user' instead of 'post_user'
        var methodName = ep.Route.Split('/').Last().Replace("-", "_");
        var funcName = Naming.Snake($"{ep.Method.ToLowerInvariant()}_{methodName}");

        var paramList = new List<string>();

        // Body parameter (if any)
        if (ep.BodyInputType is { } body)
        {
            // Use a clearer parameter name like 'body' or a type-based name
            var bodyParamName = Naming.PascalToCamel(body.ToPython().Split('[').First());
            paramList.Add($"{bodyParamName}: {body.ToPython()}");
        }

        // Route and query parameters
        var allParams = ep.QueryParameters.Concat(ep.OtherParameters).ToDictionary(kv => kv.Key, kv => kv.Value);
        foreach (var p in allParams)
        {
            paramList.Add($"{p.Key}: {p.Value.ToPython()}");
        }

        var paramsJoined = paramList.Any() ? ", " + string.Join(", ", paramList) : "";
        var returnType = ep.OutputType.ToPython();
        
        // Check if the output type is a custom class that needs deserialization
        var isCustomOutput = ep.OutputType is TypeRef.Custom;
        var isPagedOutput = returnType.StartsWith("PagedResponse");

        sb.AppendLine($"    async def {funcName}(self{paramsJoined}) -> {returnType}:");
        
        // Prepare the Python f-string for the route
        var pyRoute = ep.Route.Replace("{", "{").Replace("}", "}");

        // Build query dict for parameters *not* in the route path
        sb.AppendLine("        query_params: Dict[str, Any] = {");
        foreach (var qp in ep.QueryParameters)
        {
            sb.AppendLine($"            '{qp.Key}': {qp.Key},");
        }
        sb.AppendLine("        }");

        // Get the expression for the body payload
        string bodyExpr = "None";
        if (ep.BodyInputType is { } bodyType)
        {
            var bodyParamName = Naming.PascalToCamel(bodyType.ToPython().Split('[').First());
            bodyExpr = PythonJsonSerializer(bodyType, bodyParamName);
        }
        
        // THE FIX: Call `_req` with proper keyword arguments `params` and `json`.
        // Note the C# f-string `f"f'...'"` to generate a Python f-string.
        // The double braces '{{' escape the brace in C#.
        sb.AppendLine($"        r = await self._req('{ep.Method.ToUpperInvariant()}', f'{{self._path}}{pyRoute}', params=query_params, json={bodyExpr})");

        // Handle deserialization of the response
        if (returnType == "None") {
            sb.AppendLine("        return None");
        }
        else if (isPagedOutput)
        {
            // PagedResponse has its own from_dict that handles item types
            var innerType = (ep.OutputType as TypeRef.Custom)!.Name; // This is a simplification
            sb.AppendLine($"        return PagedResponse.from_dict(await r.json(), {innerType})");
        }
        else if (isCustomOutput)
        {
            var customTypeName = (ep.OutputType as TypeRef.Custom)!.Name;
            sb.AppendLine($"        return {customTypeName}.from_json(await r.json())");
        }
        else
        {
            // For primitive types, just return the JSON response
            sb.AppendLine("        return await r.json()");
        }
        sb.AppendLine();
    }
}