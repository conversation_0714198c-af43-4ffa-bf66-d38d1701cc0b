using System.Linq.Expressions;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class PatientAppointmentConfirmationDbo : IAuditableEntity<PatientAppointmentConfirmationDbo>
{
    public Guid PatientAppointmentId { get; set; }
    public virtual PatientAppointmentDbo PatientAppointment { get; set; } = null!;
    
    public long ConfirmationTypeId { get; set; }
    public DateTime ConfirmationDateTime { get; set; } = DateTime.UtcNow;
    public Guid ConfirmedById { get; set; }
    public DateTime AddedDateTime { get; set; } = DateTime.UtcNow;
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<PatientAppointmentConfirmationDbo>(Register);
    
    public new static void Register(EntityTypeBuilder<PatientAppointmentConfirmationDbo> entity)
    {
        IAuditableEntity<PatientAppointmentConfirmationDbo>.Register(entity);
        
        entity.HasOne(e => e.PatientAppointment)
            .WithMany()
            .HasForeignKey(e => e.PatientAppointmentId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);
        
        entity.Property(e => e.ConfirmationTypeId)
            .IsRequired();
        
        entity.Property(e => e.ConfirmationDateTime)
            .IsRequired();
        
        entity.Property(e => e.ConfirmedById)
            .IsRequired();
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(PatientAppointmentConfirmationQ.WithId), "WithId")]
[JsonDerivedType(typeof(PatientAppointmentConfirmationQ.WithPatientAppointmentId), "WithPatientAppointmentId")]
[JsonDerivedType(typeof(PatientAppointmentConfirmationQ.WithConfirmationTypeId), "WithConfirmationTypeId")]
[JsonDerivedType(typeof(PatientAppointmentConfirmationQ.WithConfirmedById), "WithConfirmedById")]
[JsonDerivedType(typeof(PatientAppointmentConfirmationQ.WithDateInRange), "WithDateInRange")]
public abstract record PatientAppointmentConfirmationQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : PatientAppointmentConfirmationQ;
    public sealed record WithPatientAppointmentId(Guid PatientAppointmentId) : PatientAppointmentConfirmationQ;
    public sealed record WithConfirmationTypeId(long ConfirmationTypeId) : PatientAppointmentConfirmationQ;
    public sealed record WithConfirmedById(Guid ConfirmedById) : PatientAppointmentConfirmationQ;
    public sealed record WithDateInRange(DateTime StartDate, DateTime EndDate) : PatientAppointmentConfirmationQ;
    
    public static PatientAppointmentConfirmationQ HasId(Guid id) => new WithId(id);
    public static PatientAppointmentConfirmationQ HasPatientAppointmentId(Guid patientAppointmentId) => new WithPatientAppointmentId(patientAppointmentId);
    public static PatientAppointmentConfirmationQ HasConfirmationTypeId(long confirmationTypeId) => new WithConfirmationTypeId(confirmationTypeId);
    public static PatientAppointmentConfirmationQ HasConfirmedById(Guid confirmedById) => new WithConfirmedById(confirmedById);
    public static PatientAppointmentConfirmationQ HasDateInRange(DateTime startDate, DateTime endDate) => new WithDateInRange(startDate, endDate);

    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithId id => Expression.Equal(Expression.Property(self, nameof(PatientAppointmentConfirmationDbo.Id)), Expression.Constant(id.Id)),
            WithPatientAppointmentId patientAppointmentId => Expression.Equal(Expression.Property(self, nameof(PatientAppointmentConfirmationDbo.PatientAppointmentId)), Expression.Constant(patientAppointmentId.PatientAppointmentId)),
            WithConfirmationTypeId confirmationTypeId => Expression.Equal(Expression.Property(self, nameof(PatientAppointmentConfirmationDbo.ConfirmationTypeId)), Expression.Constant(confirmationTypeId.ConfirmationTypeId)),
            WithConfirmedById confirmedById => Expression.Equal(Expression.Property(self, nameof(PatientAppointmentConfirmationDbo.ConfirmedById)), Expression.Constant(confirmedById.ConfirmedById)),
            WithDateInRange dateInRange => 
                Expression.AndAlso(
                    Expression.GreaterThanOrEqual(Expression.Property(self, nameof(PatientAppointmentConfirmationDbo.ConfirmationDateTime)), Expression.Constant(dateInRange.StartDate)),
                    Expression.LessThanOrEqual(Expression.Property(self, nameof(PatientAppointmentConfirmationDbo.ConfirmationDateTime)), Expression.Constant(dateInRange.EndDate))
                ),
            _ => throw new NotSupportedException($"Query type {this.GetType().Name} is not supported.")
        };
    }
}