import { MigrationInterface, QueryRunner } from 'typeorm';

export class changeTables1689316589473 implements MigrationInterface {
    name = 'changeTables1689316589473'

    public async up(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "technicians" DROP CONSTRAINT "FK_96f7243ac16a06c2f43aa99b052"');
      await queryRunner.query('CREATE TABLE "patients" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "external_id" integer, "name" character varying NOT NULL, CONSTRAINT "PK_a7f0b9fcbb3469d5ec0b0aceaa7" PRIMARY KEY ("id"))');
      await queryRunner.query('ALTER TABLE "technicians" DROP COLUMN "facility_id"');
      await queryRunner.query('ALTER TABLE "schedules" ADD CONSTRAINT "FK_a9c66f71ff39e84963f8f16d84b" FOREIGN KEY ("patient_id") REFERENCES "patients"("id") ON DELETE NO ACTION ON UPDATE NO ACTION');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "schedules" DROP CONSTRAINT "FK_a9c66f71ff39e84963f8f16d84b"');
      await queryRunner.query('ALTER TABLE "technicians" ADD "facility_id" integer');
      await queryRunner.query('DROP TABLE "patients"');
      await queryRunner.query('ALTER TABLE "technicians" ADD CONSTRAINT "FK_96f7243ac16a06c2f43aa99b052" FOREIGN KEY ("facility_id") REFERENCES "facilities"("id") ON DELETE CASCADE ON UPDATE NO ACTION');
    }

}
