﻿using System.Linq.Expressions;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class PatientGuardianDbo : IAuditableEntity<PatientGuardianDbo>
{
    public virtual ICollection<PersonNameDbo> Names { get; set; } = new List<PersonNameDbo>();
    public virtual ICollection<IdentifierDbo> Identifiers { get; set; } = new List<IdentifierDbo>();
    
    // Demographics - Stored directly
    public long? GenderId { get; set; }
    public DateOnly? DateOfBirth { get; set; }
    public string? Ssn { get; set; }
    public Guid/*GuardianRelationship*/ RelationshipToPatient { get; set; }

    // Contact Information
    public string? Email { get; set; }
    public Guid? AddressId { get; set; } // FK to internal AddressEntity
    public virtual AddressDbo? Address { get; init; }

    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<PatientGuardianDbo>(Register);
    public new static void Register(EntityTypeBuilder<PatientGuardianDbo> entity)
    {
        IAuditableEntity<PatientGuardianDbo>.Register(entity);

        entity.OwnsMany(p => p.Names, names =>
        {
            names.ToTable($"{nameof(PatientGuardianDbo)}_{nameof(Names)}", IEntity.DefaultSchema);
            names.WithOwner().HasForeignKey($"{nameof(PatientGuardianDbo)}Id");
            names.Property<int>("Id").ValueGeneratedOnAdd();
            names.HasKey("Id");
            PersonNameDbo.Configure(names);
        });
        
        entity.OwnsMany(p => p.Identifiers, identifiers =>
        {
            identifiers.ToTable($"{nameof(PatientGuardianDbo)}_{nameof(Identifiers)}", IEntity.DefaultSchema);
            identifiers.WithOwner().HasForeignKey($"{nameof(PatientGuardianDbo)}Id");
            identifiers.Property<int>("Id").ValueGeneratedOnAdd();
            identifiers.HasKey("Id");
            IdentifierDbo.Configure(identifiers);
        });

        // Other Properties
        // GenderId is just a Guid?, no navigation property
        entity.Property(g => g.Ssn).HasMaxLength(11); // Encryption/masking elsewhere
        entity.Property(g => g.Email).HasMaxLength(254);

        // Relationships
        entity.HasOne(g => g.Address)
              .WithMany()
              .HasForeignKey(g => g.AddressId)
              .OnDelete(DeleteBehavior.SetNull); // Deleting Guardian unlinks Address
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(PatientGuardianQ.WithId), "WithId")]
[JsonDerivedType(typeof(PatientGuardianQ.WithGivenName), "WithGivenName")]
[JsonDerivedType(typeof(PatientGuardianQ.WithLastName), "WithLastName")]
[JsonDerivedType(typeof(PatientGuardianQ.WithApproximateFullName), "WithApproximateFullName")]
public abstract record PatientGuardianQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : PatientGuardianQ;
    public sealed record WithGivenName(string Value) : PatientGuardianQ;
    public sealed record WithLastName(string Value) : PatientGuardianQ;
    public sealed record WithApproximateFullName(string Value) : PatientGuardianQ;

    public Expression BuildPredicateBody(ParameterExpression self) // self is guardianParam
    {
        return this switch
        {
            // Use common name builders
            WithGivenName       wfn => QueryExpressions.BuildFirstNamePredicate<PatientGuardianDbo>(wfn.Value, self),
            WithLastName        wln => QueryExpressions.BuildLastNamePredicate<PatientGuardianDbo>(wln.Value, self),
            WithApproximateFullName wan => QueryExpressions.BuildApproximateNamePredicate<PatientGuardianDbo>(wan.Value, self),

            // Guardian specific predicates
            WithId              wid => Expression.Equal(Expression.Property(self, nameof(PatientGuardianDbo.Id)), Expression.Constant(wid.Id)),
            // Add other predicates (e.g., by RelationshipToPatient)
            _ => throw new NotSupportedException($"Unsupported GuardianQ literal type: {this.GetType().Name}")
        };
    }
}