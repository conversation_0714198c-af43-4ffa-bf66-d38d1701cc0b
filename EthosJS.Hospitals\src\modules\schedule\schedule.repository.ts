import { EntityRepository, In } from 'typeorm';
import { IListResult } from '@app/common/types';
import { ScheduleEntity } from '@app/modules/schedule/schedule.entity';
import { ScheduleFiltersDto } from '@app/modules/schedule/dto/schedule.filters.dto';
import { BaseRepository } from '@app/common/base.repository';
import { ScheduleCollectionDto } from '@app/modules/schedule/dto/schedule.collection.dto';

@EntityRepository(ScheduleEntity)
export class ScheduleRepository extends BaseRepository<ScheduleEntity> {
  collectionDto = ScheduleCollectionDto;

  async list({ facilityIds, ...filters }: ScheduleFiltersDto): Promise<IListResult<ScheduleEntity>> {
    const where: any = { ...filters };

    if (facilityIds) {
      where.facilityId = In(facilityIds);
    }

    return super.list(where);
  }

  async getFacilityIdsByClinic(clinicId: number): Promise<number[]> {
    const result = await this.query('SELECT id FROM facilities WHERE clinic_id = $1', [clinicId]);

    return result.map(({ id }: { id: number }) => id);
  }

  async getTechnicianSchedulesCount(date: string): Promise<Record<number, number>> {
    const result = await this.query('SELECT COUNT(*) as count, technician_id FROM schedules WHERE date = $1 GROUP BY technician_id', [date]);

    return result.reduce((acc: Record<number, number>, item: any) => {
      acc[item.technician_id] = Number(item.count);
      return acc;
    }, {});
  }
}