using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class InsuranceHolderDataDbo : IOwnedEntity<InsuranceHolderDataDbo>
{
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<InsuranceHolderDataDbo>(Register);

    public new static void Register(EntityTypeBuilder<InsuranceHolderDataDbo> entity)
    {
        IOwnedEntity<InsuranceHolderDataDbo>.Register(entity);
    }
}