using System.Linq.Expressions;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class NoteDbo : IAuditableEntity<NoteDbo>
{
    public required EntityType EntityType { get; set; }
    public required Guid EntityId { get; set; }
    public required string Content { get; set; } = null!;

    public new static void Register(ModelBuilder modelBuilder) =>
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<NoteDbo>(Register);

    public new static void Register(EntityTypeBuilder<NoteDbo> entity)
    {
        IAuditableEntity<NoteDbo>.Register(entity);
        entity.Property(e => e.EntityType)
            .IsRequired()
            .HasConversion(
                v => v.ToString(),
                v => Enum.Parse<EntityType>(v, true));
        entity.Property(e => e.EntityId)
            .IsRequired();
        entity.Property(e => e.Content).IsRequired();
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(NoteQ.WithId), "WithId")]
[JsonDerivedType(typeof(NoteQ.WithEntityId), "WithEntityId")]
public abstract record NoteQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : NoteQ;
    public sealed record WithEntityId(EntityType Type, Guid Id) : NoteQ;

    public static NoteQ HasId(Guid id) => new WithId(id);
    public static NoteQ HasEntityId(EntityType type, Guid id) => new WithEntityId(type, id);

    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithId id => Expression.Equal(Expression.Property(self, nameof(NoteDbo.Id)), Expression.Constant(id.Id)),
            WithEntityId entityId => 
                Expression.AndAlso(
                    Expression.Equal(Expression.Property(self, nameof(NoteDbo.EntityType)), Expression.Constant(entityId.Type)),
                    Expression.Equal(Expression.Property(self, nameof(NoteDbo.EntityId)), Expression.Constant(entityId.Id))
                ),
            _ => throw new NotSupportedException($"Query type {this.GetType().Name} is not supported.")
        };
    }
}