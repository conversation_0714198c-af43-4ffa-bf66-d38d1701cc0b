﻿using Newtonsoft.Json;

namespace EnsoLib
{
    public class EnsoModelBase
    {
        protected IDictionary<string, object>? _additionalProperties;

        [JsonExtensionData]
        public IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            return JsonConvert.SerializeObject(this, new JsonSerializerSettings());

        }
        public static T? FromJson<T>(string data)
        {

            return JsonConvert.DeserializeObject<T>(data, new JsonSerializerSettings());

        }

    }
}
