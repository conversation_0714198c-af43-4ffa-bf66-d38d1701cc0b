using Ethos.Workflows.Database;
using Microsoft.AspNetCore.Authorization;
using Ethos.Model;
using Ethos.Workflows.Api;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class DraftController(DbContext dbContext)
    : EntityControllerBase<DraftDbo, CreateDraftDto, DraftDto, DraftQ>(dbContext)
{
    protected override DraftDto MapToDto(DraftDbo dbo) => dbo.ToDto();

    protected override DraftDbo CreateOrUpdateEntity(DraftDbo? entity, CreateDraftDto input, Guid? requiredId = null)
    {
        if (entity == null)
        {
            entity = new DraftDbo
            {
                Id = requiredId ?? Guid.NewGuid(),
                EntityType = input.EntityType,
                EntityId = input.EntityId,
                Data = input.Data
            };
        }
        else
        {
            entity.EntityType = input.EntityType;
            entity.EntityId = input.EntityId;
            entity.Data = input.Data;
        }
        return entity;
    }
}