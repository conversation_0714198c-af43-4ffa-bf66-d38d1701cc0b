﻿@using DoctorsApplication.Models
@using Persante.Blazor.SharedUI.Models

<PageStack FlowDirectionIsRow>
    <PageCard IsOutlined>
        <PageList>
            <PageListItem Class="brd-none">
                <PageStack FlowDirectionIsRow Gap="12" AlignItems="AlignItems.Start">
                     <PageCard>
                         <PageStack AlignItems="AlignItems.Start" FlowDirectionIsRow Gap="4">
                             <MudText><h4>Report Recipients</h4></MudText>
                         </PageStack>
                     </PageCard>
                     <PageCard>
                         <PageStack AlignItems="AlignItems.End">
                             <PageButtons IsGrouped="true"
                                          GroupVariant="Variant.Outlined"
                                          Buttons="@(new List<PageButtons.ActionButton>() {
                    new ()
                    {
                    Text = "Send Documents",
                    Variant = Variant.Text,
                    ButtonColor = Color.Primary,
                    Size = Size.Medium,
                    }
                    })" />
                         </PageStack>
                     </PageCard>
                 </PageStack>
             </PageListItem>
             <PageListItem Class="brd-none">
                 <MudDialog>
                     <DialogContent>
                         <MudDataGrid Items="PassedData"
                                      Loading="false"
                                      HorizontalScrollbar="true" Bordered="true"
                                      SortMode="SortMode.Multiple"
                                      Hideable="true">
                             <Columns>
                                 <PropertyColumn Property="x => x.requestId" Hidden />
                                 <PropertyColumn Property="x => x.recipient" Title="Name" />
                                 <PropertyColumn Property="x => x.description" Title="Description" />
                                 <PropertyColumn Property="x => x.sentTo" Title="Fax" />
                                 <TemplateColumn Title="File Name" Sortable="false">
                                     <CellTemplate>
                                         <PageCard FlexGrowItem="FlexGrowItem.None" Class="mt-0">
                                             <PageListItem Class="pa-0 mt-0" LabelTextSeparator=""
                                                           ContentFlowDirection="FlowDirection.Column">
                                                 @foreach (var data in @context.Item.fileName)
                                                {
                                                    <PageCard>
                                                        @data
                                                    </PageCard>
                                                }
                                            </PageListItem>
                                        </PageCard>
                                    </CellTemplate>
                                </TemplateColumn>
                                <PropertyColumn Property="x => x.fileDate" Title="File Date" />
                            </Columns>
                            <PagerContent>
                                <MudDataGridPager T="DocumentHistoryDetailModel" />
                            </PagerContent>
                        </MudDataGrid>
                    </DialogContent>
                    <DialogActions>
                        <PageButtons Buttons="@(new List<PageButtons.ActionButton>() {
        new ()
        {
            Text = "Cancel",
            Variant = Variant.Outlined,
            ButtonColor = Color.Info,
            ButtonType = ButtonType.Submit,
            OnClick = Cancel
        },
})" />
                        <PageButtons Buttons="@(new List<PageButtons.ActionButton>() {
        new ()
        {
            Text = "Continue",
            Variant = Variant.Filled,
            ButtonColor = Color.Info,
            ButtonType = ButtonType.Submit,
            OnClick = Cancel
        },
})" />
                    </DialogActions>
                </MudDialog>
            </PageListItem>
        </PageList>
    </PageCard>
</PageStack>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; }

    [Parameter]
    public IList<DocumentHistoryDetailModel> PassedData { get; set; }

    //  DocumentHistoryDetailModel data = PassedData.;

    // public IList<DocumentHistoryDetailModel> _itemsResendDocument = new List<DocumentHistoryDetailModel>()
    //     {
    //         new DocumentHistoryDetailModel(){requestId=PassedData.requestId,recipient=PassedData.recipient
    //             ,Description = PassedData.Description,
    //         Fax=PassedData.Fax,Documents=PassedData.Documents,fileDate=PassedData.fileDate,
    //              },
    //     };

    // static var target = PassedData.ConvertAll(x => (TargetType)x);

    //  public class ResendDocumentList
    //  {
    //      public int Id { get; set; }
    //      public string Name { get; set; }
    //      public string Description { get; set; }
    //      public string Fax { get; set; }
    //      public string Documents { get; set; }
    //      public string fileDate { get; set; }
    //  }


    void Submit() => MudDialog.Close(DialogResult.Ok(true));
    async Task Cancel() => MudDialog.Cancel();
}
