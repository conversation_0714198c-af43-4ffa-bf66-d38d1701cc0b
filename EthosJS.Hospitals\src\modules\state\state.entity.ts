import { Expose } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, OneToMany } from 'typeorm';
import { BaseEntity } from '@app/common/base.entity';
import { CityEntity } from '../city/city.entity';

@Entity({ name: 'states' })
export class StateEntity extends BaseEntity {
  @Column()
  @ApiProperty()
  @Expose()
  name: string;

  @Column()
  @ApiProperty()
  @Expose()
  code: string;

  @OneToMany(
    () => CityEntity,
    city => city.state,
  )
  cities: CityEntity[];
}
