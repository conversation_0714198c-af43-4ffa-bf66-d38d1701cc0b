using System.Linq.Expressions;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class StudyDbo : IAuditableEntity<StudyDbo>
{
    public Guid OrderId { get; set; }
    public virtual OrderDbo Order { get; set; } = null!;

    public virtual ICollection<InsuranceDbo> Insurances { get; set; } = new List<InsuranceDbo>();
    public virtual ICollection<PatientAppointmentDbo> Appointments { get; set; } = new List<PatientAppointmentDbo>();

    public string Status { get; set; } = string.Empty;
    public long? EncounterType { get; set; }
    public long? StudyType { get; set; }
    public JsonObject? StudyAttributes { get; set; }
    
    public string Location { get; set; } = string.Empty;
    public string Interpreting { get; set; } = string.Empty;
    public string Referring { get; set; } = string.Empty;
    public string? ScoredDate { get; set; } = string.Empty;

    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<StudyDbo>(Register);

    public new static void Register(EntityTypeBuilder<StudyDbo> entity)
    {
        IAuditableEntity<StudyDbo>.Register(entity);

        // Configure properties (adjust max lengths as appropriate)
        entity.Property(s => s.Status)
            .HasMaxLength(50)
            .IsRequired();
        
        entity.Property(s => s.EncounterType).IsRequired(false);
        
        entity.Property(s => s.StudyType).IsRequired(false);
        
        entity.Property(s => s.StudyAttributes).HasConversion(
                v => v == null || !v.Any() ? null : JsonSerializer.Serialize(v, DefaultJson.SerializerOptions),
                v => string.IsNullOrEmpty(v) ? null : JsonSerializer.Deserialize<JsonObject>(v, DefaultJson.SerializerOptions)
            )
            .HasColumnType("jsonb");

        entity.Property(s => s.Location)
            .HasMaxLength(100)
            .IsRequired();

        entity.Property(s => s.Interpreting)
            .HasMaxLength(100)
            .IsRequired();

        entity.Property(s => s.Referring)
            .HasMaxLength(100)
            .IsRequired();

        entity.Property(s => s.ScoredDate)
            .HasMaxLength(50)
            .IsRequired(false);

        entity.HasMany(s => s.Insurances)
            .WithMany();
        
        entity.HasMany(s => s.Appointments)
            .WithMany();

        // Configure the relationship: one Patient has many Studies.
        entity.HasOne(s => s.Order)
            .WithMany(p => p.Studies) // If PatientModel doesn’t have a Studies property, use .WithMany() instead.
            .HasPrincipalKey(p => p.Id)
            .HasForeignKey(s => s.OrderId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(StudyQ.WithId), "WithId")]
[JsonDerivedType(typeof(StudyQ.WithOrderId), "WithOrderId")]
[JsonDerivedType(typeof(StudyQ.WithStudyType), "WithStudyType")]
[JsonDerivedType(typeof(StudyQ.WithEncounterType), "WithEncounterType")]
public abstract record StudyQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : StudyQ;
    public sealed record WithOrderId(Guid OrderId) : StudyQ;
    public sealed record WithStudyType(long? StudyType) : StudyQ;
    public sealed record WithEncounterType(long? EncounterType) : StudyQ;

    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithId id => Expression.Equal(Expression.Property(self, nameof(StudyDbo.Id)), Expression.Constant(id.Id)),
            WithOrderId orderId => Expression.Equal(Expression.Property(self, nameof(StudyDbo.OrderId)), Expression.Constant(orderId.OrderId)),
            WithStudyType studyType => Expression.Equal(
                Expression.Property(self, nameof(StudyDbo.StudyType)), 
                Expression.Constant(studyType.StudyType, typeof(long?))),
            WithEncounterType encounterType => Expression.Equal(
                Expression.Property(self, "EncounterType"), 
                Expression.Constant(encounterType.EncounterType, typeof(long?))),
            _ => throw new NotImplementedException()
        };
    }
}
