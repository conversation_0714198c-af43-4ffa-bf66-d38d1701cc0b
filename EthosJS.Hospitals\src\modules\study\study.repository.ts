import moment from 'moment/moment';
import { EntityRepository } from 'typeorm';
import { IListResult } from '@app/common/types';
import { StudyEntity } from '@app/modules/study/study.entity';
import { StudyFiltersDto } from '@app/modules/study/dto/study.filters.dto';
import { BaseRepository } from '@app/common/base.repository';
import { BadRequestException } from '@nestjs/common';
import { StudyCollectionDto } from '@app/modules/study/dto/study.collection.dto';
import { StudyCredentialEntity } from '@app/modules/study/study.credential.entity';
import { IRawCredential } from '@app/modules/credential/types';
import { CredentialEntity } from '@app/modules/credential/credential.entity';

@EntityRepository(StudyEntity)
export class StudyRepository extends BaseRepository<StudyEntity> {
  collectionDto = StudyCollectionDto;

  async list(filters: StudyFiltersDto): Promise<IListResult<StudyEntity>> {
    return super.list(filters);
  }

  async checkHasSchedules(studyId: number): Promise<void> {
    const today = moment().startOf('day');

    const [{ count }] = await this.query('SELECT COUNT(*) as count FROM schedules WHERE date >= $1 AND study_id = $2 AND deleted_at IS NULL', [today, studyId]);

    if (Number(count) > 0) {
      throw new BadRequestException('Study has related schedules');
    }
  }

  async updateStudyName(studyId: number, name: string): Promise<void> {
    const today = moment().startOf('day');
    await this.query('UPDATE schedules SET study_name = $3 WHERE date >= $1 AND technician_id = $2 AND deleted_at IS NULL', [today, studyId, name]);
  }
}

@EntityRepository(StudyCredentialEntity)
export class StudyCredentialRepository extends BaseRepository<StudyCredentialEntity> {
  async getById(id: number): Promise<StudyCredentialEntity | undefined> {
    const queryRunner = this.manager.connection.createQueryRunner();
    const [result] = await queryRunner.query('SELECT sc.*, COALESCE(jsonb_agg(c.*), \'[]\'::jsonb) AS credentials\n' +
        'FROM study_credentials sc\n' +
        'LEFT JOIN LATERAL jsonb_array_elements_text(sc.credentials) AS credentials_id ON TRUE\n' +
        'LEFT JOIN credentials c ON c.id = credentials_id::int\n' +
        'WHERE sc.id = $1 AND sc.deleted_at IS NULL\n' +
        'GROUP BY sc.id', [id]);

    if (!result) {
      return result;
    }

    return StudyCredentialRepository.mapStudyCredential(result);
  }

  static mapStudyCredential(raw: any): StudyCredentialEntity {
    const studyCredential = new StudyCredentialEntity();

    studyCredential.id = raw.id;
    studyCredential.createdAt = raw.created_at;
    studyCredential.updatedAt = raw.updated_at;
    studyCredential.stateId = raw.state_id;
    studyCredential.studyId = raw.study_id;
    studyCredential.credentials = [];

    if (raw.credentials?.length) {
      studyCredential.credentials = raw.credentials.filter(Boolean).map(StudyCredentialRepository.mapCredential);
    }

    return studyCredential;
  }

  static mapCredential(raw: IRawCredential): CredentialEntity {
    const credential = new CredentialEntity();

    credential.id = raw.id;
    credential.name = raw.name;
    credential.code = raw.code;
    credential.issuedBy = raw.issued_by;
    credential.createdAt = raw.created_at;
    credential.updatedAt = raw.updated_at;

    return credential;
  }
}
