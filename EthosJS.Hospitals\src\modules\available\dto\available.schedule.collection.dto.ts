import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BaseCollectionDto } from '@app/common/dto/base.collection.dto';
import { AvailableScheduleDto } from '@app/modules/available/dto/available.schedule.dto';

export class AvailableScheduleCollectionDto extends BaseCollectionDto {
  @ApiProperty({ type: AvailableScheduleDto, isArray: true })
  @Expose()
  data: AvailableScheduleDto[]
}
