﻿
namespace Ethos.Utilities.Pagination
{
    /// <summary>
    /// 
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class PagedResponse<T>
    {
        /// <summary>
        /// 
        /// </summary>
        public ICollection<T> Items { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int Offset { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int Limit { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [System.Text.Json.Serialization.JsonIgnore(Condition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull)]
        public string? Previous { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [System.Text.Json.Serialization.JsonIgnore(Condition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull)]
        public string? Next { get; set; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="items"></param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <param name="totalCount"></param>
        public PagedResponse(ICollection<T> items, int offset, int limit, int totalCount)
        {
            Items = items;
            Offset = offset;
            Limit = limit;
            TotalCount = totalCount;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public int? GetNextOffset()
        {
            if (Items.Count == 0)
                return null;

            int nextOffset = Offset + Limit;
            return Math.Min(nextOffset, TotalCount);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public int? GetPreviousOffset()
        {
            if (Items.Count == 0)
                return null;

            int prevOffset = Offset - Limit;
            return Math.Max(prevOffset, 0);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public bool HasNext() => GetNextOffset() < TotalCount;

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public bool HasPrevious() => GetPreviousOffset() >= 0 && Offset > 0;
    }
}
