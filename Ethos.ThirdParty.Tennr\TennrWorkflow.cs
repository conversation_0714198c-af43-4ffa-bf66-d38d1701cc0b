﻿using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Net.Mime;

namespace Ethos.ThirdParty.Tennr
{
    /// <summary>
    /// 
    /// </summary>
    public class TennrWorkflow
    {
        readonly TennrClient client;

        /// <summary>
        /// 
        /// </summary>
        public string EndpointUri { get; internal set; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="client"></param>
        public TennrWorkflow(TennrClient client, string? uri = null)
        {
            this.client = client;
            EndpointUri = string.IsNullOrEmpty(uri) ? TennrConstants.WorkflowUri : uri;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="agentId"></param>
        /// <param name="documentContents"></param>
        /// <param name="documentName"></param>
        /// <param name="contentType"></param>
        /// <param name="additionalParameters"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public async Task<TennrResponse?> Run(string agentId,
                                              byte[] documentContents,
                                              string documentName,
                                              string contentType,
                                              IDictionary<string, string>? additionalParameters = null)
        {
            if (string.IsNullOrEmpty(agentId))
                throw new ArgumentException("Cannot run Tennr workflow: agent ID is required.", nameof(agentId));

            if (documentContents is null || documentContents.Length < 1)
                throw new ArgumentException("Cannot run Tennr workflow: zero-length document content detected.", nameof(documentContents));

            if (string.IsNullOrEmpty(documentName))
                throw new ArgumentException("Cannot run Tennr workflow: document name is required.", nameof(documentName));

            var req = new HttpRequestMessage(HttpMethod.Post, EndpointUri);
            var byteArrayContent = new ByteArrayContent(documentContents, 0, documentContents.Length);

            if (!string.IsNullOrEmpty(contentType))
                byteArrayContent.Headers.ContentType = new MediaTypeHeaderValue(contentType);

            var formData = new MultipartFormDataContent
                {
                    { byteArrayContent, TennrConstants.ParameterNameFiles, documentName },
                };

            additionalParameters ??= new Dictionary<string, string>();

            if (!additionalParameters.ContainsKey(TennrConstants.ParameterNameDoc))
                additionalParameters.Add(TennrConstants.ParameterNameDoc, documentName);
            else
                additionalParameters[TennrConstants.ParameterNameDoc] = documentName;

            if (!additionalParameters.ContainsKey(TennrConstants.ParameterNameAgentId))
                additionalParameters.Add(TennrConstants.ParameterNameAgentId, agentId);
            else
                additionalParameters[TennrConstants.ParameterNameAgentId] = agentId;

            if (additionalParameters.Count > 0)
            {
                foreach (var parameter in additionalParameters)
                    formData.Add(new StringContent(parameter.Value), parameter.Key);
            }
                

            req.Content = formData;

            // Debug.WriteLine(await req.Content.ReadAsStringAsync());

            var response = await client.Execute(req);

            var tennrResponse = new TennrResponse();
            string? rawResponse = null;

            if (response.Content is not null)
            {
                rawResponse = await response.Content.ReadAsStringAsync();

                if (MediaTypeNames.Application.Json.Equals(response.Content.Headers.ContentType?.MediaType))
                    tennrResponse = await response.Content.ReadFromJsonAsync<TennrResponse>() ?? new();
                else
                    throw new InvalidOperationException($"Unhandled content type in Tennr workflow: {response.Content.Headers.ContentType}");
            }

            tennrResponse.Success = response.IsSuccessStatusCode;
            tennrResponse.HttpStatusCode = (int)response.StatusCode;
            tennrResponse.Raw = rawResponse;
            return tennrResponse;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="agentId"></param>
        /// <param name="filePath"></param>
        /// <param name="additionalParameters"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public async Task<TennrResponse?> Run(string agentId,
                                              string filePath,
                                              IDictionary<string, string>? additionalParameters = null)
        {
            if (string.IsNullOrEmpty(agentId))
                throw new ArgumentException("Cannot run Tennr workflow: agent ID is required.", nameof(agentId));

            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("Cannot run Tennr workflow: Path to document is required.", nameof(filePath));

            if (!Path.IsPathRooted(filePath))
                filePath = Path.GetFullPath(filePath);

            var fileInfo = new FileInfo(filePath);

            if (!fileInfo.Exists)
                throw new FileNotFoundException($"Cannot run Tennr workflow: document {filePath} does not exist.");

            using var source = fileInfo.OpenRead();
            using var binaryReader = new BinaryReader(source);
            byte[] fileData = binaryReader.ReadBytes((int)source.Length);

            if (fileData.Length < 1)
                throw new ArgumentException("Cannot run Tennr workflow: zero-length document content detected.", nameof(filePath));

            return await Run(agentId, fileData, fileInfo.Name, fileInfo.GuessContentTypeFromFileExtension(), additionalParameters);
        }
    }
}
