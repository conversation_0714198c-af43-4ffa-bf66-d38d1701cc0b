﻿@using DoctorsApplication.Models
@using Persante.Blazor.SharedUI.Models
@using System.Collections.ObjectModel
@using System.Text.RegularExpressions

<PageStack FlowDirectionIsRow>
    <PageCard IsOutlined>
        <PageList>
            <PageListItem Gap="4" Class="brd-none">
                <MudDialog>
                    <DialogContent>
                        <PageCard IsOutlined="true">
                            <PageList>
                                <PageListItem Gap="4" Class="brd-none">
                                    <PageStack FlowDirectionIsRow="true">
                                        <MudText Class="wt-15 p-l-10 pt-13">
                                            <b>MRN</b>
                                        </MudText>
                                        <MudText Class="wt-30">
                                            <MudTextField Label="@partnerEMRFieldType[0].EmrFieldTypeName"
                                                          Variant="Variant.Outlined" T="string"
                                                          @bind-Value="@partnerEMRFieldType[0].EmrFieldTypeValue"
                                                          Margin="Margin.Dense"
                                                          OnBlur="@(()=> OnBlur(@partnerEMRFieldType[0]))"
                                                          Error="@partnerEMRFieldType[0].isNotValid"></MudTextField>
                                            @if (@partnerEMRFieldType[0].isNotValid)
                                            {
                                                <MudText Class="mud-input-helper-text mud-input-error">
                                                    @partnerEMRFieldType[0].FieldValueValidationErrorMessage
                                                </MudText>
                                            }
                                        </MudText>
                                    </PageStack>
                                </PageListItem>
                                <PageListItem Class="brd-none">
                                    <PageStack FlowDirectionIsRow="true">
                                        <MudText Class="wt-15 p-l-10 pt-13">
                                            <b>FIN</b>
                                        </MudText>
                                        <MudText Class="wt-30">
                                            <MudTextField Label="@partnerEMRFieldType[1].EmrFieldTypeName"
                                                          Variant="Variant.Outlined" T="string"
                                                          @bind-Value="@partnerEMRFieldType[1].EmrFieldTypeValue"
                                                          Margin="Margin.Dense"
                                                          OnBlur="@(()=> OnBlur(@partnerEMRFieldType[1]))"
                                                          Error="@partnerEMRFieldType[1].isNotValid"></MudTextField>
                                            @if (@partnerEMRFieldType[1].isNotValid)
                                            {
                                                <MudText Class="mud-input-helper-text mud-input-error">
                                                    @partnerEMRFieldType[1].FieldValueValidationErrorMessage
                                                </MudText>
                                            }
                                        </MudText>
                                    </PageStack>
                                </PageListItem>
                                <PageListItem Class="brd-none">
                                    <PageStack FlowDirectionIsRow="true">
                                        <MudText Class="wt-15 p-l-10 pt-13">
                                            <b>Order Num</b>
                                        </MudText>
                                        <MudText Class="wt-30">
                                            <MudTextField Label="@partnerEMRFieldType[2].EmrFieldTypeName"
                                                          Variant="Variant.Outlined" T="string"
                                                          @bind-Value="@partnerEMRFieldType[2].EmrFieldTypeValue"
                                                          Margin="Margin.Dense"
                                                          OnBlur="@(()=> OnBlur(@partnerEMRFieldType[2]))"
                                                          Error="@partnerEMRFieldType[2].isNotValid"></MudTextField>
                                            @if (@partnerEMRFieldType[2].isNotValid)
                                            {
                                                <MudText Class="mud-input-helper-text mud-input-error">
                                                    @partnerEMRFieldType[2].FieldValueValidationErrorMessage
                                                </MudText>
                                            }
                                        </MudText>
                                    </PageStack>
                                </PageListItem>
                                <PageListItem Class="brd-none" LabelTextSeparator=""
                                              ContentFlowDirection="FlowDirection.Column">
                                    <PageStack FlowDirectionIsRow="true">
                                        <MudText Class="wt-15 p-l-10 pt-13">
                                        </MudText>
                                        <MudText>
                                            <PageButtons Buttons="@(new List<PageButtons.ActionButton>() {
                                                            new ()
                                                            {
                                                                Text = "Cancel",
                                                                Variant = Variant.Outlined,
                                                                ButtonColor = Color.Info,
                                                                ButtonType = ButtonType.Submit,
                                                                Size = Size.Small,
                                                                OnClick = (()=>(CancelPartenerEMRField()))
                                                            },
                                                            new ()
                                                            {
                                                                Text = "Save",
                                                                Variant = Variant.Filled,
                                                                ButtonColor = Color.Info,
                                                                ButtonType = ButtonType.Submit,
                                                                Size = Size.Small,
                                                                OnClick = (()=>(SavePartenerEMRField()))
                                                            },
                                                     })" />
                                        </MudText>
                                    </PageStack>
                                </PageListItem>
                                <PageListItem Class="brd-none">
                                    <MudText>
                                        <strong>
                                            Last Updated : @UpdatedOn - dmcclintock
                                        </strong>
                                    </MudText>
                                </PageListItem>
                                <PageListItem Class="brd-none">
                                    <MudExpansionPanels Dense>
                                        <MudExpansionPanel>
                                            <TitleContent>
                                                <MudText Class="ml-10">
                                                    <strong>History</strong>
                                                </MudText>
                                            </TitleContent>
                                            <ChildContent>
                                                <MudDataGrid Items="@PartnerEMRFieldsList"
                                                             Class="" HeaderClass="my-header">
                                                    <Columns>
                                                        <PropertyColumn Hidden Property="x => x.Id" />
                                                         <PropertyColumn Property="x => x.EmrFieldTypeName" Title="" />
                                                         <PropertyColumn Property="x => x.EmrFieldTypeValue" Title="" />
                                                         <PropertyColumn Property="x => x.UpdatedDate" Title="" />
                                                         <PropertyColumn Property="x => x.UpdatedBy" Title="" />
                                                     </Columns>
                                                     <PagerContent>
                                                     </PagerContent>
                                                 </MudDataGrid>
                                             </ChildContent>
                                         </MudExpansionPanel>
                                     </MudExpansionPanels>
                                 </PageListItem>
                             </PageList>
                         </PageCard>
                     </DialogContent>
                     <DialogActions>
                         <PageButtons Buttons="@(new List<PageButtons.ActionButton>() {
                            new ()
        {
            Text = "Ok",
            Variant = Variant.Outlined,
            ButtonColor = Color.Primary,
            ButtonType = ButtonType.Submit,
            OnClick = Close
        },
        new ()
        {
            Text = "Cancel",
            Variant = Variant.Outlined,
            ButtonColor = Color.Info,
            ButtonType = ButtonType.Submit,
            OnClick = Cancel
        },
})" />
                     </DialogActions>
                 </MudDialog>
             </PageListItem>
         </PageList>
     </PageCard>
 </PageStack>
 <NotificationToast @ref="toaster" />
