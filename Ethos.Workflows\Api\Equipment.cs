using System.Text.Json.Nodes;
using Ethos.Model;

namespace Ethos.Workflows.Api;

public sealed record EquipmentDto
{
    public required Guid Id { get; set; }
    public required Guid CareLocationId { get; set; }
    public required Guid? RoomId { get; set; }
    public required long EquipmentTypeId { get; set; }
    public required JsonObject EquipmentData { get; set; }
}

public sealed record CreateEquipmentDto : IInputDto
{
    public required Guid? CareLocationId { get; set; }
    public required Guid? RoomId { get; set; }
    public required long? EquipmentTypeId { get; set; }
    public required JsonObject EquipmentData { get; set; }
}

public interface IEquipmentApi : IEntityHttpClient<CreateEquipmentDto, EquipmentDto, EquipmentQ>;

public class EquipmentHttpClient(System.Net.Http.HttpClient httpClient)
    : EntityHttpClientBase<CreateEquipmentDto, EquipmentDto, EquipmentQ>(httpClient, "equipment"),
        IEquipmentApi;