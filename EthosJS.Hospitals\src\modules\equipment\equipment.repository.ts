import moment from 'moment/moment';
import { BadRequestException } from '@nestjs/common';
import { EntityRepository } from 'typeorm';
import { IListResult } from '@app/common/types';
import { EquipmentFiltersDto } from '@app/modules/equipment/dto/equipment.filters.dto';
import { EquipmentEntity } from '@app/modules/equipment/equipment.entity';
import { BaseRepository } from '@app/common/base.repository';
import { EquipmentCollectionDto } from '@app/modules/equipment/dto/equipment.collection.dto';

@EntityRepository(EquipmentEntity)
export class EquipmentRepository extends BaseRepository<EquipmentEntity> {
  collectionDto = EquipmentCollectionDto;

  async list(filters: EquipmentFiltersDto): Promise<IListResult<EquipmentEntity>> {
    return super.list(filters);
  }

  async checkHasSchedules(equipmentId: number): Promise<void> {
    const today = moment().startOf('day');

    const [{ count }] = await this.query('SELECT COUNT(*) as count FROM schedules WHERE schedules.date >= $1 AND schedules.equipments ? $2 AND schedules.deleted_at IS NULL', [today, equipmentId]);

    if (Number(count) > 0) {
      throw new BadRequestException('Equipment has related schedules');
    }

    const [{ count: bedSchedulesCount }] = await this.query('SELECT COUNT(*) as count FROM bed_schedules WHERE bed_schedules.date >= $1 AND bed_schedules.equipments ? $2 AND bed_schedules.deleted_at IS NULL', [today, equipmentId]);

    if (Number(bedSchedulesCount) > 0) {
      throw new BadRequestException('Equipment has related bed schedules');
    }
  }

  async checkHasStudies(equipmentId: number): Promise<void> {
    const [{ count }] = await this.query('SELECT COUNT(*) as count FROM studies WHERE studies.equipments ? $1 AND studies.deleted_at IS NULL', [equipmentId]);

    if (Number(count) > 0) {
      throw new BadRequestException('Equipment has related studies');
    }
  }

  async checkHasFacilities(equipmentId: number): Promise<void> {
    const [{ count }] = await this.query('SELECT COUNT(*) as count FROM facilities WHERE facilities.equipments ? $1 AND facilities.deleted_at IS NULL', [equipmentId]);

    if (Number(count) > 0) {
      throw new BadRequestException('Equipment has related facilities');
    }
  }
}
