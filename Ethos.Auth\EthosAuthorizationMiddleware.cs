﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Diagnostics;
using System.Net.Mime;
using System.Security.Claims;
using System.Text.Json;

namespace Ethos.Auth
{
    /// <summary>
    /// 
    /// </summary>
    public class EthosAuthorizationMiddleware : IDisposable
    {
        readonly RequestDelegate _next;
        const string Forbidden = nameof(Forbidden);
        const string ProblemDetailType = "https://tools.ietf.org/html/rfc7231#section-6.5.3";
        const string ForbiddenMessage = "You do not have the required permissions to access this resource.";
        readonly ILogger<EthosAuthorizationMiddleware>? logger;
        readonly List<EthosScope> debugScopes = [];
        IDisposable? logScope;
        string? controllerName;

        /// <summary>
        /// Optional configuration of JWT claim names. If this property is null, default claim names will be used. 
        /// Additionally, default claim names will be used for any property value on the object that is null or empty string.
        /// </summary>
        public EthosAuthorizationClaimsConfiguration? ClaimNames { get; set; }

        /// <summary>
        /// Whether or not the default behavior is to allow any controller or method if no authorization is defined.
        /// </summary>
        public bool DefaultAllow { get; set; }

        /// <summary>
        /// Whether or not the middleware will use the application/problem+json format in error responses.
        /// </summary>
        public bool UseProblemDetails { get; set; }

        /// <summary>
        /// Default application/problem+json to use in error responses.
        /// </summary>
        public ProblemDetails? ProblemDetails { get; set; }

        /// <summary>
        /// Configuration associated with authorization, used primarily for injecting user claims for debugging/testing.
        /// </summary>
        public IConfiguration? Configuration { get; set; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="next"></param>
        /// <param name="options"></param>
        /// <param name="logger"></param>
        public EthosAuthorizationMiddleware(RequestDelegate next, IOptions<EthosAuthorizationOptions> options, ILogger<EthosAuthorizationMiddleware> logger)
        {
            _next = next;
            this.logger = logger;
            DefaultAllow = options.Value.DefaultAllow;
            UseProblemDetails = options.Value.UseProblemDetails;
            ProblemDetails = options.Value.ProblemDetails;
            ClaimNames = options.Value.ClaimNames;
            debugScopes.AddRange(options.Value.DebugScopes);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="next"></param>
        /// <param name="options"></param>
        /// <param name="logger"></param>
        public EthosAuthorizationMiddleware(RequestDelegate next, ILogger<EthosAuthorizationMiddleware> logger)
        {
            _next = next;
            this.logger = logger;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public async Task InvokeAsync(HttpContext context)
        {
            var denied = true;

            var endpoint = context.GetEndpoint();

            if (endpoint is null)
            {
                Log("No endpoint found: cannot perform authorization.");
                await _next(context);
                return;
            }

            controllerName = endpoint.GetControllerName();

            if (HttpMethod.Options.Method.Equals(context.Request.Method, StringComparison.OrdinalIgnoreCase))
            {
                // per standard, OPTIONS request (i.e., CORS) should not require authorization
                Log($"HTTP method is {HttpMethod.Options.Method}: user is authorized.");
                denied = false;
            }

            var disableEthosAuth = endpoint.GetCustomAttributes<DisableEthosAuthorizationAttribute>() ?? [];
            if (disableEthosAuth.Count > 0)
            {
                Log("Ethos authorization is explicity disabled on endpoint: skipping authorization process.");
                await _next(context);
                return;
            }

            // are we allowing anonymous access?
            var allowAnon = endpoint.GetCustomAttributes<AllowAnonymousAttribute>() ?? [];
            if (allowAnon.Count > 0)
            {
                Log("Found anonymous access metadata on endpoint: user is authorized.");
                denied = false;
            }
            
            if (denied)
            {
                var userDisplayName = $"{context.User.GetGivenName(ClaimNames?.GivenName)} {context.User.GetSurname(ClaimNames?.Surname)}";

                if (userDisplayName.Trim() == string.Empty)
                    userDisplayName = context.User.Identity?.Name ?? "<Unknown>";

                var userEmail = context.User.GetEmail(ClaimNames?.Email);

                logScope = logger?.BeginScope(new { Url = context.Request.GetDisplayUrl(), ControllerName = controllerName ?? "<Unknown>", User = userDisplayName, Email = userEmail });

                var feature = endpoint.GetFeature() ?? string.Empty;
                var satisfyAny = endpoint.SatisfyAny();

                if (!string.IsNullOrEmpty(feature))
                    Log($"Found feature name: {feature}");

                var scopeRule = satisfyAny ? "ANY" : "ALL";
                Log($"Scoping rule: Satisfy {scopeRule}", satisfyAny ? LogLevel.Information : LogLevel.Debug);

                var authorizations = endpoint.GetCustomAttributes<EthosAuthScopeAttribute>() ?? [];

                List<EthosScope> scopes = [.. authorizations.SelectMany(a => a.Scopes)];

                scopes.ForEach(s =>
                {
                    var logged = false;

                    if (string.IsNullOrEmpty(s.Feature))
                    {
                        if (string.IsNullOrEmpty(feature))
                        {
                            if (!logged)
                            {
                                Log($"No feature defined for endpoint scope '{s}' and no feature found on controller.", LogLevel.Warning);
                                logged = true;
                            }
                        }
                        else
                        {
                            Log($"Assigning feature '{feature}' to endpoint scope: {s}", LogLevel.Information);
                            s.Feature = feature;
                        }
                    }
                });

                // Get the user's permissions from the JWT (claims)
                var userPermissions = GetUserPermissions(context.User, feature);

                Log($"{Environment.NewLine}Endpoint scopes:{Environment.NewLine}{string.Join(Environment.NewLine, scopes)}{Environment.NewLine}" +
                    $"{Environment.NewLine}User scopes:{Environment.NewLine}{string.Join(Environment.NewLine, userPermissions)}");

                // Check if the user has all the required permissions
                if (HasRequiredPermissions(userPermissions, scopes, satisfyAny))
                {
                    Log($"User is authorized for the endpoint and method.", LogLevel.Information);
                    denied = false;
                }

                if (denied)
                {
                    Log($"User is NOT authorized for the endpoint and/or method.", LogLevel.Warning);

                    var statusCode = StatusCodes.Status403Forbidden;

                    if (!UseProblemDetails)
                    {
                        Log($"Returning {MediaTypeNames.Text.Plain} result.", LogLevel.Trace);
                        context.Response.ContentType = MediaTypeNames.Text.Plain;
                        context.Response.StatusCode = statusCode;
                        await context.Response.WriteAsync(ForbiddenMessage);
                    }
                    else
                    {
                        Log($"Returning {MediaTypeNames.Application.ProblemJson} result.", LogLevel.Trace);
                        var problemDetails = ProblemDetails ?? new ProblemDetails
                        {
                            Title = Forbidden,
                            Detail = ForbiddenMessage,
                            Type = ProblemDetailType,
                        };

                        if (!problemDetails.Status.HasValue)
                            problemDetails.Status = statusCode;

                        problemDetails.Instance = context.Request.GetEncodedUrl();

                        if (string.IsNullOrEmpty(problemDetails.Type))
                            problemDetails.Type = ProblemDetailType;

                        await context.WriteJsonAsync(problemDetails, statusCode, MediaTypeNames.Application.ProblemJson);
                    }
                    return;
                }
            }
            await _next(context);
        }

        const string AUTHORIZATION = nameof(AUTHORIZATION);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="message"></param>
        /// <param name="trace"></param>
        void Log(string message, LogLevel logLevel = LogLevel.Debug, Exception? ex = null)
        {
            if (string.IsNullOrEmpty(message) || logLevel == LogLevel.None)
                return;

#if DEBUG
                var _message = string.IsNullOrEmpty(controllerName) ? $"[{AUTHORIZATION}] {message}" :
                               $"[{AUTHORIZATION}] {{{controllerName}}} {message}";

                Debug.WriteLine(_message);

                if (ex is not null)
                    Debug.WriteLine(ex.ToString());
#endif            
            if (logger is not null)
            {
                if (logger.IsEnabled(logLevel))
                {
                    if (ex is not null)
                        logger.Log(logLevel, ex, message);
                    else
                        logger.Log(logLevel, message);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        List<EthosScope> GetUserPermissions(ClaimsPrincipal user, string feature)
        {
            var scopes = new List<EthosScope>();

            if (debugScopes.Count > 0)
            {
                Log($"Applying testing/debugging scopes to user: {string.Join(", ", debugScopes.Select(s => s.ToString()))}");
                scopes.AddRange(debugScopes);
            }

            var productsClaim = string.IsNullOrEmpty(ClaimNames?.Products) ? EthosClaimNames.ExtensionProducts : ClaimNames.Products;
            var extPrds = user.Claims.FirstOrDefault(c => string.Equals(c.Type, productsClaim));

            if (extPrds is null || string.IsNullOrEmpty(extPrds.Value))
            {
                Log($"No claim '{productsClaim}' found in JWT user context.", LogLevel.Warning);
                return scopes;
            }

            var prds = extPrds.GetProducts<JwtProducts>() ?? [];

            if (prds.Count == 0)
            {
                Log($"Claim '{productsClaim}' yielded an empty product collection: {extPrds.Value}", LogLevel.Warning);
                return scopes;
            }

            var scopeClaim = user.GetScopes(ClaimNames?.Scope);
            bool scopesInJwt = false;

            if (scopeClaim.Length > 0)
            {
                Log($"Found user scope(s) in JWT claim: {string.Join(", ", scopeClaim)}");
                List<string> actualScopes = [.. scopeClaim.SelectMany(s => s.Split(' '))];
                actualScopes.ForEach(s =>
                {
                    if (!string.IsNullOrEmpty(s))
                    {
                        scopesInJwt = true;
                        if (!EthosScope.TryParse(s, null, out var scope) || scope is null)
                            Log($"Invalid user scope encountered: {s}", LogLevel.Error);
                        else if (string.IsNullOrEmpty(scope.Feature))
                            Log($"No feature defined for scope: {s}", LogLevel.Error);
                        else
                            scopes.Add(scope);
                    }
                });
            }

            if (!scopesInJwt)
                Log("No user scopes found in JWT user context.", LogLevel.Warning);

            Log($"Returning {scopes.Count} scope(s) for user.");

            return scopes;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userScopes"></param>
        /// <param name="attributes"></param>
        /// <param name="satisfyAny"></param>
        /// <returns></returns>
        bool HasRequiredPermissions(IEnumerable<EthosScope> userScopes, IEnumerable<EthosScope> scopes, bool satisfyAny)
        {
            if (scopes is null || !scopes.Any())
            {
                Log($"No authorization attributes were found on endpoint, returning {nameof(DefaultAllow)}: {DefaultAllow}", LogLevel.Warning);
                return DefaultAllow;
            }

            userScopes ??= [];

            return satisfyAny ? scopes.Any(attribute => userScopes.Any(p => attribute.IsAuthorized(p))) :
                                scopes.All(attribute => userScopes.Any(p => attribute.IsAuthorized(p)));
        }

        /// <summary>
        /// 
        /// </summary>
        public void Dispose()
        {
            logScope?.Dispose();
            GC.SuppressFinalize(this);
        }
    }

    /// <summary>
    /// 
    /// </summary>
    public static class EthosAuthorizationMiddlewareExtensions
    {
        const string Auth = nameof(Auth);
        const string Authorization = nameof(Authorization);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="builder"></param>
        /// <returns></returns>
        public static IApplicationBuilder UseEthosAuthorization(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<EthosAuthorizationMiddleware>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="configuration"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static EthosAuthorizationOptions GetEthosAuthorizationOptions(this IConfiguration configuration)
        {
            var options = new EthosAuthorizationOptions();
            options.DefaultAllow = configuration.GetValue($"{Auth}:{Authorization}{nameof(options.DefaultAllow)}", false);
            options.UseProblemDetails = configuration.GetValue($"{Auth}:{Authorization}{nameof(options.UseProblemDetails)}", true);
            options.ClaimNames = configuration.GetSection($"{Auth}:{Authorization}{nameof(options.ClaimNames)}").Get<EthosAuthorizationClaimsConfiguration>();
            options.ProblemDetails = configuration.GetSection($"{Auth}:{Authorization}{nameof(options.ProblemDetails)}").Get<ProblemDetails>();

            List<EthosScope> debugScopes = [];
            var section = configuration.GetSection($"{Auth}:{Authorization}:{nameof(options.DebugScopes)}");

            if (section.Exists())
            {
                var claims = section.Get<string[]>() ?? [];

                if (claims.Length > 0)
                {
                    try
                    {
                        debugScopes.AddRange(claims.Select(c => new EthosScope(c)));
                    }
                    catch (Exception ex)
                    {
                        throw new Exception($"Cannot apply debug scopes: {ex.Message}", ex);
                    }
                }
            }

            var claimsJson = configuration.GetValue<string>($"{Auth}:{Authorization}:{nameof(options.DebugScopes)}_JSON");

            if (!string.IsNullOrEmpty(claimsJson))
            {
                try
                {
                    var scopeClaims = JsonSerializer.Deserialize<List<string>>(claimsJson) ?? [];
                    debugScopes.AddRange(scopeClaims.Select(c => new EthosScope(c)));
                }
                catch (Exception ex)
                {
                    throw new Exception($"Cannot apply debug scopes from JSON: {ex.Message}", ex);
                }
            }

            options.DebugScopes = [.. debugScopes.Distinct()];
            return options;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="serviceCollection"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        public static IServiceCollection ConfigureEthosAuthorization(this IServiceCollection serviceCollection, IConfiguration configuration)
        {
            return serviceCollection.Configure<EthosAuthorizationOptions>((options) =>
            {
                options.DefaultAllow = configuration.GetValue($"{Auth}:{Authorization}{nameof(options.DefaultAllow)}", false);
                options.UseProblemDetails = configuration.GetValue($"{Auth}:{Authorization}{nameof(options.UseProblemDetails)}", true);
                options.ClaimNames = configuration.GetSection($"{Auth}:{Authorization}{nameof(options.ClaimNames)}").Get<EthosAuthorizationClaimsConfiguration>();
                options.ProblemDetails = configuration.GetSection($"{Auth}:{Authorization}{nameof(options.ProblemDetails)}").Get<ProblemDetails>();

                List<EthosScope> debugScopes = [];
                var section = configuration.GetSection($"{Auth}:{Authorization}:{nameof(options.DebugScopes)}");

                if (section.Exists())
                {
                    var claims = section.Get<string[]>() ?? [];

                    if (claims.Length > 0)
                    {
                        try
                        {
                            debugScopes.AddRange(claims.Select(c => new EthosScope(c)));
                        }
                        catch (Exception ex)
                        {
                            throw new Exception($"Cannot apply debug scopes: {ex.Message}", ex);
                        }
                    }
                }

                var claimsJson = configuration.GetValue<string>($"{Auth}:{Authorization}:{nameof(options.DebugScopes)}_JSON");

                if (!string.IsNullOrEmpty(claimsJson))
                {
                    try
                    {
                        var scopeClaims = JsonSerializer.Deserialize<List<string>>(claimsJson) ?? [];
                        debugScopes.AddRange(scopeClaims.Select(c => new EthosScope(c)));
                    }
                    catch (Exception ex)
                    {
                        throw new Exception($"Cannot apply debug scopes from JSON: {ex.Message}", ex);
                    }
                }

                options.DebugScopes = [.. debugScopes.Distinct()];
            });
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="serviceCollection"></param>
        /// <param name="optionsBuilder"></param>
        /// <returns></returns>
        public static IServiceCollection ConfigureEthosAuthorization(this IServiceCollection serviceCollection,
                                                                     Func<EthosAuthorizationOptions> optionsBuilder)
        {
            return serviceCollection.Configure<EthosAuthorizationOptions>((options) => options = optionsBuilder());
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="builder"></param>
        /// <param name="optionBuilder"></param>
        /// <returns></returns>
        public static IApplicationBuilder UseEthosAuthorization(this IApplicationBuilder builder,
                                                                Func<EthosAuthorizationOptions> optionBuilder)
        {
            var options = optionBuilder();
            return builder.UseMiddleware<EthosAuthorizationMiddleware>(Options.Create(options));
        }
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosAuthorizationOptions
    {
        public bool DefaultAllow {  get; set; }
        public bool UseProblemDetails { get; set; }
        public ProblemDetails? ProblemDetails { get; set; }
        public EthosScope[] DebugScopes { get; set; } = [];
        public EthosAuthorizationClaimsConfiguration? ClaimNames { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosAuthorizationClaimsConfiguration
    {
        public string? Scope { get; set; }
        public string? GivenName { get; set; }
        public string? Surname { get; set; }
        public string? Email { get; set; }
        public string? Uuid { get; set; }
        public string? Products { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    internal static class HttpContextExtensions
    {
        internal static async Task WriteJsonAsync<T>(this HttpContext context, T obj, int? statusCode = null, string contentType = "application/json")
        {
            context.Response.ContentType = contentType;
            if (statusCode.HasValue)
            {
                context.Response.StatusCode = statusCode.Value;
            }
            await JsonSerializer.SerializeAsync(context.Response.Body, obj);
        }
    }
}
