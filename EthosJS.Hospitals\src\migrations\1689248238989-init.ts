import { MigrationInterface, QueryRunner } from 'typeorm';

export class init1689248238989 implements MigrationInterface {
    name = 'init1689248238989'

    public async up(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('CREATE TABLE "technicians" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "name" character varying NOT NULL, "number_of_beds" integer, "clinic_id" integer NOT NULL, "facility_id" integer, CONSTRAINT "PK_b14514b23605f79475be53065b3" PRIMARY KEY ("id"))');
      await queryRunner.query('CREATE TABLE "clinics" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "name" character varying NOT NULL, CONSTRAINT "PK_5513b659e4d12b01a8ab3956abc" PRIMARY KEY ("id"))');
      await queryRunner.query('CREATE TABLE "facilities" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "name" character varying NOT NULL, "number_of_beds" integer NOT NULL DEFAULT 0, "clinic_id" integer NOT NULL, CONSTRAINT "PK_2e6c685b2e1195e6d6394a22bc7" PRIMARY KEY ("id"))');
      await queryRunner.query('CREATE TYPE "bed_schedules_shift_enum" AS ENUM(\'day\', \'night\')');
      await queryRunner.query('CREATE TABLE "bed_schedules" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "facility_id" integer NOT NULL, "number_of_beds" integer NOT NULL, "shift" "bed_schedules_shift_enum" NOT NULL, "date" date NOT NULL, CONSTRAINT "PK_14d89702021f8ea249771dabb4d" PRIMARY KEY ("id"))');
      await queryRunner.query('CREATE TABLE "study_equipments" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "equipment_id" integer NOT NULL, "study_id" integer NOT NULL, "count" integer NOT NULL, CONSTRAINT "PK_9ee48580e26950d0003ca687a85" PRIMARY KEY ("id"))');
      await queryRunner.query('CREATE TABLE "studies" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "name" character varying NOT NULL, CONSTRAINT "PK_b100ff0c4a0ad02a9c2270d45b6" PRIMARY KEY ("id"))');
      await queryRunner.query('CREATE TABLE "schedule_equipments" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "schedule_id" integer NOT NULL, "equipment_id" integer NOT NULL, "count" integer NOT NULL, CONSTRAINT "PK_a1521e6ab08e2f34c20558c5581" PRIMARY KEY ("id"))');
      await queryRunner.query('CREATE TYPE "schedules_shift_enum" AS ENUM(\'day\', \'night\')');
      await queryRunner.query('CREATE TABLE "schedules" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "technician_id" integer NOT NULL, "patient_id" integer NOT NULL, "study_id" integer NOT NULL, "shift" "schedules_shift_enum" NOT NULL, "date" date NOT NULL, CONSTRAINT "PK_7e33fc2ea755a5765e3564e66dd" PRIMARY KEY ("id"))');
      await queryRunner.query('CREATE TABLE "equipments" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "name" character varying NOT NULL, CONSTRAINT "PK_250348d5d9ae4946bcd634f3e61" PRIMARY KEY ("id"))');
      await queryRunner.query('CREATE TYPE "technician_schedules_shift_enum" AS ENUM(\'day\', \'night\', \'day_off\')');
      await queryRunner.query('CREATE TABLE "technician_schedules" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "technician_id" integer NOT NULL, "facility_id" integer NOT NULL, "shift" "technician_schedules_shift_enum" NOT NULL, "date" date NOT NULL, CONSTRAINT "PK_edc22a2c0147605affa00a6e376" PRIMARY KEY ("id"))');
      await queryRunner.query('CREATE TABLE "users" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "email" character varying NOT NULL, "role" character varying, "name" character varying NOT NULL, "token" character varying, CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email"), CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"))');
      await queryRunner.query('ALTER TABLE "technicians" ADD CONSTRAINT "FK_46ac6d72b344e14071b21fd62a2" FOREIGN KEY ("clinic_id") REFERENCES "clinics"("id") ON DELETE CASCADE ON UPDATE NO ACTION');
      await queryRunner.query('ALTER TABLE "technicians" ADD CONSTRAINT "FK_96f7243ac16a06c2f43aa99b052" FOREIGN KEY ("facility_id") REFERENCES "facilities"("id") ON DELETE CASCADE ON UPDATE NO ACTION');
      await queryRunner.query('ALTER TABLE "facilities" ADD CONSTRAINT "FK_dd71ccbb372c83f6651276a649a" FOREIGN KEY ("clinic_id") REFERENCES "clinics"("id") ON DELETE CASCADE ON UPDATE NO ACTION');
      await queryRunner.query('ALTER TABLE "bed_schedules" ADD CONSTRAINT "FK_6096b6c62896cea3ac426d378a8" FOREIGN KEY ("facility_id") REFERENCES "facilities"("id") ON DELETE NO ACTION ON UPDATE NO ACTION');
      await queryRunner.query('ALTER TABLE "study_equipments" ADD CONSTRAINT "FK_75bec157d6a2da16fa84c403af9" FOREIGN KEY ("equipment_id") REFERENCES "equipments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION');
      await queryRunner.query('ALTER TABLE "study_equipments" ADD CONSTRAINT "FK_6cbe07f5f2b9a8a514d952f7f2e" FOREIGN KEY ("study_id") REFERENCES "studies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION');
      await queryRunner.query('ALTER TABLE "schedule_equipments" ADD CONSTRAINT "FK_aa16709a5d2cde12f729aa25ac0" FOREIGN KEY ("schedule_id") REFERENCES "schedules"("id") ON DELETE NO ACTION ON UPDATE NO ACTION');
      await queryRunner.query('ALTER TABLE "schedule_equipments" ADD CONSTRAINT "FK_6ab845d06a3527ad75f89b9c382" FOREIGN KEY ("equipment_id") REFERENCES "equipments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "schedule_equipments" DROP CONSTRAINT "FK_6ab845d06a3527ad75f89b9c382"');
      await queryRunner.query('ALTER TABLE "schedule_equipments" DROP CONSTRAINT "FK_aa16709a5d2cde12f729aa25ac0"');
      await queryRunner.query('ALTER TABLE "study_equipments" DROP CONSTRAINT "FK_6cbe07f5f2b9a8a514d952f7f2e"');
      await queryRunner.query('ALTER TABLE "study_equipments" DROP CONSTRAINT "FK_75bec157d6a2da16fa84c403af9"');
      await queryRunner.query('ALTER TABLE "bed_schedules" DROP CONSTRAINT "FK_6096b6c62896cea3ac426d378a8"');
      await queryRunner.query('ALTER TABLE "facilities" DROP CONSTRAINT "FK_dd71ccbb372c83f6651276a649a"');
      await queryRunner.query('ALTER TABLE "technicians" DROP CONSTRAINT "FK_96f7243ac16a06c2f43aa99b052"');
      await queryRunner.query('ALTER TABLE "technicians" DROP CONSTRAINT "FK_46ac6d72b344e14071b21fd62a2"');
      await queryRunner.query('DROP TABLE "users"');
      await queryRunner.query('DROP TABLE "technician_schedules"');
      await queryRunner.query('DROP TYPE "technician_schedules_shift_enum"');
      await queryRunner.query('DROP TABLE "equipments"');
      await queryRunner.query('DROP TABLE "schedules"');
      await queryRunner.query('DROP TYPE "schedules_shift_enum"');
      await queryRunner.query('DROP TABLE "schedule_equipments"');
      await queryRunner.query('DROP TABLE "studies"');
      await queryRunner.query('DROP TABLE "study_equipments"');
      await queryRunner.query('DROP TABLE "bed_schedules"');
      await queryRunner.query('DROP TYPE "bed_schedules_shift_enum"');
      await queryRunner.query('DROP TABLE "facilities"');
      await queryRunner.query('DROP TABLE "clinics"');
      await queryRunner.query('DROP TABLE "technicians"');
    }

}
