import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { ETechnicianScheduleShift } from '@app/modules/technicianSchedule/enums';
import { BaseDto } from '@app/common/dto/base.dto';

export class TechnicianScheduleDto extends BaseDto {
  @ApiProperty()
  @Expose()
  technicianId: number;

  @ApiProperty()
  @Expose()
  technicianName: string;

  @ApiPropertyOptional()
  @Expose()
  facilityId?: number;

  @ApiProperty({ enum: ETechnicianScheduleShift })
  @Expose()
  shift: ETechnicianScheduleShift;

  @ApiPropertyOptional()
  @Expose()
  capacity?: number;

  @ApiProperty()
  @Expose()
  date: string;
}
