//@ts-nocheck
// import '@axmit/persante-hospitals';
import { IRequestSuccess } from 'common/models';

export interface ITechniciansCollectionPayload  {}
export interface ITechniciansCollectionDto  {}
export interface ITechnicianModelCreatePayload extends IRequestSuccess {}
export interface ITechnicianModelUpdatePayload extends IRequestSuccess {}
export interface ITechnicianModelDeletePayload extends  IRequestSuccess {}
export interface ITechnicianModelDto {}
export interface ITechnicianCredentialCreateDto {}
export interface ITechnicianCredentialDto {}
export interface ITechnicianStandardScheduleModelDto  {
  key?: string;
}

export interface ITechniciansCollection {
  data: ITechniciansCollectionDto | null;
  loading: boolean;
}

export interface ITechnicianModel {
  data: ITechnicianModelDto | null;
  loading: boolean;
}

export interface ITechnicianStandardScheduleModel {
  data: ITechnicianStandardScheduleModelDto;
}
