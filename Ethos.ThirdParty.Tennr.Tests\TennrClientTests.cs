﻿using Xunit;
using FluentAssertions;

namespace Ethos.ThirdParty.Tennr.Tests
{
    public class TennrClientTests
    {
        private readonly TennrClient client;
        const string agentId = "67bd02f90e031b8117761473";
        const string apiKey = "WQYtefuqG9h7/317GumhRHz7muKl6xxaenRjGFl5mpc=";
        const string filePath = @"C:\Users\<USER>\OneDrive\Desktop\IBF3029422.pdf";

        public TennrClientTests()
        {
            var httpClient = new HttpClient();
            client = new TennrClient(httpClient, apiKey);
        }

        [Fact]
        public async Task SendDocument_Success()
        {
            var result = await client.Workflow.Run(agentId, filePath, new Dictionary<string, string>() { { "tenantId", Guid.NewGuid().ToString() } });

            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Output.Should().BeNullOrEmpty();
            return;
        }

        [Fact]
        public async Task SendDocument_Success_Bytes()
        {
            var contents = File.ReadAllBytes(filePath);
            //var paramDict = new Dictionary<string, string>() { { "tenantId", Guid.NewGuid().ToString() } };
            var paramDict = new Dictionary<string, string>();
            var result = await client.Workflow.Run(agentId, contents, "IBF3029422_test.pdf", "application/pdf", paramDict);

            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Output.Should().BeNullOrEmpty();
            return;
        }
    }
}
