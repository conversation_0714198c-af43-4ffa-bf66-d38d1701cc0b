using Dapper;
using EnsoMsltConverter.StudyDataDto;
using Microsoft.Data.SqlClient;
using System.Configuration;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Xml;
using System.Xml.Serialization;

namespace EnsoMsltConverter
{
    public partial class Form1 : Form
    {
        private JsonSerializerOptions jsonSerializerOptions;
        IQueryProvider queryProvider;
        IConnectionStringProvider connectionStringProvider;
        IConnectionStringNameProvider connectionStringNameProvider;
        public Form1()
        {
            InitializeComponent();
            this.Icon = Properties.Resources.favicon;
            jsonSerializerOptions = new JsonSerializerOptions { WriteIndented = true };
            queryProvider = new QueryProvider();
            connectionStringNameProvider = new ConnectionStringNameProvider(ConfigurationManager.AppSettings["ConnectionStringName"]);
            connectionStringProvider = new ConnectionStringProvider(
                ConfigurationManager.ConnectionStrings[connectionStringNameProvider.ConnectionStringName].ConnectionString);
        }

        private StudyData selectedStudy = new();
        public StudyData SelectedStudy
        {
            get => selectedStudy;
            set
            {
                selectedStudy = value;
                try
                {
                    var json = JsonSerializer.Serialize(selectedStudy, jsonSerializerOptions);
                    txtStudyInfo.Text = json;
                }
                catch (Exception ex)
                {
                    txtStudyInfo.Text = $"Error serializing StudyData: {ex}";
                }
            }
        }

        private void BtnSearchStudy_Click(object sender, EventArgs e)
        {
            if (!int.TryParse(this.textBox1.Text, out int StudyId))
            {
                txtStudyInfo.Text = $"Error parsing Study Id as integer: '{this.textBox1.Text}'";
                return;
            }



            var searchQuery = queryProvider.SearchByStudyIdQuery();
            try
            {
                using (var conn = new SqlConnection(connectionStringProvider.ConnectionString))
                {
                    var result = conn.QueryFirst<string>(searchQuery, new { StudyId });
                    var ser = new XmlSerializer(typeof(StudyData));
                    var rdr = new StringReader(result);

                    SelectedStudy = (StudyData)ser.Deserialize(rdr);


                }
            }
            catch (Exception ex)
            {
                txtStudyInfo.Text = $"Error retrieving StudyData: {ex}";
            }
        }

        private void btnParseRml_Click(object sender, EventArgs e)
        {
            this.btnParseRml.Enabled = false;
            this.btnParseRml.Text = "Parsing Rml";
            Application.DoEvents();
            using (var ofd = new OpenFileDialog())
            {
                var initialDirectory = GetInitialRmlDirectory();
                ofd.InitialDirectory = initialDirectory;
                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    var file = ofd.FileName;
                    var fi = new FileInfo(file);
                    if (!fi.Exists)
                    {
                        txtStudyInfo.Text = "File does not exist";
                        return;
                    }
                    SaveRmlDirectory(fi);
                    FormatRml(fi);
                }
            }
            this.btnParseRml.Enabled = true;
            this.btnParseRml.Text = "Parse Rml";
            Application.DoEvents();
        }

        private void FormatRml(FileInfo fi)
        {
            string inputFilePath = fi.FullName; // Replace with your input XML file path
            string outputFilePath = "output.xml"; // Replace with your output XML file path

            // Create an XmlReader to read the input XML
            using (XmlReader reader = XmlReader.Create(inputFilePath))
            {
                // Create an XmlWriter with settings for formatting and indentation
                XmlWriterSettings settings = new XmlWriterSettings
                {
                    Indent = true,
                    IndentChars = "    ", // Use four spaces for indentation
                    NewLineChars = "\n",
                    NewLineHandling = NewLineHandling.Replace,
                    OmitXmlDeclaration = false // Include XML declaration, 

                };


                using (XmlWriter writer = XmlWriter.Create(outputFilePath, settings))
                {
                    // Read the input XML and write it with formatted indentation to the output
                    writer.WriteNode(reader, true);

                }
            }
            txtStudyInfo.Lines = File.ReadAllText(outputFilePath).Split('\n');

        }

        private string rmlSettingsFile = "rml.directory.settings";
        private void SaveRmlDirectory(FileInfo fi)
        {
            SaveRmlDirectory(fi.Directory.FullName);
        }

        private void SaveRmlDirectory(string fullName)
        {
            File.WriteAllText(rmlSettingsFile, fullName);
        }

        private string GetInitialRmlDirectory()
        {

            if (!File.Exists(rmlSettingsFile))
                SaveRmlDirectory(@"\\sleepcarecenter.com\devwebfiles\ClinicalData\CPR - Cooper Voorhees\00002211-112225");
            return File.ReadAllText(rmlSettingsFile);
        }
    }

    internal class ConnectionStringNameProvider : IConnectionStringNameProvider
    {
        public ConnectionStringNameProvider(string? connectionStringName)
        {
            ConnectionStringName = connectionStringName;
        }

        public string? ConnectionStringName { get; set; }
    }

    internal interface IConnectionStringNameProvider
    {
        string ConnectionStringName { get; set; }
    }

    internal interface IConnectionStringProvider
    {
        string ConnectionString { get; set; }
    }
    public class ConnectionStringProvider : IConnectionStringProvider
    {
        public string ConnectionString { get; set; }

        public ConnectionStringProvider(string connectionString)
        {
            ConnectionString = connectionString;
        }
    }
    public interface IQueryProvider
    {
        string SearchByStudyIdQuery();
    }
    public class QueryProvider : IQueryProvider
    {
        #region Queries
        string searchByStudyIdQuery()
        {
            var result = $@"select 
                s.Study_ID, s.Patient_ID, p.First_Name, p.Last_Name, p.Patient_DOB
                ,sf.FileName sfName, l.HQDataFolder, hss.SleepStudyRootFolder
                ,hcf.FolderPath, G3P.PatientID as G3PatientId

                , *
                from tbl_scoring s 
	                join tbl_patients p on s.patient_id= p.Patient_ID
	                join tbl_StudyFiles sf on s.study_id = sf.study_id
	                join tbl_locations l on s.LocationID=l.Location_ID
	                join HeliosStudyFiles hsf on hsf.StudyID=s.study_id
	                join helios..SleepStudy hss on hsf.HeliosSleepStudyID = hss.SleepStudyID
	                join helios..ClientFolder hcf on hss.SleepStudyRootFolderID = hcf.ClientFolderID 
	                join helios..Client hc on hcf.ClientID=  hc.ClientID and hc.HQClient=1
	                join [SleepwareG3]..[Patient] G3P on cast(s.patient_id as varchar(20)) = G3P.PatientUUID
	                join [SleepwareG3].[dbo].[SleepwareAcquisition] a on G3P.patientId= a.PatientID
                where
	                s.study_Id =@StudyId
                    ";
            return result;
        }

        string searchStudyByIdDtoQuery()
        {
            var result = $@"
                select distinct
	                Study.Study_ID as StudyId
	                , Patient.Patient_ID as PatientId 
	                , Patient.First_Name as FirstName 
	                , Patient.Last_Name as LastName
	                , Patient.Patient_DOB as Dob
	                , G3Patient.PatientID as G3PatientId
	                --, StudyFile.FileName, 
	                --, Location.HQDataFolder as HqFolder
	                ,StudyFolder.FolderPath
	                --, SleepStudy.SleepStudyRootFolder as CollectionFolder
	                --, *
                from tbl_scoring Study
	                join tbl_patients Patient on Study.patient_id= Patient.Patient_ID
	                join tbl_StudyFiles StudyFile on Study.study_id = StudyFile.study_id
	                join tbl_locations [Location] on Study.LocationID=[Location].Location_ID
	                join HeliosStudyFiles hsf on hsf.StudyID=Study.study_id
	                join helios..SleepStudy SleepStudy on hsf.HeliosSleepStudyID = SleepStudy.SleepStudyID
	                join helios..ClientFolder StudyFolder on SleepStudy.SleepStudyRootFolderID = StudyFolder.ClientFolderID 
	                join helios..Client hc on StudyFolder.ClientID=  hc.ClientID and hc.HQClient=1
	                join [SleepwareG3]..[Patient] G3Patient on cast(Patient.patient_id as varchar(20)) = G3Patient.PatientUUID
	                join [SleepwareG3].[dbo].[SleepwareAcquisition] a on G3Patient.patientId= a.PatientID
                where
	                Study.study_Id  = @StudyId
                --	in (**********,
                --**********)
                for Xml Auto, Root('StudyData')
                ";
            return result;
        }
        #endregion
        public string SearchByStudyIdQuery()
        {
            return searchStudyByIdDtoQuery();
        }
    }

    namespace StudyDataDto
    {

        // NOTE: Generated code may require at least .NET Framework 4.5 or .NET Core/Standard 2.0.
        /// <remarks/>
        [System.SerializableAttribute()]
        [System.ComponentModel.DesignerCategoryAttribute("code")]
        [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
        [System.Xml.Serialization.XmlRootAttribute(Namespace = "", IsNullable = false)]
        public partial class StudyData
        {

            private StudyDataStudy[] studyField;

            /// <remarks/>
            [System.Xml.Serialization.XmlElementAttribute("Study")]
            public StudyDataStudy[] Study
            {
                get
                {
                    return this.studyField;
                }
                set
                {
                    this.studyField = value;
                }
            }
        }

        /// <remarks/>
        [System.SerializableAttribute()]
        [System.ComponentModel.DesignerCategoryAttribute("code")]
        [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
        public partial class StudyDataStudy
        {

            private StudyDataStudyPatient patientField;

            private uint studyIdField;

            [JsonPropertyOrder(0)]
            /// <remarks/>
            [System.Xml.Serialization.XmlAttributeAttribute()]
            public uint StudyId
            {
                get
                {
                    return this.studyIdField;
                }
                set
                {
                    this.studyIdField = value;
                }
            }

            [JsonPropertyOrder(1)]
            /// <remarks/>
            public StudyDataStudyPatient Patient
            {
                get
                {
                    return this.patientField;
                }
                set
                {
                    this.patientField = value;
                }
            }


        }

        /// <remarks/>
        [System.SerializableAttribute()]
        [System.ComponentModel.DesignerCategoryAttribute("code")]
        [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
        public partial class StudyDataStudyPatient
        {

            private StudyDataStudyPatientG3Patient g3PatientField;

            private uint patientIdField;

            private string firstNameField;

            private string lastNameField;

            private System.DateTime dobField;

            [JsonPropertyOrder(1000)]
            /// <remarks/>
            public StudyDataStudyPatientG3Patient G3Patient
            {
                get
                {
                    return this.g3PatientField;
                }
                set
                {
                    this.g3PatientField = value;
                }
            }

            [JsonPropertyOrder(0)]
            /// <remarks/>
            [System.Xml.Serialization.XmlAttributeAttribute()]
            public uint PatientId
            {
                get
                {
                    return this.patientIdField;
                }
                set
                {
                    this.patientIdField = value;
                }
            }

            [JsonPropertyOrder(1)]
            /// <remarks/>
            [System.Xml.Serialization.XmlAttributeAttribute()]
            public string FirstName
            {
                get
                {
                    return this.firstNameField;
                }
                set
                {
                    this.firstNameField = value;
                }
            }

            [JsonPropertyOrder(2)]
            /// <remarks/>
            [System.Xml.Serialization.XmlAttributeAttribute()]
            public string LastName
            {
                get
                {
                    return this.lastNameField;
                }
                set
                {
                    this.lastNameField = value;
                }
            }

            [JsonPropertyOrder(3)]
            /// <remarks/>
            [System.Xml.Serialization.XmlAttributeAttribute()]
            public System.DateTime Dob
            {
                get
                {
                    return this.dobField;
                }
                set
                {
                    this.dobField = value;
                }
            }
        }

        /// <remarks/>
        [System.SerializableAttribute()]
        [System.ComponentModel.DesignerCategoryAttribute("code")]
        [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
        public partial class StudyDataStudyPatientG3Patient
        {

            private StudyDataStudyPatientG3PatientStudyFolder[] studyFolderField;

            private uint g3PatientIdField;

            [JsonPropertyOrder(1000)]
            /// <remarks/>
            [System.Xml.Serialization.XmlElementAttribute("StudyFolder")]
            public StudyDataStudyPatientG3PatientStudyFolder[] StudyFolder
            {
                get
                {
                    return this.studyFolderField;
                }
                set
                {
                    this.studyFolderField = value;
                }
            }

            [JsonPropertyOrder(0)]
            /// <remarks/>
            [System.Xml.Serialization.XmlAttributeAttribute()]
            public uint G3PatientId
            {
                get
                {
                    return this.g3PatientIdField;
                }
                set
                {
                    this.g3PatientIdField = value;
                }
            }
        }

        /// <remarks/>
        [System.SerializableAttribute()]
        [System.ComponentModel.DesignerCategoryAttribute("code")]
        [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
        public partial class StudyDataStudyPatientG3PatientStudyFolder
        {

            private string folderPathField;

            [JsonPropertyOrder(0)]
            /// <remarks/>
            [System.Xml.Serialization.XmlAttributeAttribute()]
            public string FolderPath
            {
                get
                {
                    return this.folderPathField;
                }
                set
                {
                    this.folderPathField = value;
                }
            }
        }


    }
}
