﻿using System.Linq.Expressions;
using System.Reflection;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class PhysicalMeasurementsDbo // Suitable for OwnsOne
{
    public required decimal? HeightInches { get; set; }
    public required decimal? WeightPounds { get; set; }
    public required decimal? NeckSize { get; set; }
    public required decimal? Bmi { get; set; }

    // Configure method used by OwnsOne
    public static void Configure(OwnedNavigationBuilder<PatientDbo, PhysicalMeasurementsDbo> builder)
    {
        builder.Property(p => p.HeightInches).HasPrecision(5, 2);
        builder.Property(p => p.WeightPounds).HasPrecision(6, 2);
        builder.Property(p => p.NeckSize).HasPrecision(4, 2);
        builder.Property(p => p.Bmi).HasPrecision(4, 2);
        // Validation > 0 etc., in application layer
    }
}

public class DemographicsDbo
{
    public required DateOnly? DateOfBirth { get; set; }
    public required long? GenderId { get; set; }
    public required long? SexId { get; set; }
    public required long? MaritalStatusId { get; set; }
    public required long? RaceId { get; set; }
    public required long? EthnicityId { get; set; }
    
    // Configure method used by OwnsOne
    public static void Configure<T>(OwnedNavigationBuilder<T, DemographicsDbo> builder) where T : class
    {
        builder.Property(d => d.DateOfBirth).IsRequired(false);
        builder.Property(d => d.GenderId).IsRequired(false);
        builder.Property(d => d.SexId).IsRequired(false);
        builder.Property(d => d.MaritalStatusId).IsRequired(false);
        builder.Property(d => d.RaceId).IsRequired(false);
        builder.Property(d => d.EthnicityId).IsRequired(false);
    }
}

public class IdentifierDbo
{
    // FIXME: make this into a reference data
    public required string System { get; set; }
    public required string Value { get; set; }

    // Configure method used by OwnsMany
    public static void Configure<T>(OwnedNavigationBuilder<T, IdentifierDbo> builder) where T : class
    {
        builder.Property(i => i.System).IsRequired().HasMaxLength(100);
        builder.Property(i => i.Value).IsRequired().HasMaxLength(100);
        // builder.HasIndex(i => new { i.System, i.Value }); // Index within the context of the owner
    }
}

public class PatientDbo : IAuditableEntity<PatientDbo>
{
    public virtual ICollection<PersonNameDbo> Names { get; set; } = new List<PersonNameDbo>();
    public DemographicsDbo? Demographics { get; set; }
    public ICollection<IdentifierDbo> Identifiers { get; set; } = new List<IdentifierDbo>();
    public Guid? ContactDetailId { get; set; }
    public PersonalContactDetailDbo? ContactDetail { get; set; }
    
    public PhysicalMeasurementsDbo? PhysicalMeasurements { get; set; } // Nullable
    public ICollection<InsuranceDbo> Insurances { get; set; } = new List<InsuranceDbo>();
    public ICollection<long> ClinicalConsiderations { get; set; } = new List<long>(); // e.g., allergies, conditions, etc.
    public ICollection<long> PreferredWeekdays { get; set; } = new List<long>(); // e.g., 1 for Monday, 2 for Tuesday, etc.
    public long? TechnicianPreference { get; set; } // e.g., preferred technician type
    
    // Guardian (One-to-One optional)
    public Guid? GuardianId { get; set; }
    public virtual PatientGuardianDbo? Guardian { get; set; }
    
    public virtual ICollection<OrderDbo> Orders { get; set; } = new List<OrderDbo>();
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<PatientDbo>(Register);

    public new static void Register(EntityTypeBuilder<PatientDbo> entity)
    {
        IAuditableEntity<PatientDbo>.Register(entity);
        
        // Configure Owned Types
        entity.OwnsOne(p => p.Demographics, builder =>
        {
            builder.ToTable($"{nameof(PatientDbo)}_{nameof(Demographics)}", IEntity.DefaultSchema);
            builder.WithOwner().HasForeignKey($"{nameof(PatientDbo)}Id");
            DemographicsDbo.Configure(builder);
        });
        
        entity.OwnsOne(p => p.PhysicalMeasurements, PhysicalMeasurementsDbo.Configure);
        
        entity.OwnsMany(p => p.Names, names =>
        {
            names.ToTable($"{nameof(PatientDbo)}_{nameof(Names)}", IEntity.DefaultSchema);
            names.WithOwner().HasForeignKey($"{nameof(PatientDbo)}Id");
            names.Property<int>("Id").ValueGeneratedOnAdd();
            names.HasKey("Id");
            PersonNameDbo.Configure(names);
        });
        
        entity.OwnsMany(p => p.Identifiers, identifiers =>
        {
            identifiers.ToTable($"{nameof(PatientDbo)}_{nameof(Identifiers)}", IEntity.DefaultSchema);
            identifiers.WithOwner().HasForeignKey($"{nameof(PatientDbo)}Id");
            identifiers.Property<int>("Id").ValueGeneratedOnAdd();
            identifiers.HasKey("Id");
            IdentifierDbo.Configure(identifiers);
        });

        entity.Property(p => p.ClinicalConsiderations);
        entity.Property(p => p.PreferredWeekdays);
        entity.Property(p => p.TechnicianPreference).IsRequired(false);
        
        entity.HasOne(p => p.Guardian)
            .WithOne()
            .HasForeignKey<PatientDbo>(p => p.GuardianId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.SetNull);
        
        entity.HasMany(p => p.Orders)
            .WithOne(o => o.Patient)
            .HasForeignKey(o => o.PatientId)
            .HasPrincipalKey(p => p.Id)
            .OnDelete(DeleteBehavior.Cascade);
        
        entity.HasMany(p => p.Insurances)
            .WithOne(p => p.Patient)
            .HasForeignKey(p => p.PatientId)
            .HasPrincipalKey(p => p.Id)
            .OnDelete(DeleteBehavior.Cascade);

        entity.HasOne(p => p.ContactDetail)
            .WithMany()
            .HasForeignKey(p => p.ContactDetailId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(PatientQ.WithId), "WithId")]
[JsonDerivedType(typeof(PatientQ.WithGivenName), "WithGivenName")]
[JsonDerivedType(typeof(PatientQ.WithLastName), "WithLastName")]
[JsonDerivedType(typeof(PatientQ.WithApproximateFullName), "WithApproximateFullName")]
[JsonDerivedType(typeof(PatientQ.WithDateOfBirth), "WithDateOfBirth")]
[JsonDerivedType(typeof(PatientQ.WithStudyLocation), "WithStudyLocation")] // Renamed from WithLocation
[JsonDerivedType(typeof(PatientQ.WithStudyDate), "WithStudyDate")]
[JsonDerivedType(typeof(PatientQ.WithIdentifier), "WithIdentifier")]
[JsonDerivedType(typeof(PatientQ.WithEmail), "WithEmail")]
[JsonDerivedType(typeof(PatientQ.WithPhoneNumber), "WithPhoneNumber")]
public abstract record PatientQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : PatientQ;
    
    public sealed record WithGivenName(string Value) : PatientQ; // Search any given name
    public sealed record WithLastName(string Value) : PatientQ;
    public sealed record WithApproximateFullName(string Value) : PatientQ; // e.g., "Jo D"
    
    public sealed record WithDateOfBirth(DateOnly Dob) : PatientQ;
    public sealed record WithStudyLocation(string Location) : PatientQ; // Searches *any* study location
    public sealed record WithStudyDate(DateOnly StudyDate) : PatientQ; // Searches *any* study date
    public sealed record WithIdentifier(string System, string Value) : PatientQ;
    public sealed record WithEmail(string Email) : PatientQ;
    public sealed record WithPhoneNumber(string PhoneNumber) : PatientQ; // Exact match?

    
    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // Predicate Building Logic
    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    private static Expression BuildWithIdPredicate(WithId withId, ParameterExpression patientParam)
    {
        // p => p.Id == withId.Id
        var idProperty = Expression.Property(patientParam, nameof(PatientDbo.Id));
        var idConstant = Expression.Constant(withId.Id);
        return Expression.Equal(idProperty, idConstant);
    }

    // private static Expression BuildWithDateOfBirthPredicate(WithDateOfBirth withDob, ParameterExpression patientParam)
    // {
    //     // p => p.DateOfBirth.HasValue && p.DateOfBirth.Value == withDob.Dob
    //     var dobProperty = Expression.Property(patientParam, nameof(PatientDbo.DateOfBirth));
    //     var dobConstant = Expression.Constant(withDob.Dob, typeof(DateOnly?)); // Ensure constant is nullable DateOnly
    //
    //     // p.DateOfBirth != null
    //     var hasValueCheck = Expression.NotEqual(dobProperty, Expression.Constant(null, typeof(DateOnly?)));
    //
    //     // p.DateOfBirth == dobConstant (value comparison for nullable)
    //     var equalsCheck = Expression.Equal(dobProperty, dobConstant);
    //
    //     return Expression.AndAlso(hasValueCheck, equalsCheck);
    // }
    
    private static Expression BuildWithDateOfBirthPredicate(WithDateOfBirth withDob, ParameterExpression patientParam)
    {
        // p => p.Demographics != null && p.Demographics.DateOfBirth.HasValue && p.Demographics.DateOfBirth.Value == withDob.Dob
        var demographicsProperty = Expression.Property(patientParam, nameof(PatientDbo.Demographics));
        var demographicsNullCheck = Expression.NotEqual(demographicsProperty, Expression.Constant(null, typeof(DemographicsDbo)));

        var dobProperty = Expression.Property(demographicsProperty, nameof(DemographicsDbo.DateOfBirth));
        var dobConstant = Expression.Constant(withDob.Dob, typeof(DateOnly?)); // Ensure constant is nullable DateOnly

        // p.Demographics.DateOfBirth != null
        var hasValueCheck = Expression.NotEqual(dobProperty, Expression.Constant(null, typeof(DateOnly?)));

        // p.Demographics.DateOfBirth == dobConstant (value comparison for nullable)
        var equalsCheck = Expression.Equal(dobProperty, dobConstant);

        return Expression.AndAlso(demographicsNullCheck, Expression.AndAlso(hasValueCheck, equalsCheck));
    }

    private static Expression BuildStudyRelatedPredicate(ParameterExpression patientParam, Expression<Func<StudyDbo, bool>> studyPredicate)
    {
        // p => p.Orders.Any(o => o.Studies.Any(studyPredicate))
        var orderParam = Expression.Parameter(typeof(OrderDbo), "o");
        var studiesProperty = Expression.Property(orderParam, nameof(OrderDbo.Studies));

        var anyStudyMethod = QueryExpressions.Enumerable_Any(typeof(StudyDbo));
        var anyStudyCall = Expression.Call(anyStudyMethod, studiesProperty, studyPredicate);
        var orderLambda = Expression.Lambda<Func<OrderDbo, bool>>(anyStudyCall, orderParam);

        var ordersProperty = Expression.Property(patientParam, nameof(PatientDbo.Orders));
        var anyOrderMethod = QueryExpressions.Enumerable_Any(typeof(OrderDbo));
        return Expression.Call(anyOrderMethod, ordersProperty, orderLambda);
    }


    private static Expression BuildWithStudyLocationPredicate(WithStudyLocation withLocation, ParameterExpression patientParam)
    {
        // s => s.Location.ToLower() == withLocation.Location.ToLower()
        var locationConstantLower = Expression.Constant(withLocation.Location.ToLower());
        var studyParam = Expression.Parameter(typeof(StudyDbo), "s");
        var locationProperty = Expression.Property(studyParam, nameof(StudyDbo.Location));

        // Handle potential null location property
        var locationLower = Expression.Call(locationProperty, QueryExpressions.ToLowerMethod);
        var equals = Expression.Equal(locationLower, locationConstantLower);
        var nullCheck = Expression.NotEqual(locationProperty, Expression.Constant(null, typeof(string)));
        var studyCondition = Expression.AndAlso(nullCheck, equals);

        var studyLambda = Expression.Lambda<Func<StudyDbo, bool>>(studyCondition, studyParam);

        return BuildStudyRelatedPredicate(patientParam, studyLambda);
    }

    private static Expression BuildWithStudyDatePredicate(WithStudyDate withStudyDate, ParameterExpression patientParam)
{
    // The goal is to build a predicate that translates to the following LINQ-like logic:
    // s => s.Appointments.AsQueryable() // <-- Note the AsQueryable()
    //       .Where(a => a.Date >= today)
    //       .OrderBy(a => a.Date)
    //       .Select(a => (DateOnly?)a.Date) // Cast to nullable to handle FirstOrDefault on empty sequence
    //       .FirstOrDefault() == withStudyDate.StudyDate
    //
    // This finds the earliest appointment for a given study that is on or after today,
    // and checks if it matches the requested date.

    var studyParam = Expression.Parameter(typeof(StudyDbo), "s");
    var appointmentParam = Expression.Parameter(typeof(PatientAppointmentDbo), "a");

    // s.Appointments
    var appointmentsProperty = Expression.Property(studyParam, nameof(StudyDbo.Appointments));

    // *********************************************************************************
    // FIX: Explicitly call AsQueryable() on the collection navigation property.
    // This converts the expression's type from ICollection<T> to IQueryable<T>,
    // allowing subsequent calls to Queryable methods (Where, OrderBy, etc.) to resolve correctly.
    var appointmentsAsQueryable = Expression.Call(
        typeof(Queryable),
        nameof(Queryable.AsQueryable),
        new[] { typeof(PatientAppointmentDbo) },
        appointmentsProperty
    );
    // *********************************************************************************

    // a.Date >= today
    var today = DateOnly.FromDateTime(DateTime.UtcNow);
    var todayConstant = Expression.Constant(today);
    var dateProperty = Expression.Property(appointmentParam, nameof(PatientAppointmentDbo.Date));
    var futureDateCheck = Expression.GreaterThanOrEqual(dateProperty, todayConstant);
    var whereLambda = Expression.Lambda<Func<PatientAppointmentDbo, bool>>(futureDateCheck, appointmentParam);

    // .Where(a => a.Date >= today)
    var whereCall = Expression.Call(
        typeof(Queryable),
        nameof(Queryable.Where),
        new[] { typeof(PatientAppointmentDbo) },
        appointmentsAsQueryable, // <-- Use the converted expression
        whereLambda
    );

    // a => a.Date (for OrderBy)
    var dateSelectorLambda = Expression.Lambda<Func<PatientAppointmentDbo, DateOnly>>(dateProperty, appointmentParam);

    // .OrderBy(a => a.Date)
    var orderByCall = Expression.Call(
        typeof(Queryable),
        nameof(Queryable.OrderBy),
        new[] { typeof(PatientAppointmentDbo), typeof(DateOnly) },
        whereCall,
        dateSelectorLambda
    );

    // a => (DateOnly?)a.Date (for Select)
    // We cast to nullable DateOnly so that FirstOrDefault returns null for empty sequences,
    // which prevents false positives with default(DateOnly).
    var nullableDateProperty = Expression.Convert(dateProperty, typeof(DateOnly?));
    var selectLambda = Expression.Lambda<Func<PatientAppointmentDbo, DateOnly?>>(nullableDateProperty, appointmentParam);

    // .Select(a => (DateOnly?)a.Date)
    var selectCall = Expression.Call(
        typeof(Queryable),
        nameof(Queryable.Select),
        new[] { typeof(PatientAppointmentDbo), typeof(DateOnly?) },
        orderByCall,
        selectLambda
    );

    // .FirstOrDefault()
    var firstOrDefaultCall = Expression.Call(
        typeof(Queryable),
        nameof(Queryable.FirstOrDefault),
        new[] { typeof(DateOnly?) },
        selectCall
    );

    // ... == withStudyDate.StudyDate
    var studyDateConstant = Expression.Constant((DateOnly?)withStudyDate.StudyDate, typeof(DateOnly?));
    var finalEqualityCheck = Expression.Equal(firstOrDefaultCall, studyDateConstant);

    // We now have the complete predicate for a single StudyEntity:
    // s => (complex logic...)
    var studyLambda = Expression.Lambda<Func<StudyDbo, bool>>(finalEqualityCheck, studyParam);

    // Finally, we wrap this in the existing helper that checks across Orders and Studies:
    // p => p.Orders.Any(o => o.Studies.Any(studyLambda))
    return BuildStudyRelatedPredicate(patientParam, studyLambda);
}

    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithGivenName          wfn  => QueryExpressions.BuildFirstNamePredicate<PatientDbo>(wfn.Value, self),
            WithLastName           wln  => QueryExpressions.BuildLastNamePredicate<PatientDbo>(wln.Value, self),
            WithApproximateFullName wan => QueryExpressions.BuildApproximateNamePredicate<PatientDbo>(wan.Value, self),
            
            WithId                 withId           => BuildWithIdPredicate(withId, self),
            WithDateOfBirth        withDob          => BuildWithDateOfBirthPredicate(withDob, self),
            WithStudyLocation      withLocation     => BuildWithStudyLocationPredicate(withLocation, self),
            WithStudyDate          withStudyDate    => BuildWithStudyDatePredicate(withStudyDate, self),
            WithIdentifier         withIdentifier   => QueryExpressions.HasIdentifierPredicate<PatientDbo>(self, withIdentifier.System, withIdentifier.Value),
            WithEmail              withEmail        => QueryExpressions.HasEmailPredicate<PatientDbo>(self, withEmail.Email),
            WithPhoneNumber        withPhone        => QueryExpressions.HasPhoneNumberPredicate<PatientDbo>(self, withPhone.PhoneNumber),
            // Add cases for other predicates if needed
            _ => throw new NotSupportedException($"Unsupported PatientQ literal type: {this.GetType().Name}")
        };
    }
    
    public static PatientQ HasId(Guid id) => new WithId(id);
    public static PatientQ HasFirstName(string name) => new WithGivenName(name);
    public static PatientQ HasLastName(string name) => new WithLastName(name);
    public static PatientQ HasApproximateFullName(string name) => new WithApproximateFullName(name);
    public static PatientQ HasDateOfBirth(DateOnly dob) => new WithDateOfBirth(dob);
    public static PatientQ HasStudyLocation(string location) => new WithStudyLocation(location);
    public static PatientQ HasStudyDate(DateOnly date) => new WithStudyDate(date);
    public static PatientQ HasIdentifier(string system, string value) => new WithIdentifier(system, value);
    public static PatientQ HasEmail(string email) => new WithEmail(email);
    public static PatientQ HasPhoneNumber(string phone) => new WithPhoneNumber(phone);
}