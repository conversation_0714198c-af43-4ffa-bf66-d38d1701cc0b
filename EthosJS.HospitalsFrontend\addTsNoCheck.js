const fs = require('fs');
const path = require('path');

/**
 * Prepends the //@ts-nocheck comment to the file if it's not already present.
 * @param {string} filePath - The path to the file to update.
 */
function addTsNoCheck(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    // Check if the file already starts with //@ts-nocheck (ignoring leading whitespace)
    if (!content.trimStart().startsWith('//@ts-nocheck')) {
      const newContent = '//@ts-nocheck\n' + content;
      fs.writeFileSync(filePath, newContent, 'utf8');
      console.log(`Updated: ${filePath}`);
    } else {
      console.log(`Already updated: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error);
  }
}

/**
 * Recursively traverses the given directory and processes all .tsx files.
 * @param {string} dir - The directory to traverse.
 */
function traverseDirectory(dir) {
  // Read directory contents
  fs.readdirSync(dir, { withFileTypes: true }).forEach((dirent) => {
    const fullPath = path.join(dir, dirent.name);
    if (dirent.isDirectory()) {
      // Recursively traverse subdirectories
      traverseDirectory(fullPath);
    } else if (dirent.isFile() && fullPath.endsWith('.tsx')) {
      addTsNoCheck(fullPath);
    } else if (dirent.isFile() && fullPath.endsWith('.ts')) {
      addTsNoCheck(fullPath);
    }
  });
}

// Define the src directory relative to this script file
const srcDir = path.join(__dirname, 'src');

if (fs.existsSync(srcDir)) {
  traverseDirectory(srcDir);
  console.log('Finished processing .tsx files in all subfolders.');
} else {
  console.error(`The directory ${srcDir} does not exist.`);
}
