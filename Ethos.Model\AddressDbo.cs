﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class AddressDbo : IOwnedEntity<AddressDbo>
{
    public required string Line1 { get; set; }
    public required string? Line2 { get; set; }
    public required long CountryId { get; set; }
    public required long? StateId { get; set; }
    public required string City { get; set; }
    public required string PostalCode { get; set; }
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<AddressDbo>(Register);

    public new static void Register(EntityTypeBuilder<AddressDbo> entity)
    {
        IOwnedEntity<AddressDbo>.Register(entity);
        
        entity.Property(a => a.Line1).HasMaxLength(200).IsRequired();
        entity.Property(a => a.Line2).HasMaxLength(200);
        entity.Property(a => a.City).HasMaxLength(200).IsRequired();
        entity.Property(a => a.PostalCode).HasMaxLength(10).IsRequired();
    }
}