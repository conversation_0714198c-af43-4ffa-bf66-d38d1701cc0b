import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, IsPositive } from 'class-validator';
import { Transform } from 'class-transformer';
import { BaseFiltersDto } from '@app/common/dto/base.filters.dto';
import { EBedScheduleSort } from '@app/modules/bedSchedule/enums';
import { EShift } from '@app/common/enums';
import { IsDateOnly, IsIntegerList } from '@app/common/decorators/validators.decorator';

export class BedScheduleFiltersDto extends BaseFiltersDto {
  @ApiPropertyOptional({
    isArray: false,
    type: 'string',
    description: 'List of integers concatenated through ,',
    example: '1,2,3',
  })
  @IsOptional()
  @IsIntegerList()
  @Transform((value) => value?.split(',').map((item: string) => Number(item)))
  facilityIds?: number[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  @IsPositive()
  clinicId?: number;

  @ApiPropertyOptional({ enum: EShift })
  @IsOptional()
  @IsEnum(EShift)
  shift?: EShift;

  @ApiPropertyOptional({ format: 'date-time', example: '2020-01-01' })
  @IsOptional()
  @IsDateOnly()
  dateFrom?: string;

  @ApiPropertyOptional({ format: 'date-time', example: '2024-01-01' })
  @IsOptional()
  @IsDateOnly()
  dateTo?: string;

  @ApiPropertyOptional({ enum: EBedScheduleSort })
  @IsOptional()
  @IsEnum(EBedScheduleSort)
  orderField?: EBedScheduleSort = EBedScheduleSort.CreatedAt;
}
