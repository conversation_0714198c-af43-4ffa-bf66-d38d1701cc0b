using System.Text.Json;
using Ethos.Auth;
using Ethos.Events.Client;
using Ethos.Utilities;
using Ethos.Utilities.Filtering;
using Ethos.Utilities.Pagination;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.PlatformManager.Controllers
{
    [ApiController]
    [Route("api/users")]
    [Authorize]
    [EthosAuthFeature(Name = "Core")]
    public class UserController : ControllerBase
    {
        readonly ILogger<UserController> logger;
        readonly IServiceScopeFactory scopeFactory;
        readonly PlatformManagerDbContext dbContext;
        readonly IConfiguration configuration;
        readonly IEthosEventClient eventClient;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="scopeFactory"></param>
        public UserController(ILogger<UserController> logger,
                              IServiceScopeFactory scopeFactory,
                              PlatformManagerDbContext dbContext,
                              IConfiguration configuration,
                              IEthosEventClient eventClient)
        {
            this.logger = logger;
            this.scopeFactory = scopeFactory;
            this.dbContext = dbContext;
            this.configuration = configuration;
            this.eventClient = eventClient;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("scim")]
        [EthosAuthScope(ScopeDefinitions.UserWrite)]
        public async Task<IActionResult> CreateUser(ScimUser user)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(CreateUser), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var tenantId = this.GetRequiredTenantId();

                var ethosUser = new EthosUserDto()
                {
                    DisplayName = user.DisplayName ?? string.Empty,
                    StartDate = DateTimeOffset.UtcNow.Date,
                    EndDate = DateTimeOffset.MaxValue,
                    Active = user.Active ?? true,
                    Title = user.Title,
                    UserType = user.UserType,
                    Name = new EthosUserNameDto()
                    {
                        GivenName = user.Name?.GivenName,
                        Surname = user.Name?.FamilyName,
                        Prefix = user.Name?.HonorificPrefix,
                        Suffix = user.Name?.HonorificSuffix,
                        MiddleName = user.Name?.MiddleName,
                        Nickname = user.NickName,
                    },
                    Id = Guid.TryParse(user.Id, out var id) ? id : Guid.TryParse(user.ExternalId, out var extId) ? extId : Guid.NewGuid(),
                };
                return await CreateUser(ethosUser);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [EthosAuthScope(ScopeDefinitions.UserWrite)]
        public async Task<IActionResult> CreateUser(EthosUserDto user)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(CreateUser), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var isUserAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.UserAdmin);
                var tenantId = this.GetRequiredTenantId();

                if (string.IsNullOrEmpty(user.DisplayName))
                    return this.EthosErrorBadRequest("User display name is required.");

                if (!user.Id.HasValue || user.Id.Value == Guid.Empty)
                    user.Id = Guid.NewGuid();

                if (user.Name is null || string.IsNullOrEmpty(user.Name.Surname) || string.IsNullOrEmpty(user.Name.GivenName))
                    return this.EthosErrorBadRequest("User given name and surname are required.");

                var ethosUser = dbContext.Users.FirstOrDefault(r => r.TenantId == tenantId && r.Id == user.Id.Value);

                var isUpdate = ethosUser is not null;

                if (ethosUser is not null && !ethosUser.Deleted)
                    return this.EthosErrorConflict($"User already exists with ID {user.Id.Value}.");

                ethosUser ??= new EthosUser();
                ethosUser.DisplayName = user.DisplayName;
                ethosUser.Id = user.Id.Value;
                ethosUser.TenantId = tenantId;
                ethosUser.Active = user.Active;
                ethosUser.Deleted = false;
                ethosUser.Department = user.Department;
                ethosUser.Title = user.Title;
                ethosUser.UserType = user.UserType;
                ethosUser.Surname = user.Name.Surname;
                ethosUser.GivenName = user.Name.Surname;
                ethosUser.MiddleName = user.Name.MiddleName;
                ethosUser.NamePrefix = user.Name.Prefix;
                ethosUser.NameSuffix = user.Name.Suffix;
                ethosUser.Nickname = user.Name.Nickname;

                using var trans = dbContext.Database.BeginTransaction();
                try
                {
                    if (isUpdate)
                        dbContext.Users.Update(ethosUser);
                    else
                        dbContext.Users.Add(ethosUser);

                    await dbContext.SaveChangesAsync();
                    trans.Commit();

                    return CreatedAtAction(nameof(GetUser), new { userId = ethosUser.Id }, GetUserDto(ethosUser));
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error creating new user.");
                    trans.Rollback();
                    await eventClient.CreateExceptionEvent(ex);
                    return Problem("Unexpected server error while creating user.", null, 500);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{userId}")]
        [EthosAuthScope(ScopeDefinitions.UserWrite)]
        public async Task<IActionResult> UpdateUser([FromRoute] Guid userId, [FromBody] EthosUserDto user)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(CreateUser), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (userId == Guid.Empty)
                    return this.EthosErrorBadRequest("Invalid user ID.");

                var isUserAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.UserAdmin);
                var tenantId = this.GetRequiredTenantId();

                if (string.IsNullOrEmpty(user.DisplayName))
                    return this.EthosErrorBadRequest("User display name is required.");

                if (user.Id.HasValue && user.Id.Value != Guid.Empty && user.Id.Value != userId)
                    return this.EthosErrorBadRequest($"User ID {userId} does not match entity user ID {user.Id.Value}.");

                user.Id = userId;

                if (user.Name is null || string.IsNullOrEmpty(user.Name.Surname) || string.IsNullOrEmpty(user.Name.GivenName))
                    return this.EthosErrorBadRequest("User given name and surname are required.");

                var ethosUser = dbContext.Users.FirstOrDefault(r => r.TenantId == tenantId && r.Id == user.Id.Value);

                if (ethosUser is null || ethosUser!.Deleted)
                    return this.EthosErrorConflict($"User with ID {userId} does not exist.");

                ethosUser.DisplayName = user.DisplayName;
                ethosUser.TenantId = tenantId;
                ethosUser.Active = user.Active;
                ethosUser.Deleted = false;
                ethosUser.Department = user.Department;
                ethosUser.Title = user.Title;
                ethosUser.UserType = user.UserType;
                ethosUser.Surname = user.Name.Surname;
                ethosUser.GivenName = user.Name.Surname;
                ethosUser.MiddleName = user.Name.MiddleName;
                ethosUser.NamePrefix = user.Name.Prefix;
                ethosUser.NameSuffix = user.Name.Suffix;
                ethosUser.Nickname = user.Name.Nickname;

                using var trans = dbContext.Database.BeginTransaction();
                try
                {
                    dbContext.Users.Update(ethosUser);
                    await dbContext.SaveChangesAsync();
                    trans.Commit();

                    return Ok(GetUserDto(ethosUser));
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error creating new user.");
                    trans.Rollback();
                    await eventClient.CreateExceptionEvent(ex);
                    return Problem("Unexpected server error while creating user.", null, 500);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="u"></param>
        /// <returns></returns>
        EthosUserDto GetUserDto(EthosUser u)
        {
            return new EthosUserDto()
            {
                Id = u.Id,
                Title = u.Title,
                Department = u.Department,
                UserType = u.UserType,
                Active = u.Active,
                DisplayName = u.DisplayName,
                Name = new EthosUserNameDto()
                {
                    GivenName = u.GivenName,
                    MiddleName = u.MiddleName,
                    Nickname = u.Nickname,
                    Prefix = u.NamePrefix,
                    Suffix = u.NameSuffix,
                    Surname = u.Surname,
                }
            };
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        [Route("{userId}")]
        [EthosAuthScope(ScopeDefinitions.UserDelete)]
        public async Task<IActionResult> DeleteUser([FromRoute] Guid userId)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(DeleteUser), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (userId == Guid.Empty)
                    return this.EthosErrorBadRequest("Invalid user ID.");

                var tenantId = this.GetRequiredTenantId();

                var delUser = dbContext.Users.FirstOrDefault(r => r.Id == userId && r.TenantId == tenantId && !r.Deleted);

                if (delUser is null)
                    return this.EthosErrorNotFound($"No such user with ID {userId}.");

                delUser.Deleted = true;
                delUser.Active = false;
                dbContext.Users.Update(delUser);
                await dbContext.SaveChangesAsync();
                return NoContent();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.UserRead)]
        public async Task<ActionResult<PagedResponse<EthosRoleDto>>> GetUsers([FromQuery] PagingParameters pagingParameters, [FromQuery] string? filter = null)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetUsers), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var tenantId = this.GetRequiredTenantId();

                var userReadAll = HttpContext.User.IsAllowedScope(ScopeDefinitions.UserReadAll);

                var users = dbContext.Users
                                     .Where(r => r.TenantId == tenantId && (r.Id == HttpContext.User.GetRequiredUniqueId() || userReadAll))
                                     .Filter(filter)
                                     .OrderBy(r => r.Id);

                return Ok(await users.PaginateWithLinksAsync(this, (users) =>
                {
                    var _users = new List<EthosUserDto>();
                    _users.AddRange(users.Select(r => GetUserDto(r)));
                    return _users;
                }, pagingParameters.limit, pagingParameters.offset));
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("{userId}")]
        [EthosAuthScope(ScopeDefinitions.UserRead)]
        public async Task<IActionResult> GetUser([FromRoute] Guid userId)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetUser), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var tenantId = this.GetRequiredTenantId();
                if (userId != HttpContext.User.GetRequiredUniqueId() && !HttpContext.User.IsAllowedScope(ScopeDefinitions.UserReadAll))
                    return this.EthosErrorForbidden("Current user cannot view other user details.");

                var user = await dbContext.Users.FirstOrDefaultAsync(r => r.TenantId == tenantId && r.Id == userId);

                if (user is null)
                    return this.EthosErrorNotFound($"No such user with ID {userId}.");

                return Ok(GetUserDto(user));
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("me")]
        [EthosAuthScope(ScopeDefinitions.UserRead)]
        public async Task<IActionResult> GetMe()
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetMe), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var userId = HttpContext.User.GetRequiredUniqueId();
                return await GetUser(userId);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("me")]
        [EthosAuthScope(ScopeDefinitions.UserRead)]
        public async Task<IActionResult> UpdateMe([FromBody] EthosUserDto user)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(UpdateMe), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var userId = HttpContext.User.GetRequiredUniqueId();
                user.Id = userId;
                return await UpdateUser(userId, user);
            }
        }
    }
}
