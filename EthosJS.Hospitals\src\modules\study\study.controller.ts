import { AuthGuard } from '@app/common/guards/auth.guard';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiB<PERSON>erAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { StudyService } from '@app/modules/study/study.service';
import { StudyCollectionDto } from '@app/modules/study/dto/study.collection.dto';
import { StudyFiltersDto } from '@app/modules/study/dto/study.filters.dto';
import { CreateStudyDto } from '@app/modules/study/dto/create.study.dto';
import { UpdateStudyDto } from '@app/modules/study/dto/update.study.dto';
import { DeleteStudyDto } from '@app/modules/study/dto/delete.study.dto';
import { StudyDto } from '@app/modules/study/dto/study.dto';

@Controller('study')
@ApiTags('Studies')
// @UseGuards(AuthGuard)
@ApiBearerAuth()
export class StudyController {
  constructor(private readonly service: StudyService) {}

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: StudyCollectionDto,
    description: 'Get list of studies',
  })
  async list(@Query() filters: StudyFiltersDto): Promise<StudyCollectionDto> {
    const { data, count } = await this.service.list(filters);

    return {
      data: data.map((item) => StudyService.mapToDto(item, [])),
      count,
    };
  }

  @Get('/:studyId')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: StudyDto,
    description: 'Get study by id',
  })
  async getById(@Param('studyId', new ParseIntPipe()) studyId: number): Promise<StudyDto> {
    const [study, credentials] = await this.service.getByIdOrFail(studyId);

    return StudyService.mapToDto(study, credentials);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOkResponse({
    type: StudyDto,
    description: 'Create study',
  })
  async create(@Body() create: CreateStudyDto): Promise<StudyDto> {
    const [study, credentials] = await this.service.create(create);

    return StudyService.mapToDto(study, credentials);
  }

  @Put()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: StudyDto,
    description: 'Update study',
  })
  async update(@Body() update: UpdateStudyDto): Promise<StudyDto> {
    const [study, credentials] = await this.service.update(update);

    return StudyService.mapToDto(study, credentials);
  }

  @Delete()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: StudyDto,
    description: 'Delete study',
  })
  async delete(@Body() { id }: DeleteStudyDto): Promise<StudyDto> {
    const [study, credentials] = await this.service.delete(id);

    return StudyService.mapToDto(study, credentials);
  }
}
