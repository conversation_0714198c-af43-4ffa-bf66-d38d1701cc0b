using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

// Represents Email contact information for a Patient
public class PersonalEmailDbo : IAuditableEntity<PersonalEmailDbo>
{
    public Guid ParentId { get; set; }
    public virtual PersonalContactDetailDbo Parent { get; set; } = null!;

    public string Email { get; set; } = null!;
    public long Use { get; set; }
    public bool IsPreferred { get; set; }

    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<PersonalEmailDbo>(Register);
    public new static void Register(EntityTypeBuilder<PersonalEmailDbo> entity)
    {
        IAuditableEntity<PersonalEmailDbo>.Register(entity);

        entity.Property(e => e.Email).IsRequired().HasMaxLength(254);
        entity.Property(e => e.Use).IsRequired();
        entity.HasIndex(e => new { e.ParentId, e.Email }).IsUnique(); // Ensure unique email per patient

        entity.HasOne(e => e.Parent)
            .WithMany(p => p.Emails)
            .HasForeignKey(e => e.ParentId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}