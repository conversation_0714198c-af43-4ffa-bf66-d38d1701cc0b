import { AuthGuard } from '@app/common/guards/auth.guard';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { EquipmentService } from '@app/modules/equipment/equipment.service';
import { EquipmentCollectionDto } from '@app/modules/equipment/dto/equipment.collection.dto';
import { EquipmentFiltersDto } from '@app/modules/equipment/dto/equipment.filters.dto';
import { CreateEquipmentDto } from '@app/modules/equipment/dto/create.equipment.dto';
import { UpdateEquipmentDto } from '@app/modules/equipment/dto/update.equipment.dto';
import { EquipmentEntity } from '@app/modules/equipment/equipment.entity';
import { DeleteEquipmentDto } from '@app/modules/equipment/dto/delete.equipment.dto';

@Controller('equipment')
@ApiTags('Equipments')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class EquipmentController {
  constructor(private readonly service: EquipmentService) {
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: EquipmentCollectionDto,
    description: 'Get list of equipments',
  })
  async list(@Query() filters: EquipmentFiltersDto): Promise<EquipmentCollectionDto> {
    return this.service.list(filters);
  }

  @Get('/:equipmentId')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: EquipmentEntity,
    description: 'Get equipment by id',
  })
  async getById(@Param('equipmentId', new ParseIntPipe()) equipmentId: number): Promise<EquipmentEntity> {
    return this.service.getByIdOrFail(equipmentId);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOkResponse({
    type: EquipmentEntity,
    description: 'Create equipment',
  })
  async create(@Body() equipment: CreateEquipmentDto): Promise<EquipmentEntity> {
    return this.service.create(equipment);
  }

  @Put()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: EquipmentEntity,
    description: 'Update equipment',
  })
  async update(@Body() update: UpdateEquipmentDto): Promise<EquipmentEntity> {
    return this.service.update(update);
  }

  @Delete()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: EquipmentEntity,
    description: 'Delete equipment',
  })
  async delete(@Body() { id }: DeleteEquipmentDto): Promise<EquipmentEntity> {
    return this.service.delete(id);
  }
}
