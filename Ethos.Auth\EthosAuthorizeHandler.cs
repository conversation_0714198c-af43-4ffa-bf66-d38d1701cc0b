using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Ethos.Auth;

public class EthosAuthorizeHandler(
    IServiceProvider serviceProvider, 
    ILogger<EthosAuthorizeHandler> logger, 
    IHttpContextAccessor httpContextAccessor) : AuthorizationHandler<EthosAuthorize>
{
    const string products = nameof(products);

    protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, EthosAuthorize requirement)
    {
        var user = httpContextAccessor.HttpContext?.User;
        if (user is null)
        {
            context.Fail();
            return Task.CompletedTask;
        }

        var app = requirement.App;
        var module = requirement.Module;
        var feature = requirement.Feature;
        var scope = requirement.Scope;

        if (app is not null && !user.HasClaim("app", app))
        {
            logger.LogWarning("User does not have required app claim");
            context.Fail();
            return Task.CompletedTask;
        }

        if (module is not null && !user.HasClaim("module", module))
        {
            logger.LogWarning("User does not have required module claim");
            context.Fail();
            return Task.CompletedTask;
        }

        if (feature is not null && !user.HasClaim("feature", feature))
        {
            logger.LogWarning("User does not have required feature claim");
            context.Fail();
            return Task.CompletedTask;
        }

        if (scope is not null && !user.HasClaim("scope", scope))
        {
            logger.LogWarning("User does not have required scope claim");
            context.Fail();
            return Task.CompletedTask;
        }

        context.Succeed(requirement);
        return Task.CompletedTask;
    }
}