﻿using System;
using System.CodeDom.Compiler;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;

namespace EnsoLib
{
    [GeneratedCode("NSwag", "13.18.2.0 (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial interface IGenerateClient
    {

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Generate a link that allows external users with the link to access a resource.
        /// </summary>
        /// <remarks>
        /// Generates an expiring signed url that contains information required
        /// <br/>to access a resource within EnsoSleep,  available at an unprotected route.
        /// <br/>The link expiration defaults to 90 minutes, but is configurable.
        /// </remarks>
        /// <param name="expiring_link_type">Type of resource the expiring link grants access to</param>
        /// <returns>Successful response</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        Task<GenerateLinkResponseSchema> LinkAsync(Expiring_link_type expiring_link_type, ExpiringLinkStudy body, CancellationToken cancellationToken = default(CancellationToken));

    }

    [GeneratedCode("NSwag", "13.18.2.0 (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial interface IClient
    {

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Create an access token
        /// </summary>
        /// <remarks>
        /// Create an access token by including a refresh token in the `Authorization` header as `Bearer &lt;refresh_token&gt;`.
        /// </remarks>
        /// <param name="authorization">Refresh token in the format `Bearer &lt;refresh_token&gt;`.</param>
        /// <returns>Successful response</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        Task<CreateAccessTokenResponseSchema> RefreshAsync(string authorization, CancellationToken cancellationToken = default(CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of studies.
        /// </summary>
        /// <remarks>
        /// Studies can be queried by their `created`, `state`, and `status` fields. Results are paginated and ordered by creation date in descending order.
        /// </remarks>
        /// <param name="id__in">Retrieve studies in bulk by `id`.</param>
        /// <param name="created__gte">Retrieve studies created after or on the specified ISO 8601 datetime (UTC).</param>
        /// <param name="created__gt">Retrieve studies created strictly after the specified ISO 8601 datetime (UTC).</param>
        /// <param name="created__lte">Retrieve studies created before or on the specified ISO 8601 datetime (UTC).</param>
        /// <param name="created__lt">Retrieve studies created strictly before the specified ISO 8601 datetime (UTC).</param>
        /// <param name="pid">Retrieve studies by `pid`.</param>
        /// <param name="pid__in">Retrieve studies in bulk by `pid`.</param>
        /// <param name="state">Limit results to studies in the specified state.</param>
        /// <param name="state__in">Limit results to studies in the specified states.</param>
        /// <param name="status">Limit results to studies in the specified status.</param>
        /// <param name="status__in">Limit results to studies in the specified statuses.</param>
        /// <param name="limit">Limit the number of results returned.</param>
        /// <param name="skip">A cursor to use for pagination.  The (zero-based) offset of the first item in the collection to return.</param>
        /// <returns>Successful response</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        Task<PaginatedStudyResponseSchema> StudyGETAsync(IEnumerable<string>? id__in = null, DateTimeOffset? created__gte = null, DateTimeOffset? created__gt = null, DateTimeOffset? created__lte = null, DateTimeOffset? created__lt = null, string? pid = null, IEnumerable<string>? pid__in = null, State? state = null, IEnumerable<State>? state__in = null, Status? status = null, IEnumerable<Anonymous2>? status__in = null, int? limit = null, int? skip = null, CancellationToken cancellationToken = default(CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Create a study.
        /// </summary>
        /// <remarks>
        /// Most service accounts are configured to provide default values for `hardware`, `software`, and `desaturation` when you create a study, so only provide these values if instructed to or you need to override the defaults.
        /// </remarks>
        /// <returns>Successful response</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        Task<StudyResponseSchema> StudyPOSTAsync(CreateStudy body, CancellationToken cancellationToken = default(CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a study by ID.
        /// </summary>
        /// <param name="study_id">The unique ID of the study.</param>
        /// <returns>Successful response</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        Task<StudyResponseSchema> StudyGET2Async(string study_id, CancellationToken cancellationToken = default(CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Update a study by ID.
        /// </summary>
        /// <param name="study_id">The unique ID of the study.</param>
        /// <returns>Successful response</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        Task<StudyResponseSchema> StudyPATCHAsync(string study_id, UpdateStudy body, CancellationToken cancellationToken = default(CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of study files.
        /// </summary>
        /// <remarks>
        /// Retrieves study file metadata and a URL to download the study. Note that, even if the `content_encoding` of a file is `gzip`, Google Cloud Storage will automatically &lt;a href="https://cloud.google.com/storage/docs/transcoding"&gt;decompress the file&lt;/a&gt; for you upon download.
        /// </remarks>
        /// <param name="study_id">The unique ID of the study.</param>
        /// <param name="version">The version of the study. New studies should upload to the `CURRENT` version and grab EnsoSleep scoring from the `SCORED` version. A backup of the study is made to `ORIGINAL`.</param>
        /// <returns>Successful response</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        Task<ListRetrieveFileResponseSchema> FileGETAsync(string study_id, Version version, CancellationToken cancellationToken = default(CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Create a study file.
        /// </summary>
        /// <remarks>
        /// Creating a study file will return an `upload_url`. You can upload the file to this URL by following the directions from &lt;a href="https://cloud.google.com/storage/docs/performing-resumable-uploads#upload-file"&gt;Google Cloud Storage - Performing Resumable Uploads&lt;/a&gt;. For large files or slower networks, we recommend the Multiple Chunk Upload method.  If you receive a timeout error, you need to reduce your chunk size to a smaller value that your network can tolerate.
        /// </remarks>
        /// <param name="study_id">The unique ID of the study.</param>
        /// <param name="version">The version of the study. New studies should upload to the `CURRENT` version and grab EnsoSleep scoring from the `SCORED` version. A backup of the study is made to `ORIGINAL`.</param>
        /// <returns>Successful response</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        Task<CreateFileResponseSchema> FilePOSTAsync(string study_id, Version version, CreateFile body, CancellationToken cancellationToken = default(CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a study file by name.
        /// </summary>
        /// <remarks>
        /// Retrieves study file metadata and a URL to download the study. Note that, even if the `content_encoding` of a file is `gzip`, Google Cloud Storage will automatically &lt;a href="https://cloud.google.com/storage/docs/transcoding"&gt;decompress the file&lt;/a&gt; for you upon download.
        /// </remarks>
        /// <param name="study_id">The unique ID of the study.</param>
        /// <param name="version">The version of the study. New studies should upload to the `CURRENT` version and grab EnsoSleep scoring from the `SCORED` version. A backup of the study is made to `ORIGINAL`.</param>
        /// <param name="filename">The name of the file.</param>
        /// <returns>Successful response</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        Task<RetrieveFileResponseSchema> FileGET2Async(string study_id, Version version, string filename, CancellationToken cancellationToken = default(CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of score sets.
        /// </summary>
        /// <remarks>
        /// Score sets can be queried by their `patient_id` and `name`. Results are paginated. To retreive EnsoSleep scoring, use the querystring `?patient_id=&lt;patient_id&gt;&amp;name=prepared`.
        /// </remarks>
        /// <param name="name">The name of the score set.</param>
        /// <param name="patient_id">The ID the study.</param>
        /// <returns>Successful response</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        Task<PaginatedScoreSetResponseSchema> ScoresetAsync(string? name = null, IEnumerable<string>? id__in = null, string? patient_id = null, CancellationToken cancellationToken = default(CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a score set by ID.
        /// </summary>
        /// <param name="scoreset_id">The unique ID of the score set.</param>
        /// <returns>Successful response</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        Task<ScoreSetResponseSchema> Scoreset2Async(string scoreset_id, CancellationToken cancellationToken = default(CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of events for a given score set.
        /// </summary>
        /// <remarks>
        /// Use the querystring `?group_by=name` to group events by name for easier parsing.
        /// </remarks>
        /// <param name="group_by">Grouping events will return them as `{"hypopnea": [&lt;event_1&gt;, &lt;event_2&gt;], "desat": ...}`</param>
        /// <returns>Successful response</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        Task<ListScoreEventResponseSchema> EventAsync(Group_by? group_by = null, CancellationToken cancellationToken = default(CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a report by ID.
        /// </summary>
        /// <param name="report_id">The unique ID of the report.</param>
        /// <returns>Successful response</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        Task<ReportResponseSchema> ReportGETAsync(string report_id, CancellationToken cancellationToken = default(CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Update a report's notes.
        /// </summary>
        /// <param name="report_id">The unique ID of the report.</param>
        /// <returns>Successful response</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        Task<ReportResponseSchema> ReportPATCHAsync(string report_id, UpdateReport body, CancellationToken cancellationToken = default(CancellationToken));

    }

}
