import { EntityRepository } from 'typeorm';
import { BadRequestException } from '@nestjs/common';
import { IListResult } from '@app/common/types';
import { BaseRepository } from '@app/common/base.repository';
import { CredentialEntity } from '@app/modules/credential/credential.entity';
import { CredentialCollectionDto } from '@app/modules/credential/dto/credential.collection.dto';
import { CredentialFiltersDto } from '@app/modules/credential/dto/credential.filters.dto';

@EntityRepository(CredentialEntity)
export class CredentialRepository extends BaseRepository<CredentialEntity> {
  collectionDto = CredentialCollectionDto;

  async list(filters: CredentialFiltersDto): Promise<IListResult<CredentialEntity>> {
    return super.list(filters);
  }

  async checkHasRelatedEntities(credentialId: number): Promise<void> {
    const [{ count: studiesCount }] = await this.query('SELECT COUNT(*) as count FROM study_credentials WHERE $1 = ANY (SELECT jsonb_array_elements_text(credentials)::text) AND deleted_at IS NULL', [credentialId]);

    if (Number(studiesCount) > 0) {
      throw new BadRequestException('Credential has related studies');
    }

    const [{ count: techniciansCount }] = await this.query('SELECT COUNT(*) as count FROM technician_credentials WHERE credential_id = $1 AND deleted_at IS NULL', [credentialId]);

    if (Number(techniciansCount) > 0) {
      throw new BadRequestException('Credential has related technicians');
    }
  }
}
