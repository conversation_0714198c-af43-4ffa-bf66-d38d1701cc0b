import { AuthGuard } from '@app/common/guards/auth.guard';
import {
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { StateService } from '@app/modules/state/state.service';
import { StateFiltersDto } from '@app/modules/state/dto/state.filters.dto';
import { StateCollectionDto } from '@app/modules/state/dto/state.collection.dto';
import { StateEntity } from '@app/modules/state/state.entity';

@Controller('state')
@ApiTags('States')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class StateController {
  constructor(private readonly service: StateService) {
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: StateCollectionDto,
    description: 'Get list of states',
  })
  async list(@Query() filters: StateFiltersDto): Promise<StateCollectionDto> {
    return this.service.list(filters);
  }

  @Get('/:stateId')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: StateEntity,
    description: 'Get state by id',
  })
  async getById(@Param('stateId', new ParseIntPipe()) stateId: number): Promise<StateEntity> {
    return this.service.getByIdOrFail(stateId);
  }
}
