﻿using Newtonsoft.Json;
using System;
using System.CodeDom.Compiler;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EnsoLib
{


    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GenerateLink : EnsoModelBase
    {
        /// <summary>
        /// Signed Expiring URL that grants an external user access to a study.
        /// </summary>
        [JsonProperty("url", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Url { get; set; } = default!;

        public static GenerateLinkResponseSchema?  FromJson(string data)
            => EnsoModelBase.FromJson<GenerateLinkResponseSchema>(data);


    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GenerateLinkResponseSchema : EnsoModelBase
    {
        [JsonProperty("message", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Message { get; set; } = default!;

        [JsonProperty("data", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public GenerateLink? Data { get; set; } = default!;

        public static GenerateLinkResponseSchema?  FromJson(string data)
            => EnsoModelBase.FromJson<GenerateLinkResponseSchema>(data);

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CreateAccessToken : EnsoModelBase
    {
        /// <summary>
        /// An access token that can be used to access protected endpoints.
        /// </summary>
        [JsonProperty("access_token", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Access_token { get; set; } = default!;

        public static CreateAccessToken?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<CreateAccessToken>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CreateAccessTokenResponseSchema : EnsoModelBase
    {
        [JsonProperty("message", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Message { get; set; } = default!;

        [JsonProperty("data", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public CreateAccessToken? Data { get; set; } = default!;

        public static CreateAccessTokenResponseSchema?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<CreateAccessTokenResponseSchema>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Export : EnsoModelBase
    {
        /// <summary>
        /// Scoreset ID
        /// </summary>
        [JsonProperty("scoreset_id", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Scoreset_id { get; set; } = default!;

        /// <summary>
        /// Report ID
        /// </summary>
        [JsonProperty("report_id", Required = Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Report_id { get; set; } = default!;

        /// <summary>
        /// Patient ID
        /// </summary>
        [JsonProperty("patient_id", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Patient_id { get; set; } = default!;

        /// <summary>
        /// Return as HTML instead of PDF
        /// </summary>
        [JsonProperty("export_html", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public bool? Export_html { get; set; } = default!;

        /// <summary>
        /// Used to override the header/footer report text
        /// </summary>
        [JsonProperty("report_name", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Report_name { get; set; } = default!;

        /// <summary>
        /// Template ID
        /// </summary>
        [JsonProperty("template_id", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Template_id { get; set; } = default!;
        public static Export?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<Export>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ExportSchema : EnsoModelBase
    {
        [JsonProperty("message", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Message { get; set; } = default!;

        [JsonProperty("data", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Export? Data { get; set; } = default!;



        public static ExportSchema?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<ExportSchema>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Event : EnsoModelBase
    {
        [JsonProperty("count", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Count { get; set; } = default!;

        [JsonProperty("total_duration", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Total_duration { get; set; } = default!;

        [JsonProperty("median_duration", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Median_duration { get; set; } = default!;

        [JsonProperty("percent", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public double? Percent { get; set; } = default!;

        [JsonProperty("min_duration", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Min_duration { get; set; } = default!;

        [JsonProperty("max_duration", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Max_duration { get; set; } = default!;

        [JsonProperty("mean_duration", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Mean_duration { get; set; } = default!;

        [JsonProperty("index", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public double? Index { get; set; } = default!;

        public static Event?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<Event>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SimpleEvent : EnsoModelBase
    {
        [JsonProperty("count", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Count { get; set; } = default!;

        [JsonProperty("index", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public double? Index { get; set; } = default!;

        public static SimpleEvent?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<SimpleEvent>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Associations : EnsoModelBase
    {
        [JsonProperty("arousal", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public IDictionary<string, SimpleEvent>? Arousal { get; set; } = default!;
        
        public static Associations?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<Associations>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Axis : EnsoModelBase
    {
        [JsonProperty("events", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public IDictionary<string, Event>? Events { get; set; } = default!;

        [JsonProperty("percent", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public double? Percent { get; set; } = default!;

        [JsonProperty("sleep_time", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Sleep_time { get; set; } = default!;

        [JsonProperty("seconds", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Seconds { get; set; } = default!;

        [JsonProperty("associations", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Associations? Associations { get; set; } = default!;

        public static Axis?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<Axis>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Staging : EnsoModelBase
    {
        [JsonProperty("n1", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Axis? N1 { get; set; } = default!;

        [JsonProperty("non_rem", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Axis? Non_rem { get; set; } = default!;

        [JsonProperty("n2", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Axis? N2 { get; set; } = default!;

        [JsonProperty("sleep", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Axis? Sleep { get; set; } = default!;

        [JsonProperty("n3", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Axis? N3 { get; set; } = default!;

        [JsonProperty("rem_supine", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Axis? Rem_supine { get; set; } = default!;

        [JsonProperty("rem", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Axis? Rem { get; set; } = default!;

        [JsonProperty("wake", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Axis? Wake { get; set; } = default!;

        public static Staging?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<Staging>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Position : EnsoModelBase
    {
        [JsonProperty("right", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Axis? Right { get; set; } = default!;

        [JsonProperty("left", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Axis? Left { get; set; } = default!;

        [JsonProperty("prone", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Axis? Prone { get; set; } = default!;

        [JsonProperty("supine", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Axis? Supine { get; set; } = default!;

        [JsonProperty("non_supine", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Axis? Non_supine { get; set; } = default!;

        [JsonProperty("upright", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Axis? Upright { get; set; } = default!;

        public static Position?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<Position>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class TimeInRange : EnsoModelBase
    {
        [JsonProperty("duration", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Duration { get; set; } = default!;

        [JsonProperty("upper_bound", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Upper_bound { get; set; } = default!;

        [JsonProperty("percent", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public double? Percent { get; set; } = default!;

        [JsonProperty("lower_bound", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Lower_bound { get; set; } = default!;

        public static TimeInRange?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<TimeInRange>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Signal : EnsoModelBase
    {
        [JsonProperty("min", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Min { get; set; } = default!;

        [JsonProperty("time_in_range", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public ICollection<TimeInRange>? Time_in_range { get; set; } = default!;

        [JsonProperty("max", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Max { get; set; } = default!;

        [JsonProperty("mean", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Mean { get; set; } = default!;

        [JsonProperty("median", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Median { get; set; } = default!;

        [JsonProperty("percent_bad_data", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public double? Percent_bad_data { get; set; } = default!;
        public static Signal?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<Signal>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Signals : EnsoModelBase
    {
        [JsonProperty("spo2", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public IDictionary<string, Signal>? Spo2 { get; set; } = default!;

        [JsonProperty("heart_rate", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public IDictionary<string, Signal>? Heart_rate { get; set; } = default!;

        public static Signals?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<Signals>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Titration : EnsoModelBase
    {
        [JsonProperty("recording_time", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Recording_time { get; set; } = default!;

        [JsonProperty("awakenings", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Awakenings { get; set; } = default!;

        [JsonProperty("staging", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Staging? Staging { get; set; } = default!;

        [JsonProperty("end", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? End { get; set; } = default!;

        [JsonProperty("start_time", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public System.DateTimeOffset? Start_time { get; set; } = default!;

        [JsonProperty("position", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Position? Position { get; set; } = default!;

        [JsonProperty("associations", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Associations? Associations { get; set; } = default!;

        [JsonProperty("duration", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Duration { get; set; } = default!;

        [JsonProperty("sleep_latency", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Sleep_latency { get; set; } = default!;

        [JsonProperty("end_time", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public System.DateTimeOffset? End_time { get; set; } = default!;

        [JsonProperty("wake_after_sleep_onset", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Wake_after_sleep_onset { get; set; } = default!;

        [JsonProperty("sleep_fragmentation", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Sleep_fragmentation { get; set; } = default!;

        [JsonProperty("monitoring_time", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Monitoring_time { get; set; } = default!;

        [JsonProperty("start", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Start { get; set; } = default!;

        [JsonProperty("events", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public IDictionary<string, Event>? Events { get; set; } = default!;

        [JsonProperty("sleep_time", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Sleep_time { get; set; } = default!;

        [JsonProperty("sleep_efficiency", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public double? Sleep_efficiency { get; set; } = default!;

        [JsonProperty("treatment", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Treatment { get; set; } = default!;

        [JsonProperty("signals", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Signals? Signals { get; set; } = default!;

        [JsonProperty("settings", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public IDictionary<string, double>? Settings { get; set; } = default!;

        [JsonProperty("rem_latency", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Rem_latency { get; set; } = default!;

        public static Titration?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<Titration>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SubReport : EnsoModelBase
    {
        [JsonProperty("recording_time", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Recording_time { get; set; } = default!;

        [JsonProperty("awakenings", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Awakenings { get; set; } = default!;

        [JsonProperty("staging", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Staging? Staging { get; set; } = default!;

        [JsonProperty("end", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? End { get; set; } = default!;

        [JsonProperty("start_time", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public System.DateTimeOffset? Start_time { get; set; } = default!;

        [JsonProperty("position", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Position? Position { get; set; } = default!;

        [JsonProperty("associations", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Associations? Associations { get; set; } = default!;

        [JsonProperty("duration", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Duration { get; set; } = default!;

        [JsonProperty("sleep_latency", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Sleep_latency { get; set; } = default!;

        [JsonProperty("end_time", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public System.DateTimeOffset? End_time { get; set; } = default!;

        [JsonProperty("wake_after_sleep_onset", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Wake_after_sleep_onset { get; set; } = default!;

        [JsonProperty("sleep_fragmentation", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Sleep_fragmentation { get; set; } = default!;

        [JsonProperty("monitoring_time", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Monitoring_time { get; set; } = default!;

        [JsonProperty("start", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Start { get; set; } = default!;

        [JsonProperty("events", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public IDictionary<string, Event>? Events { get; set; } = default!;

        [JsonProperty("sleep_time", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Sleep_time { get; set; } = default!;

        [JsonProperty("sleep_efficiency", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public double? Sleep_efficiency { get; set; } = default!;

        [JsonProperty("signals", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Signals? Signals { get; set; } = default!;

        [JsonProperty("rem_latency", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Rem_latency { get; set; } = default!;

        public static SubReport?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<SubReport>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class StudyMetrics : EnsoModelBase
    {
        [JsonProperty("recording_time", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Recording_time { get; set; } = default!;

        [JsonProperty("awakenings", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Awakenings { get; set; } = default!;

        [JsonProperty("staging", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Staging? Staging { get; set; } = default!;

        [JsonProperty("end", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? End { get; set; } = default!;

        [JsonProperty("total_recording_time", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Total_recording_time { get; set; } = default!;

        [JsonProperty("start_time", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public System.DateTimeOffset? Start_time { get; set; } = default!;

        [JsonProperty("position", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Position? Position { get; set; } = default!;

        [JsonProperty("associations", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Associations? Associations { get; set; } = default!;

        [JsonProperty("duration", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Duration { get; set; } = default!;

        [JsonProperty("sleep_latency", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Sleep_latency { get; set; } = default!;

        [JsonProperty("alerts_count", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Alerts_count { get; set; } = default!;

        [JsonProperty("end_time", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public System.DateTimeOffset? End_time { get; set; } = default!;

        [JsonProperty("wake_after_sleep_onset", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Wake_after_sleep_onset { get; set; } = default!;

        [JsonProperty("sleep_fragmentation", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Sleep_fragmentation { get; set; } = default!;

        [JsonProperty("monitoring_time", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Monitoring_time { get; set; } = default!;

        [JsonProperty("total_sleep_time", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Total_sleep_time { get; set; } = default!;

        [JsonProperty("lights_on_time", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public System.DateTimeOffset? Lights_on_time { get; set; } = default!;

        [JsonProperty("start", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Start { get; set; } = default!;

        [JsonProperty("events", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public IDictionary<string, Event>? Events { get; set; } = default!;

        [JsonProperty("lights_off_time", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public System.DateTimeOffset? Lights_off_time { get; set; } = default!;

        [JsonProperty("sleep_time", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Sleep_time { get; set; } = default!;

        [JsonProperty("lights_on", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public ICollection<int>? Lights_on { get; set; } = default!;

        [JsonProperty("lights_off", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public ICollection<int>? Lights_off { get; set; } = default!;

        [JsonProperty("sleep_efficiency", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public double? Sleep_efficiency { get; set; } = default!;

        [JsonProperty("signals", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Signals? Signals { get; set; } = default!;

        [JsonProperty("rem_latency", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Rem_latency { get; set; } = default!;

        public static StudyMetrics?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<StudyMetrics>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Report : EnsoModelBase
    {
        [JsonProperty("titration", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public ICollection<Titration>? Titration { get; set; } = default!;

        [JsonProperty("split", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public SubReport? Split { get; set; } = default!;

        [JsonProperty("notes", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Notes { get; set; } = default!;

        [JsonProperty("study", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public StudyMetrics? Study { get; set; } = default!;

        [JsonProperty("warnings", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public object? Warnings { get; set; } = default!;

        [JsonProperty("mslt", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public ICollection<SubReport>? Mslt { get; set; } = default!;

        public static Report?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<Report>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ReportResponseSchema : EnsoModelBase
    {
        [JsonProperty("message", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Message { get; set; } = default!;

        [JsonProperty("data", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Report? Data { get; set; } = default!;

        public static ReportResponseSchema?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<ReportResponseSchema>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ExportReport : EnsoModelBase
    {
        /// <summary>
        /// Template ID
        /// </summary>
        [JsonProperty("template_id", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Template_id { get; set; } = default!;

        /// <summary>
        /// Used to override the header/footer report text
        /// </summary>
        [JsonProperty("report_name", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Report_name { get; set; } = default!;

        public static ExportReport?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<ExportReport>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ExportReportSchema : EnsoModelBase
    {
        [JsonProperty("message", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Message { get; set; } = default!;

        [JsonProperty("data", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public ExportReport? Data { get; set; } = default!;

        public static ExportReportSchema?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<ExportReportSchema>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ScoreSet : EnsoModelBase
    {
        /// <summary>
        /// The unique ID of the score set.
        /// </summary>
        [JsonProperty("id", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Id { get; set; } = default!;

        /// <summary>
        /// The ID the study.
        /// </summary>
        [JsonProperty("patient_id", Required = Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Patient_id { get; set; } = default!;

        /// <summary>
        /// The ID of the clinic
        /// </summary>
        [JsonProperty("clinic_id", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Clinic_id { get; set; } = default!;

        /// <summary>
        /// The name of the score set.
        /// </summary>
        [JsonProperty("name", Required = Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Name { get; set; } = default!;

        /// <summary>
        /// ISO 8601 datetime (UTC) when the score set was created.
        /// </summary>
        [JsonProperty("created", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public System.DateTimeOffset? Created { get; set; } = default!;

        /// <summary>
        /// ISO 8601 datetime (UTC) when the score set was last modified.
        /// </summary>
        [JsonProperty("last_modified", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public System.DateTimeOffset? Last_modified { get; set; } = default!;

        /// <summary>
        /// ID of the user who created the score set.
        /// </summary>
        [JsonProperty("user_id", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? User_id { get; set; } = default!;

        /// <summary>
        /// Name of the parent score set.
        /// </summary>
        [JsonProperty("copy_from", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Copy_from { get; set; } = default!;

        /// <summary>
        /// Alias name for the score set.
        /// </summary>
        [JsonProperty("alias", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Alias { get; set; } = default!;

        public static ScoreSet?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<ScoreSet>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PaginatedScoreSetResponseSchema : EnsoModelBase
    {
        [JsonProperty("message", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Message { get; set; } = default!;

        [JsonProperty("data", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public ICollection<ScoreSet>? Data { get; set; } = default!;

        /// <summary>
        /// Total number of documents returned.
        /// </summary>
        [JsonProperty("returned", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Returned { get; set; } = default!;

        /// <summary>
        /// Total number of documents matching the query, including those not returned in the response.
        /// </summary>
        [JsonProperty("count", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Count { get; set; } = default!;

        /// <summary>
        /// Whether or not this list has another page of items after this one that can be fetched.
        /// </summary>
        [JsonProperty("has_next", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public bool? Has_next { get; set; } = default!;

        public static PaginatedScoreSetResponseSchema?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<PaginatedScoreSetResponseSchema>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ScoreSetResponseSchema : EnsoModelBase
    {
        [JsonProperty("message", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Message { get; set; } = default!;

        [JsonProperty("data", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public ScoreSet? Data { get; set; } = default!;

        public static ScoreSetResponseSchema?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<ScoreSetResponseSchema>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ScoreEvent : EnsoModelBase
    {
        /// <summary>
        /// The unique UUID of the event.
        /// </summary>
        [JsonProperty("id", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Id { get; set; } = default!;

        /// <summary>
        /// Name of the event.
        /// </summary>
        [JsonProperty("name", Required = Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ScoreEventName Name { get; set; } = default!;

        /// <summary>
        /// The start time in seconds of the event.
        /// </summary>
        [JsonProperty("start", Required = Required.Always)]
        public double Start { get; set; } = default!;

        /// <summary>
        /// The duration in seconds of the event.
        /// </summary>
        [JsonProperty("duration", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public double? Duration { get; set; } = default!;

        /// <summary>
        /// Heart rate before the event.
        /// </summary>
        [JsonProperty("hr_before", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public double? Hr_before { get; set; } = default!;

        /// <summary>
        /// Heart rate extremum during the event.
        /// </summary>
        [JsonProperty("hr_extreme", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public double? Hr_extreme { get; set; } = default!;

        /// <summary>
        /// Oxygen level before the event.
        /// </summary>
        [JsonProperty("o2_before", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public double? O2_before { get; set; } = default!;

        /// <summary>
        /// Minimum oxygen level during the event.
        /// </summary>
        [JsonProperty("o2_min", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public double? O2_min { get; set; } = default!;

        /// <summary>
        /// The association for `arousal` events.
        /// </summary>
        [JsonProperty("association", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ScoreEventAssociation? Association { get; set; } = default!;

        /// <summary>
        /// Event channel.
        /// </summary>
        [JsonProperty("channel", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Channel { get; set; } = default!;

        /// <summary>
        /// The titration settings and value for any titration event.
        /// </summary>
        [JsonProperty("titration", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public IDictionary<string, double>? Titration { get; set; } = default!;

        /// <summary>
        /// User comments on the event.
        /// </summary>
        [JsonProperty("comment", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Comment { get; set; } = default!;

        public static ScoreEvent?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<ScoreEvent>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ListScoreEventResponseSchema : EnsoModelBase
    {
        [JsonProperty("message", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Message { get; set; } = default!;

        [JsonProperty("data", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public ICollection<ScoreEvent>? Data { get; set; } = default!;

        public static ListScoreEventResponseSchema?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<ListScoreEventResponseSchema>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Management : EnsoModelBase
    {
        /// <summary>
        /// The current state of the study in the management workflow.
        /// </summary>
        [JsonProperty("state", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public string? State { get; set; } = default!;

        /// <summary>
        /// The time the management state was last changed.
        /// </summary>
        [JsonProperty("last_changed", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public System.DateTimeOffset? Last_changed { get; set; } = default!;

        /// <summary>
        /// Whether or not the study has been locked.
        /// </summary>
        [JsonProperty("locked", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public bool? Locked { get; set; } = default!;

        public static Management?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<Management>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class StudyPatient : EnsoModelBase
    {
        /// <summary>
        /// ISO 8601 date in the format `YYYY-MM-DD`.
        /// </summary>
        [JsonProperty("birth_date", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        [JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset? Birth_date { get; set; } = default!;

        /// <summary>
        /// Body mass index.
        /// </summary>
        [JsonProperty("bmi", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public double? Bmi { get; set; } = default!;

        /// <summary>
        /// Patient's first name.
        /// </summary>
        [JsonProperty("first_name", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public string? First_name { get; set; } = default!;

        /// <summary>
        /// Patient's gender.
        /// </summary>
        [JsonProperty("gender", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public StudyPatientGender? Gender { get; set; } = default!;

        /// <summary>
        /// Patient's height in inches.
        /// </summary>
        [JsonProperty("height", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public double? Height { get; set; } = default!;

        /// <summary>
        /// Patient's last name.
        /// </summary>
        [JsonProperty("last_name", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public string? Last_name { get; set; } = default!;

        /// <summary>
        /// Array of medications the patient takes.
        /// </summary>
        [JsonProperty("medication", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public ICollection<string>? Medication { get; set; } = default!;

        /// <summary>
        /// Medical record number of the Patient
        /// </summary>
        [JsonProperty("mrn", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public string? Mrn { get; set; } = default!;

        /// <summary>
        /// Patient's neck size in inches.
        /// </summary>
        [JsonProperty("neck_size", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public bool? Neck_size { get; set; } = default!;

        /// <summary>
        /// Order number for the specific Study
        /// </summary>
        [JsonProperty("order_number", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public string? Order_number { get; set; } = default!;

        /// <summary>
        /// Patient's weight in lbs.
        /// </summary>
        [JsonProperty("weight", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public double? Weight { get; set; } = default!;

        public static StudyPatient?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<StudyPatient>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class StudyOptions : EnsoModelBase
    {
        /// <summary>
        /// Desaturation criterion for scoring. Use `3` to score at least 3% desaturations. Use `4` to score at least 4% desaturations.
        /// </summary>
        [JsonProperty("desaturation", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public StudyOptionsDesaturation? Desaturation { get; set; } = default!;

        public static StudyOptions?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<StudyOptions>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Realtime : EnsoModelBase
    {
        /// <summary>
        /// Total seconds of data scored during realtime scoring
        /// </summary>
        [JsonProperty("scored_seconds", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Scored_seconds { get; set; } = default!;

        /// <summary>
        /// State of realtime scoring
        /// </summary>
        [JsonProperty("enabled", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public bool? Enabled { get; set; } = default!;

        /// <summary>
        /// Total seconds of data read and saved in hdf5 files
        /// </summary>
        [JsonProperty("read_seconds", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Read_seconds { get; set; } = default!;

        /// <summary>
        /// Completed state for realtime scoring
        /// </summary>
        [JsonProperty("complete", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public bool? Complete { get; set; } = default!;

        public static Realtime?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<Realtime>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Advice : EnsoModelBase
    {
        /// <summary>
        /// Minimum total sleep time before alert is triggered
        /// </summary>
        [JsonProperty("min_tst", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Min_tst { get; set; } = default!;

        /// <summary>
        /// Minimum apnea-hypopnea index before alert is triggered
        /// </summary>
        [JsonProperty("min_ahi", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public double? Min_ahi { get; set; } = default!;

        /// <summary>
        /// Minimum SPO2 value before alert is triggered
        /// </summary>
        [JsonProperty("min_spo2", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Min_spo2 { get; set; } = default!;

        public static Advice?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<Advice>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Study : EnsoModelBase
    {
        /// <summary>
        /// The unique identifier of the study.
        /// </summary>
        [JsonProperty("id", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Id { get; set; } = default!;

        /// <summary>
        /// ISO 8601 datetime (UTC) when study was created.
        /// </summary>
        [JsonProperty("created", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public System.DateTimeOffset? Created { get; set; } = default!;

        /// <summary>
        /// The ID of the clinic to which the study belongs.
        /// </summary>
        [JsonProperty("clinic_id", Required = Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Clinic_id { get; set; } = default!;

        /// <summary>
        /// The ID of the computer from which the study was uploaded.
        /// </summary>
        [JsonProperty("computer_id", Required = Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Computer_id { get; set; } = default!;

        /// <summary>
        /// Software used to encode study data.
        /// </summary>
        [JsonProperty("software", Required = Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public StudySoftware Software { get; set; } = default!;

        /// <summary>
        /// Hardware used to collect signal data.
        /// </summary>
        [JsonProperty("hardware", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public string? Hardware { get; set; } = default!;

        [JsonProperty("management", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Management? Management { get; set; } = default!;

        [JsonProperty("patient", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public StudyPatient? Patient { get; set; } = default!;

        /// <summary>
        /// External Patient ID.
        /// </summary>
        [JsonProperty("pid", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public string? Pid { get; set; } = default!;

        [JsonProperty("options", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public StudyOptions? Options { get; set; } = default!;

        /// <summary>
        /// The type of the study.
        /// </summary>
        [JsonProperty("study_type", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Study_type { get; set; } = default!;

        /// <summary>
        /// The state of the study.
        /// </summary>
        [JsonProperty("state", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public StudyState? State { get; set; } = default!;

        /// <summary>
        /// The status of the study
        /// </summary>
        [JsonProperty("status", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public StudyStatus? Status { get; set; } = default!;

        [JsonProperty("realtime", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Realtime? Realtime { get; set; } = default!;

        [JsonProperty("advice", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Advice? Advice { get; set; } = default!;

        public static Study?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<Study>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class StudyResponseSchema : EnsoModelBase
    {
        [JsonProperty("message", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Message { get; set; } = default!;

        [JsonProperty("data", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Study? Data { get; set; } = default!;

        public static StudyResponseSchema?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<StudyResponseSchema>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PaginatedStudyResponseSchema : EnsoModelBase
    {
        [JsonProperty("message", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Message { get; set; } = default!;

        [JsonProperty("data", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public ICollection<Study>? Data { get; set; } = default!;

        /// <summary>
        /// Total number of documents returned.
        /// </summary>
        [JsonProperty("returned", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Returned { get; set; } = default!;

        /// <summary>
        /// Total number of documents matching the query, including those not returned in the response.
        /// </summary>
        [JsonProperty("count", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public int? Count { get; set; } = default!;

        /// <summary>
        /// Whether or not this list has another page of items after this one that can be fetched.
        /// </summary>
        [JsonProperty("has_next", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public bool? Has_next { get; set; } = default!;

        public static PaginatedStudyResponseSchema?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<PaginatedStudyResponseSchema>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CreateFile : EnsoModelBase
    {
        /// <summary>
        /// Name of the file. Do not include this field if this is an EDF study.
        /// </summary>
        [JsonProperty("filename", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public string? Filename { get; set; } = default!;

        /// <summary>
        /// Content encoding of the file. Use `gzip` if you plan to gzip compress your uploads.
        /// </summary>
        [JsonProperty("content_encoding", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public string? Content_encoding { get; set; } = default!;

        /// <summary>
        /// URL to upload the file.
        /// </summary>
        [JsonProperty("upload_url", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Upload_url { get; set; } = default!;

        public static CreateFile?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<CreateFile>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CreateFileResponseSchema : EnsoModelBase
    {
        [JsonProperty("message", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Message { get; set; } = default!;

        [JsonProperty("data", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public CreateFile? Data { get; set; } = default!;

        public static CreateFileResponseSchema?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<CreateFileResponseSchema>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class RetrieveFile : EnsoModelBase
    {
        /// <summary>
        /// Name of the file. Do not include this field if this is an EDF study.
        /// </summary>
        [JsonProperty("filename", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public string? Filename { get; set; } = default!;

        /// <summary>
        /// Content encoding of the file. Use `gzip` if you plan to gzip compress your uploads.
        /// </summary>
        [JsonProperty("content_encoding", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public string? Content_encoding { get; set; } = default!;

        /// <summary>
        /// URL to download the file.
        /// </summary>
        [JsonProperty("url", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Url { get; set; } = default!;

        public static RetrieveFile?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<RetrieveFile>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class RetrieveFileResponseSchema : EnsoModelBase
    {
        [JsonProperty("message", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Message { get; set; } = default!;

        [JsonProperty("data", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public RetrieveFile? Data { get; set; } = default!;

        public static RetrieveFileResponseSchema?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<RetrieveFileResponseSchema>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ListRetrieveFileResponseSchema : EnsoModelBase
    {
        [JsonProperty("message", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Message { get; set; } = default!;

        [JsonProperty("data", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public ICollection<RetrieveFile>? Data { get; set; } = default!;

        public static ListRetrieveFileResponseSchema?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<ListRetrieveFileResponseSchema>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ExpiringLinkStudy : EnsoModelBase
    {
        /// <summary>
        /// External user's email address.
        /// </summary>
        [JsonProperty("email", Required = Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Email { get; set; } = default!;

        /// <summary>
        /// External user's first name.
        /// </summary>
        [JsonProperty("first_name", Required = Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string First_name { get; set; } = default!;

        /// <summary>
        /// External user's last name.
        /// </summary>
        [JsonProperty("last_name", Required = Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Last_name { get; set; } = default!;

        /// <summary>
        /// The unique ID of the study.
        /// </summary>
        [JsonProperty("study_id", Required = Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Study_id { get; set; } = default!;

        /// <summary>
        /// The unique ID of the scoreset that should be initially displayed when the external user accesses the study.              Exclude from the request to bring external user to the scoreset selection window.
        /// </summary>
        [JsonProperty("initial_score_set_id", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public string? Initial_score_set_id { get; set; } = default!;

        public static ExpiringLinkStudy?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<ExpiringLinkStudy>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CreateStudy : EnsoModelBase
    {
        /// <summary>
        /// The unique identifier of the study.
        /// </summary>
        [JsonProperty("id", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Id { get; set; } = default!;

        /// <summary>
        /// ISO 8601 datetime (UTC) when study was created.
        /// </summary>
        [JsonProperty("created", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public System.DateTimeOffset? Created { get; set; } = default!;

        /// <summary>
        /// The ID of the clinic to which the study belongs.
        /// </summary>
        [JsonProperty("clinic_id", Required = Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Clinic_id { get; set; } = default!;

        /// <summary>
        /// The ID of the computer from which the study was uploaded.
        /// </summary>
        [JsonProperty("computer_id", Required = Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Computer_id { get; set; } = default!;

        /// <summary>
        /// Software used to encode study data.
        /// </summary>
        [JsonProperty("software", Required = Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public CreateStudySoftware Software { get; set; } = default!;

        /// <summary>
        /// Hardware used to collect signal data.
        /// </summary>
        [JsonProperty("hardware", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public string? Hardware { get; set; } = default!;

        [JsonProperty("management", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Management? Management { get; set; } = default!;

        [JsonProperty("patient", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public StudyPatient? Patient { get; set; } = default!;

        /// <summary>
        /// External Patient ID.
        /// </summary>
        [JsonProperty("pid", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public string? Pid { get; set; } = default!;

        [JsonProperty("options", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public StudyOptions? Options { get; set; } = default!;

        /// <summary>
        /// The type of the study.
        /// </summary>
        [JsonProperty("study_type", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Study_type { get; set; } = default!;

        /// <summary>
        /// The state of the study.
        /// </summary>
        [JsonProperty("state", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public CreateStudyState? State { get; set; } = default!;

        /// <summary>
        /// The status of the study
        /// </summary>
        [JsonProperty("status", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public CreateStudyStatus? Status { get; set; } = default!;

        [JsonProperty("realtime", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Realtime? Realtime { get; set; } = default!;

        [JsonProperty("advice", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Advice? Advice { get; set; } = default!;

        /// <summary>
        /// The upload root folder on the computer in which the study resides.
        /// </summary>
        [JsonProperty("root", Required = Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Root { get; set; } = default!;

        public static CreateStudy?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<CreateStudy>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class UpdateStudy : EnsoModelBase
    {
        /// <summary>
        /// The unique identifier of the study.
        /// </summary>
        [JsonProperty("id", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Id { get; set; } = default!;

        /// <summary>
        /// ISO 8601 datetime (UTC) when study was created.
        /// </summary>
        [JsonProperty("created", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public System.DateTimeOffset? Created { get; set; } = default!;

        /// <summary>
        /// The ID of the clinic to which the study belongs.
        /// </summary>
        [JsonProperty("clinic_id", Required = Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Clinic_id { get; set; } = default!;

        /// <summary>
        /// The ID of the computer from which the study was uploaded.
        /// </summary>
        [JsonProperty("computer_id", Required = Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Computer_id { get; set; } = default!;

        /// <summary>
        /// Software used to encode study data.
        /// </summary>
        [JsonProperty("software", Required = Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public UpdateStudySoftware Software { get; set; } = default!;

        /// <summary>
        /// Hardware used to collect signal data.
        /// </summary>
        [JsonProperty("hardware", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public string? Hardware { get; set; } = default!;

        [JsonProperty("management", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Management? Management { get; set; } = default!;

        [JsonProperty("patient", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public StudyPatient? Patient { get; set; } = default!;

        /// <summary>
        /// External Patient ID.
        /// </summary>
        [JsonProperty("pid", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public string? Pid { get; set; } = default!;

        [JsonProperty("options", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public StudyOptions? Options { get; set; } = default!;

        /// <summary>
        /// The type of the study.
        /// </summary>
        [JsonProperty("study_type", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Study_type { get; set; } = default!;

        /// <summary>
        /// The state of the study.
        /// </summary>
        [JsonProperty("state", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public UpdateStudyState? State { get; set; } = default!;

        /// <summary>
        /// The status of the study
        /// </summary>
        [JsonProperty("status", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public UpdateStudyStatus? Status { get; set; } = default!;

        [JsonProperty("realtime", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Realtime? Realtime { get; set; } = default!;

        [JsonProperty("advice", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public Advice? Advice { get; set; } = default!;

        /// <summary>
        /// ISO 8601 date in the format `YYYY-MM-DD`.
        /// </summary>
        [JsonProperty("patient__birth_date", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        [JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset? Patient__birth_date { get; set; } = default!;

        /// <summary>
        /// Body mass index.
        /// </summary>
        [JsonProperty("patient__bmi", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public double? Patient__bmi { get; set; } = default!;

        /// <summary>
        /// Patient's first name.
        /// </summary>
        [JsonProperty("patient__first_name", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public string? Patient__first_name { get; set; } = default!;

        /// <summary>
        /// Patient's gender.
        /// </summary>
        [JsonProperty("patient__gender", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public UpdateStudyPatient__gender? Patient__gender { get; set; } = default!;

        /// <summary>
        /// Patient's height in inches.
        /// </summary>
        [JsonProperty("patient__height", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public double? Patient__height { get; set; } = default!;

        /// <summary>
        /// Patient's last name.
        /// </summary>
        [JsonProperty("patient__last_name", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public string? Patient__last_name { get; set; } = default!;

        /// <summary>
        /// Patient medical record number.
        /// </summary>
        [JsonProperty("patient__mrn", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Patient__mrn { get; set; } = default!;

        /// <summary>
        /// Name of a medication to append to the array of medications.
        /// </summary>
        [JsonProperty("push__patient__medication", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Push__patient__medication { get; set; } = default!;

        /// <summary>
        /// Array of medications to add to the end of the array of all medications.
        /// </summary>
        [JsonProperty("push_all__patient__medication", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public ICollection<string>? Push_all__patient__medication { get; set; } = default!;

        /// <summary>
        /// Remove the specified medication from the array of all medications.
        /// </summary>
        [JsonProperty("pull__patient__medication", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Pull__patient__medication { get; set; } = default!;

        /// <summary>
        /// Array of medications to be removed from the array of all medications.
        /// </summary>
        [JsonProperty("pull_all__patient__medication", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public ICollection<string>? Pull_all__patient__medication { get; set; } = default!;

        /// <summary>
        /// Add the specified medication to the list of all medications if it does not already exist.
        /// </summary>
        [JsonProperty("add_to_set__patient__medication", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Add_to_set__patient__medication { get; set; } = default!;

        /// <summary>
        /// Patient's neck size in inches.
        /// </summary>
        [JsonProperty("patient__neck_size", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public bool? Patient__neck_size { get; set; } = default!;

        /// <summary>
        /// Order number for Study.
        /// </summary>
        [JsonProperty("patient__order_number", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Patient__order_number { get; set; } = default!;

        /// <summary>
        /// Patient's weight in lbs.
        /// </summary>
        [JsonProperty("patient__weight", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public double? Patient__weight { get; set; } = default!;

        /// <summary>
        /// Desaturation criterion for scoring. Use `3` to score at least 3% desaturations. Use `4` to score at least 4% desaturations.
        /// </summary>
        [JsonProperty("options__desaturation", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public UpdateStudyOptions__desaturation? Options__desaturation { get; set; } = default!;

        public static UpdateStudy?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<UpdateStudy>(data, new JsonSerializerSettings());

        }

    }

    [GeneratedCode("NJsonSchema", "********* (NJsonSchema v10.8.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class UpdateReport : EnsoModelBase
    {
        [JsonProperty("notes", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public string? Notes { get; set; } = default!;

        public static UpdateReport?  FromJson(string data)
        {

            return JsonConvert.DeserializeObject<UpdateReport>(data, new JsonSerializerSettings());

        }

    }
}
