﻿using System.Net.Http.Headers;
using System.Net.Mime;

namespace Ethos.ThirdParty.Tennr
{
    /// <summary>
    /// 
    /// </summary>
    public class TennrClient
    {
        readonly HttpClient client;

        /// <summary>
        /// 
        /// </summary>
        public TennrWorkflow Workflow { get; private set; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="httpClient"></param>
        /// <param name="apiKey"></param>
        /// <exception cref="ArgumentException"></exception>
        public TennrClient(HttpClient httpClient, string apiKey)
        {
            if (string.IsNullOrEmpty(apiKey))
                throw new ArgumentException("Tennr API key is required.", nameof(apiKey));

            client = httpClient;
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(MediaTypeNames.Application.Json));
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(TennrConstants.AuthScheme, apiKey);
            Workflow = new TennrWorkflow(this, TennrConstants.WorkflowUri);  // can change URI if needed by overriding here
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        internal async Task<HttpResponseMessage> Execute(HttpRequestMessage request)
        {
            return await client.SendAsync(request);

        }
    }
}
