using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using Ethos.Model;
using Ethos.Utilities.Pagination;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Net.Http.Headers;

namespace Ethos.Workflows.Api;

// Base Output DTO might need an Etag property for concurrency
// public interface IOutputDto {
//    string? Etag { get; set; }
// } // Define TOutputDto appropriately in derived classes

public class RequiredEntityDoesNotExistException : Exception
{
    public RequiredEntityDoesNotExistException(string message) : base(message) { }
    public RequiredEntityDoesNotExistException(EntityType entityType, Guid id)
        : base($"Required entity of type {entityType} with ID {id} does not exist.") { }
}

[ApiController]
[Route("api/[controller]")]
public abstract class EntityControllerBase<TEntity, TInputDto, TOutputDto, TQuery> : ControllerBase
    where TEntity : IAuditableEntity<TEntity> // Added 'class' constraint
    where TInputDto : IInputDto
    where TOutputDto : class // Consider adding : IOutputDto if using interface for Etag
    where TQuery : IPrimitiveQuery
{
    protected readonly DbContext _dbContext;
    protected readonly DbSet<TEntity> _dbSet;
    protected readonly DbSet<DraftDbo> _draftDbSet;

    public EntityControllerBase(DbContext dbContext)
    {
        _dbContext = dbContext;
        _dbSet = _dbContext.Set<TEntity>();
        _draftDbSet = _dbContext.Set<DraftDbo>();
    }

    // --- Abstract Methods ---

    /// <summary>
    /// Maps the domain entity to the output DTO.
    /// Should also populate ETag on the DTO if applicable.
    /// </summary>
    protected abstract TOutputDto MapToDto(TEntity entity);

    /// <summary>
    /// Creates a new entity or updates an existing one based on the input DTO.
    /// </summary>
    /// <param name="entity">The existing entity to update (null if creating).</param>
    /// <param name="input">The input DTO.</param>
    /// <param name="requiredId">For PUT operations, this is the ID from the route. Implementations should ensure the created/updated entity has this ID.</param>
    /// <remarks>
    /// For PATCH operations, the implementation should ideally inspect the input DTO
    /// and only apply changes for properties that are explicitly provided (e.g., non-null).
    /// For PUT/POST, all applicable fields from the input should be mapped.
    /// </remarks>
    /// <returns>The created or updated entity.</returns>
    protected abstract TEntity CreateOrUpdateEntity(TEntity? entity, TInputDto input, Guid? requiredId = null);

    /// <summary>
    /// Generates a concurrency ETag for the given entity.
    /// </summary>
    /// <param name="entity">The entity to generate an ETag for.</param>
    /// <returns>The calculated ETag string.</returns>
    protected virtual string GenerateEtag(TEntity entity)
    {
        // Generate ETag based on the entity's properties
        var hashBytes = SHA256.HashData(Encoding.UTF8.GetBytes(JsonSerializer.Serialize(MapToDto(entity))));
        return Convert.ToBase64String(hashBytes);
    }

    protected virtual void DoAdditionalValidation(TEntity entity)
    {
        
    }

    // --- Virtual Methods ---

    /// <summary>
    /// Allows derived controllers to specify includes for optimal data loading.
    /// Used by GetById, Put, Patch, Search, Get.
    /// </summary>
    protected virtual IQueryable<TEntity> ApplyIncludes(IQueryable<TEntity> query)
    {
        // By default, no includes. Derived classes override this if needed.
        return query;
    }

    // --- Helper Methods ---

    /// <summary>
    /// Checks the If-Match header against the current ETag.
    /// </summary>
    /// <param name="currentEtag">The ETag of the current entity state.</param>
    /// <param name="etagIsRequired">If true, the request will fail if the If-Match header is absent.</param>
    /// <returns>True if the ETag matches or If-Match header is absent, False otherwise.</returns>
    protected bool CheckPrecondition(string currentEtag, bool etagIsRequired = false)
    {
        if (Request.Headers.TryGetValue(HeaderNames.IfMatch, out var headerEtag))
        {
            // Trim quotes and compare. Add W/ prefix handling if using weak ETags.
            string requestEtag = headerEtag.ToString().Trim('"');
            return string.Equals(requestEtag, currentEtag.Trim('"'), StringComparison.Ordinal);
        }
        // If no If-Match header is present, the precondition passes by default (or fail - depends on policy)
        // For simplicity here, we allow updates without If-Match. Require it by returning false if header is absent.
        return !etagIsRequired;
    }

    // --- Action Methods ---

    [HttpGet("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)] // Type ActionResult<TOutputDto> infers schema
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public virtual async Task<ActionResult<TOutputDto>> GetById(Guid id)
    {
        var entity = await ApplyIncludes(_dbSet)
            .AsNoTracking() // Use NoTracking for read-only operations
            .FirstOrDefaultAsync(p => p.Id == id);

        if (entity == null)
        {
            return NotFound();
        }

        var currentEtag = GenerateEtag(entity);
        Response.Headers[HeaderNames.ETag] = $"\"{currentEtag}\""; // Use strong ETag format

        return Ok(MapToDto(entity)); // Ok() is optional, return MapToDto(entity) directly works too
    }

    // Kept legacy POST search endpoint
    [HttpPost("search")]
    [ProducesResponseType(StatusCodes.Status200OK)] // Type ActionResult<PagedResponse<TOutputDto>> infers schema
    public virtual async Task<ActionResult<PagedResponse<TOutputDto>>> Search(
        [FromBody] QueryDto<TQuery>? query,
        [FromQuery] PagingParameters pagingParameters)
    {
        IQueryable<TEntity> q = ApplyIncludes(_dbSet.AsNoTracking());

        if (query != null)
        {
            q = q.Where(query.BuildPredicate<TEntity>());
        }

        // Using the provided PaginateWithLinksAsync extension
        var pagedResult = await q.PaginateWithLinksAsync<TEntity, TOutputDto>(
            this, // Pass controller instance for link generation
            items => items.Select(MapToDto).ToList(), // Provide the mapping function
            pagingParameters.limit,
            pagingParameters.offset
        );

        return Ok(pagedResult);
    }

    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)] // Type ActionResult<PagedResponse<TOutputDto>> infers schema
    public virtual async Task<ActionResult<PagedResponse<TOutputDto>>> Get(
        // Base64 encoded.
        [FromQuery] string? queryBase64, /*QueryDto<TQuery>?*/
        [FromQuery] PagingParameters pagingParameters)
    {
        var queryRaw = string.IsNullOrEmpty(queryBase64) ? null : Convert.FromBase64String(queryBase64);
        var query = (queryRaw == null) ? null : JsonSerializer.Deserialize<QueryDto<TQuery>>(queryRaw);
        
        // Logic is identical to Search for now.
        IQueryable<TEntity> q = ApplyIncludes(_dbSet.AsNoTracking());

        if (query != null)
        {
            // TODO: Add validation or try-catch if BuildPredicate can throw exceptions for invalid queries
            q = q.Where(query.BuildPredicate<TEntity>());
        }

        var pagedResult = await q.PaginateWithLinksAsync<TEntity, TOutputDto>(
            this,
            items => items.Select(MapToDto).ToList(),
            pagingParameters.limit,
            pagingParameters.offset
        );

        return Ok(pagedResult);
    }
    
    // [HttpPost("draft")]
    // [ProducesResponseType(StatusCodes.Status201Created)] // Type ActionResult<TOutputDto> infers schema via CreatedAtAction
    // [ProducesResponseType(StatusCodes.Status400BadRequest)]
    // public virtual async Task<ActionResult<TOutputDto>> Create([FromQuery] Guid? id, [FromBody] JsonObject input)
    // {
    //     var validationResult = DataValidator.ValidateJson<TInputDto>(input);
    //
    //     if (validationResult.IsFailure)
    //     {
    //         return new BadRequestObjectResult(new
    //         {
    //             Errors = validationResult.Errors,
    //             Warnings = validationResult.Warnings
    //         });
    //     }
    //
    //     var newEntity = new DraftEntity()
    //     {
    //         EntityType = IEntity.GetEntityType<TEntity>(),
    //         EntityId = id ?? Guid.NewGuid(), // Use provided ID or generate a new one
    //         Content = input
    //     };
    //     _dbContext.Set<DraftEntity>().Add(newEntity);
    //
    //     try
    //     {
    //         await _dbContext.SaveChangesAsync();
    //     }
    //     catch (DbUpdateException ex) // Example: Catch potential DB constraint errors
    //     {
    //         // Log the exception ex
    //         return BadRequest("Failed to save entity. Check constraints or data.");
    //         // Or handle specific exceptions like unique key violations etc.
    //     }
    //
    //     var outputDto = MapToDto(newEntity);
    //     var currentEtag = GenerateEtag(newEntity);
    //     Response.Headers[HeaderNames.ETag] = $"\"{currentEtag}\"";
    //
    //     return CreatedAtAction(nameof(GetById), new { id = newEntity.Id }, outputDto);
    // }

    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)] // Type ActionResult<TOutputDto> infers schema via CreatedAtAction
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public virtual async Task<ActionResult<TOutputDto>> Create([FromBody] TInputDto input)
    {
        var validationResult = DataValidator.Validate(input); // Validate input DTO before processing

        if (validationResult.IsFailure)
        {
            return new BadRequestObjectResult(new
            {
                Errors = validationResult.Errors,
                Warnings = validationResult.Warnings
            });
        }
        
        var newEntity = CreateOrUpdateEntity(null, input); // requiredId is null for POST
        _dbSet.Add(newEntity);

        try
        {
            await _dbContext.SaveChangesAsync();
        }
        catch (DbUpdateException ex) // Example: Catch potential DB constraint errors
        {
            // Log the exception ex
            return BadRequest("Failed to save entity. Check constraints or data.");
            // Or handle specific exceptions like unique key violations etc.
        }


        var outputDto = MapToDto(newEntity);
        var currentEtag = GenerateEtag(newEntity);
        Response.Headers[HeaderNames.ETag] = $"\"{currentEtag}\"";

        return CreatedAtAction(nameof(GetById), new { id = newEntity.Id }, outputDto);
    }

    [HttpPut("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]      // Updated existing
    [ProducesResponseType(StatusCodes.Status201Created)]  // Created new (Upsert)
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)] // Should not happen with Upsert logic, but good practice
    [ProducesResponseType(StatusCodes.Status412PreconditionFailed)] // ETag mismatch
    public virtual async Task<ActionResult<TOutputDto>> Put(Guid id, [FromBody] TInputDto input)
    {
        // Optional: Validate input.Id consistency if needed, but route 'id' is authoritative.
        // if (input.Id.HasValue && input.Id.Value != id)
        // {
        //     return BadRequest("ID in body does not match ID in route.");
        // }

        TEntity? existingEntity = await ApplyIncludes(_dbSet.AsTracking()) // Use Tracking for updates
                                       .FirstOrDefaultAsync(e => e.Id == id);
        bool created = false;
        string currentEtag;

        if (existingEntity != null)
        {
            // Check ETag for existing entity
            currentEtag = GenerateEtag(existingEntity);
            if (!CheckPrecondition(currentEtag))
            {
                return StatusCode(StatusCodes.Status412PreconditionFailed);
            }
            CreateOrUpdateEntity(existingEntity, input, id); // Update existing
        }
        else
        {
            // Create new entity for PUT (Upsert)
            existingEntity = CreateOrUpdateEntity(null, input, id); // Ensure ID is set
            _dbSet.Add(existingEntity);
            created = true;
            // No ETag to check for creation, will generate one after save.
        }

        try
        {
             await _dbContext.SaveChangesAsync();
        }
        catch (DbUpdateConcurrencyException ex)
        {
             // Log ex
             // This could happen if RowVersion/Timestamp is used alongside/instead of ETag check
             return Conflict("Concurrency conflict detected.");
        }
        catch (DbUpdateException ex)
        {
            // Log ex
            return BadRequest("Failed to save entity.");
        }

        // Generate ETag *after* successful save
        var finalEtag = GenerateEtag(existingEntity);
        Response.Headers[HeaderNames.ETag] = $"\"{finalEtag}\"";

        var outputDto = MapToDto(existingEntity);

        if (created)
        {
            return CreatedAtAction(nameof(GetById), new { id = existingEntity.Id }, outputDto);
        }
        else
        {
            return Ok(outputDto);
        }
    }

    [HttpPatch("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)] // Type ActionResult<TOutputDto> infers schema
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)] // e.g., if patch format is invalid
    [ProducesResponseType(StatusCodes.Status412PreconditionFailed)] // ETag mismatch
    public virtual async Task<ActionResult<TOutputDto>> Patch(Guid id, [FromBody] TInputDto input)
    {
        // For true PATCH, consider using JsonPatchDocument<TInputDto> or JsonPatchDocument<TEntity>
        // Using TInputDto here relies on CreateOrUpdateEntity handling partial updates.

        TEntity? existingEntity = await ApplyIncludes(_dbSet.AsTracking())
                                       .FirstOrDefaultAsync(e => e.Id == id);

        if (existingEntity == null)
        {
            return NotFound();
        }

        // Check ETag
        var currentEtag = GenerateEtag(existingEntity);
        if (!CheckPrecondition(currentEtag))
        {
            return StatusCode(StatusCodes.Status412PreconditionFailed);
        }

        // Apply partial update - Assumes CreateOrUpdateEntity knows how.
        CreateOrUpdateEntity(existingEntity, input, id);

        try
        {
            await _dbContext.SaveChangesAsync();
        }
        catch (DbUpdateConcurrencyException ex)
        {
            // Log ex
            return Conflict("Concurrency conflict detected.");
        }
        catch (DbUpdateException ex)
        {
             // Log ex
            return BadRequest("Failed to save entity.");
        }


        // Generate ETag *after* successful save
        var finalEtag = GenerateEtag(existingEntity);
        Response.Headers[HeaderNames.ETag] = $"\"{finalEtag}\"";

        var outputDto = MapToDto(existingEntity);
        return Ok(outputDto);
    }
    
    #region Draft Endpoints
    
    /// <summary>
    /// Generates a concurrency ETag for the given draft entity.
    /// ETag is based on the draft's data content.
    /// </summary>
    /// <param name="draftDbo">The draft entity to generate an ETag for.</param>
    /// <returns>The calculated ETag string.</returns>
    protected virtual string GenerateEtagForDraft(DraftDbo draftDbo)
    {
        // Generate ETag based only on the draft's mutable data
        var hashBytes = SHA256.HashData(Encoding.UTF8.GetBytes(JsonSerializer.Serialize(draftDbo.Data)));
        return Convert.ToBase64String(hashBytes);
    }

    [HttpPost("draft")]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ValidatedDraftDto))]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public virtual async Task<ActionResult<ValidatedDraftDto>> CreateDraft([FromQuery] Guid? entityId, [FromBody] JsonObject? data)
    {
        if (entityId != null)
        {
            if (data != null)
            {
                return BadRequest("When providing an entity ID, the request body must be empty to create a draft for an existing entity.");
            }
            
            var entity = _dbSet.AsNoTracking()
                .FirstOrDefault(e => e.Id == entityId.Value);
            if (entity == null)
            {
                return NotFound($"Entity with ID {entityId} does not exist.");
            }

            data = JsonSerializer.SerializeToNode(MapToDto(entity), JsonSerializerOptions.Web) as JsonObject;
        }

        if (data == null)
        {
            return BadRequest("Request body cannot be empty.");
        }
        
        var draft = await _draftDbSet
            .AsNoTracking()
            .FirstOrDefaultAsync(d => d.EntityType == IEntity.GetEntityType<TEntity>() && d.EntityId == (entityId ?? Guid.Empty));
        var newDraft = draft == null;
        
        var validationResult = DataValidator.ValidateJson<TInputDto>(data, allowPartial: true);

        draft ??= new DraftDbo
        {
            // A draft is tied to an entity type and a specific (potentially new) entity ID
            EntityType = IEntity.GetEntityType<TEntity>(),
            EntityId = entityId ?? Guid.NewGuid(),
            Data = data,
        };
        // If the draft already exists, we update its data
        draft.Data = data;

        if (newDraft) _draftDbSet.Add(draft);
        await _dbContext.SaveChangesAsync();

        var currentEtag = GenerateEtagForDraft(draft);
        Response.Headers[HeaderNames.ETag] = $"\"{currentEtag}\"";

        // Prepare the response DTO with the draft data and validation results
        var responseDto = new ValidatedDraftDto
        {
            Id = draft.Id,
            EntityType = draft.EntityType,
            EntityId = draft.EntityId,
            Data = draft.Data,
            Errors = validationResult.Errors?.Select(i => i.ToDto()).ToList(),
            Warnings = validationResult.Warnings?.Select(i => i.ToDto()).ToList(),
        };

        return CreatedAtAction(nameof(GetDraftById), new { entityId = draft.EntityId }, responseDto);
    }
    
    [HttpPut("draft/{entityId}")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ValidatedDraftDto))]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status412PreconditionFailed)]
    public virtual async Task<ActionResult<ValidatedDraftDto>> ModifyDraft(Guid entityId, [FromBody] JsonObject data)
    {
        if (data == null)
        {
            return BadRequest("Request body cannot be empty.");
        }

        var draftEntity = await _draftDbSet.FirstOrDefaultAsync(d => d.EntityId == entityId);

        if (draftEntity == null)
        {
            return NotFound($"No draft found with Entity ID {entityId}.");
        }
        
        // Ensure the draft being modified belongs to the correct entity type for this controller
        if (draftEntity.EntityType != IEntity.GetEntityType<TEntity>())
        {
            return BadRequest($"Draft {entityId} is for entity type '{draftEntity.EntityType}' not '{IEntity.GetEntityType<TEntity>()}'.");
        }

        var currentEtag = GenerateEtagForDraft(draftEntity);
        if (!CheckPrecondition(currentEtag))
        {
            return StatusCode(StatusCodes.Status412PreconditionFailed, "The draft has been modified by another process.");
        }

        draftEntity.Data = data; // Update the data
        
        await _dbContext.SaveChangesAsync();

        var finalEtag = GenerateEtagForDraft(draftEntity);
        Response.Headers[HeaderNames.ETag] = $"\"{finalEtag}\"";
        
        var validationResult = DataValidator.ValidateJson<TInputDto>(data, allowPartial: true);

        // Prepare the response DTO with the updated draft data and validation results
        var responseDto = new ValidatedDraftDto
        {
            Id = draftEntity.Id,
            EntityType = draftEntity.EntityType,
            EntityId = draftEntity.EntityId,
            Data = draftEntity.Data,
            Errors = validationResult.Errors?.Select(i => i.ToDto()).ToList(),
            Warnings = validationResult.Warnings?.Select(i => i.ToDto()).ToList()
        };

        return Ok(responseDto);
    }

    [HttpPost("draft/{entityId}/commit")]
    [ProducesResponseType(StatusCodes.Status200OK)]         // On existing entity update
    [ProducesResponseType(StatusCodes.Status201Created)]    // On new entity creation
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status412PreconditionFailed)]
    public virtual async Task<ActionResult<TOutputDto>> CommitDraft(Guid entityId)
    {
        var draftEntity = await _draftDbSet.FirstOrDefaultAsync(d => d.EntityId == entityId);

        if (draftEntity == null)
        {
            return NotFound($"No draft found with Entity ID {entityId}.");
        }
        
        if (draftEntity.EntityType != IEntity.GetEntityType<TEntity>())
        {
            return BadRequest($"Draft {entityId} is for entity type '{draftEntity.EntityType}' not '{IEntity.GetEntityType<TEntity>()}'.");
        }

        // Check ETag for the DRAFT to prevent committing a stale version
        var draftEtag = GenerateEtagForDraft(draftEntity);
        if (!CheckPrecondition(draftEtag))
        {
            return StatusCode(StatusCodes.Status412PreconditionFailed, "The draft has been modified since you retrieved it. Please fetch the latest version and recommit.");
        }

        // *** This is where extraneous fields are stripped away ***
        // Deserializing the flexible JsonObject into the strongly-typed TInputDto
        // will automatically discard any properties not defined in TInputDto.
        var options = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true, 
            RespectRequiredConstructorParameters = false
        };
        TInputDto? inputDto = draftEntity.Data.Deserialize<TInputDto>(options);

        if (inputDto == null)
        {
            return BadRequest("Draft data could not be deserialized into the required format.");
        }

        // Use standard validation logic
        var validationResult = DataValidator.Validate(inputDto);
        if (validationResult.IsFailure)
        {
            return new BadRequestObjectResult(new { Errors = validationResult.Errors, Warnings = validationResult.Warnings });
        }

        // Find the "real" entity this draft corresponds to
        TEntity? existingEntity = await ApplyIncludes(_dbSet.AsTracking())
            .FirstOrDefaultAsync(e => e.Id == draftEntity.EntityId);
        
        bool created = existingEntity == null;

        // Use the existing, robust logic to create or update the entity
        var finalEntity = CreateOrUpdateEntity(existingEntity, inputDto, draftEntity.EntityId);
        
        if (created)
        {
            _dbSet.Add(finalEntity);
        }
        
        // The draft has served its purpose and should be removed
        // _draftDbSet.Remove(draftEntity);
        
        try
        {
            // This will save the new/updated TEntity and delete the DraftEntity in a single transaction
            await _dbContext.SaveChangesAsync();
        }
        catch (DbUpdateConcurrencyException ex)
        {
             // Log ex
             return Conflict("A concurrency conflict occurred while saving the entity. It may have been modified by another process.");
        }
        catch (DbUpdateException ex)
        {
            // Log ex
            return BadRequest("Failed to save the committed entity.");
        }

        var outputDto = MapToDto(finalEntity);
        var finalEtag = GenerateEtag(finalEntity);
        Response.Headers[HeaderNames.ETag] = $"\"{finalEtag}\"";

        if (created)
        {
            return CreatedAtAction(nameof(GetById), new { id = finalEntity.Id }, outputDto);
        }
        else
        {
            return Ok(outputDto);
        }
    }

    [HttpGet("draft/{entityId}")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(DraftDto))]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public virtual async Task<ActionResult<DraftDto>> GetDraftById(Guid entityId)
    {
        var entity = await _draftDbSet.AsNoTracking()
            .FirstOrDefaultAsync(p => p.EntityId == entityId);

        if (entity == null)
        {
            return NotFound();
        }
        
        // Ensure the draft belongs to the entity type of this controller
        if (entity.EntityType != IEntity.GetEntityType<TEntity>())
        {
            return NotFound(); // Or Forbid() / BadRequest()
        }

        var draftEtag = GenerateEtagForDraft(entity);
        Response.Headers[HeaderNames.ETag] = $"\"{draftEtag}\"";

        return Ok(new DraftDto
        {
            Id = entity.Id,
            EntityType = entity.EntityType,
            EntityId = entity.EntityId,
            Data = entity.Data
        });
    }
    
    /// <summary>
    /// Validates a given JSON payload against the entity's input DTO rules without saving it.
    /// This is ideal for client-side validation before a draft is even created.
    /// </summary>
    /// <param name="data">The JSON object representing the draft data to validate.</param>
    /// <returns>A DTO containing the original data along with any validation errors or warnings.</returns>
    [HttpPost("draft/validate")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ValidatedDraftDto))]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public virtual ActionResult<ValidatedDraftDto> ValidateUnsavedDraft([FromBody] JsonObject data)
    {
        if (data == null)
        {
            return BadRequest("Request body cannot be empty.");
        }

        // Perform the validation against the provided JSON data
        var validationResult = DataValidator.ValidateJson<TInputDto>(data, allowPartial: true);

        // Prepare the response DTO.
        // Note: Id and EntityId will be null/default as this draft doesn't exist on the server.
        var responseDto = new ValidatedDraftDto
        {
            // We can still inform the client what type this data was validated against.
            EntityType = IEntity.GetEntityType<TEntity>(),
            Data = data,
            Errors = validationResult.Errors?.Select(i => i.ToDto()).ToList(),
            Warnings = validationResult.Warnings?.Select(i => i.ToDto()).ToList()
        };

        return Ok(responseDto);
    }
    
    // <summary>
    /// Validates an existing draft by its ID.
    /// </summary>
    /// <param name="id">The ID of the draft to validate.</param>
    /// <returns>A DTO containing the draft data along with any validation errors or warnings.</returns>
    [HttpPost("draft/{entityId}/validate")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ValidatedDraftDto))]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public virtual async Task<ActionResult<ValidatedDraftDto>> ValidateDraft(Guid entityId)
    {
        var draftEntity = await _draftDbSet.AsNoTracking()
            .FirstOrDefaultAsync(d => d.EntityId == entityId);

        if (draftEntity == null)
        {
            return NotFound($"No draft found with Entity ID {entityId}.");
        }
        
        // Ensure the draft being validated belongs to the correct entity type for this controller
        if (draftEntity.EntityType != IEntity.GetEntityType<TEntity>())
        {
            return BadRequest($"Draft {entityId} is for entity type '{draftEntity.EntityType}' not '{IEntity.GetEntityType<TEntity>()}'.");
        }

        // Perform the validation against the draft's JSON data
        var validationResult = DataValidator.ValidateJson<TInputDto>(draftEntity.Data, allowPartial: true);
        
        // The ETag tells the client which version of the draft this validation result applies to.
        var draftEtag = GenerateEtagForDraft(draftEntity);
        Response.Headers[HeaderNames.ETag] = $"\"{draftEtag}\"";

        // Prepare the response DTO with the draft data and the fresh validation results
        var responseDto = new ValidatedDraftDto
        {
            Id = draftEntity.Id,
            EntityType = draftEntity.EntityType,
            EntityId = draftEntity.EntityId,
            Data = draftEntity.Data,
            Errors = validationResult.Errors?.Select(i => i.ToDto()).ToList(),
            Warnings = validationResult.Warnings?.Select(i => i.ToDto()).ToList()
        };

        return Ok(responseDto);
    }

    #endregion
}