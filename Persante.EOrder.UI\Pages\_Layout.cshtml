﻿@using Microsoft.AspNetCore.Components.Web
@namespace ScoreApplication.Pages
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="~/" />
    <link rel="icon" type="image/x-icon" href="_content/Persante.Blazor.SharedUI/favicon.ico">
    <link href="_content/Persante.Blazor.SharedUI/css/themes/persante-material/persante.css" rel="stylesheet">
    @* <link href="_content/CodeBeam.Extensions/MudExtensions.min.css" rel="stylesheet" /> *@
    <link href="_content/CodeBeam.MudBlazor.Extensions/MudExtensions.min.css" rel="stylesheet" />
    <link href="~/css/mudextensions.min.css" rel="stylesheet" />
    <link href="css/custom-site.css" rel="stylesheet" />   
    <component type="typeof(HeadOutlet)" render-mode="ServerPrerendered" />
</head>
<body>
    @RenderBody()

    <div id="blazor-error-ui">
        <environment include="Staging,Production">
            An error has occurred. This application may no longer respond until reloaded.
        </environment>
        <environment include="Development">
            An unhandled exception has occurred. See browser dev tools for details.
        </environment>
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>

    <script src="_framework/blazor.server.js"></script>
    <script src="_content/Persante.Blazor.SharedUI/scripts/persante.js" type="module"></script>
    <script src="_content/MudBlazor/MudBlazor.min.js"></script>
    <script src="_content/CodeBeam.MudBlazor.Extensions/MudExtensions.min.js"></script>
    <script src="~/css/mudextensions.min.js"></script>
</body>
</html>
