﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Ethos.TenantConfig.Migrations
{
    /// <inheritdoc />
    public partial class DynamicMigration_638785324276447677 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Config",
                table: "TenantConfig",
                newName: "Values");

            migrationBuilder.AddColumn<string>(
                name: "Name",
                table: "TenantConfig",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "IX_TenantConfig_Name_TenantId",
                table: "TenantConfig",
                columns: new[] { "Name", "TenantId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TenantConfig_Name_TenantId",
                table: "TenantConfig");

            migrationBuilder.DropColumn(
                name: "Name",
                table: "TenantConfig");

            migrationBuilder.RenameColumn(
                name: "Values",
                table: "TenantConfig",
                newName: "Config");
        }
    }
}
