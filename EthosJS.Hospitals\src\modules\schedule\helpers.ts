import moment from 'moment';
import { ISchedule } from '@app/modules/schedule/types';
import { ScheduleDto } from '@app/modules/schedule/dto/schedule.dto';
import { ScheduleEquipmentDto } from '@app/modules/schedule/dto/schedule.equipment.dto';
import { DATE_FORMAT } from '@app/common/constants';

export const convertRawSchedule = (schedule: any): ISchedule => ({
  id: schedule.id,
  credentials: schedule.credentials,
  createdAt: schedule.created_at,
  updatedAt: schedule.updated_at,
  deletedAt: schedule.deleted_at,
  technicianId: schedule.technician_id,
  technicianName: schedule.technician_name,
  patientId: schedule.patient_id,
  patientName: schedule.patient_name,
  shift: schedule.shift,
  studyId: schedule.study_id,
  studyName: schedule.study_name,
  date: schedule.date,
  weekday: schedule.weekday,
  facilityId: schedule.facility_id,
  facilityName: schedule.facility_name,
  equipments: schedule.equipments,
});

export const mapToScheduleDto = (entity: any): ScheduleDto => {
  const dto = new ScheduleDto();
  dto.id = entity.id;
  dto.createdAt = entity.createdAt;
  dto.updatedAt = entity.updatedAt;
  dto.technicianId = entity.technicianId;
  dto.technicianName = entity.technicianName;
  dto.patientId = entity.patientId;
  dto.patientName = entity.patientName;
  dto.studyId = entity.studyId;
  dto.studyName = entity.studyName;
  dto.facilityId = entity.facilityId;
  dto.facilityName = entity.facilityName;
  dto.shift = entity.shift;
  dto.date = moment(entity.date).format(DATE_FORMAT);
  dto.equipments = Object.values(entity.equipments).map(({ count, studyCount, equipmentName, equipmentId }: any) => {
    const dto = new ScheduleEquipmentDto();

    dto.count = count;
    dto.studyCount = studyCount;
    dto.equipmentId = Number(equipmentId);
    dto.scheduleId = entity.id;
    dto.equipmentName = equipmentName;

    return dto;
  });

  return dto;
};