using System.Runtime.Serialization;
using Ethos.ReferenceData.Client;
using Microsoft.EntityFrameworkCore;
using BindingFlags = System.Reflection.BindingFlags;

namespace Ethos.Model.Test;

public class BasicCrudTests
{
    private DbContextOptions<TestDbContext> CreateInMemoryOptions()
    {
        return new DbContextOptionsBuilder<TestDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;
    }

    [Fact]
    public async Task GetReferenceDataFromService()
    {
        var service = new ReferenceDataClient(new HttpClient());
        service.BaseUri = "https://localhost:64286/api/reference";
        service.BearerToken = "eyJhbGciOiJSUzI1NiIsImtpZCI6Ilg1ZVhrNHh5b2pORnVtMWtsMll0djhkbE5QNC1jNTdkTzZRR1RWQndhTmsiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EAjBe3WRn5VyCZ2WZdcp4Ci0X2HF5RPlRj1laqpTllrNM9tgGMXzLysP9cdyKNTCpPa1duVbAQHCDQIBMCBUmR3wdWoGHJWVc0SFxqudl82JyHV0AV6XQZFtPc_a4bonioWasTiAr-03wpket1MBkyPWfMn9NH0o3S19b_Yi8R_GsXjes_zAgWmUtYPtwjh5KowahyxYbKb7zVQf1iHB1ChFpperJo_x12x3hzhQ6po3-O3SiachCWjR8L9puaQ7X9JPar7Vzx-kdHAb4fj_tnMddgoWr_oAt01LEY9HjHHFpa3Sqf2yeAIPJ0y6cR_rAgTyfMmhW3gY3tGsHvErfQ";
        var sexes = await SexEntity.FromService(service);
    }

    [Fact]
    public async Task BootstrapRefData()
    {
        var service = new ReferenceDataClient(new HttpClient());
        service.BaseUri = "https://apidev.ethosbridge.com/api/reference";
        service.BearerToken = "eyJhbGciOiJSUzI1NiIsImtpZCI6Ilg1ZVhrNHh5b2pORnVtMWtsMll0djhkbE5QNC1jNTdkTzZRR1RWQndhTmsiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EAjBe3WRn5VyCZ2WZdcp4Ci0X2HF5RPlRj1laqpTllrNM9tgGMXzLysP9cdyKNTCpPa1duVbAQHCDQIBMCBUmR3wdWoGHJWVc0SFxqudl82JyHV0AV6XQZFtPc_a4bonioWasTiAr-03wpket1MBkyPWfMn9NH0o3S19b_Yi8R_GsXjes_zAgWmUtYPtwjh5KowahyxYbKb7zVQf1iHB1ChFpperJo_x12x3hzhQ6po3-O3SiachCWjR8L9puaQ7X9JPar7Vzx-kdHAb4fj_tnMddgoWr_oAt01LEY9HjHHFpa3Sqf2yeAIPJ0y6cR_rAgTyfMmhW3gY3tGsHvErfQ";
        await service.AddDefaultReferenceDataValues();
    }

    //[Fact]
    //public async Task CanAddReferenceData()
    //{
    //    var options = CreateInMemoryOptions();

    //    using (var ctx = new TestDbContext(options))
    //    {
    //        // Ensure database is created
    //        await ctx.Database.EnsureCreatedAsync();
            
    //        //ctx.AddDefaultReferenceDataValues<SexEntity>();
    //        //ctx.AddDefaultReferenceDataValues<GenderEntity>();
    //        //ctx.AddDefaultReferenceDataValues<RaceEntity>();
    //        //ctx.AddDefaultReferenceDataValues<EthnicityEntity>();
    //        //ctx.AddDefaultReferenceDataValues<EmailUseEntity>();
            
    //        //await ctx.SaveChangesAsync();
            
    //        //// Check that the reference data was added
    //        //var sexCount = await ctx.Set<SexEntity>().CountAsync();
    //        //Assert.True(sexCount > 0, "SexEntity reference data not added.");
    //        //var genderCount = await ctx.Set<GenderEntity>().CountAsync();
    //        //Assert.True(genderCount > 0, "GenderEntity reference data not added.");
    //        //var raceCount = await ctx.Set<RaceEntity>().CountAsync();
    //        //Assert.True(raceCount > 0, "RaceEntity reference data not added.");
    //        //var ethnicityCount = await ctx.Set<EthnicityEntity>().CountAsync();
    //        //Assert.True(ethnicityCount > 0, "EthnicityEntity reference data not added.");
            
    //        //var gender = await ctx.Set<GenderEntity>().Where((e) => e.Name.ToLower() == "male").FirstAsync();
    //        //var sex = await ctx.Set<SexEntity>().Where(e => e.Name.ToLower() == "female").FirstAsync();
    //    }
    //}

    //[Fact]
    //public async Task CanCreateAndRetrievePatient()
    //{
    //    var options = CreateInMemoryOptions();
    //    var Id0 = Guid.NewGuid();

    //    // 1) CREATE & ADD Entities
    //    using (var ctx = new TestDbContext(options))
    //    {
    //        // Ensure database is created
    //        await ctx.Database.EnsureCreatedAsync();
            

    //        var patient = new PatientEntity
    //        {
    //            Id = Id0, // Remember you have a composite key in your base classes; Id might matter
    //            Names = [new PersonName()
    //            {
    //                FirstName = "John",
    //                MiddleName = null,
    //                LastName = "Doe",
    //            }],
    //            // Optional fields
    //            DateOfBirth = new DateOnly(1980, 1, 1),
    //            Emails = [new PatientEmail()
    //                {
    //                    Email = "<EMAIL>",
    //                }],
                
    //            PhoneNumbers = [new PatientPhoneNumber()
    //            {
    //                PhoneNumber = "************",
    //                Type = 0, // references to PhoneTypeEntity
    //                Use = 0, // references to PhoneUseEntity
    //                AllowsSMS = true,
    //                AllowsVoice = true,
    //                AllowsCommunication = true,
    //                IsPreferred = true
    //            }],
    //            Addresses = [new PatientAddress()
    //            {
    //                UseId = 0, // references to AddressUseEntity
    //                TypeId = 0, // references to AddressTypeEntity
    //                Address = new AddressEntity
    //                {
    //                    Line1 = "123 Main St",
    //                    Line2 = "Apt 4B",
    //                    CountryId = 0, // references to CountryEntity
    //                    City = "Test City",
    //                    StateId = 0,
    //                    PostalCode = "99999"
    //                }
    //            }]
    //        };

    //        ctx.Patients.Add(patient);
    //        await ctx.SaveChangesAsync();
    //    }

    //    // 2) QUERY to confirm it was saved
    //    using (var ctx = new TestDbContext(options))
    //    {
    //        var savedPatient = await ctx.Patients
    //            .Include(p => p.Names)
    //            .Include(p => p.Addresses)
    //            .Include(p => p.Emails)
    //            .FirstOrDefaultAsync(p => p.Id == Id0);

    //        Assert.NotNull(savedPatient);
    //        Assert.Equal("John", savedPatient!.Names.First().FirstName);
    //        Assert.Equal("Doe", savedPatient.Names.First().LastName);
    //        Assert.NotEmpty(savedPatient.Addresses);
    //    }
    //}

    //[Fact]
    //public async Task CanCreateAndDeleteAddress()
    //{
    //    var options = CreateInMemoryOptions();

    //    Guid addressId = Guid.NewGuid();

    //    // Create and save an AddressEntity
    //    using (var ctx = new TestDbContext(options))
    //    {
    //        await ctx.Database.EnsureCreatedAsync();

    //        var address = new AddressEntity
    //        {
    //            Id = addressId,
    //            Line1 = "100 Redwood Hwy",
    //            Line2 = "Suite 500",
    //            City = "MyCity",
    //            PostalCode = "12345",
    //            CountryId = 0,
    //            StateId = 0
    //        };

    //        ctx.Addresses.Add(address);
    //        await ctx.SaveChangesAsync();
    //    }

    //    // Confirm that we can retrieve it
    //    using (var ctx = new TestDbContext(options))
    //    {
    //        var found = await ctx.Addresses.FindAsync(addressId); 

    //        Assert.NotNull(found);
    //        Assert.Equal("MyCity", found.City);
    //    }

    //    // Delete it
    //    using (var ctx = new TestDbContext(options))
    //    {
    //        var toDelete = await ctx.Addresses.FindAsync(addressId);
    //        Assert.NotNull(toDelete);

    //        ctx.Addresses.Remove(toDelete!);
    //        await ctx.SaveChangesAsync();
    //    }

    //    // Confirm deletion
    //    using (var ctx = new TestDbContext(options))
    //    {
    //        var foundAgain = await ctx.Addresses.FindAsync(addressId);
    //        Assert.Null(foundAgain);
    //    }
    //}

    //[Fact]
    //public async Task CanCreateAndCascadeDeletePatientWithStudies()
    //{
    //    var options = CreateInMemoryOptions();
        
    //    var Id0 = Guid.NewGuid();
    //    var Id1 = Guid.NewGuid();

    //    // 1) CREATE & SAVE a Patient with a few Studies
    //    using (var ctx = new TestDbContext(options))
    //    {
    //        await ctx.Database.EnsureCreatedAsync();

    //        var patient = new PatientEntity
    //        {
    //            Id = Id0,
    //            Names = [new PersonName()
    //            {
    //                FirstName = "Jane",
    //                MiddleName = null,
    //                LastName = "Roe",
    //            }],
    //        };

    //        var order1 = new OrderEntity()
    //        {
    //            Id = Id1,
    //            PatientId = Id0
    //        };

    //        var study1 = new StudyEntity
    //        {
    //            OrderId = Id1,
    //            Status = "Scheduled",
    //            DOS = "2025-04-01",
    //            StudyType = "Type A",
    //            Location = "Clinic X",
    //            Interpreting = "Dr. Smith",
    //            Referring = "Dr. Jones"
    //        };

    //        var study2 = new StudyEntity
    //        {
    //            OrderId = Id1,
    //            Status = "Completed",
    //            DOS = "2025-04-02",
    //            StudyType = "Type B",
    //            Location = "Clinic Y",
    //            Interpreting = "Dr. Brown",
    //            Referring = "Dr. White"
    //        };

    //        patient.Orders.Add(order1);
    //        order1.Studies.Add(study1);
    //        order1.Studies.Add(study2);
    //        ctx.Patients.Add(patient);
    //        await ctx.SaveChangesAsync();
    //    }

    //    // 2) Verify
    //    using (var ctx = new TestDbContext(options))
    //    {
    //        var p = await ctx.Patients
    //            .Include(x => x.Orders)
    //            .ThenInclude(o => o.Studies)
    //            .FirstOrDefaultAsync(x => x.Id == Id0);

    //        Assert.NotNull(p);
    //        Assert.Equal(1, p!.Orders.Count);
    //        Assert.Equal(2, p.Orders.First().Studies.Count);
    //    }

    //    // 3) Delete the patient and cascade remove the Studies
    //    using (var ctx = new TestDbContext(options))
    //    {
    //        var p = await ctx.Patients
    //            .Include(x => x.Orders)
    //            .FirstOrDefaultAsync(x => x.Id == Id0);

    //        Assert.NotNull(p);

    //        ctx.Patients.Remove(p!);
    //        await ctx.SaveChangesAsync();
    //    }

    //    // 4) Confirm everything is gone
    //    using (var ctx = new TestDbContext(options))
    //    {
    //        var p = await ctx.Patients.FindAsync(Id0, null);
    //        Assert.Null(p);

    //        var s1 = await ctx.Orders.FindAsync(Id1, null);
    //        Assert.Null(s1);
    //    }
    //}
}