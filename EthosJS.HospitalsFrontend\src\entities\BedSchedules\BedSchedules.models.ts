//@ts-nocheck
// import '@axmit/persante-hospitals';
import { EBedScheduleProperties } from 'common/const/enum.const';
import { IRequestSuccess } from 'common/models';

export interface IBedScheduleCollectionParams  {}
export interface IBedScheduleCollectionDto {}
export interface IBedScheduleCollectionUpdatePayload  {}
export interface IBedScheduleCollectionUpdateResponse {}
export interface IBedScheduleDto  {
  edited?: boolean;
  error?: string;
  scheduleId?: number;
}

export interface IBedScheduleErrorEntity {
  [key: string]: any;
}

export interface IBedScheduleCollection {
  data: IBedScheduleDto[] | null;
  formChanged: boolean;
  loading: boolean;
}

export interface IBedScheduleEquipmentDto {}

export interface IBedScheduleEquipmentEntity {
  equipmentId: number;
  equipmentName: string;
  count: number;
  total: number;
}

export interface IBedScheduleDayEntity {
  date: string;
  dayShiftBeds: number;
  nightShiftBeds: number;
  equipments: IBedScheduleEquipmentEntity[];
  error?: string;
}

export interface IBedScheduleEntity extends Record<string, any> {
  key: number;
  id?: number;
  facilityId: number;
  facilityName: string;
  bedsTotalCount: number;
  mon?: IBedScheduleDayEntity;
  tue?: IBedScheduleDayEntity;
  wed?: IBedScheduleDayEntity;
  thu?: IBedScheduleDayEntity;
  fri?: IBedScheduleDayEntity;
  sat?: IBedScheduleDayEntity;
  sun?: IBedScheduleDayEntity;
}

export interface IBedScheduleCollectionChangePayload {
  record: IBedScheduleEntity;
  weekDay: string;
  property: EBedScheduleProperties;
  value: number | IBedScheduleEquipmentEntity[];
}
