import { Expose } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { BaseDto } from '@app/common/dto/base.dto';
import { StudyEquipmentDto } from '@app/modules/study/dto/study.equipment.dto';
import { StudyCredentialDto } from '@app/modules/study/dto/study.credential.dto';

export class StudyDto extends BaseDto {
  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty({ type: StudyCredentialDto, isArray: true })
  @Expose()
  credentials: StudyCredentialDto[];

  @ApiProperty({ type: StudyEquipmentDto, isArray: true })
  @Expose()
  equipments: StudyEquipmentDto[];
}
