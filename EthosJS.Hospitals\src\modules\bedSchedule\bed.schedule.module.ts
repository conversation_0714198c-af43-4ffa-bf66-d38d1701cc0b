import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BedScheduleRepository } from '@app/modules/bedSchedule/bed.schedule.repository';
import { BedScheduleService } from '@app/modules/bedSchedule/bed.schedule.service';
import { BedScheduleController } from '@app/modules/bedSchedule/bed.schedule.controller';
import { FacilityModule } from '@app/modules/facility/facility.module';
import { EquipmentModule } from '@app/modules/equipment/equipment.module';

@Module({
  imports: [
    forwardRef(() => FacilityModule),
    EquipmentModule,
    TypeOrmModule.forFeature([
      BedScheduleRepository,
    ]),
  ],
  providers: [BedScheduleService],
  controllers: [BedScheduleController],
  exports: [BedScheduleService],
})
export class BedScheduleModule {}
