﻿
namespace Ethos.Events
{
    public class EventException
    {
        public long Id { get; set; }
        public string Message { get; set; } = null!;
        public string? StackTrace { get; set; }
        public long EventId { get; set; }
        public Event? Event { get; set; }
    }

    public class EventExceptionDto
    {
        public long Id { get; set; }
        public string Message { get; set; } = null!;
        public string? StackTrace { get; set; }
    }
}