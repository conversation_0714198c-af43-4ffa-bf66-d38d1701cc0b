using System.Collections.Immutable;
using System.Reflection;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.RegularExpressions;
using Ethos.Workflows.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Routing;

namespace Ethos.Workflows.Api.Analysis;

public static class ApiAnalyzer
{
    private class State
    {
        public required Assembly Assembly { get; init; }
        public Dictionary<Type, TypeRef> ProcessedTypes { get; } = new();
        public Dictionary<string, TypeDefinition> TypeDefinitions { get; } = new();
        public Dictionary<string, EntityControllerDefinition> EntityControllers { get; } = new();
        public Dictionary<string, ControllerDefinition> Controllers { get; } = new();
        public Action<string> Log { get; init; } = _ => { /* No-op by default */ };
    }

    public static ApiModel Analyze(Assembly assembly) => Analyze(assembly, s => { });
    
    public static ApiModel Analyze(Assembly assembly, Action<string> log)
    {
        var state = new State { Assembly = assembly, Log = log };
        var controllerBase = typeof(ControllerBase);
        var entityControllerBaseType = typeof(EntityControllerBase<,,,>);
        
        var controllers = assembly.GetTypes()
            .Where(t => t.IsClass && !t.IsAbstract && t.IsSubclassOf(controllerBase))
            .ToList();
        
        state.Log($"Found {controllers.Count} controllers in assembly '{assembly.GetName().Name}'.");

        foreach (var controller in controllers)
        {
            if (controller.BaseType is { IsGenericType: true } &&
                controller.BaseType.GetGenericTypeDefinition() == entityControllerBaseType)
            {
                // This is an EntityControllerBase
                var definition = BuildEntityControllerDefinition(state, controller);
                state.Log($"Discovered controller: {definition.EntityName} at route '{definition.Route}'");
                state.EntityControllers[definition.EntityName] = definition;
            }
            else
            {
                state.Log($"Skipping non-entity controller: {controller.Name}");
                var def = BuildRegularControllerDefinition(state, controller);
                if (def.Endpoints.Any())
                {
                    state.Controllers[controller.Name.Replace("Controller", "")] = def;
                    state.Log($"Discovered REST controller: {controller.Name} with {def.Endpoints.Count} endpoint(s)");
                }
            }
        }
        
        state.Log($"New types discovered: {state.TypeDefinitions.Count}");
        state.Log($"Entity controllers discovered: {state.EntityControllers.Count}");

        return new ApiModel(
            EntityControllers: state.EntityControllers,
            Controllers: state.Controllers,
            AllTypes: state.TypeDefinitions);
    }
    
    private static EntityControllerDefinition BuildEntityControllerDefinition(State state, Type controllerType)
    {
        var entityName = controllerType.Name.Replace("Controller", ""); // CareLocationController -> CareLocation
        var route = $"api/{entityName.ToLowerInvariant()}"; // Simplified route generation

        // Get TInput, TOutput, TQuery from EntityControllerBase<TEntity, TInput, TOutput, TQuery, TContext>
        var baseArgs = controllerType.BaseType!.GetGenericArguments();
        var tInput  = baseArgs[1];
        var tOutput = baseArgs[2];
        var tQuery  = baseArgs[3];

        return new EntityControllerDefinition(
            EntityName: entityName,
            Route: route,
            InputDto: DiscoverType(state, tInput).typeRef,
            OutputDto: DiscoverType(state, tOutput).typeRef,
            QueryType: DiscoverType(state, tQuery).typeRef
        );
    }
    
    private static (TypeRef typeRef, TypeDefinition? typeDef) DiscoverType(State state, Type t)
    {
        if (state.ProcessedTypes.TryGetValue(t, out var existingRef))
        {
            if (existingRef is TypeRef.Custom customRef &&
                state.TypeDefinitions.TryGetValue(customRef.Name, out var existingDef))
            {
                // If we have a custom type, return both the reference and definition
                return (existingRef, existingDef);
            }
        }

        // --- Core Type Logic ---
        var (typeRef, typeDef) = DecodeTypeInternal(state, t);
        state.ProcessedTypes[t] = typeRef;
        return (typeRef, typeDef);
    }
    
    private static readonly NullabilityInfoContext NlCtx = new();
    
    private static (TypeRef typeRef, TypeDefinition? typeDef) DecodeTypeInternal(State state, Type t)
    {
        state.Log($"Discovering type: {t.FullName}");
        // Handle Nullable<T>
        if (Nullable.GetUnderlyingType(t) is { } underlying)
        {
            return (new TypeRef.Nullable(DiscoverType(state, underlying).typeRef), null);
        }
        
        if (t.IsArray)
        { 
            var elemType = t.GetElementType()!; 
            return (new TypeRef.List(DiscoverType(state, elemType).typeRef), null);
        }

        // Handle Primitives
        if (TryMapPrimitive(t, out var primitiveKind))
        {
            return (new TypeRef.Primitive(primitiveKind.Value), null);
        }
        
        // Treat System.Object as “any” (here mapped to Json, pick what makes sense for you)
        if (t == typeof(object))
            return (new TypeRef.Primitive(PrimitiveKind.Json), null);
        
        // Handle Collections
        if (t.IsGenericType)
        {
            var genericDef = t.GetGenericTypeDefinition();
            var args = t.GetGenericArguments();
            if (genericDef == typeof(IEnumerable<>) || genericDef == typeof(IReadOnlyList<>) ||
                genericDef == typeof(List<>) || genericDef == typeof(ICollection<>))
            {
                return (new TypeRef.List(DiscoverType(state, args[0]).typeRef), null);
            }
            
            if (genericDef == typeof(ISet<>) || genericDef == typeof(HashSet<>) || genericDef == typeof(ImmutableHashSet<>))
            {
                return (new TypeRef.Set(DiscoverType(state, args[0]).typeRef), null);
            }
            
            if (genericDef == typeof(IDictionary<,>) || genericDef == typeof(Dictionary<,>) || genericDef == typeof(IReadOnlyDictionary<,>))
            {
                return (new TypeRef.Dict(DiscoverType(state, args[0]).typeRef, DiscoverType(state, args[1]).typeRef), null);
            }
        }
        
        // Handle C# Enums
        if (t.IsEnum)
        {
            if (!state.TypeDefinitions.ContainsKey(t.Name))
            {
                 var enumDef = new EnumDefinition(t.Name, Enum.GetNames(t).ToList());
                 state.TypeDefinitions.Add(t.Name, enumDef);
                 return (new TypeRef.Custom(t.Name), enumDef);
            }

            return (new TypeRef.Custom(t.Name), state.TypeDefinitions[t.Name]);
        }

        // --- Handle Complex Types (DataClass or ADT) ---
        if (t.IsClass || (t.IsValueType && !t.IsPrimitive))
        {
            // Avoid re-processing if already defined
            if (!state.TypeDefinitions.ContainsKey(t.Name))
            {
                 // ADT Detection: Is this a abstract record class?
                if (t.IsAbstract)
                {
                    var variants = t.Assembly.GetTypes()
                        .Where(impl => impl.IsClass && !impl.IsAbstract && t.IsAssignableFrom(impl))
                        .ToList();

                    var variantDefs = new List<DataClassDefinition>();
                    
                    // First, define the ADT itself
                    var adtDef = new AdtDefinition(
                        Name: t.Name,
                        DiscriminatorField: "$type", // Convention-based
                        Variants: variantDefs
                    );
                    state.TypeDefinitions.Add(t.Name, adtDef);
                    
                    state.Log($"Discovered ADT: {t.Name} with {variants.Count} variants.");
                    
                    // Then, recursively discover each concrete variant
                    foreach (var variant in variants)
                    {
                        var (typeRef, typeDef) = DiscoverType(state, variant);
                        variantDefs.Add((typeDef as DataClassDefinition)!);
                        state.TypeDefinitions.Remove(variant.Name); // Remove to avoid duplicates
                    }
                    
                    return (new TypeRef.Custom(t.Name), adtDef);
                }
                else // Assume it's a regular DataClass
                {
                    var properties = t.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                        .Select(p =>
                        {
                            var ctx = new NullabilityInfoContext();
                            NullabilityInfo info = ctx.Create(p);          // property/field/parameter/return value
                            bool isNullable = info.ReadState == NullabilityState.Nullable;

                            var typeRef = DiscoverType(state, p.PropertyType).typeRef;
                            
                            if (NlCtx.Create(p).ReadState == NullabilityState.Nullable && typeRef is not TypeRef.Nullable)
                            { 
                                typeRef = new TypeRef.Nullable(typeRef);
                            }
                            
                            return new PropertyDefinition(p.Name, typeRef);
                        })
                        .ToList();

                    var dataClassDef = new DataClassDefinition(t.Name, properties);
                    state.TypeDefinitions.Add(t.Name, dataClassDef);
                    
                    return (new TypeRef.Custom(t.Name), dataClassDef);
                }
            }
            return (new TypeRef.Custom(t.Name), state.TypeDefinitions[t.Name]);
        }

        if (t.IsValueType && t.FullName!.StartsWith("System.ValueTuple"))
        {
            throw new NotSupportedException(
                $"ValueTuple types are not supported for code generation. Consider using a custom class or record instead.");
            // var elemTypes = t.GetGenericArguments();
            // var fields = new List<PropertyDefinition>();
            //
            // // Try to honour C# element names first (TupleElementNamesAttribute)
            // var nameAttr = t.GetCustomAttribute<System.Runtime.CompilerServices.TupleElementNamesAttribute>();
            // var elemNames = nameAttr?.TransformNames?.ToArray() ?? Enumerable.Range(1, elemTypes.Length).Select(i => $"item{i}").ToArray();
            //
            // for (int i = 0; i < elemTypes.Length; ++i)
            // {
            //     var (tr, _) = DiscoverType(state, elemTypes[i]);
            //     fields.Add(new PropertyDefinition(PascalCase(elemNames[i]), tr));
            // }
            // var syntheticName = $"Tuple_{Math.Abs(t.FullName!.GetHashCode()):X}";
            // if (!state.TypeDefinitions.ContainsKey(syntheticName))
            //     state.TypeDefinitions[syntheticName] = new DataClassDefinition(syntheticName, fields);
            // return (new TypeRef.Custom(syntheticName), state.TypeDefinitions[syntheticName]);
        }
        
        throw new NotSupportedException($"Type '{t.FullName}' is not supported for code generation.");
    }
    
    /// <summary>
    /// Returns <c>true</c> for any type that can safely be passed on the query-string
    /// without JSON-encoding, i.e. primitives, enums, and their nullable wrappers.
    /// </summary>
    private static bool IsPrimitiveLike(Type t)
    {
        // Unwrap Nullable<T>
        if (Nullable.GetUnderlyingType(t) is { } inner)
            return IsPrimitiveLike(inner);

        // C# primitive set plus those we map explicitly in TryMapPrimitive
        if (TryMapPrimitive(t, out _))
            return true;

        // Enums serialize as strings or ints – treat as primitive
        if (t.IsEnum)
            return true;

        return false;
    }
    
    private static bool TryMapPrimitive(Type t, out PrimitiveKind? kind)
    {
        kind = t switch
        {
            var v when v == typeof(bool) => PrimitiveKind.Bool,
            var v when v == typeof(int) || v == typeof(long) => PrimitiveKind.Int,
            var v when v == typeof(float) || v == typeof(double) || v == typeof(decimal) => PrimitiveKind.Float,
            var v when v == typeof(string) => PrimitiveKind.String,
            var v when v == typeof(Guid) => PrimitiveKind.Guid,
            var v when v == typeof(TimeOnly) => PrimitiveKind.TimeOnly,
            var v when v == typeof(DateOnly) => PrimitiveKind.DateOnly,
            var v when v == typeof(DateTime) => PrimitiveKind.DateTime,
            var v when v == typeof(DateTimeOffset) => PrimitiveKind.DateTimeOffset,
            var v when v == typeof(JsonElement) => PrimitiveKind.Json,
            var v when v == typeof(JsonNode) => PrimitiveKind.Json,
            var v when v == typeof(JsonObject) => PrimitiveKind.JsonDict,
            _ => null
        };
        return kind.HasValue;
    }
    
    private static string StripInterfaceSuffix(string name)
        => name.StartsWith('I') && name.EndsWith("Api") ? name[1..^3] : name;
    
    private static string GetEntityName(Type iface)
        => iface.Name.StartsWith('I') && iface.Name.EndsWith("Api")
            ? iface.Name[1..^3]
            : iface.Name;
    
    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //
    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    
    private static ControllerDefinition BuildRegularControllerDefinition(State state, Type controllerType)
    {
        // 1. Resolve class‑level route
        var classRouteAttr = controllerType.GetCustomAttribute<RouteAttribute>();
        var controllerName = controllerType.Name.Replace("Controller", "");
        var classTemplate   = classRouteAttr?.Template ?? $"api/{controllerName.ToLowerInvariant()}";
        classTemplate       = classTemplate.Replace("[controller]", controllerName);

        var endpoints = new List<EndpointDefinition>();

        var methods = controllerType.GetMethods(BindingFlags.Instance | BindingFlags.Public | BindingFlags.DeclaredOnly);
        foreach (var m in methods)
        {
            if (!TryGetHttpMethod(m, out var httpMethod, out var methodTemplate))
                continue; // Not an endpoint

            var fullRoute = CombineRoute(classTemplate, methodTemplate);

            // ---- Parameters ----------------------------------------------------
            TypeRef? body = null;
            var queryParams  = new Dictionary<string, TypeRef>();
            var otherParams  = new Dictionary<string, TypeRef>();

            foreach (var p in m.GetParameters())
            {
                var pType = DiscoverType(state, p.ParameterType).typeRef;
                if (p.GetCustomAttribute<FromBodyAttribute>() != null)
                    body = pType;
                else if (p.GetCustomAttribute<FromQueryAttribute>() != null ||
                         (p.GetCustomAttribute<FromRouteAttribute>() == null && IsPrimitiveLike(p.ParameterType)))
                    queryParams[p.Name!] = pType;
                else
                    otherParams[p.Name!] = pType;
            }

            // ---- Return type ---------------------------------------------------
            var outputType = DecodeReturnType(state, m.ReturnType);

            endpoints.Add(new EndpointDefinition(
                Method: httpMethod,
                Route : fullRoute,
                BodyInputType : body,
                QueryParameters : queryParams,
                OtherParameters : otherParams,
                OutputType : outputType));
        }

        return new ControllerDefinition(controllerName, classTemplate, endpoints);
    }
    
    private static bool TryGetHttpMethod(MethodInfo mi, out string method, out string? template)
    {
        method   = "GET"; template = null;
        foreach (var attr in mi.GetCustomAttributes<HttpMethodAttribute>())
        {
            method   = attr.HttpMethods.First();
            template = attr.Template;          // may be null / empty
            return true;
        }
        template = null;
        return false;
    }
    
    private static string CombineRoute(string classTemplate, string? methodTemplate)
    {
        if (string.IsNullOrWhiteSpace(methodTemplate))
            return classTemplate;
        if (methodTemplate.StartsWith('/'))
            return methodTemplate.TrimStart('/');
        return $"{classTemplate}/{methodTemplate}";
    }

    private static TypeRef DecodeReturnType(State state, Type returnType)
    {
        state.Log($"Decoding return type: {returnType.FullName}");
        // Unwrap Task<> / ValueTask<>
        if (returnType.IsGenericType &&
            (returnType.GetGenericTypeDefinition() == typeof(Task<>) ||
             returnType.GetGenericTypeDefinition() == typeof(ValueTask<>)))
            returnType = returnType.GetGenericArguments()[0];

        // Unwrap ActionResult<T>
        if (returnType.IsGenericType &&
            returnType.GetGenericTypeDefinition() == typeof(ActionResult<>))
            returnType = returnType.GetGenericArguments()[0];

        // IActionResult / ActionResult without <T>
        if (typeof(IActionResult).IsAssignableFrom(returnType))
            return DiscoverType(state, typeof(JsonNode)).typeRef; // treat loosely as JSON

        return DiscoverType(state, returnType).typeRef;
    }
}