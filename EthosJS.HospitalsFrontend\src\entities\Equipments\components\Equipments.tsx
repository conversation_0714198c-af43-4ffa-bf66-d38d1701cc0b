//@ts-nocheck
import React, { useEffect, useState } from 'react';
import { <PERSON>ton, Spin } from 'antd';
import { connect } from 'react-redux';
import { EDatabaseActions, EDatabaseTabValues, EOrderDirections, ECommonOrderFields } from 'common/const/enum.const';
import { DatabaseTable } from 'common/components/DatabaseTable';
import { toDataSourseMapper } from 'common/helpers/data.helper';
import { ConfirmationModal } from 'common/components/СonfirmationModal';
import { RootDispatch, RootState, history } from 'app/store';
import { getDatabaseColumns, getDatabasePath } from 'entities/Database/Database.helper';
import { databaseColumns } from 'entities/Database/Database.const';

type AllType = ReturnType<typeof mapState> & ReturnType<typeof mapDispatch>;

const EquipmentsComponent: React.FC<AllType> = (props) => {
  const { equipmentsCollection, equipmentModel, getEquipmentsCollection, clearEquipmentsCollection, deleteEquipmentModel } =
    props;
  const [orderField, setOrderField] = useState<ECommonOrderFields>(ECommonOrderFields.Name);
  const [orderDirection, setOrderDirection] = useState<EOrderDirections | undefined>(EOrderDirections.ASC);
  const [equipmentId, setEquipmentId] = useState<number | null>(null);
  const [open, setOpen] = useState<boolean>(false);
  const { data: equipmentsCollectionData, loading: equipmentsCollectionLoading } = equipmentsCollection;
  const { loading: equipmentModelLoading } = equipmentModel;

  const dataSource = toDataSourseMapper(equipmentsCollectionData?.data);

  const addEquipment = () => {
    const path = getDatabasePath(EDatabaseTabValues.SpecialEquipment, EDatabaseActions.Add);

    history.push(path);
  };

  const onEdit = (id: number) => {
    const path = getDatabasePath(EDatabaseTabValues.SpecialEquipment, EDatabaseActions.Edit, id);

    history.push(path);
  };

  const onDelete = (id: number) => {
    setEquipmentId(id);
    setOpen(true);
  };

  const onYesClick = () => {
    equipmentId &&
      deleteEquipmentModel({
        id: equipmentId,
        onSuccess: () => {
          getEquipmentsCollection({ orderField, orderDirection });
          setEquipmentId(null);
          setOpen(false);
        },
      });
  };

  const onNoClick = () => {
    setEquipmentId(null);
    setOpen(false);
  };

  const handleSort = (orderField: ECommonOrderFields, orderDirection?: EOrderDirections) => {
    setOrderField(orderField);
    setOrderDirection(orderDirection);
  };

  const columns = getDatabaseColumns(databaseColumns, onEdit, onDelete);

  useEffect(() => {
    getEquipmentsCollection({ orderField, orderDirection });

    return () => {
      clearEquipmentsCollection();
    };
  }, [orderField, orderDirection]);

  return (
    <Spin spinning={equipmentsCollectionLoading}>
      <div className="database__container_main-header">
        <Button className="btn-primary" onClick={addEquipment}>
          Add Equipment
        </Button>
      </div>

      <DatabaseTable dataSource={dataSource} columns={columns} sort={handleSort} />

      <ConfirmationModal open={open} loading={equipmentModelLoading} onYesClick={onYesClick} onNoClick={onNoClick} />
    </Spin>
  );
};

const mapState = (state: RootState) => ({
  equipmentsCollection: state.equipmentsCollection,
  equipmentModel: state.equipmentModel,
});
const mapDispatch = (dispatch: RootDispatch) => ({
  getEquipmentsCollection: dispatch.equipmentsCollection.getEquipmentsCollection,
  clearEquipmentsCollection: dispatch.equipmentsCollection.clearEquipmentsCollection,
  deleteEquipmentModel: dispatch.equipmentModel.deleteEquipmentModel,
});

export const Equipments = connect(mapState, mapDispatch)(EquipmentsComponent);
