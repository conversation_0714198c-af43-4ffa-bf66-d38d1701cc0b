using System.Linq.Expressions;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class TechnicianAppointmentDbo : IAuditableEntity<TechnicianAppointmentDbo>
{
    public Guid TechnicianId { get; set; }
    public virtual TechnicianDbo Technician { get; set; } = null!;
    
    public Guid StudyId { get; set; }
    public virtual StudyDbo Study { get; set; } = null!;
    
    public Guid RoomId { get; set; }
    public virtual RoomDbo Room { get; set; } = null!;
    
    public Guid CareLocationShiftId { get; set; }
    public virtual CareLocationShiftDbo CareLocationShift { get; set; } = null!;
    
    public DateOnly Date { get; set; }
    public Guid ScheduledById { get; set; }
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<TechnicianAppointmentDbo>(Register);

    public new static void Register(EntityTypeBuilder<TechnicianAppointmentDbo> entity)
    {
        IAuditableEntity<TechnicianAppointmentDbo>.Register(entity);
        
        entity.HasOne(e => e.Technician)
            .WithMany()
            .HasForeignKey(e => e.TechnicianId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);
        
        entity.HasOne(e => e.Study)
            .WithMany()
            .HasForeignKey(e => e.StudyId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);
        
        entity.HasOne(e => e.Room)
            .WithMany()
            .IsRequired()
            .HasForeignKey(e => e.RoomId);

        entity.HasOne(e => e.CareLocationShift)
            .WithMany()
            .IsRequired()
            .HasForeignKey(e => e.CareLocationShiftId);
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(TechnicianAppointmentQ.WithId), "WithId")]
[JsonDerivedType(typeof(TechnicianAppointmentQ.WithStudyId), "WithStudyId")]
[JsonDerivedType(typeof(TechnicianAppointmentQ.WithOrderId), "WithOrderId")]
[JsonDerivedType(typeof(TechnicianAppointmentQ.WithPatientId), "WithPatientId")]
[JsonDerivedType(typeof(TechnicianAppointmentQ.WithCareLocationId), "WithCareLocationId")]
[JsonDerivedType(typeof(TechnicianAppointmentQ.WithRoomId), "WithRoomId")]
[JsonDerivedType(typeof(TechnicianAppointmentQ.WithCareLocationShiftId), "WithCareLocationShiftId")]
[JsonDerivedType(typeof(TechnicianAppointmentQ.WithDateInRange), "WithDateInRange")]
public abstract record TechnicianAppointmentQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : TechnicianAppointmentQ;
    public sealed record WithStudyId(Guid Id) : TechnicianAppointmentQ;
    public sealed record WithOrderId(Guid Id) : TechnicianAppointmentQ;
    public sealed record WithPatientId(Guid Id) : TechnicianAppointmentQ;
    public sealed record WithCareLocationId(Guid Id) : TechnicianAppointmentQ;
    public sealed record WithRoomId(Guid Id) : TechnicianAppointmentQ;
    public sealed record WithCareLocationShiftId(Guid Id) : TechnicianAppointmentQ;
    public sealed record WithDateInRange(DateOnly Start, DateOnly End) : TechnicianAppointmentQ;

    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            TechnicianAppointmentQ.WithId                   wid => Expression.Equal(self.Get(nameof(TechnicianAppointmentDbo.Id)), Expression.Constant(wid.Id)),
            TechnicianAppointmentQ.WithStudyId              wid => Expression.Equal(self.Get(nameof(TechnicianAppointmentDbo.StudyId)), Expression.Constant(wid.Id)),
            TechnicianAppointmentQ.WithOrderId              wid => Expression.Equal(self.Get(nameof(PatientAppointmentDbo.Study)).Get(nameof(StudyDbo.OrderId)), Expression.Constant(wid.Id)),
            TechnicianAppointmentQ.WithPatientId            wna => Expression.Equal(self.Get(nameof(PatientAppointmentDbo.Study)).Get(nameof(StudyDbo.Order)).Get(nameof(OrderDbo.PatientId)), Expression.Constant(wna.Id)),
            TechnicianAppointmentQ.WithCareLocationId       wcl => Expression.Equal(self.Get(nameof(PatientAppointmentDbo.Room)).Get(nameof(RoomDbo.CareLocationId)), Expression.Constant(wcl.Id)),
            TechnicianAppointmentQ.WithRoomId               wcl => Expression.Equal(self.Get(nameof(TechnicianAppointmentDbo.RoomId)), Expression.Constant(wcl.Id)),
            TechnicianAppointmentQ.WithCareLocationShiftId  wcl => Expression.Equal(self.Get(nameof(TechnicianAppointmentDbo.CareLocationShiftId)), Expression.Constant(wcl.Id)),
            TechnicianAppointmentQ.WithDateInRange range => 
                Expression.AndAlso(
                    Expression.GreaterThanOrEqual(Expression.Property(self, nameof(TechnicianAppointmentDbo.Date)), Expression.Constant(range.Start)),
                    Expression.LessThanOrEqual(Expression.Property(self, nameof(TechnicianAppointmentDbo.Date)), Expression.Constant(range.End))
                ),
            _ => throw new NotSupportedException($"Unsupported {this.GetType()} literal type.")
        };
    }
}