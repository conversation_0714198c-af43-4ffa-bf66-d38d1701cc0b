import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, IsPositive } from 'class-validator';
import { Expose } from 'class-transformer';
import { ETechnicianScheduleShift } from '@app/modules/technicianSchedule/enums';

@Expose()
export class TechnicianStandardScheduleItemDto {
  @ApiProperty({ enum: ETechnicianScheduleShift })
  @IsEnum(ETechnicianScheduleShift)
  shift: ETechnicianScheduleShift;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  @IsPositive()
  capacity?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  @IsPositive()
  facilityId?: number;
}
