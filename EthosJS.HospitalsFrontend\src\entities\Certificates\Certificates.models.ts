//@ts-nocheck
// import '@axmit/persante-hospitals';
import { IRequestSuccess } from 'common/models';

export interface ICertificateCollectionPayload {}
export interface ICertificateCollectionDto {}

export interface ICertificateCollection {
  data: ICertificateCollectionDto | null;
  loading: boolean;
}

export interface ICertificateModelCreatePayload extends IRequestSuccess {}
export interface ICertificateModelUpdatePayload extends IRequestSuccess {}
export interface ICertificateModelDeletePayload extends IRequestSuccess {}
export interface ICertificateDto{}

export interface ICertificateModel {
  data: ICertificateDto | null;
  loading: boolean;
}
