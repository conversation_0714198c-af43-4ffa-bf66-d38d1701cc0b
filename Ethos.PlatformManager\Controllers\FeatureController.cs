using Ethos.Auth;
using Ethos.Utilities;
using Ethos.Utilities.Filtering;
using Ethos.Utilities.Pagination;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.PlatformManager.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("api/features")]
    [Authorize]
    [EthosAuthFeature(Name = FeatureConstants.Core)]
    public class FeatureController : ControllerBase
    {
        readonly ILogger<FeatureController> _logger;
        readonly PlatformManagerDbContext _dbContext;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="dbContext"></param>
        public FeatureController(ILogger<FeatureController> logger, PlatformManagerDbContext dbContext)
        {
            _logger = logger;
            _dbContext = dbContext;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="feature"></param>
        /// <returns></returns>
        [HttpPost]
        [EthosAuthScope(ScopeDefinitions.FeatureWrite)]
        public async Task<ActionResult<FeatureDto>> CreateFeature([FromBody] FeatureDto feature)
        {
            var errors = new Dictionary<string, string[]>();

            if (string.IsNullOrEmpty(feature.Name))
                errors.Add("name", ["Feature name is required."]);

            if (string.IsNullOrEmpty(feature.ScopePrefix))
                errors.Add("scopePrefix", ["Feature scope prefix is required."]);

            if (errors.Count > 0)
                return this.EthosErrorBadRequest("One or more errors occurred.", errors);

            if (!feature.Id.HasValue || feature.Id.Value == Guid.Empty)
                feature.Id = Guid.NewGuid();

            var existingFeat = _dbContext.Features.FirstOrDefault(l => l.Id == feature.Id || string.Equals(feature.Name.ToLower(), l.Name.ToLower()));

            if (existingFeat is not null)
                return this.EthosErrorConflict($"Feature '{existingFeat.Name}' already exists with ID {feature.Id}.");

            var feat = new Feature()
            {
                Id = feature.Id.Value,
                Name = feature.Name,
                ScopePrefix = feature.ScopePrefix,
            };

            _dbContext.Features.Add(feat);
            await _dbContext.SaveChangesAsync();
            return CreatedAtAction(nameof(GetFeature), new { featureId = feat.Id }, GetFeatureDto(feat));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.FeatureRead)]
        public async Task<ActionResult<PagedResponse<FeatureDto>>> GetFeatures([FromQuery] PagingParameters pagingParameters, [FromQuery] string? filter = null)
        {
            var features = _dbContext.Features.Filter(filter).OrderBy(f => f.Name);

            return Ok(await features.PaginateWithLinksAsync(this, (fs) =>
            {
                return fs.Select(f => GetFeatureDto(f)).ToList();
            }, pagingParameters.limit, pagingParameters.offset));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="featureId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{featureId}")]
        [EthosAuthScope(ScopeDefinitions.FeatureRead)]
        public async Task<ActionResult<FeatureDto>> GetFeature([FromRoute] Guid featureId)
        {
            if (featureId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid feature ID.");

            var feature = await _dbContext.Features.FirstOrDefaultAsync(f => f.Id == featureId);

            if (feature is null)
                return this.EthosErrorNotFound($"Feature not found with ID {featureId}.");

            return Ok(GetFeatureDto(feature));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="featureId"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{featureId}")]
        [EthosAuthScope(ScopeDefinitions.FeatureDelete)]
        public async Task<IActionResult> DeleteFeature([FromRoute] Guid featureId)
        {
            if (featureId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid feature ID.");

            var existingFeature = _dbContext.Features.FirstOrDefault(f => f.Id == featureId);

            if (existingFeature is null)
                return this.EthosErrorNotFound($"Feature not found with ID {featureId}.");

            _dbContext.Features.Remove(existingFeature);
            await _dbContext.SaveChangesAsync();
            return NoContent();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="feature"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{featureId}")]
        [EthosAuthScope(ScopeDefinitions.FeatureWrite)]
        public async Task<ActionResult<FeatureDto>> UpdateFeature([FromRoute] Guid featureId, [FromBody] FeatureDto feature)
        {
            if (featureId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid feature ID.");

            var existingFeature = _dbContext.Features.FirstOrDefault(f => f.Id == featureId);

            if (existingFeature is null)
                return this.EthosErrorNotFound($"Feature not found with ID {featureId}.");

            if (string.IsNullOrEmpty(feature.Name))
                feature.Name = existingFeature.Name;

            if (string.IsNullOrEmpty(feature.ScopePrefix))
                feature.ScopePrefix = existingFeature.ScopePrefix;

            var existingFeatureByName = _dbContext.Features.FirstOrDefault(l => l.Id != featureId && string.Equals(feature.Name.ToLower(), l.Name.ToLower()));
            if (existingFeatureByName is not null)
                return this.EthosErrorConflict($"Feature '{feature.Name}' already exists with ID {existingFeatureByName.Id}.");

            existingFeature.Name = feature.Name;
            existingFeature.ScopePrefix = feature.ScopePrefix;

            _dbContext.Features.Update(existingFeature);
            await _dbContext.SaveChangesAsync();
            return Ok(GetFeatureDto(existingFeature));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="f"></param>
        /// <returns></returns>
        static FeatureDto GetFeatureDto(Feature f)
        {
            return new FeatureDto()
            {
                Name = f.Name,
                ScopePrefix = f.ScopePrefix,
                Id = f.Id,
            };
        }
    }
}
