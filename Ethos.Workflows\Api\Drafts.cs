using System.Collections.Immutable;
using System.Text.Json.Nodes;
using Ethos.Model;
using Ethos.Utilities;

namespace Ethos.Workflows.Api;

public sealed record CreateDraftDto : IInputDto
{
    public required EntityType EntityType { get; set; }
    public required Guid EntityId { get; set; }
    public required JsonObject Data { get; set; }
}

public sealed record DraftDto
{
    public required Guid Id { get; set; }
    public required EntityType EntityType { get; set; }
    public required Guid EntityId { get; set; }
    public required JsonObject Data { get; set; }
}

public sealed record IssueDto
{
    public required IReadOnlyList<string> Paths { get; set; }
    public required string Message { get; set; }
    public required Guid? IssueId { get; set; }
    public required JsonObject? Data { get; set; }
}

/// <summary>
/// A DTO for API responses when creating or modifying a draft,
/// including the draft's data and any validation issues found.
/// </summary>
public sealed record ValidatedDraftDto
{
    public Guid Id { get; set; }
    public EntityType EntityType { get; set; }
    public Guid EntityId { get; set; }
    public JsonObject Data { get; set; } = null!;
    public IReadOnlyList<IssueDto>? Errors { get; set; }
    public IReadOnlyList<IssueDto>? Warnings { get; set; }
}

public interface IDraftApi : IEntityHttpClient<CreateDraftDto, DraftDto, DraftQ>;

public class DraftHttpClient(System.Net.Http.HttpClient httpClient)
    : EntityHttpClientBase<CreateDraftDto, DraftDto, DraftQ>(httpClient, "draft"),
        IDraftApi;