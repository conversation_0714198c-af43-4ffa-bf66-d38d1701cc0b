import { AuthGuard } from '@app/common/guards/auth.guard';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { ScheduleService } from '@app/modules/schedule/schedule.service';
import { ScheduleCollectionDto } from '@app/modules/schedule/dto/schedule.collection.dto';
import { ScheduleFiltersDto } from '@app/modules/schedule/dto/schedule.filters.dto';
import { CreateScheduleDto } from '@app/modules/schedule/dto/create.schedule.dto';
import { DeleteScheduleDto } from '@app/modules/schedule/dto/delete.schedule.dto';
import { ScheduleDto } from '@app/modules/schedule/dto/schedule.dto';

@Controller('schedule')
@ApiTags('Schedules')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class ScheduleController {
  constructor(private readonly service: ScheduleService) {
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: ScheduleCollectionDto,
    description: 'Get list of schedules',
  })
  async list(@Query() filters: ScheduleFiltersDto): Promise<ScheduleCollectionDto> {
    return this.service.list(filters);
  }

  @Get('/:scheduleId')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: ScheduleDto,
    description: 'Get schedule by id',
  })
  async getById(@Param('scheduleId', new ParseIntPipe()) scheduleId: number): Promise<ScheduleDto> {
    const schedule = await this.service.getByIdOrFail(scheduleId);

    return ScheduleService.mapToDto(schedule);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOkResponse({
    type: ScheduleDto,
    description: 'Create schedule',
  })
  async create(@Body() createScheduleDto: CreateScheduleDto): Promise<ScheduleDto> {
    const schedule = await this.service.create(createScheduleDto);

    return ScheduleService.mapToDto(schedule);
  }

  @Delete()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: ScheduleDto,
    description: 'Delete schedule',
  })
  async delete(@Body() { id }: DeleteScheduleDto): Promise<ScheduleDto> {
    const schedule = await this.service.delete(id);

    return ScheduleService.mapToDto(schedule);
  }
}
