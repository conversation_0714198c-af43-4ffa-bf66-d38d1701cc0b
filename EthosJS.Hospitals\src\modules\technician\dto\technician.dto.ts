import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { TechnicianStandardScheduleDto } from '@app/modules/technician/dto/technician.standard.schedule.dto';
import { BaseDto } from '@app/common/dto/base.dto';
import { TechnicianCredentialDto } from '@app/modules/technician/dto/technician.credential.dto';

export class TechnicianDto extends BaseDto {
  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  capacity: number;

  @ApiProperty({ type: TechnicianStandardScheduleDto })
  @Type(() => TechnicianStandardScheduleDto)
  @Expose()
  standardSchedule: TechnicianStandardScheduleDto;

  @ApiProperty()
  @Expose()
  clinicId: number;

  @ApiProperty()
  @Expose()
  clinicName: string;

  @ApiProperty({ isArray: true, type: TechnicianCredentialDto })
  @Type(() => TechnicianCredentialDto)
  @Expose()
  credentials: TechnicianCredentialDto[];
}
