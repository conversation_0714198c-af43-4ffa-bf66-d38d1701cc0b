import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { Column, Entity, ManyToOne } from 'typeorm';
import { BaseEntity } from '@app/common/base.entity';
import { CredentialEntity } from '@app/modules/credential/credential.entity';
import { TechnicianEntity } from '@app/modules/technician/technician.entity';

@Entity({ name: 'technician_credentials' })
export class TechnicianCredentialEntity extends BaseEntity {
  @Column({ type: 'date', nullable: true })
  @ApiPropertyOptional()
  @Expose()
  validUntil?: string;

  @Column()
  @ApiProperty()
  @Expose()
  credentialId: number;

  @ManyToOne(() => CredentialEntity)
  credential: CredentialEntity;

  @Column()
  @ApiProperty()
  @Expose()
  technicianId: number;

  @ManyToOne(() => TechnicianEntity)
  technician: TechnicianEntity;
}
