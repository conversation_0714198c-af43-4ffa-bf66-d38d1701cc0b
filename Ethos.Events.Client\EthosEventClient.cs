﻿using System.Diagnostics;
using System.Text.Json;
using Ethos.Auth;
using Ethos.Model;
using Ethos.Utilities.Pagination;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Ethos.Events.Client
{
    /// <summary>
    /// 
    /// </summary>
    public interface IEthosEventClient : IWebClient
    {
        Task<Guid?> CreateEvent(EthosEventDto evt);
        Task<Guid?> CreateEventWithData<T>(EthosEventDto<T> evt);
        Task<Guid?> CreateEvent(string type, string name, string? code, IDictionary<string, string>? correlationIds);
        Task<Guid?> CreateEvent<TEntity>(string type,
                                         string name,
                                         string? code,
                                         HttpContext httpContext,
                                         TEntity entity) where TEntity : IAuditableEntity<TEntity>;
        Task<Guid?> CreateEvent<TEntity>(string type,
                                         string name,
                                         string? code,
                                         HttpContext httpContext,
                                         params TEntity[] entities) where TEntity : IAuditableEntity<TEntity>;

        Task<Guid?> CreateEvent<TEntity>(string type, string name, HttpContext httpContext,
                                         params TEntity[] entities) where TEntity : IAuditableEntity<TEntity>;

        Task<Guid?> CreateEvent(string type, string name, HttpContext httpContext);
        Task<Guid?> CreateEventWithData<T>(string type, string name, T eventData, HttpContext httpContext);
        Task<Guid?> CreateExceptionEvent(params Exception[] exes);
        Task<Guid?> CreateExceptionEvent(HttpContext httpContext, params Exception[] exes);
        Task<EthosEventDto?> GetEventById(Guid eventId);
        Task<EthosEventDto<T>?> GetEventWithDataById<T>(Guid eventId);
        Task<List<EthosEventDto>> GetEvents(string? filter);
        Task<List<EthosEventDto<T>>> GetEvents<T>(string? filter);
    }

    /// <summary>
    /// 
    /// </summary>
    public static class EthosEventClientExtensions
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="serviceCollection"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static IHttpClientBuilder AddEthosEventClient(this IServiceCollection serviceCollection, IConfiguration configuration)
        {
            var baseUri = configuration.GetValue<string>("Events:BaseUri");
            if (string.IsNullOrEmpty(baseUri))
                throw new Exception("No base URI found in configuration for event service.");
            return serviceCollection.AddEthosEventClient(baseUri);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="serviceCollection"></param>
        /// <param name="baseUri"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static IHttpClientBuilder AddEthosEventClient(this IServiceCollection serviceCollection, string baseUri)
        {
            if (string.IsNullOrEmpty(baseUri))
                throw new ArgumentException("Base URI is required for event service.", nameof(baseUri));
            if (!Uri.TryCreate(baseUri, UriKind.Absolute, out var _baseUri) || _baseUri is null)
                throw new ArgumentException($"Invalid base URI for event service: {baseUri}", nameof(baseUri));

            return serviceCollection.AddHttpClient<IEthosEventClient, EthosEventClient>()
                                    .ConfigureHttpClient(client =>
            {
                client.BaseAddress = _baseUri;
            });
        }
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosEventClient : WebClient, IWebClient, IEthosEventClient
    {
        const string ApiUri = "/api/events";
        const string limit = nameof(limit);
        const string offset = nameof(offset);
        readonly ILogger<EthosEventClient> _logger;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="httpClient"></param>
        public EthosEventClient(HttpClient httpClient, ILogger<EthosEventClient> logger) : base(httpClient)
        {
            _logger = logger;
            if (httpClient.BaseAddress is not null)
                BaseUri = httpClient.BaseAddress.ToString();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="path"></param>
        /// <param name="queryParams"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Uri CreateUri(string path, Dictionary<string, string?>? queryParams = null)
        {
            if (string.IsNullOrEmpty(BaseUri))
            {
                _logger.LogError("No base URI defined for event service: events will not be logged.");
                throw new Exception("No base URI found for event service.");
            }

            var uriBuilder = new UriBuilder(BaseUri)
            {
                Path = path
            };

            if (queryParams != null && queryParams.Count > 0)
                uriBuilder.Query = QueryHelpers.AddQueryString(uriBuilder.Query, queryParams);

            return uriBuilder.Uri;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="uriPath"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        async Task<List<T>> GetPagedResult<T>(string uriPath, Dictionary<string, string?>? query = null)
        {
            var list = new List<T>();
            query ??= [];

            var offset = 0;

            if (!query.TryGetValue(limit, out _))
                query.Add(limit, "250");

            if (!query.TryGetValue(nameof(offset), out _))
                query.Add(nameof(offset), offset.ToString());

            var totalCount = 0;
            var uri = CreateUri(uriPath, query);

            do
            {
                var result = await GetStringAsync(uri).ConfigureAwait(false);

                if (string.IsNullOrEmpty(result))
                    break;

                var parsedResult = JsonSerializer.Deserialize<PagedResponse<T>>(result, JsonOptions);
                if (parsedResult != null)
                {
                    totalCount = parsedResult?.TotalCount ?? 0;
                    if (parsedResult?.Items.Count > 0)
                        list.AddRange(parsedResult.Items);

                    if (!int.TryParse(query[nameof(offset)], out offset))
                        break;

                    query[nameof(offset)] = (offset + limit).ToString();
                    uri = CreateUri(uriPath, query);

                }
            } while (list.Count < totalCount && uri != null);

            return list;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setName"></param>
        /// <param name="version"></param>
        /// <param name="keyName"></param>
        /// <param name="items"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<Guid?> CreateEvent(EthosEventDto evt)
        {
            return await InternalCreateEvent(evt).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="type"></param>
        /// <param name="name"></param>
        /// <param name="code"></param>
        /// <param name="correlationIds"></param>
        /// <returns></returns>
        public async Task<Guid?> CreateEvent(string type, string name, string? code, IDictionary<string, string>? correlationIds = null)
        {
            var evt = new EthosEventDto()
            {
                Type = type,
                Name = name,
                Code = code,
                CorrelationIds = [..correlationIds?.Select(c => new EthosEventCorrelationIdDto()
                {
                     Type = c.Key,
                     Value = c.Value
                }) ?? []]
            };
            return await CreateEvent(evt).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="jsonContent"></param>
        /// <returns></returns>
        async Task<Guid?> InternalCreateEvent(EthosEventDto evt)
        {
            ValidateEvent(evt, nameof(evt));
            if (_logger.IsEnabled(LogLevel.Trace))
                _logger.LogTrace("Creating new event: {Payload}", JsonSerializer.Serialize(evt, JsonOptions));
            var result = await PostJsonAsync(CreateUri(ApiUri), evt);
            var eventDto = JsonSerializer.Deserialize<EthosEventDto>(await result.Content.ReadAsStringAsync(), JsonOptions);
            if (_logger.IsEnabled(LogLevel.Debug) && eventDto is not null && eventDto.Id.HasValue)
                _logger.LogDebug("Created new event of type {EventType} with ID {EventId}.", evt.Type, eventDto?.Id);
            return eventDto?.Id;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="evt"></param>
        /// <returns></returns>
        async Task<Guid?> InternalCreateEventWithData<T>(EthosEventDto<T> evt)
        {
            ValidateEvent(evt, nameof(evt));
            if (_logger.IsEnabled(LogLevel.Trace))
                _logger.LogTrace("Creating new event: {Payload}", JsonSerializer.Serialize(evt, JsonOptions));
            var result = await PostJsonAsync(CreateUri(ApiUri), evt);
            var eventDto = JsonSerializer.Deserialize<EthosEventDto>(await result.Content.ReadAsStringAsync(), JsonOptions);
            if (_logger.IsEnabled(LogLevel.Debug) && eventDto is not null && eventDto.Id.HasValue)
                _logger.LogDebug("Created new event of type {EventType} with ID {EventId}.", evt.Type, eventDto?.Id);
            return eventDto?.Id;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TEntity"></typeparam>
        /// <param name="type"></param>
        /// <param name="name"></param>
        /// <param name="code"></param>
        /// <param name="httpContext"></param>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<Guid?> CreateEvent<TEntity>(string type,
                                                      string name,
                                                      string? code,
                                                      HttpContext httpContext,
                                                      TEntity entity) where TEntity : IAuditableEntity<TEntity>
        {
            return await CreateEvent(type, name, code, httpContext, [entity]).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TEntity"></typeparam>
        /// <param name="type"></param>
        /// <param name="name"></param>
        /// <param name="code"></param>
        /// <param name="httpContext"></param>
        /// <param name="entities"></param>
        /// <returns></returns>
        public async Task<Guid?> CreateEvent<TEntity>(string type,
                                                      string name,
                                                      string? code,
                                                      HttpContext httpContext,
                                                      params TEntity[] entities) where TEntity : IAuditableEntity<TEntity>
        {
            var correlationIds = new List<EthosEventCorrelationIdDto>()
            {
                new()
                {
                    Type = "http",
                    Value = httpContext.TraceIdentifier,
                },
            };

            correlationIds.AddRange(entities.Select(e => new EthosEventCorrelationIdDto()
            {
                Type = typeof(TEntity).Name,
                Value = e.Id.ToString()
            }));

            var evt = new EthosEventDto()
            {
                Type = type,
                Name = name,
                Code = code,
                CorrelationIds = [.. correlationIds]
            };
            return await CreateEvent(evt).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="evt"></param>
        /// <param name="parameterName"></param>
        /// <exception cref="ArgumentException"></exception>
        void ValidateEvent(IEthosEventDto evt, string? parameterName = null)
        {
            parameterName = parameterName ?? nameof(evt);
            if (string.IsNullOrEmpty(evt.Type))
                throw new ArgumentException($"Event type is required.", parameterName);
            if (string.IsNullOrEmpty(evt.Name))
                throw new ArgumentException($"Event name is required.", parameterName);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="evt"></param>
        /// <returns></returns>
        public async Task<Guid?> CreateEventWithData<T>(EthosEventDto<T> evt)
        {
            return await InternalCreateEventWithData(evt).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="httpContext"></param>
        /// <returns></returns>
        EthosEventDto<List<Exception>> GetExceptionEvent(IList<Exception> exceptions, HttpContext? httpContext = null)
        {
            var evt = new EthosEventDto<List<Exception>>()
            {
                EventData = [.. exceptions],
                Type = "Exception",
                Name = "One or more exceptions occurred",
            };

            if (!string.IsNullOrEmpty(httpContext?.TraceIdentifier))
                evt.CorrelationIds = [.. evt.CorrelationIds, new() { Type = "http", Value = httpContext.TraceIdentifier }];

            if (_logger.IsEnabled(LogLevel.Error))
            {
                foreach (var ex in exceptions)
                {
                    if (httpContext != null)
                        _logger.LogError(ex, "[{Controller}] {TraceId} - {Url}", 
                                         httpContext.GetEndpoint()?.GetControllerName(), httpContext.TraceIdentifier, httpContext.Request.GetDisplayUrl());
                    else
                        _logger.LogError(ex, "{Message}", ex.Message);
                }
            }

            foreach (var ex in exceptions)
            {
                var msg = httpContext != null ? $"[{httpContext.GetEndpoint()?.GetControllerName()}] {httpContext.TraceIdentifier} - {httpContext.Request.GetDisplayUrl()} - {ex}" :
                    ex.ToString();
                Debug.WriteLine(msg);
                Console.WriteLine(msg);
            }
            return evt;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="exes"></param>
        /// <returns></returns>
        public async Task<Guid?> CreateExceptionEvent(params Exception[] exes)
        {
            return await CreateEventWithData(GetExceptionEvent([..exes], null)).ConfigureAwait(false);
        }
      
        /// <summary>
        /// 
        /// </summary>
        /// <param name="httpContext"></param>
        /// <param name="exes"></param>
        /// <returns></returns>
        public async Task<Guid?> CreateExceptionEvent(HttpContext httpContext, params Exception[] exes)
        {
            return await CreateEventWithData(GetExceptionEvent([..exes], httpContext)).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="eventId"></param>
        /// <returns></returns>
        public async Task<EthosEventDto?> GetEventById(Guid eventId)
        {
            if (eventId == Guid.Empty)
                return default;
            return await GetJsonAsync<EthosEventDto>(CreateUri($"{ApiUri}/{eventId}")).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="eventId"></param>
        /// <returns></returns>
        public async Task<EthosEventDto<T>?> GetEventWithDataById<T>(Guid eventId)
        {
            if (eventId == default)
                return default;
            return await GetJsonAsync<EthosEventDto<T>>(CreateUri($"{ApiUri}/{eventId}")).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        public async Task <List<EthosEventDto>> GetEvents(string? filter = null)
        {
            var query = new Dictionary<string, string?>();
            if (!string.IsNullOrEmpty(filter))
                query.Add("filter", filter);
            var result = await GetPagedResult<EthosEventDto>(ApiUri, query).ConfigureAwait(false);
            return result ?? [];
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="filter"></param>
        /// <returns></returns>
        public async Task<List<EthosEventDto<T>>> GetEvents<T>(string? filter = null)
        {
            var query = new Dictionary<string, string?>();
            if (!string.IsNullOrEmpty(filter))
                query.Add("filter", filter);
            var result = await GetPagedResult<EthosEventDto<T>>(ApiUri, query).ConfigureAwait(false);
            return result ?? [];
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TEntity"></typeparam>
        /// <param name="type"></param>
        /// <param name="name"></param>
        /// <param name="httpContext"></param>
        /// <param name="entities"></param>
        /// <returns></returns>
        public async Task<Guid?> CreateEvent<TEntity>(string type, string name, HttpContext httpContext, params TEntity[] entities) where TEntity : IAuditableEntity<TEntity>
        {
            return await CreateEvent(type, name, null, httpContext, entities).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TEntity"></typeparam>
        /// <param name="type"></param>
        /// <param name="name"></param>
        /// <param name="httpContext"></param>
        /// <returns></returns>
        public async Task<Guid?> CreateEvent(string type, string name, HttpContext httpContext)
        {
            var correlationIds = new List<EthosEventCorrelationIdDto>()
            {
                new()
                {
                    Type = "http",
                    Value = httpContext.TraceIdentifier,
                },
            };

            var evt = new EthosEventDto()
            {
                Type = type,
                Name = name,
                CorrelationIds = [.. correlationIds]
            };
            return await CreateEvent(evt).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="type"></param>
        /// <param name="name"></param>
        /// <param name="eventData"></param>
        /// <param name="httpContext"></param>
        /// <returns></returns>
        public async Task<Guid?> CreateEventWithData<T>(string type, string name, T eventData, HttpContext httpContext)
        {
            var correlationIds = new List<EthosEventCorrelationIdDto>()
            {
                new()
                {
                    Type = "http",
                    Value = httpContext.TraceIdentifier,
                },
            };
            return await CreateEventWithData(new EthosEventDto<T> { Type = type, Name = name, EventData = eventData, CorrelationIds = correlationIds  });
        }
    }
}
