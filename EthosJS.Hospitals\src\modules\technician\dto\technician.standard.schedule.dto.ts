import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, ValidateNested } from 'class-validator';
import { Expose, Type } from 'class-transformer';
import { EDayWeek } from '@app/common/enums';
import { TechnicianStandardScheduleItemDto } from '@app/modules/technician/dto/technician.standard.schedule.item.dto';

@Expose()
export class TechnicianStandardScheduleDto {
  @ApiPropertyOptional({ type: TechnicianStandardScheduleItemDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => TechnicianStandardScheduleItemDto)
  [EDayWeek.Monday]?: TechnicianStandardScheduleItemDto;

  @ApiPropertyOptional({ type: TechnicianStandardScheduleItemDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => TechnicianStandardScheduleItemDto)
  [EDayWeek.Tuesday]?: TechnicianStandardScheduleItemDto;

  @ApiPropertyOptional({ type: TechnicianStandardScheduleItemDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => TechnicianStandardScheduleItemDto)
  [EDayWeek.Wednesday]?: TechnicianStandardScheduleItemDto;

  @ApiPropertyOptional({ type: TechnicianStandardScheduleItemDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => TechnicianStandardScheduleItemDto)
  [EDayWeek.Thursday]?: TechnicianStandardScheduleItemDto;

  @ApiPropertyOptional({ type: TechnicianStandardScheduleItemDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => TechnicianStandardScheduleItemDto)
  [EDayWeek.Friday]?: TechnicianStandardScheduleItemDto;

  @ApiPropertyOptional({ type: TechnicianStandardScheduleItemDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => TechnicianStandardScheduleItemDto)
  [EDayWeek.Saturday]?: TechnicianStandardScheduleItemDto;

  @ApiPropertyOptional({ type: TechnicianStandardScheduleItemDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => TechnicianStandardScheduleItemDto)
  [EDayWeek.Sunday]?: TechnicianStandardScheduleItemDto;
}
