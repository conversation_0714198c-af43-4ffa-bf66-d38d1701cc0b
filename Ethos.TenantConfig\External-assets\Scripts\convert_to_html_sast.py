import json
import sys
from collections import defaultdict
from datetime import datetime

def main(sarif_path, html_path):
    with open(sarif_path, 'r', encoding='utf-8') as f:
        sarif = json.load(f)

    # Group results by level
    grouped = defaultdict(list)
    runs = sarif.get('runs', [])
    for run in runs:
        tool = run.get('tool', {}).get('driver', {}).get('name', 'Unknown Tool')
        rules = {rule['id']: rule for rule in run.get('tool', {}).get('driver', {}).get('rules', [])}
        for result in run.get('results', []):
            rule_id = result.get('ruleId', 'N/A')
            rule = rules.get(rule_id, {})
            message = result.get('message', {}).get('text', '')
            level = result.get('level', 'warning')
            locations = result.get('locations', [])
            if locations:
                loc = locations[0].get('physicalLocation', {})
                file = loc.get('artifactLocation', {}).get('uri', 'N/A')
                line = loc.get('region', {}).get('startLine', 'N/A')
            else:
                file = 'N/A'
                line = 'N/A'
            grouped[level].append({
                'tool': tool,
                'rule_id': rule_id,
                'rule_name': rule.get('name', ''),
                'message': message,
                'file': file,
                'line': line,
                'level': level
            })

    # Order of severity
    severity_order = ['error', 'warning', 'note']
    severity_labels = {'error': 'Error', 'warning': 'Warning', 'note': 'Note'}
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    error_count = len(grouped['error'])
    warning_count = len(grouped['warning'])
    note_count = len(grouped['note'])
    html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset=\"UTF-8\">
    <title>SAST SCAN Report - {now}</title>
    <style>
        table {{ border-collapse: collapse; width: 100%; margin-bottom: 30px; }}
        th, td {{ border: 1px solid #ccc; padding: 8px; }}
        th {{ background: #f2f2f2; }}
        tr:nth-child(even) {{ background: #f9f9f9; }}
        .summary {{ font-size: 1.2em; margin-top: 30px; }}
        .summary-table {{ width: 300px; margin-bottom: 30px; }}
        .top-flex {{ display: flex; flex-direction: row; justify-content: flex-start; align-items: flex-start; gap: 40px; margin-bottom: 30px; }}
        .chart-container {{ text-align: right; margin-left: auto; margin-right: auto;}}
    </style>
    <!-- Chart.js CDN -->
    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>
</head>
<body>
    <h1>SAST Scan Report - Ethos.TenantConfig  </h1>
    <h4>Document generated on: <b>{now}</b></h4>
    <div class=\"top-flex\">
        <table class=\"summary-table\">
            <tr><th>Severity</th><th>Count</th></tr>
            <tr><td>Error</td><td>{error_count}</td></tr>
            <tr><td>Warning</td><td>{warning_count}</td></tr>
            <tr><td>Note</td><td>{note_count}</td></tr>
        </table>
        <div class=\"chart-container\">
            <canvas id=\"pieChart\" width=\"300\" height=\"300\"></canvas>
        </div>
    </div>
"""

    # Add tables for each severity
    for sev in severity_order:
        items = grouped.get(sev, [])
        html += f'<div class="summary"><b>{severity_labels[sev]}s: {len(items)}</b></div>'
        html += f'<table>'
        html += '<tr><th>Rule ID</th><th>Rule Name</th><th>Message</th><th>File</th><th>Line</th></tr>'
        if items:
            for r in items:
                html += f'<tr>'
                html += f'<td>{r["rule_id"]}</td>'
                html += f'<td>{r["rule_name"]}</td>'
                html += f'<td>{r["message"]}</td>'
                html += f'<td>{r["file"]}</td>'
                html += f'<td>{r["line"]}</td>'
                html += f'</tr>'
        else:
            html += '<tr><td colspan="5" style="text-align:center;">No results.</td></tr>'
        html += '</table>'

    html += """
<script>
    var ctx = document.getElementById('pieChart').getContext('2d');
    var pieChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: ['Error', 'Warning', 'Note'],
            datasets: [{
                data: [%d, %d, %d],
                backgroundColor: [
                    'rgba(220, 53, 69, 0.7)',    // Error - red
                    'rgba(255, 193, 7, 0.7)',     // Warning - yellow
                    'rgba(23, 162, 184, 0.7)'     // Note - blue
                ],
                borderColor: [
                    'rgba(220, 53, 69, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(23, 162, 184, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'bottom'
                }
            }
        }
    });
</script>
</body>
</html>
""" % (error_count, warning_count, note_count)

    with open(html_path, 'w', encoding='utf-8') as f:
        f.write(html)

    print(f"HTML report generated: {html_path}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python sarif_to_table_html.py <input.sarif> <output.html>")
        sys.exit(1)
    main(sys.argv[1], sys.argv[2]) 