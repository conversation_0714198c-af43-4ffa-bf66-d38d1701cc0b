import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TechnicianScheduleRepository } from '@app/modules/technicianSchedule/technician.schedule.repository';
import { TechnicianScheduleService } from '@app/modules/technicianSchedule/technician.schedule.service';
import { TechnicianScheduleController } from '@app/modules/technicianSchedule/technician.schedule.controller';
import { FacilityModule } from '@app/modules/facility/facility.module';
import { TechnicianModule } from '@app/modules/technician/technician.module';

@Module({
  imports: [
    FacilityModule,
    TechnicianModule,
    TypeOrmModule.forFeature([TechnicianScheduleRepository]),
  ],
  providers: [TechnicianScheduleService],
  controllers: [TechnicianScheduleController],
  exports: [TechnicianScheduleService],
})
export class TechnicianScheduleModule {}
