using System.Linq.Expressions;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class TechnicianQualificationDbo // Suitable for OwnsOne
{
    public required long QualificationId { get; set; }
    public required DateOnly? DateObtained { get; set; }
    public required DateOnly? DateExpires { get; set; }

    // Configure method used by OwnsOne
    public static void Configure(OwnedNavigationBuilder<TechnicianDbo, TechnicianQualificationDbo> builder)
    {
        builder.Property(p => p.QualificationId).IsRequired();
        builder.Property(p => p.DateObtained).IsRequired(false);
        builder.Property(p => p.DateExpires).IsRequired(false);
        // Validation > 0 etc., in application layer
    }
}

public class TechnicianDbo : IAuditableEntity<TechnicianDbo>
{
    public virtual ICollection<PersonNameDbo> Names { get; set; } = new List<PersonNameDbo>();
    public DemographicsDbo? Demographics { get; set; }
    public ICollection<IdentifierDbo> Identifiers { get; set; } = new List<IdentifierDbo>();
    public Guid? ContactDetailId { get; set; }
    public PersonalContactDetailDbo? ContactDetail { get; set; } = null!;
    
    public virtual ICollection<TechnicianQualificationDbo> Qualifications { get; set; } = new List<TechnicianQualificationDbo>();
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<TechnicianDbo>(Register);

    public new static void Register(EntityTypeBuilder<TechnicianDbo> entity)
    {
        IAuditableEntity<TechnicianDbo>.Register(entity);
        
        entity.OwnsMany(p => p.Names, names =>
        {
            names.ToTable($"{nameof(TechnicianDbo)}_{nameof(Names)}", IEntity.DefaultSchema);
            names.WithOwner().HasForeignKey($"{nameof(TechnicianDbo)}Id");
            names.Property<int>("Id").ValueGeneratedOnAdd();
            names.HasKey("Id");
            PersonNameDbo.Configure(names);
        });
        
        entity.OwnsOne(p => p.Demographics, builder =>
        {
            builder.ToTable($"{nameof(TechnicianDbo)}_{nameof(Demographics)}", IEntity.DefaultSchema);
            builder.WithOwner().HasForeignKey($"{nameof(TechnicianDbo)}Id");
            DemographicsDbo.Configure(builder);
        });
        
        entity.OwnsMany(p => p.Identifiers, identifiers =>
        {
            identifiers.ToTable($"{nameof(TechnicianDbo)}_{nameof(Identifiers)}", IEntity.DefaultSchema);
            identifiers.WithOwner().HasForeignKey($"{nameof(TechnicianDbo)}Id");
            identifiers.Property<int>("Id").ValueGeneratedOnAdd();
            identifiers.HasKey("Id");
            IdentifierDbo.Configure(identifiers);
        });
        
        entity.OwnsMany(p => p.Qualifications, qualifications =>
        {
            qualifications.ToTable($"{nameof(TechnicianDbo)}_{nameof(Qualifications)}", IEntity.DefaultSchema);
            qualifications.WithOwner().HasForeignKey($"{nameof(TechnicianDbo)}Id");
            TechnicianQualificationDbo.Configure(qualifications);
        });
        
        entity.HasOne(s => s.ContactDetail)
            .WithMany()
            .HasForeignKey("ContactDetailId")
            .IsRequired(false)
            .HasPrincipalKey(c => c.Id);
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(TechnicianQ.WithId), "WithId")]
[JsonDerivedType(typeof(TechnicianQ.WithFirstName), "WithFirstName")]
[JsonDerivedType(typeof(TechnicianQ.WithLastName), "WithLastName")]
[JsonDerivedType(typeof(TechnicianQ.WithApproximateName), "WithApproximateName")]
// Add other technician specific searches if needed (e.g., WithCredentials?)
public abstract record TechnicianQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : TechnicianQ;
    public sealed record WithFirstName(string FirstName) : TechnicianQ;
    public sealed record WithLastName(string LastName) : TechnicianQ;
    public sealed record WithApproximateName(string Name) : TechnicianQ;

    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithFirstName       wfn => QueryExpressions.BuildFirstNamePredicate<TechnicianDbo>(wfn.FirstName, self),
            WithLastName        wln => QueryExpressions.BuildLastNamePredicate<TechnicianDbo>(wln.LastName, self),
            WithApproximateName wan => QueryExpressions.BuildApproximateNamePredicate<TechnicianDbo>(wan.Name, self),

            WithId              wid => Expression.Equal(Expression.Property(self, nameof(TechnicianDbo.Id)), Expression.Constant(wid.Id)),
            _ => throw new NotSupportedException($"Unsupported TechnicianQ literal type: {this.GetType().Name}")
        };
    }
}