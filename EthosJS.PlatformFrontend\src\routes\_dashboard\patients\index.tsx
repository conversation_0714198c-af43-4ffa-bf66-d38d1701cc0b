import { useEffect, useMemo, useRef, useState } from "react";
import {
  Box,
  But<PERSON>,
  <PERSON>,
  debounce,
  FormControl,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import {
  DataGridPro,
  GridColDef,
  GridFilterModel,
  useGridApiRef,
} from "@mui/x-data-grid-pro";
import { createFileRoute } from "@tanstack/react-router";
import PageContainer from "@components/page-container";
import { Add, Search, FilterAlt, PeopleOutline } from "@mui/icons-material";
import dayjs, { Dayjs } from "dayjs";
import LoadingComponent from "@components/loading-component";
import { useQuery } from "@tanstack/react-query";
import { PatientCreate, PatientRead } from "@auth/scopes";
import { paginationQueryParams } from "@utils/query";
import { queryParams } from "./-utils/-definitions";
import { buildPatientQueryDto } from "./-utils/-query-helpers";
import {
  EthosModelQueryDto1EthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
  EthosModelQueryDto1EthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
  EthosWorkflowsApiPatientDto,
  postApiDraft,
  postApiDraftSearch,
} from "@client/workflows";
import {
  postApiDraftOptions,
  postApiDraftSearchOptions,
  postApiPatientSearchOptions,
} from "@client/workflows/@tanstack/react-query.gen";
import usePatientCreate from "@features/patient-create/hooks/use-patient-create";
import StyledCardFooter from "@components/card-footer";
import { PatientQuery, Query, DraftQuery } from "@utils/query-dsl";

// Extended patient type to include draft information
type ExtendedPatientData = EthosWorkflowsApiPatientDto & {
  isDraft?: boolean;
  draftStatus?: string;
};

const getStatusChipProps = (status: string) => {
  switch (status?.toLowerCase()) {
    case "active":
      return { color: "success" as const, variant: "filled" as const };
    case "draft":
    case "inprogress":
      return { color: "warning" as const, variant: "filled" as const };
    case "complete":
      return { color: "info" as const, variant: "filled" as const };
    case "archived":
      return { color: "default" as const, variant: "outlined" as const };
    default:
      return { color: "default" as const, variant: "outlined" as const };
  }
};

const columns: GridColDef<ExtendedPatientData>[] = [
  {
    field: "patientInformation",
    headerName: "Patient Information",
    flex: 30.5,
    minWidth: 300,
    sortable: true,
    renderHeader: () => (
      <Box
        sx={{
          display: "flex",
          fontWeight: "medium",
          alignItems: "center",
          gap: 0.5,
          fontSize: "0.875rem",
          padding: "16px",
        }}
      >
        Patient Information
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;
      const firstName = row.names?.[0]?.firstName ?? "";
      const middleName = row.names?.[0]?.middleName ?? "";
      const lastName = row.names?.[0]?.lastName ?? "";
      const fullName = `${firstName} ${middleName} ${lastName}`;
      const patientId = row.id ?? "";
      const dateOfBirth = row.demographics?.dateOfBirth
        ? `DOB: ${dayjs(row.demographics.dateOfBirth).format("MM/DD/YYYY")}`
        : "DOB: N/A";
      const gender = row.demographics?.gender ? row.demographics.gender : "N/A";
      const maritalStatus = row.demographics?.maritalStatus
        ? row.demographics.maritalStatus
        : "N/A";

      return (
        <Box
          sx={{ height: "100%", fontSize: "0.875rem" }}
          color="black"
          padding={"16px"}
          display={"flex flex-col"}
          alignItems={"center"}
        >
          <Typography
            variant="body2"
            sx={{ fontWeight: "bold", lineHeight: 1.2 }}
          >
            {fullName}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2 }}
          >
            {patientId} • {dateOfBirth}
          </Typography>
          <Typography
            variant="body2"
            sx={{ lineHeight: 1.2, display: "block" }}
          >
            {gender} • {maritalStatus}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "contact",
    headerName: "Contact",
    flex: 30.5,
    minWidth: 300,
    renderHeader: () => (
      <Box
        sx={{
          display: "flex",
          fontWeight: "medium",
          alignItems: "center",
          gap: 0.5,
          fontSize: "0.875rem",
          padding: "16px",
        }}
      >
        Contact
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;
      const phoneNumber = row.contactInformation?.phoneNumbers?.[0]?.value
        ? `+1 ${row.contactInformation?.phoneNumbers?.[0]?.value}`
        : "N/A";
      const email = row.contactInformation?.emails?.[0]?.value ?? "N/A";

      return (
        <Box
          padding={"16px"}
          color={"black"}
          sx={{ height: "100%", fontSize: "0.875rem" }}
          fontWeight={"regular"}
          display={"flex flex-col"}
          alignItems={"center"}
        >
          <Typography variant="body2" sx={{ lineHeight: 1.2 }}>
            {phoneNumber}
          </Typography>
          <Typography variant="body2" sx={{ lineHeight: 1.2 }}>
            {email}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "address",
    headerName: "Address",
    flex: 30.5,
    minWidth: 300,
    renderHeader: () => (
      <Box
        sx={{
          display: "flex",
          fontWeight: "medium",
          alignItems: "center",
          gap: 0.5,
          padding: "16px",
          fontSize: "0.875rem",
        }}
      >
        Address
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;
      const address = row.contactInformation?.addresses?.[0]?.address;
      const line1 = address?.line1 ?? "";
      const line2 = address?.line2 ? `${address.line2}, ` : "";
      const city = address?.city ? `${address.city}, ` : "";
      const postalCode = address?.postalCode ?? "";
      const state = address?.state ?? "";

      return (
        <Box
          padding={"16px"}
          sx={{ height: "100%", fontSize: "0.875rem" }}
          color={"black"}
          display={"flex flex-col"}
          alignItems={"center"}
        >
          <Typography variant="body2" sx={{ lineHeight: 1.2 }}>
            {line1}
          </Typography>
          <Typography variant="body2" sx={{ lineHeight: 1.2 }}>
            {line2}
            {city} {state} {postalCode}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "status",
    headerName: "Status",
    flex: 8.5,
    minWidth: 100,
    renderHeader: () => (
      <Box
        sx={{
          display: "flex",
          fontWeight: "medium",
          alignItems: "center",
          gap: 0.5,
          fontSize: "0.875rem",
          padding: "16px",
        }}
      >
        Status
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;
      const status = row.isDraft ? row.draftStatus || "Draft" : "Active";
      const chipProps = getStatusChipProps(status);

      return (
        <Box
          padding={"16px"}
          sx={{ height: "100%", fontSize: "0.875rem" }}
          display={"flex"}
          alignItems={"center"}
        >
          <Chip
            label={status}
            size="small"
            {...chipProps}
            sx={{
              borderRadius: 5,
              minWidth: 70,
              fontSize: "0.75rem",
              fontWeight: 500,
            }}
          />
        </Box>
      );
    },
  },
];

export const Route = createFileRoute("/_dashboard/patients/")({
  component: PatientsPage,
  validateSearch: queryParams,
  loaderDeps: ({ search }) => ({ ...search }),
  loader: async ({ context: { queryClient }, deps }) => {
    return queryClient.fetchQuery(
      postApiPatientSearchOptions({
        scopes: [PatientCreate.value, PatientRead.value],
        responseType: "json",
        body: buildPatientQueryDto(Object.keys(deps) ? deps : undefined),
      })
    );
  },
  pendingComponent: () => <LoadingComponent />,
});

interface SearchParams {
  FirstName: string;
  LastName: string;
  Location: string;
  Status: string;
  DateOfBirth: Dayjs | null;
  StudyDate: Dayjs | null;
}

interface SearchDeps
  extends Partial<Omit<SearchParams, "DateOfBirth" | "StudyDate">> {
  DateOfBirth?: string;
  StudyDate?: string;
}

function PatientsPage() {
  const navigate = Route.useNavigate();
  const [userSearchValue, setUserearchValue] = useState("");
  const [searchValue, setSearchValue] = useState("");
  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchValue(value);
      }, 300),
    []
  );

  const [searchParams, setSearchParams] = useState<SearchParams>({
    FirstName: "",
    LastName: "",
    Location: "",
    Status: "Active",
    DateOfBirth: null,
    StudyDate: null,
  });

  const [paginationModel, setPaginationModel] = useState({
    pageSize: 100,
    page: 0,
  });

  const [filterModel, setFilterModel] = useState<GridFilterModel>({
    items: [],
  });
  const filterButtonRef = useRef<HTMLButtonElement | null>(null);
  const apiRef = useGridApiRef();

  // Build search parameters from current state
  // const searchQueryParams = useMemo(() => {
  //   const params: any = {};

  //   // Add search value as FirstName and LastName search
  //   if (searchValue) {
  //     params.FirstName = searchValue;
  //     params.LastName = searchValue;
  //   }

  //   // Add status filter if not "all"
  //   if (searchParams.Status && searchParams.Status !== "all") {
  //     // Note: Status filtering might need to be handled differently
  //     // depending on how the backend expects status filters
  //     params.Status = searchParams.Status;
  //   }

  //   // Include original deps if they exist
  //   if (Object.keys(deps).length > 0) {
  //     Object.assign(params, deps);
  //   }

  //   return Object.keys(params).length > 0 ? params : undefined;
  // }, [searchValue, searchParams.Status, deps]);

  // Build search query parameters based on current search inputs
  const searchQueryParams = useMemo(() => {
    const params: any = {};

    // Add search value as FirstName and LastName search
    if (searchValue) {
      params.FirstName = searchValue;
      params.LastName = searchValue;
    }

    // Add other search parameters
    if (searchParams.FirstName) {
      params.FirstName = searchParams.FirstName;
    }
    if (searchParams.LastName) {
      params.LastName = searchParams.LastName;
    }
    if (searchParams.Location) {
      params.Location = searchParams.Location;
    }
    if (searchParams.DateOfBirth) {
      params.DateOfBirth = searchParams.DateOfBirth.format("YYYY-MM-DD");
    }
    if (searchParams.StudyDate) {
      params.StudyDate = searchParams.StudyDate.format("YYYY-MM-DD");
    }

    return Object.keys(params).length > 0 ? params : undefined;
  }, [searchValue, searchParams]);

  // Build search query for Active patients
  const buildActivePatientQuery = useMemo(() => {
    if (!searchValue && !searchQueryParams) return undefined;

    const queries = [];

    // If there's a search value, create OR query for firstName OR lastName
    if (searchValue) {
      queries.push(
        Query.or([
          Query.literal(PatientQuery.withGivenName(searchValue)),
          Query.literal(PatientQuery.withLastName(searchValue)),
        ])
      );
    }

    if (queries.length === 0) return undefined;
    if (queries.length === 1) return queries[0];
    return Query.and(queries);
  }, [searchValue, searchQueryParams]);

  // Query for Active patients
  const { data: patientData, isFetching: isPatientFetching } = useQuery({
    ...postApiPatientSearchOptions({
      scopes: [PatientCreate.value, PatientRead.value],
      responseType: "json",
      query: paginationQueryParams(paginationModel),
      body: PatientQuery.withLastName(searchValue) as unknown as EthosModelQueryDto1EthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
      // body: buildActivePatientQuery as unknown as EthosModelQueryDto1EthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
    }),
    enabled: searchParams.Status === "Active",
  });

  // Build search query for Draft patients
  const buildDraftPatientQuery = useMemo(() => {
    const queries = [];

    // Always filter for Patient entity type
    queries.push(Query.literal(DraftQuery.withEntityType("Patient")));

    // If there's a search value, create OR query for firstName OR lastName
    if (searchValue) {
      queries.push(
        Query.or([
          Query.literal(DraftQuery.withPatientFirstName(searchValue)),
          Query.literal(DraftQuery.withPatientLastName(searchValue)),
        ])
      );
    }

    // Add other search parameters with AND logic
    if (
      searchQueryParams?.FirstName &&
      searchQueryParams.FirstName !== searchValue
    ) {
      queries.push(
        Query.literal(
          DraftQuery.withPatientFirstName(searchQueryParams.FirstName)
        )
      );
    }
    if (
      searchQueryParams?.LastName &&
      searchQueryParams.LastName !== searchValue
    ) {
      queries.push(
        Query.literal(
          DraftQuery.withPatientLastName(searchQueryParams.LastName)
        )
      );
    }

    if (queries.length === 1) return queries[0];
    return Query.and(queries);
  }, [searchValue, searchQueryParams]);

  // Query for Draft patients
  const { data: draftData, isFetching: isDraftFetching } = useQuery({
    ...postApiDraftSearchOptions({
      scopes: [PatientCreate.value, PatientRead.value],
      responseType: "json",
      query: paginationQueryParams(paginationModel),
      body: buildDraftPatientQuery as unknown as EthosModelQueryDto1EthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
    }),
    enabled: searchParams.Status === "Draft",
  });

  // Process draft data to transform to match patient structure
  const processedDraftData = useMemo(() => {
    if (!draftData?.items)
      return { items: [], totalCount: draftData?.totalCount || 0 };

    const patientDrafts: ExtendedPatientData[] = draftData.items.map(
      (item: any) => {
        const patientInfo = item.data?.patientInformation || {};
        const demographics = item.data?.demographics || {};
        const contactInfo = item.data?.contactInformation || {};

        return {
          id: item.entityId,
          names: [
            {
              prefix: null,
              firstName: patientInfo.firstName || "",
              middleName: patientInfo.middleName || "",
              lastName: patientInfo.lastName || "",
              suffix: null,
            },
          ],
          demographics: {
            dateOfBirth: demographics.dateOfBirth,
            gender: demographics.gender,
            maritalStatus: demographics.maritalStatus,
          },
          contactInformation: {
            phoneNumbers: contactInfo.phoneNumbers || [],
            emails: contactInfo.emails || [],
            addresses: contactInfo.addresses || [],
          },
          identifiers: [],
          orderIds: [],
          lastStudyLocation: null,
          isDraft: true,
          draftStatus: item.data?._state?.flowState?.status || "InProgress",
        } as unknown as ExtendedPatientData;
      }
    );

    return {
      items: patientDrafts,
      totalCount: draftData.totalCount || patientDrafts.length,
    };
  }, [draftData]);

  // Determine which data to use based on status
  const data =
    searchParams.Status === "Draft"
      ? processedDraftData
      : {
          ...patientData,
          items: (patientData?.items || []).map(
            (item) => ({ ...item, isDraft: false }) as ExtendedPatientData
          ),
        };
  const isFetching =
    searchParams.Status === "Draft" ? isDraftFetching : isPatientFetching;

  const { createNewPatient, isCreatingPatient } = usePatientCreate();

  const rowCountRef = useRef(data?.totalCount || 0);

  const rowCount = useMemo(() => {
    if (data?.totalCount !== undefined) {
      rowCountRef.current = data?.totalCount;
    }
    return rowCountRef.current;
  }, [data?.totalCount]);

  const statusOptions = [
    { label: "Active", value: "Active" },
    { label: "Draft", value: "Draft" },
  ];

  const debouncedNavigate = useMemo(
    () =>
      debounce((searchParams: SearchParams) => {
        const deps = Object.keys(searchParams).reduce((acc, key) => {
          const val = searchParams[key as keyof typeof searchParams];
          if (typeof val === "string") {
            acc[key as keyof typeof searchParams] =
              val === "" ? undefined : val;
          }
          if (val instanceof dayjs && val.isValid()) {
            acc[key as keyof typeof searchParams] = val.format("YYYY-MM-DD");
          }
          if (val === null) {
            acc[key as keyof typeof searchParams] = undefined;
          }
          return acc;
        }, {} as SearchDeps);
        navigate({
          search: (old) => ({
            ...old,
            ...deps,
          }),
        });
      }, 300),
    [navigate]
  );

  useEffect(() => {
    if (userSearchValue !== searchValue && searchValue) {
      debouncedSearch(userSearchValue);
    }
  }, [debouncedSearch, searchValue, userSearchValue]);

  useEffect(() => {
    if (
      Object.keys(searchParams).some(
        (key) =>
          searchParams[key as keyof typeof searchParams] !== "" ||
          searchParams[key as keyof typeof searchParams] !== null
      )
    ) {
      debouncedNavigate(searchParams);
    }
  }, [debouncedNavigate, searchParams]);

  const handleClickNewPatient = async () => {
    createNewPatient(
      {
        flowState: {
          progress: 0,
          status: "InProgress",
          lastUpdate: dayjs().format("MMM D, YYYY h:mm A"),
        },
        stepState: {
          BasicInformation: "InProgress",
          Contacts: "NotStarted",
          Addresses: "NotStarted",
          Insurances: "NotStarted",
          Guardians: "NotStarted",
          ClinicalConsiderations: "NotStarted",
        },
      },
      (data) => {
        navigate({
          to: "/patients/$patientId/patient-information",
          params: { patientId: data.entityId! },
        });
      }
    );
  };

  return (
    <PageContainer
      title="Patients"
      icon={PeopleOutline}
      actions={
        <Stack direction="row" alignItems="center" spacing={"16px"}>
          <TextField
            value={userSearchValue}
            onChange={(e) => {
              setUserearchValue(e.target.value);
              debouncedSearch(e.target.value);
            }}
            variant="outlined"
            placeholder="Search"
            size="small"
            sx={{ width: "300px" }}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              },
            }}
          />
          <FormControl size="small" sx={{ width: "180px" }}>
            <InputLabel id="status-label">Status</InputLabel>
            <Select
              labelId="status-label"
              id="status-select"
              value={searchParams.Status}
              onChange={(e) => {
                setSearchParams((prev) => ({
                  ...prev,
                  Status: e.target.value,
                }));
              }}
              label="Status"
            >
              {statusOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <IconButton
            ref={filterButtonRef}
            onClick={() => {
              apiRef?.current?.showFilterPanel();
            }}
          >
            <FilterAlt />
          </IconButton>

          <Button
            variant="contained"
            color="primary"
            loading={isCreatingPatient}
            startIcon={<Add />}
            sx={{ height: "36px" }}
            onClick={handleClickNewPatient}
          >
            Create New Patient
          </Button>
        </Stack>
      }
    >
      {filterModel.items.some((filter) => filter.value) && (
        <StyledCardFooter
          sx={{
            display: "flex",
            flexWrap: "wrap",
            gap: "1px",
            padding: "20px",
          }}
        >
          {filterModel.items.map(
            (filter, index) =>
              filter.value && (
                <Chip
                  key={index}
                  label={`${filter.value}`}
                  onDelete={() => {
                    const newItems = [...filterModel.items];
                    newItems.splice(index, 1);
                    setFilterModel({ items: newItems });
                  }}
                  size="small"
                  variant="filled"
                  color="primary"
                  sx={{
                    borderRadius: 5,
                    minWidth: 70,
                    fontSize: "0.75rem",
                    fontWeight: 500,
                  }}
                />
              )
          )}
        </StyledCardFooter>
      )}

      <Box
        sx={{
          minHeight: 400,
          width: "100%",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <DataGridPro
          className="custom-data-grid"
          loading={isFetching}
          rows={data?.items ?? []}
          rowCount={rowCount}
          paginationMode="server"
          paginationModel={paginationModel}
          onPaginationModelChange={setPaginationModel}
          pageSizeOptions={[10, 25, 50, 100]}
          pagination
          getRowHeight={() => "auto"}
          columns={columns}
          sx={{
            border: "none",
            borderRadius: "none",
            ".css-13socp2-MuiDataGrid-root": {
              border: "none",
              borderRadius: "none",
            },
          }}
          filterModel={filterModel}
          onFilterModelChange={setFilterModel}
          filterMode="server"
          filterDebounceMs={500}
          apiRef={apiRef}
        />
      </Box>
    </PageContainer>
  );
}
