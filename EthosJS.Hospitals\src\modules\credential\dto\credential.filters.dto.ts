import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { BaseFiltersDto } from '@app/common/dto/base.filters.dto';
import { ECredentialSort } from '@app/modules/credential/enums';

export class CredentialFiltersDto extends BaseFiltersDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ enum: ECredentialSort })
  @IsOptional()
  @IsEnum(ECredentialSort)
  orderField: ECredentialSort = ECredentialSort.CreatedAt;
}
