import {
  initializeTransactionalContext,
  patchTypeORMRepositoryWithBaseRepository,
} from 'typeorm-transactional-cls-hooked';
import {
  ClassSerializerInterceptor,
  INestApplication,
  ValidationPipe,
} from '@nestjs/common';
import { Transport } from '@nestjs/microservices';
import { Reflector } from '@nestjs/core';

export async function configApp(app: INestApplication) {
  initializeTransactionalContext();
  patchTypeORMRepositoryWithBaseRepository();

  const reflector = app.get(Reflector);

  app.useGlobalFilters();
  app.useGlobalInterceptors(new ClassSerializerInterceptor(reflector));

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      dismissDefaultMessages: true,
      validationError: {
        target: false,
      },
    }),
  );

  app.connectMicroservice({
    transport: Transport.TCP,
    options: {
      port: 80,
      retryAttempts: 5,
      retryDelay: 3000,
    },
  });

  return app;
}
