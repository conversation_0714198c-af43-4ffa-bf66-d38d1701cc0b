﻿
namespace Ethos.ReferenceData.Client
{
    /// <summary>
    /// When present, the property or field is ignored when mapping reference data.
    /// </summary>
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field, Inherited = true)]
    public class ReferenceDataIgnoreAttribute : Attribute
    {
    }

    /// <summary>
    /// When present, the property or field is mapped to the key in the external set with the value of the property or field.
    /// </summary>
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field, AllowMultiple = false, Inherited = false)]
    public class ReferenceDataMapToExternalSetAttribute(string name, string? version) : Attribute
    {
        public string SetName { get; set; } = name;
        public string Version { get; set; } = version ?? string.Empty;
        public string? Alias { get; set; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="externalType"></param>
        /// <exception cref="Exception"></exception>
        public ReferenceDataMapToExternalSetAttribute(Type externalType) : this(string.Empty, string.Empty)
        {
            var setDetail = externalType.GetReferenceDataSetDetail();
            if (setDetail is null || string.IsNullOrEmpty(setDetail.Name))
                throw new Exception($"Cannot find external reference data set for type '{externalType.Name}'.");
            SetName = setDetail.Name;
            Version = setDetail.Version ?? string.Empty;
        }
    }

    /// <summary>
    /// When present, the class or struct will be included as a reference data set.
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Struct, AllowMultiple = false, Inherited = false)]
    public class ReferenceDataSetAttribute : Attribute
    {
        public string? SetName { get; set; }
        public string? Version { get; set; }
        public string? Source { get; set; }
        public string? Authority { get; set; }

        /// <summary>
        /// Name of the field or property that represents the key for the data set. If this value is set, 
        /// any properties or fields in the class tagged with the <see cref="ReferenceDataSetKeyAttribute"/> will be ignored.
        /// </summary>
        public string? Key { get; set; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <param name="version"></param>
        /// <param name="authority"></param>
        /// <param name="source"></param>
        public ReferenceDataSetAttribute(string name, string? version, string? authority, string? source) : this(name, version)
        {
            Authority = authority;
            Source = source;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <param name="version"></param>
        public ReferenceDataSetAttribute(string name, string? version)
        {
            Version = version ?? string.Empty;
            SetName = name;
        }

        /// <summary>
        /// 
        /// </summary>
        public ReferenceDataSetAttribute()
        {
        }
    }

    /// <summary>
    /// When present, the property or field will be the key in the reference data set for the class or struct 
    /// of which the property or field is a member.
    /// </summary>
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field, AllowMultiple = false, Inherited = false)]
    public class ReferenceDataSetKeyAttribute : Attribute
    {
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="setName"></param>
    /// <param name="version"></param>
    /// <param name="value"></param>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Struct, AllowMultiple = true, Inherited = false)]
    public class ReferenceDataAlternateKeyAttribute(string setName, string? version, object value) : Attribute
    {
        public string SetName { get; set; } = setName;
        public string? Version { get; set; } = version ?? string.Empty;
        public object KeyValue { get; set; } = value;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="refDataType"></param>
    /// <param name="version"></param>
    /// <param name="value"></param>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Struct, AllowMultiple = true, Inherited = false)]
    public class ReferenceDataAlternateKeyTypeAttribute(Type refDataType, string? version, object value) : Attribute
    {
        public Type Type { get; set; } = refDataType;
        public string? Version { get; set; } = version ?? string.Empty;
        public object KeyValue { get; set; } = value;
    }
}
