import moment from 'moment';
import { BadRequestException, Injectable } from '@nestjs/common';
import { IListResult } from '@app/common/types';
import { FacilityService } from '@app/modules/facility/facility.service';
import { StudyService } from '@app/modules/study/study.service';
import { AvailableScheduleFiltersDto } from '@app/modules/available/dto/available.schedule.filters.dto';
import { AvailableScheduleDto } from '@app/modules/available/dto/available.schedule.dto';
import Logger from '@app/common/logger';
import { ScheduleService } from '@app/modules/schedule/schedule.service';
import { AvailableStudyFiltersDto } from '@app/modules/available/dto/available.study.filters.dto';
import { AvailableStudyCollectionDto } from '@app/modules/available/dto/available.study.collection.dto';
import { AvailableStudyDayFiltersDto } from '@app/modules/available/dto/available.study.day.filters.dto';
import { AvailableStudyDayCollectionDto } from '@app/modules/available/dto/available.study.day.collection.dto';
import { AvailableStudyDayDto } from '@app/modules/available/dto/available.study.day.dto';
import { DATE_FORMAT } from '@app/common/constants';
import { EquipmentService } from '@app/modules/equipment/equipment.service';

@Injectable()
export class AvailableService {
  constructor(
    private readonly facilityService: FacilityService,
    private readonly studyService: StudyService,
    private readonly scheduleService: ScheduleService,
    private readonly equipmentService: EquipmentService,
  ) {}

  async getAvailableSchedules({ shift, ...params }: AvailableScheduleFiltersDto): Promise<IListResult<AvailableScheduleDto>> {
    const daysCount = moment(params.dateTo).diff(moment(params.dateFrom), 'days');

    if (daysCount <= 0) {
      return {
        data: [],
        count: 0,
      };
    }

    if (daysCount > 35) {
      throw new BadRequestException('Period cannot be more than 35 days');
    }

    const facility = await this.facilityService.getByIdOrFail(params.facilityId);
    const [study] = await this.studyService.getByIdOrFail(params.studyId);

    const fullEquipments = { ...study.equipments };

    if (params.equipments?.length) {
      const equipmentIds = params.equipments.map(({ equipmentId }) => equipmentId);
      const existingEquipmentMap = await this.equipmentService.checkExistence(equipmentIds);
      params.equipments.forEach((item) => {
        const equipment = existingEquipmentMap[item.equipmentId];
        if (!fullEquipments[item.equipmentId]) {
          fullEquipments[item.equipmentId] = {
            count: item.count,
            equipmentId: item.equipmentId,
            equipmentName: equipment.name,
          };
        } else {
          fullEquipments[item.equipmentId].count = fullEquipments[item.equipmentId].count + item.count;
        }
      });
    }

    const studyCredentials = await this.studyService.getCredentialsForStudy(params.studyId, facility.city.stateId);

    const dates = [];

    for (let i = 0; i <= daysCount; i++) {
      dates.push(moment(params.dateFrom).add(i, 'day').format(DATE_FORMAT));
    }

    const items: AvailableScheduleDto[] = [];

    for (const date of dates) {
      try {
        await this.scheduleService.getAvailableTechnicianId({
          ...params,
          facility,
          shift,
          date,
          equipments: fullEquipments,
          studyCredentials,
        });

        items.push({
          date,
          shift,
          studyId: params.studyId,
          facilityId: params.facilityId,
        });

        if (params.limit && items.length === params.limit) {
          break;
        }
      } catch {
        Logger.log(['APP', 'DEFAULT'], `${date} doesn\'t have available schedules`);
      }
    }

    return {
      data: items,
      count: items?.length,
    };
  }

  async getAvailableStudies(params: AvailableStudyFiltersDto): Promise<AvailableStudyCollectionDto> {
    const facility = await this.facilityService.getByIdOrFail(params.facilityId);
    const studies = await this.studyService.getAll();

    const items = await Promise.all(studies.map(async (study) => {
      const studyCredentials = await this.studyService.getCredentialsForStudy(study.id, facility.city.stateId);
      const availableStudy = {
        date: params.date,
        shift: params.shift,
        studyId: study.id,
        name: study.name,
        facilityId: params.facilityId,
        isAvailable: false,
      };

      try {
        await this.scheduleService.getAvailableTechnicianId({
          ...params,
          facility,
          studyId: study.id,
          equipments: study.equipments,
          studyCredentials,
        });

        availableStudy.isAvailable = true;
      } catch {
        availableStudy.isAvailable = false;
      }

      return availableStudy;
    }));

    return {
      data: items,
      count: items.length,
    };
  }

  async getAvailableStudyDays(params: AvailableStudyDayFiltersDto): Promise<AvailableStudyDayCollectionDto> {
    const daysCount = moment(params.dateTo).diff(moment(params.dateFrom), 'days');

    if (daysCount <= 0) {
      return {
        data: [],
        count: 0,
      };
    }

    if (daysCount > 35) {
      throw new BadRequestException('Period cannot be more than 35 days');
    }

    const dates = [];

    for (let i = 0; i <= daysCount; i++) {
      dates.push(moment(params.dateFrom).add(i, 'day').format(DATE_FORMAT));
    }

    const facility = await this.facilityService.getByIdOrFail(params.facilityId);
    const { data: studies } = await this.studyService.list({ limit: 0 });
    const items: AvailableStudyDayDto[] = [];

    await Promise.all(dates.map(async (date) => {
      const availableStudyDay = {
        date,
        isAvailable: false,
      };

      for (const study of studies) {
        const studyCredentials = await this.studyService.getCredentialsForStudy(study.id, facility.city.stateId);

        try {
          await this.scheduleService.getAvailableTechnicianId({
            ...params,
            facility,
            date,
            studyId: study.id,
            equipments: study.equipments,
            studyCredentials,
          });

          availableStudyDay.isAvailable = true;
          break;
        } catch {
          availableStudyDay.isAvailable = false;
        }
      }

      items.push(availableStudyDay);
    }));

    return {
      data: items,
      count: items.length,
    };
  }
}
