import { BadRequestException, Injectable } from '@nestjs/common';
import { IListResult } from '@app/common/types';
import { StateFiltersDto } from '@app/modules/state/dto/state.filters.dto';
import { StateEntity } from '@app/modules/state/state.entity';
import { StateRepository } from '@app/modules/state/state.repository';

@Injectable()
export class StateService {
  constructor(
    private readonly repository: StateRepository,
  ) {}

  async list(filters: StateFiltersDto): Promise<IListResult<StateEntity>> {
    return this.repository.list(filters);
  }

  async getByIdOrFail(stateId: number): Promise<StateEntity> {
    const state = await this.repository.findOne({
      where: {
        id: stateId,
      },
    });

    if (!state) {
      throw new BadRequestException('State is not found');
    }

    return state;
  }
}
