﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" />
        <PackageReference Include="Newtonsoft.Json" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Ethos.Auth\Ethos.Auth.csproj" />
        <ProjectReference Include="..\Ethos.ReferenceData.Client\Ethos.ReferenceData.Client.csproj" />
        <ProjectReference Include="..\Ethos.Utilities\Ethos.Utilities.csproj" />
    </ItemGroup>

</Project>
