using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

// Represents a link between Patient and Address with metadata
public class PersonalAddressDbo : IAuditableEntity<PersonalAddressDbo>
{
    public Guid ParentId { get; set; }
    public virtual PersonalContactDetailDbo Parent { get; set; } = null!;

    public Guid AddressId { get; set; }
    public virtual AddressDbo Address { get; set; } = null!; // Link to internal AddressEntity

    public long UseId { get; set; }
    public long TypeId { get; set; }

    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<PersonalAddressDbo>(Register);
    public new static void Register(EntityTypeBuilder<PersonalAddressDbo> entity)
    {
        IAuditableEntity<PersonalAddressDbo>.Register(entity);

        entity.HasOne(e => e.Parent)
            .WithMany(p => p.Addresses)
            .HasForeignKey(e => e.ParentId)
            .OnDelete(DeleteBehavior.Cascade);

        entity.HasOne(e => e.Address)
            .WithMany() // Assuming Address doesn't navigate back to PatientAddress links
            .HasForeignKey(e => e.AddressId)
            .OnDelete(DeleteBehavior.Restrict); // Prevent deleting Address if linked
    }
}