import moment from 'moment';
import { BadRequestException, Injectable } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional-cls-hooked';
import { IListResult } from '@app/common/types';
import { ScheduleRepository } from '@app/modules/schedule/schedule.repository';
import { ScheduleFiltersDto } from '@app/modules/schedule/dto/schedule.filters.dto';
import { ScheduleEntity } from '@app/modules/schedule/schedule.entity';
import { CreateScheduleDto } from '@app/modules/schedule/dto/create.schedule.dto';
import { PatientService } from '@app/modules/patient/patient.service';
import { FacilityService } from '@app/modules/facility/facility.service';
import { StudyService } from '@app/modules/study/study.service';
import { TechnicianScheduleService } from '@app/modules/technicianSchedule/technician.schedule.service';
import { ETechnicianScheduleShift } from '@app/modules/technicianSchedule/enums';
import { BedScheduleService } from '@app/modules/bedSchedule/bed.schedule.service';
import { EShift } from '@app/common/enums';
import {
  ICheckBedScheduleParams,
  IGetAvailableTechnicianIdParams,
  IScheduleEquipment,
} from '@app/modules/schedule/types';
import { ScheduleDto } from '@app/modules/schedule/dto/schedule.dto';
import { EquipmentService } from '@app/modules/equipment/equipment.service';
import { mapToScheduleDto } from '@app/modules/schedule/helpers';
import { TechnicianEntity } from '@app/modules/technician/technician.entity';
import { getWeekDay } from '@app/common/helpers';

@Injectable()
export class ScheduleService {
  constructor(
    private readonly repository: ScheduleRepository,
    private readonly patientService: PatientService,
    private readonly bedScheduleService: BedScheduleService,
    private readonly technicianScheduleService: TechnicianScheduleService,
    private readonly facilityService: FacilityService,
    private readonly studyService: StudyService,
    private readonly equipmentService: EquipmentService,
  ) {}

  async list({ clinicId, ...filters }: ScheduleFiltersDto): Promise<IListResult<ScheduleDto>> {
    if (clinicId) {
      filters.facilityIds = await this.repository.getFacilityIdsByClinic(clinicId);
    }

    const { data, count } = await this.repository.list(filters);

    return {
      data: data.map(ScheduleService.mapToDto),
      count,
    };
  }

  async getByIdOrFail(scheduleId: number): Promise<ScheduleEntity> {
    const schedule = await this.repository.findOne({
      where: {
        id: scheduleId,
      },
    });

    if (!schedule) {
      throw new BadRequestException(`Schedule ${scheduleId} is not found`);
    }

    return schedule;
  }

  @Transactional()
  async create({ equipments, ...schedule }: CreateScheduleDto): Promise<ScheduleEntity> {
    const facility = await this.facilityService.getByIdOrFail(schedule.facilityId);
    const [study] = await this.studyService.getByIdOrFail(schedule.studyId);

    const fullEquipments = { ...study.equipments };

    if (equipments?.length) {
      const equipmentIds = equipments.map(({ equipmentId }) => equipmentId);
      const existingEquipmentMap = await this.equipmentService.checkExistence(equipmentIds);

      equipments.forEach((item) => {
        const equipment = existingEquipmentMap[item.equipmentId];
        if (!fullEquipments[item.equipmentId]) {
          fullEquipments[item.equipmentId] = {
            count: item.count,
            equipmentId: item.equipmentId,
            equipmentName: equipment.name,
          };
        } else {
          fullEquipments[item.equipmentId].count = fullEquipments[item.equipmentId].count + item.count;
        }
      });
    }

    const studyCredentials = await this.studyService.getCredentialsForStudy(schedule.studyId, facility.city.stateId);
    const availableTechnician = await this.getAvailableTechnicianId({
      ...schedule,
      facility,
      equipments: fullEquipments,
      studyCredentials,
    });

    const patient = await this.patientService.upsert({ externalId: schedule.patient.id, name: schedule.patient.name });
    const entity = this.repository.create({
      ...schedule,
      weekday: getWeekDay(moment(schedule.date).weekday()),
      facilityName: facility.name,
      studyName: study.name,
      patientId: patient.id,
      patientName: patient.name,
      technicianId: availableTechnician.id,
      technicianName: availableTechnician.name,
    });

    if (Object.keys(fullEquipments).length) {
      entity.equipments = Object.values(fullEquipments).reduce((acc, item) => {

        acc[item.equipmentId] = {
          ...item,
          studyCount: study.equipments[item.equipmentId]?.count || 0,
        };

        return acc;
      }, {} as Record<number, IScheduleEquipment>);
    }

    await entity.save();

    return this.getByIdOrFail(entity.id);
  }

  async delete(scheduleId: number): Promise<ScheduleEntity> {
    const schedule = await this.getByIdOrFail(scheduleId);

    await this.repository.softDelete({ id: scheduleId });

    return schedule;
  }

  async getAvailableTechnicianId(params: IGetAvailableTechnicianIdParams): Promise<TechnicianEntity> {
    await this.checkBedSchedule(params);

    const technicianCounts = await this.repository.getTechnicianSchedulesCount(params.date);

    return this.technicianScheduleService.getAvailableTechnicianId({
      ...params,
      shift: params.shift as unknown as ETechnicianScheduleShift,
      technicianCounts,
    });
  }

  async checkBedSchedule(params: ICheckBedScheduleParams): Promise<void> {
    const bedSchedule = await this.bedScheduleService.getBedScheduleByDate(params.facility, params.date);
    const schedules = await this.repository.find({
      where: {
        date: params.date,
        facilityId: params.facility.id,
        shift: params.shift,
      },
    });

    if (params.shift === EShift.Night) {
      if (schedules.length >= bedSchedule.nightShiftBeds) {
        throw new BadRequestException('Limit of beds exhausted');
      }
    } else {
      if (schedules.length >= bedSchedule.dayShiftBeds) {
        throw new BadRequestException('Limit of beds exhausted');
      }
    }

    const scheduledEquipments = schedules.reduce((acc, schedule) => {
      Object.entries(schedule.equipments).forEach(([equipmentId, { count }]) => {
        const id = Number(equipmentId);
        if (!acc[id]) {
          acc[id] = count;
        } else {
          acc[id] = acc[id] + count;
        }
      });
      return acc;
    }, {} as Record<number, number>);

    Object.values(params.equipments).forEach(({ equipmentId, count }) => {
      const bedScheduleEquipment = bedSchedule.equipments[Number(equipmentId)] || 0;
      const scheduledCount = scheduledEquipments[Number(equipmentId)] || 0;

      if (bedScheduleEquipment.count < (scheduledCount + count)) {
        throw new BadRequestException(`Not enough equipmentId ${equipmentId} for scheduling, required: ${count}, total - ${bedScheduleEquipment.count}, already scheduled - ${scheduledCount}.`);
      }
    });
  }

  static mapToDto(entity: ScheduleEntity): ScheduleDto {
    return mapToScheduleDto(entity);
  }
}
