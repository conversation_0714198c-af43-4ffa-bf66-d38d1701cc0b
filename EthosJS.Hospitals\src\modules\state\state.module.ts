import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { StateRepository } from '@app/modules/state/state.repository';
import { StateService } from '@app/modules/state/state.service';
import { StateController } from '@app/modules/state/state.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([StateRepository]),
  ],
  providers: [StateService],
  controllers: [StateController],
  exports: [StateService],
})
export class StateModule {}
