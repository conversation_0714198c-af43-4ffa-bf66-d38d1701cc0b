﻿using System.Net.Mime;

namespace Ethos.ThirdParty.Tennr
{
    /// <summary>
    /// 
    /// </summary>
    public static class TennrExtensions
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="fileInfo"></param>
        /// <returns></returns>
        public static string GuessContentTypeFromFileExtension(this FileInfo fileInfo)
        {
            var extension = fileInfo.Extension.ToLower().TrimStart('.');
            return extension switch
            {
                "pdf" => MediaTypeNames.Application.Pdf,
                "zip" => MediaTypeNames.Application.Zip,
                "png" => MediaTypeNames.Image.Png,
                "jpg" or "jpeg" or "jpe" => MediaTypeNames.Image.Jpeg,
                "bmp" => MediaTypeNames.Image.Bmp,
                "tif" or "tiff" => MediaTypeNames.Image.Tiff,
                "txt" or "text" or "asc" => MediaTypeNames.Text.Plain,
                "csv" => MediaTypeNames.Text.Csv,
                "html" or "htm" => MediaTypeNames.Text.Html,
                "xml" => MediaTypeNames.Text.Xml,
                "json" => MediaTypeNames.Application.Json,
                "gif" => MediaTypeNames.Image.Gif,
                "svg" => MediaTypeNames.Image.Svg,
                "css" => MediaTypeNames.Text.Css,
                "rtf" => MediaTypeNames.Text.RichText,
                "md" or "markdown" => MediaTypeNames.Text.Markdown,
                "woff2" => MediaTypeNames.Font.Woff2,
                "woff" => MediaTypeNames.Font.Woff,
                "yaml" or "yml" => "application/yaml",
                "webp" => MediaTypeNames.Image.Webp,
                "ico" => MediaTypeNames.Image.Icon,
                "dtd" => MediaTypeNames.Application.XmlDtd,
                "js" => MediaTypeNames.Text.JavaScript,
                "ttf" => MediaTypeNames.Font.Ttf,
                _ => MediaTypeNames.Application.Octet,
            };
        }
    }
}
