import { ApiPropertyOptional } from '@nestjs/swagger';
import { BaseFiltersDto } from '@app/common/dto/base.filters.dto';
import { EFacilitySort } from '@app/modules/facility/enums';
import { IsEnum, IsInt, IsOptional, IsPositive, IsString } from 'class-validator';

export class FacilityFiltersDto extends BaseFiltersDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  @IsPositive()
  clinicId?: number;

  @ApiPropertyOptional({ enum: EFacilitySort })
  @IsOptional()
  @IsEnum(EFacilitySort)
  orderField?: EFacilitySort = EFacilitySort.CreatedAt;
}
