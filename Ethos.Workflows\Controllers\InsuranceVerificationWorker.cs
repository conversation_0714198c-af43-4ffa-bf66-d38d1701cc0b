using Microsoft.Extensions.Options;

namespace Ethos.Workflows.Controllers;

public class InsuranceVerificationWorker : BackgroundService
{
    private readonly ILogger<InsuranceVerificationWorker> _logger;
    private readonly IServiceProvider _serviceProvider; // To create scopes
    private readonly TimeSpan _period; // How often to run OnIdle

    // You might want to configure the polling interval
    public class WorkerConfig
    {
        public int PollingIntervalSeconds { get; set; } = 5; // Default to 60 seconds
    }

    public InsuranceVerificationWorker(
        ILogger<InsuranceVerificationWorker> logger,
        IServiceProvider serviceProvider,
        IOptions<WorkerConfig> workerConfigOptions) // Inject configuration
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _period = TimeSpan.FromSeconds(workerConfigOptions.Value.PollingIntervalSeconds);
        _logger.LogInformation("InsuranceVerificationWorker will run OnIdle every {PollingIntervalSeconds} seconds.", workerConfigOptions.Value.PollingIntervalSeconds);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("InsuranceVerificationWorker is starting.");

        stoppingToken.Register(() =>
            _logger.LogInformation("InsuranceVerificationWorker is stopping."));

        while (!stoppingToken.IsCancellationRequested)
        {
            _logger.LogDebug("InsuranceVerificationWorker executing OnIdle at: {time}", DateTimeOffset.Now);

            try
            {
                using (var scope = _serviceProvider.CreateScope())
                {
                    var insuranceService = scope.ServiceProvider
                        .GetRequiredService<InsuranceVerificationService>();

                    await insuranceService.OnIdle();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred in InsuranceVerificationWorker while executing OnIdle.");
            }

            try
            {
                await Task.Delay(_period, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("InsuranceVerificationWorker stopping delay due to cancellation.");
                break;
            }
        }

        _logger.LogInformation("InsuranceVerificationWorker has stopped.");
    }
}