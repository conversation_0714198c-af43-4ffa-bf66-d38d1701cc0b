variables:
  IMAGE: $CI_REGISTRY/$CI_PROJECT_PATH:$CI_COMMIT_REF_SLUG
  CHART: app-api

stages:
  - dependencies
  - build
  - pack
  - deploy

package publish:
  image: node:18.13.0-alpine
  stage: dependencies
  variables:
    BRANCH: dev
  before_script:
    - apk add --no-cache git
    - echo https://${GITLAB_AUTH}@${CI_SERVER_HOST} > /etc/.git-credentials
    - git config --global credential.helper 'store --file /etc/.git-credentials'
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "Mr. Robot"
  script:
    - git clone https://${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git .packaging && cd .packaging
    - git checkout ${BRANCH} && git pull origin ${BRANCH}
    - cd model
    - echo "//registry.npmjs.org/:_authToken=$NPM_TOKEN" > .npmrc
    - npm version patch
    - git add package.json
    - git commit -m "Increase package version [skip ci]"
    - npm publish
    - git push --set-upstream origin ${BRANCH}
  only:
    refs:
      - dev
    changes:
      - model/type.d.ts

build:
  image: node:18.13.0-alpine
  stage: build
  needs: []
  cache:
    key:
      files:
        - package.json
      prefix: $CI_PROJECT_PATH_SLUG
    paths:
      - node_modules/
      - yarn.lock
  artifacts:
    expire_in: 1h
    paths:
      - node_modules/
      - dist/
  script:
    - yarn --prefer-offline
    - yarn build

docker:
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: ['']
  stage: pack
  before_script:
    - echo "{\"auths\":{\"$CI_REGISTRY\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$CI_REGISTRY_PASSWORD\"}}}" > /kaniko/.docker/config.json
  script:
    - /kaniko/executor --cache --context $CI_PROJECT_DIR --dockerfile $CI_PROJECT_DIR/.deploy/Dockerfile --destination $IMAGE

include:
  - project: axmit-infra/ci-templates
    file: deploy_to_axmit.yml