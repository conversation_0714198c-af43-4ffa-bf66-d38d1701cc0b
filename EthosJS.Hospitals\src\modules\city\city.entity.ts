import { Expose } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, ManyToOne, OneToMany } from 'typeorm';
import { BaseEntity } from '@app/common/base.entity';
import { StateEntity } from '../state/state.entity';
import { FacilityEntity } from '@app/modules/facility/facility.entity';

@Entity({ name: 'cities' })
export class CityEntity extends BaseEntity {
  @Column()
  @ApiProperty()
  @Expose()
  name: string;

  @Column()
  @ApiProperty()
  @Expose()
  stateId: number;

  @ManyToOne(
    () => StateEntity,
    state => state.cities,
  )
  state: StateEntity[];

  @OneToMany(
    () => FacilityEntity,
    facility => facility.city,
  )
  facilities: FacilityEntity[];
}
