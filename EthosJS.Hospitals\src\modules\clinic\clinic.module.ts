import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClinicRepository } from '@app/modules/clinic/clinic.repository';
import { ClinicService } from '@app/modules/clinic/clinic.service';
import { ClinicController } from '@app/modules/clinic/clinic.controller';

@Module({
  imports: [TypeOrmModule.forFeature([ClinicRepository])],
  providers: [ClinicService],
  controllers: [ClinicController],
  exports: [ClinicService],
})
export class ClinicModule {}
