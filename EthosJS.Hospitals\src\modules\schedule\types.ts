import { StudyCredentialEntity } from '@app/modules/study/study.credential.entity';
import { EDayWeek, EShift } from '@app/common/enums';
import { FacilityEntity } from '@app/modules/facility/facility.entity';
import { IStudyEquipment } from '@app/modules/study/types';

export interface IGetAvailableTechnicianIdParams {
    facility: FacilityEntity;
    studyId: number;
    date: string;
    shift: EShift;
    studyCredentials: StudyCredentialEntity[];
    equipments: Record<number, IStudyEquipment>;
}

export interface ICheckBedScheduleParams {
    facility: FacilityEntity;
    date: string;
    shift: EShift;
    equipments: Record<number, IStudyEquipment>;
}

export interface IScheduleEquipment {
    equipmentId: number;
    equipmentName: string;
    count: number;
    studyCount: number;
}

export interface ISchedule {
    id: number;
    createdAt: string;
    updatedAt: string;
    deletedAt?: string;
    technicianId: number;
    technicianName: string;
    patientId: number;
    patientName: string;
    studyId: number;
    studyName: string;
    facilityId: number;
    facilityName: string;
    shift: EShift;
    date: string;
    weekday: EDayWeek;
    credentials: number[];
    equipments: Record<number, IScheduleEquipment>
}