using System.Diagnostics;
using System.Reflection;
using Ethos.Workflows.Database;
using Ethos.Workflows.Files;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;

namespace Ethos.Workflows.Controllers;

public sealed record HealthReport( // Renamed for clarity, or keep HealthCheck
    string Status,
    bool CanConnectToDatabase,
    bool CanListPatients,
    bool IsFileStorageHealthy,
    string? GitCommit,
    string? GitBranch,
    string? ApplicationVersion,
    string? AspNetCoreEnvironment,
    DateTimeOffset Timestamp,
    TimeSpan Uptime);

[ApiController]
[Route("api/[controller]")]
public class HealthController : ControllerBase
{
    private readonly IWebHostEnvironment _env;
    private readonly AppDbContext _dbContext;
    private IFileStorageService _fileStorageService;
    
    public HealthController(IWebHostEnvironment env, AppDbContext dbContext, IFileStorageService fileStorageService)
    {
        _env = env;
        _dbContext = dbContext;
        _fileStorageService = fileStorageService;
    }
    
    
    [HttpGet]
    public async Task<ActionResult<HealthReport>> Get()
    {
        // Get GIT_COMMIT and GIT_BRANCH from environment variables
        var gitCommit = Environment.GetEnvironmentVariable("GIT_COMMIT");
        var gitBranch = Environment.GetEnvironmentVariable("GIT_BRANCH");
        
        // Get Application Version
        var assembly = Assembly.GetEntryAssembly();
        var applicationVersion = assembly?.GetCustomAttribute<AssemblyInformationalVersionAttribute>()?.InformationalVersion
                                 ?? assembly?.GetName().Version?.ToString();

        // Get ASP.NET Core Environment
        var aspNetCoreEnvironment = _env.EnvironmentName;

        // Get Timestamp
        var timestamp = DateTimeOffset.UtcNow;

        // Get Uptime
        var processStartTime = Process.GetCurrentProcess().StartTime.ToUniversalTime();
        var uptime = timestamp - processStartTime;
        
        // Check database connectivity and responsiveness
        var canConnectToDatabase = _dbContext.Database.CanConnect();
        
        // Check if we can list patients
        var canListPatients = false;
        try
        {
            // Attempt to list patients to check if the database is responsive
            _ = _dbContext.Patients.Any();
            canListPatients = true;
        }
        catch
        {
            // If an exception occurs, we assume we cannot list patients
            canListPatients = false;
        }
        
        // Check file storage service health
        var isFileStorageHealthy = await _fileStorageService.IsHealthyAsync();

        // Determine overall health status
        var status = "Healthy";
        if (!canConnectToDatabase) status = "Unhealthy - Cannot connect to database";
        else if (!canListPatients) status = "Unhealthy - Cannot list patients";
        else if (!isFileStorageHealthy) status = "Unhealthy - File storage service is not healthy";

        // Construct the response
        var report = new HealthReport(
            Status: status, // Indicates the API endpoint itself is responsive
            CanConnectToDatabase: canConnectToDatabase,
            CanListPatients: canListPatients,
            IsFileStorageHealthy: isFileStorageHealthy,
            GitCommit: gitCommit,
            GitBranch: gitBranch,
            ApplicationVersion: applicationVersion,
            AspNetCoreEnvironment: aspNetCoreEnvironment,
            Timestamp: timestamp,
            Uptime: uptime
        );
        
        return Ok(report);
    }
}