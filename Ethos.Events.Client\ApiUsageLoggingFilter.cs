﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Net.Mime;
using Ethos.Auth;
using System.Reflection;
using Microsoft.Extensions.DependencyInjection;

namespace Ethos.Events.Client
{
    /// <summary>
    /// 
    /// </summary>
    public class ApiCallEventData
    {
        public string? Endpoint { get; set; }
        public long? RequestSize { get; set; }
        public long? ResponseSize { get; set; }
        public string? Filename { get; set; }
        public string? ContentType { get; set; }
        public string? Method { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ApiUsageLoggingFilter : IAsyncActionFilter
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="eventClient"></param>
        public ApiUsageLoggingFilter(ILogger<ApiUsageLoggingFilter> logger, IEthosEventClient eventClient) 
        {
            _logger = logger;
            _eventClient = eventClient;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        class ObjectIdClass<T> : ObjectIdClass
        {
            T? obj;

            public T? Id
            {
                get
                {
                    return obj;
                }

                set
                {
                    obj = value;
                    IdValue = value;
                }
            }
        }

        class ObjectIdClass
        {
            public object? IdValue { get; set;  }
        }

        readonly ILogger<ApiUsageLoggingFilter> _logger;
        readonly IEthosEventClient _eventClient;
        string? _token, _tokenScheme;
        readonly bool _trackDataUsage = true;
        long? requestSize, responseSize;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        ObjectIdClass? GetClassResult(string input)
        {
            try
            {
                return JsonConvert.DeserializeObject<ObjectIdClass<Guid>>(input);
            }
            catch
            {
                try
                {
                    return JsonConvert.DeserializeObject<ObjectIdClass<long>>(input);
                }
                catch
                {
                    try
                    {
                        return JsonConvert.DeserializeObject<ObjectIdClass<string>>(input);
                    }
                    catch
                    {
                        return default;
                    }
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="context"></param>
        /// <param name="next"></param>
        /// <returns></returns>
        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            var resultContext = await next(); // This calls the action method and any subsequent filters

            if (_eventClient is null || string.IsNullOrEmpty(_eventClient.BaseUri))
            {
                _logger.LogWarning("Invalid event system configuration: cannot send API event to logging service.");
                return;
            }

            var endpoint = context.HttpContext.GetEndpoint();
            if (endpoint is null)
            {
                _logger.LogWarning("No endpoint found: cannot create API event.");
                return;
            }

            var result = resultContext.Result;

            try
            {

                var endpointStr = resultContext.HttpContext.Request.Path.Value;
                var controllerName = endpoint.GetControllerName();

                object? methodResult = null;
                Type? resultType = null;

                var evt = new EthosEventDto<ApiCallEventData>()
                {
                    EventData = new()
                    {
                        Method = resultContext.HttpContext.Request.Method,
                        Endpoint = endpointStr,
                        ContentType = resultContext.HttpContext.Response.ContentType,
                        RequestSize = requestSize,
                        ResponseSize = responseSize,
                    },
                    Type = "ApiCall",
                    Name = $"API Call: {resultContext.HttpContext.Request.Method} {endpointStr}",
                    GeneratedBy = resultContext.HttpContext.User.GetUniqueId(),
                    TenantId = resultContext.HttpContext.User.GetTenantId(),
                    EventTime = DateTimeOffset.UtcNow,
                    CorrelationIds = [new() { Type = "http", Value = resultContext.HttpContext.TraceIdentifier }]
                };

                if (result is JsonResult jsonResult)
                {
                    if (jsonResult.Value is not null)
                    {
                        methodResult = jsonResult.Value;
                        resultType = methodResult.GetType();
                        _logger.LogDebug("Found controller method return value: {Type}", nameof(JsonResult));
                    }
                }
                else if (result is ObjectResult objResult)
                {
                    if (objResult.Value is not null)
                    {
                        methodResult = objResult.Value;
                        resultType = methodResult.GetType();
                        _logger.LogDebug("Found controller method return value: {Type}", nameof(ObjectResult));
                    }
                }
                else if (result is ViewResult viewResult)
                {
                    if (viewResult.Model is not null)
                    {
                        methodResult = viewResult.Model;
                        resultType = methodResult.GetType();
                        _logger.LogDebug("Found controller method return value: {Type}", nameof(ViewResult));
                    }
                }
                else if (result is ContentResult contentResult)
                {
                    _logger.LogDebug("Found controller method return value: {Type}", nameof(ContentResult));

                    if (!string.IsNullOrEmpty(contentResult.Content) && !string.IsNullOrEmpty(contentResult.ContentType))
                    {
                        try
                        {
                            if (MediaTypeNames.Application.Json.Equals(contentResult.ContentType, StringComparison.OrdinalIgnoreCase))
                                methodResult = GetClassResult(contentResult.Content);
                            else
                                _logger.LogDebug("Cannot process content result of type {ContentType}", contentResult.ContentType);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error processing content result.");
                            methodResult = null;
                        }
                    }
                    else
                    {
                        _logger.LogDebug("Content result is empty.");
                    }
                }
                else if (result is FileResult fileResult)
                {
                    evt.EventData.Filename = fileResult.FileDownloadName;
                    _logger.LogDebug("Found controller method return value: {Type}", nameof(FileResult));
                }

                // Add more else if blocks for other ActionResult types (e.g., RedirectResult, FileResult)
                // For RedirectResult, you can get redirectUrl = ((RedirectResult)result).Url;

                if (methodResult is not null)
                {
                    string? idVal;
                    if (methodResult is ObjectIdClass idCls)
                        idVal = idCls.IdValue?.ToString();
                    else
                    {
                        idVal = null;

                        resultType ??= methodResult.GetType();
                        var bindingFlags = BindingFlags.Public | BindingFlags.Instance | BindingFlags.FlattenHierarchy;
                        var idProp = resultType.GetProperty("Id", bindingFlags) ?? resultType.GetProperty("id", bindingFlags);
                        if (idProp is not null)
                            idVal = idProp.GetValue(methodResult)?.ToString();
                    }

                    var correlationIdType = resultType?.Name ?? controllerName;

                    if (!string.IsNullOrEmpty(idVal) && !string.IsNullOrEmpty(correlationIdType))
                        evt.CorrelationIds = [.. evt.CorrelationIds, new() { Type = correlationIdType, Value = idVal }];
                }

                // get the current token
                var tokenVal = resultContext.HttpContext.Request.Headers.Authorization.FirstOrDefault();
                if (!string.IsNullOrEmpty(tokenVal))
                {
                    var tokenVals = tokenVal.Split(' ');
                    if (tokenVals?.Length >= 2)
                    {
                        _token = tokenVals[1];
                        _tokenScheme = tokenVals[0];
                    }
                }

                try
                {
                    if (!string.IsNullOrEmpty(_token))
                        _eventClient.BearerToken = _token;

                    var eventId = await _eventClient.CreateEventWithData(evt);

                    if (eventId.HasValue && eventId.Value != default)
                        _logger.LogDebug("Created API usage event with ID {EventId}", eventId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error sending API event to event system: {Message}", ex.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during API usage logging.");
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="stream"></param>
        /// <returns></returns>
        static async Task<long> GetStreamLength(Stream stream)
        {
            long originalPosition = stream.Position;
            stream.Position = 0;

            long length = 0;
            using (var memoryStream = new MemoryStream())
            {
                await stream.CopyToAsync(memoryStream);
                length = memoryStream.Length;
            }

            stream.Position = originalPosition;
            return length;
        }
    }

    /// <summary>
    /// 
    /// </summary>
    public static class ApiUsageLoggingFilterExtensions
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection AddApiUsageLoggingFilter(this IServiceCollection services)
        {
            return services.AddScoped<ApiUsageLoggingFilter>();
        }
    }
}
