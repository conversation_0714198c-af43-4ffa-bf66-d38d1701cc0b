using Microsoft.AspNetCore.Authorization;

namespace Ethos.Auth;

[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true, Inherited = true)]
public class EthosAuthorize : AuthorizeAttribute, IAuthorizeData, IAuthorizationRequirement
{
    public EthosAuthorize() : base(nameof(EthosAuthorize)) { }

    public EthosAuthorize(
        string? app = default, 
        string? module = default,
        string? feature = default,
        string? scope = default)
        : base(nameof(EthosAuthorize)) 
    {
        App = app;
        Module = module;
        Feature = feature;
        Scope = scope;
    }
    
    public EthosAuthorize(
        string? module = default, 
        string? feature = default, 
        string? scope= default) 
        : base(nameof(EthosAuthorize)) 
    {
        Module = module;
        Feature = feature;
        Scope = scope;
    }
    
    public string? App { get; }
    public string? Module { get; }
    public string? Feature { get; }
    public string? Scope { get; }

    //public string? Policy { get; set; }
    //public string? Roles { get; set; }
    //public string? AuthenticationSchemes { get; set; }
}