import _ from 'lodash';
import moment from 'moment/moment';
import { Raw } from 'typeorm';
import { BadRequestException, Injectable } from '@nestjs/common';
import { IListResult } from '@app/common/types';
import { TechnicianScheduleRepository } from '@app/modules/technicianSchedule/technician.schedule.repository';
import { TechnicianScheduleEntity } from '@app/modules/technicianSchedule/technician.schedule.entity';
import { TechnicianScheduleFiltersDto } from '@app/modules/technicianSchedule/dto/technician.schedule.filters.dto';
import { CreateTechnicianScheduleDto } from '@app/modules/technicianSchedule/dto/create.technician.schedule.dto';
import { FacilityService } from '@app/modules/facility/facility.service';
import { TechnicianService } from '@app/modules/technician/technician.service';
import { IGetAvailableTechnicianParams, ITechnicianScheduleFilters } from '@app/modules/technicianSchedule/types';
import { TechnicianScheduleDto } from '@app/modules/technicianSchedule/dto/technician.schedule.dto';
import {
  UpsertTechnicianScheduleResultDto,
} from '@app/modules/technicianSchedule/dto/upsert.technician.schedule.result.dto';
import { UpsertTechnicianScheduleDto } from '@app/modules/technicianSchedule/dto/upsert.technician.schedule.dto';
import { ETechnicianScheduleShift } from '@app/modules/technicianSchedule/enums';
import { TechnicianEntity } from '@app/modules/technician/technician.entity';

@Injectable()
export class TechnicianScheduleService {
  constructor(
    private readonly repository: TechnicianScheduleRepository,
    private readonly facilityService: FacilityService,
    private readonly technicianService: TechnicianService,
  ) {}

  async list({ clinicId, ...filters }: TechnicianScheduleFiltersDto): Promise<IListResult<TechnicianScheduleEntity>> {
    const where: ITechnicianScheduleFilters = { ...filters };
    if (clinicId) {
      const { data: facilities } = await this.facilityService.list({ clinicId });
      const facilityIds = facilities.map(({ id }) => id);

      if (facilityIds.length) {
        where.facilityId = Raw(() => `(facility_id IN (${facilityIds}) OR facility_id IS NULL)`);
      } else {
        where.facilityId = Raw(() => 'facility_id IS NULL');
      }
    }

    return this.repository.list(where);
  }

  async getByIdOrFail(scheduleId: number): Promise<TechnicianScheduleEntity> {
    const schedule = await this.repository.findOne({
      where: {
        id: scheduleId,
      },
      relations: ['technician'],
    });

    if (!schedule) {
      throw new BadRequestException(`Technician schedule ${scheduleId} not found`);
    }

    return schedule;
  }

  async create(schedule: CreateTechnicianScheduleDto): Promise<TechnicianScheduleEntity> {
    const technician = await this.technicianService.getByIdOrFail(schedule.technicianId);

    const existed = await this.repository.findOne({
      where: {
        technicianId: schedule.technicianId,
        date: schedule.date,
      },
    });

    if (existed) {
      throw new BadRequestException('Technician schedule already exists');
    }

    if (schedule.facilityId) {
      const facility = await this.facilityService.getByIdOrFail(schedule.facilityId);

      if (facility.clinicId !== technician.clinicId) {
        throw new BadRequestException('Technician and facility from different clinics');
      }
    }

    const entity = this.repository.create(schedule);
    const created = await this.repository.save(entity);

    return this.getByIdOrFail(created.id);
  }

  async upsert(dto: UpsertTechnicianScheduleDto): Promise<UpsertTechnicianScheduleResultDto> {
    const items = await Promise.all(dto.items.map(async (sourceItem) => {
      try {
        if (sourceItem.id) {
          await this.delete(sourceItem.id);
        }

        const item = await this.create(_.omit(sourceItem, 'id'));

        return {
          item: TechnicianScheduleService.mapToDto(item),
        };
      } catch (error: any) {

        return {
          sourceItem,
          error: error?.message,
        };
      }
    }));

    return { items };
  }

  async delete(technicianScheduleId: number): Promise<TechnicianScheduleEntity> {
    const technicianSchedule = await this.getByIdOrFail(technicianScheduleId);

    await this.repository.checkHasSchedules(technicianSchedule.technicianId, technicianSchedule.date);
    await this.repository.softRemove(technicianSchedule);

    return technicianSchedule;
  }

  async getAvailableTechnicianId({ facility, shift, date, technicianCounts, studyCredentials }: IGetAvailableTechnicianParams): Promise<TechnicianEntity> {
    const technicians = await this.technicianService.getTechniciansToDate(facility, date);
    const schedules = await this.repository.find({
      where: {
        facilityId: facility.id,
        shift,
        date,
      },
      relations: ['technician', 'technician.credentials'],
    });

    const technicianSchedules = schedules.map(({ capacity, technician }) => ({ capacity, technician }));
    technicians.forEach((technician) => {
      const schedule = technicianSchedules.find((item) => item.technician.id === technician.id);

      if (!schedule) {
        technicianSchedules.push({ capacity: technician.capacity, technician });
      }
    });

    const filteredSchedules = technicianSchedules.filter(
      (schedule) => {
        const technicianCount = technicianCounts[schedule.technician.id] || 0;

        if (!schedule.capacity || schedule.capacity <= technicianCount) {
          return false;
        }

        if (!studyCredentials.length) {
          return true;
        }

        return studyCredentials.some(
          (studyCredential) =>
            studyCredential.credentials.every(
              (credential) => schedule.technician.credentials.find(
                (technicianCredential) => {
                  if (technicianCredential.credentialId !== credential) {
                    return;
                  }

                  if (technicianCredential.validUntil) {
                    const validUntilDate = moment(technicianCredential.validUntil);
                    const day = moment(date);
                    const diff = validUntilDate.diff(day, 'days');

                    if (diff < 0) {
                      return;
                    }

                    if (diff === 0 && shift === ETechnicianScheduleShift.Night) {
                      return;
                    }
                  }
                  return technicianCredential;
                }
              )
            )
        );
      }
    );

    if (!filteredSchedules.length) {
      throw new BadRequestException(`All technicians are unavailable at ${date}`);
    }

    const min = filteredSchedules.reduce((acc, item) => {
      const technicianCount = technicianCounts[item.technician.id] || 0;

      return technicianCount < acc ? technicianCount : acc;
    }, Number.MAX_SAFE_INTEGER);

    const minSchedules = filteredSchedules.filter((item) => {
      const technicianCount = technicianCounts[item.technician.id] || 0;

      return technicianCount === min;
    });

    const sorted = minSchedules.sort((a, b) => {
      return a.technician.name > b.technician.name ? 0.5 : -0.5;
    });

    return sorted[0].technician;
  }

  static mapToDto(entity: TechnicianScheduleEntity): TechnicianScheduleDto {
    const dto = new TechnicianScheduleDto();

    dto.technicianName = entity.technician.name;
    dto.id = entity.id;
    dto.technicianId = entity.technicianId;
    dto.facilityId = entity.facilityId;
    dto.shift = entity.shift;
    dto.capacity = entity.capacity;
    dto.date = entity.date;

    return dto;
  }
}
