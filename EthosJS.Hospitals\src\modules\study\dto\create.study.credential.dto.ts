import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsInt, IsOptional, IsPositive } from 'class-validator';

export class CreateStudyCredentialDto {
  @ApiProperty({ isArray: true, type: 'integer' })
  @IsArray()
  @ArrayMinSize(1)
  @IsInt({ each: true })
  @IsPositive({ each: true })
  credentials: number[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  @IsPositive()
  stateId?: number;
}
