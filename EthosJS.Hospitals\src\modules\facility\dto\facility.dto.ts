import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BaseEntity } from '@app/common/base.entity';
import { FacilityEquipmentDto } from '@app/modules/facility/dto/facility.equipment.dto';

export class FacilityDto extends BaseEntity {
  @ApiProperty()
  @Expose()
  name: string;

  @ApiPropertyOptional()
  @Expose()
  addressLine1?: string;

  @ApiPropertyOptional()
  @Expose()
  addressLine2?: string;

  @ApiPropertyOptional()
  @Expose()
  zip?: string;

  @ApiPropertyOptional()
  @Expose()
  phone?: string;

  @ApiPropertyOptional()
  @Expose()
  fax?: string;

  @ApiProperty()
  @Expose()
  capacity: number;

  @ApiProperty()
  @Expose()
  cityId: number;

  @ApiProperty()
  @Expose()
  cityName: string;

  @ApiProperty()
  @Expose()
  clinicId: number;

  @ApiProperty()
  @Expose()
  clinicName: string;

  @ApiProperty({ type: FacilityEquipmentDto, isArray: true })
  @Expose()
  equipments: FacilityEquipmentDto[];
}
