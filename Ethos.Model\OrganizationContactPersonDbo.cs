using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class OrganizationContactPersonDbo : IAuditableEntity<OrganizationContactPersonDbo>
{
    public Guid ParentId { get; set; }
    public virtual OrganizationContactDetailDbo Parent { get; set; } = null!;

    public required PersonNameDbo Name { get; set; }
    
    public Guid ContactDetailId { get; set; }
    public PersonalContactDetailDbo ContactDetail { get; set; } = null!;

    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<OrganizationContactPersonDbo>(Register);
    
    public new static void Register(EntityTypeBuilder<OrganizationContactPersonDbo> entity)
    {
        IAuditableEntity<OrganizationContactPersonDbo>.Register(entity);
        
        entity.HasOne(p => p.ContactDetail)
            .WithMany()
            .HasForeignKey(p => p.ContactDetailId)
            .OnDelete(DeleteBehavior.Cascade);

        entity.OwnsOne(e => e.Name, PersonNameDbo.Configure);
    }
}