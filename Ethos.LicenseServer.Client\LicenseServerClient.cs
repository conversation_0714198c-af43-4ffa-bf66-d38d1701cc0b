﻿using System.Text.Json;
using Microsoft.AspNetCore.WebUtilities;

namespace Ethos.LicenseServer.Client
{
    /// <summary>
    /// 
    /// </summary>
    public interface ILicenseServerClient :  IWebClient, IDisposable
    {
        Task<LicenseServerLicense?> CreateTenantLicense(Guid tenantId, DateTimeOffset effectiveDate, DateTimeOffset expirationDate, string? state);
        Task<List<LicenseServerLicense>> ListTenantLicenses(Guid tenantId);
        Task<LicenseServerUserAccount?> AddUserToTenant(Guid tenantId, Guid userDirectoryId);
        Task<LicenseServerLicensedProduct?> AddProductToLicense(Guid tenantId, Guid licenseId, Guid productId, string name, params string[] permissions);
        Task<LicenseServerFeature?> AddFeatureToProduct(Guid licensedProductId, string name, string? description, params LicenseServerEligibilityRule[] eligibilityRules);
        Task<List<LicenseServerFeature>> ListFeaturesForProduct(Guid licensedProductId);
        Task<List<LicenseServerLicensedProduct>> ListProductsForLicense(Guid tenantId, Guid licenseId);
        Task<List<LicenseServerTenant>> ListTenantsForUser(Guid userDirectoryId);
        Task<LicenseServerPreTokenIssuanceResponse?> DoAzurePreTokenIssuance(string clientId, Guid directoryUserId, string email, string displayName);
    }

    /// <summary>
    /// 
    /// </summary>
    public class LicenseServerClient : WebClient, IWebClient, IDisposable, ILicenseServerClient
    {
        const string v1 = nameof(v1);
        const string api = nameof(api);
        const string ApiUri = $"/{api}/{v1}";
        const string licenses = nameof(licenses);
        const string accounts = nameof(accounts);
        const string users = nameof(users);
        const string products = nameof(products);
        const string features = nameof(features);
        const string limit = nameof(limit);
        const string ACTIVE = nameof(ACTIVE);
        const string PreTokenIssuance = nameof(PreTokenIssuance);
        const int DefaultLimit = 250;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="httpClient"></param>
        public LicenseServerClient(HttpClient httpClient) : base(httpClient)
        {
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="httpClient"></param>
        /// <param name="baseUri"></param>
        public LicenseServerClient(HttpClient httpClient, string baseUri) : this(httpClient)
        {
            BaseUri = baseUri;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="guid"></param>
        /// <param name="argumentName"></param>
        /// <exception cref="ArgumentException"></exception>
        static void ThrowIfGuidEmpty(Guid? guid, string argumentName)
        {
            if (guid.HasValue && guid.Value == Guid.Empty)
                throw new ArgumentException($"Invalid ID: {guid.Value}", argumentName);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="path"></param>
        /// <param name="queryParams"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Uri CreateUri(string path, Dictionary<string, string>? queryParams = null)
        {
            if (string.IsNullOrEmpty(BaseUri))
                throw new Exception("No base URI found for reference data service.");

            var uriBuilder = new UriBuilder(BaseUri)
            {
                Path = path
            };

            if (queryParams != null && queryParams.Count > 0)
                uriBuilder.Query = QueryHelpers.AddQueryString(uriBuilder.Query, queryParams);

            return uriBuilder.Uri;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="uriPath"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        async Task<List<T>> GetPagedResult<T>(string uriPath, Dictionary<string, string>? query = null) where T : ILicenseServerEntity
        {
            var list = new List<T>();
            query ??= [];

            var offset = 0;

            if (!query.TryGetValue(limit, out _))
                query.Add(limit, DefaultLimit.ToString());

            if (!query.TryGetValue(nameof(offset), out _))
                query.Add(nameof(offset), offset.ToString());

            var totalCount = 0;
            var uri = CreateUri(uriPath, query);

            do
            {
                var result = await GetStringAsync(uri).ConfigureAwait(false);

                if (string.IsNullOrEmpty(result))
                    break;

                var parsedResult = JsonSerializer.Deserialize<LicensePagedResult<T>>(result, JsonOptions);
                if (parsedResult != null)
                {
                    totalCount = parsedResult.Pagination?.Total ?? 0;
                    if (parsedResult.Data.Count > 0)
                        list.AddRange(parsedResult.Data);

                    if (!int.TryParse(query[nameof(offset)], out offset))
                        break;

                    query[nameof(offset)] = (offset + limit).ToString();
                    uri = CreateUri(uriPath, query);

                }
            } while (list.Count < totalCount && uri != null);

            return list;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        public async Task<List<LicenseServerLicense>> ListTenantLicenses(Guid tenantId)
        {
            ThrowIfGuidEmpty(tenantId, nameof(tenantId));
            var offset = 0;
            return await GetPagedResult<LicenseServerLicense>($"{ApiUri}/{accounts}/{tenantId}/{licenses}",
                                                              new() { { limit, DefaultLimit.ToString() }, { nameof(offset), offset.ToString() } });
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tenantId">ID for existing tenant from Ethos tenant manager service.</param>
        /// <param name="startDate"></param>
        /// <param name="expirationDate"></param>
        /// <returns></returns>
        public async Task<LicenseServerLicense?> CreateTenantLicense(Guid tenantId, DateTimeOffset startDate, DateTimeOffset expirationDate,
            string? state = "ACTIVE")
        {
            ThrowIfGuidEmpty(tenantId, nameof(tenantId));

            if (string.IsNullOrEmpty(state))
                state = "ACTIVE";

            var result = await PostJsonAsync(CreateUri($"{ApiUri}/{accounts}/{tenantId}/{licenses}"),
                                             new LicenseServerLicense()
                                             {
                                                 StartDate = startDate.ToUnixTimeSeconds(),
                                                 ExpirationDate = expirationDate.ToUnixTimeSeconds(),
                                                 State = state,
                                             });
            return JsonSerializer.Deserialize<LicenseServerLicense>(await result.Content.ReadAsStringAsync(), JsonOptions);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="licenseId"></param>
        /// <param name="name"></param>
        /// <param name="permissions"></param>
        /// <returns></returns>
        public async Task<LicenseServerLicensedProduct?> AddProductToLicense(Guid tenantId, Guid licenseId, Guid productId, string name, params string[] permissions)
        {
            ThrowIfGuidEmpty(tenantId, nameof(tenantId));
            ThrowIfGuidEmpty(licenseId, nameof(licenseId));
            ThrowIfGuidEmpty(productId, nameof(productId));

            if (string.IsNullOrEmpty(name))
                throw new ArgumentException("License product name is required.", nameof(name));

            var result = await PostJsonAsync(CreateUri($"{ApiUri}/{accounts}/{tenantId}/{licenses}/{licenseId}/{products}"),
                                             new LicenseServerLicensedProduct()
                                             {
                                                 Name = name,
                                                 Permissions = permissions ?? [],
                                                 Product = new LicenseServerProduct()
                                                 {
                                                     Id = productId
                                                 },
                                             });
            return JsonSerializer.Deserialize<LicenseServerLicensedProduct>(await result.Content.ReadAsStringAsync(), JsonOptions);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="licensedProductId">ID of the product assigned to a specific license. This is NOT the product ID.</param>
        /// <param name="name"></param>
        /// <param name="description"></param>
        /// <param name="eligibilityRules"></param>
        /// <returns></returns>
        public async Task<LicenseServerFeature?> AddFeatureToProduct(Guid licensedProductId, string name, string? description, params LicenseServerEligibilityRule[] eligibilityRules)
        {
            ThrowIfGuidEmpty(licensedProductId, nameof(licensedProductId));

            if (string.IsNullOrEmpty(name))
                throw new ArgumentException("Feature name is required.", nameof(name));

            if (string.IsNullOrEmpty(description))
                description = null;

            var result = await PostJsonAsync(CreateUri($"{ApiUri}/{products}/{licensedProductId}/{features}"),
                                             new LicenseServerFeature()
                                             {
                                                 Name = name,
                                                 Description = description,
                                                 EligibilityRules = eligibilityRules ?? []
                                             });

            return JsonSerializer.Deserialize<LicenseServerFeature>(await result.Content.ReadAsStringAsync(), JsonOptions);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tenantId">ID of the tenant from external Ethos tenant manager.</param>
        /// <param name="userDirectoryId">ID of the user from Azure AD B2C or external directory service (typically the objectGUID).</param>
        /// <returns></returns>
        public async Task<LicenseServerUserAccount?> AddUserToTenant(Guid tenantId, Guid userDirectoryId)
        {
            ThrowIfGuidEmpty(tenantId, nameof(tenantId));
            ThrowIfGuidEmpty(userDirectoryId, nameof(userDirectoryId));

            var result = await PostJsonAsync(CreateUri($"{ApiUri}/{users}/{userDirectoryId}/{accounts}"),
                                             new LicenseServerUserAccount()
                                             {
                                                 Account = new LicenseServerTenant()
                                                 {
                                                     Id = tenantId,
                                                 },
                                                 State = ACTIVE,
                                             });
            return JsonSerializer.Deserialize<LicenseServerUserAccount>(await result.Content.ReadAsStringAsync(), JsonOptions);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="licensedProductId"></param>
        /// <returns></returns>
        public async Task<List<LicenseServerFeature>> ListFeaturesForProduct(Guid licensedProductId)
        {
            ThrowIfGuidEmpty(licensedProductId, nameof(licensedProductId));
            var offset = 0;
            return await GetPagedResult<LicenseServerFeature>($"{ApiUri}/{products}/{licensedProductId}/{features}",
                                                                new() { { limit, DefaultLimit.ToString() }, { nameof(offset), offset.ToString() } });
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="licenseId"></param>
        /// <returns></returns>
        public async Task<List<LicenseServerLicensedProduct>> ListProductsForLicense(Guid tenantId, Guid licenseId)
        {
            ThrowIfGuidEmpty(tenantId, nameof(tenantId));
            ThrowIfGuidEmpty(licenseId, nameof(licenseId));
            var offset = 0;
            return await GetPagedResult<LicenseServerLicensedProduct>($"{ApiUri}/{accounts}/{tenantId}/{licenses}/{licenseId}/{products}",
                                                                        new() { { limit, DefaultLimit.ToString() }, { nameof(offset), offset.ToString() } });
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userDirectoryId">ID of the user from Azure AD B2C or external directory service (typically the objectGUID).</param>
        /// <returns></returns>
        public async Task<List<LicenseServerTenant>> ListTenantsForUser(Guid userDirectoryId)
        {
            ThrowIfGuidEmpty(userDirectoryId, nameof(userDirectoryId));
            var offset = 0;
            return await GetPagedResult<LicenseServerTenant>($"{ApiUri}/{users}/{userDirectoryId}/{accounts}",
                                                              new() { { limit, DefaultLimit.ToString() }, { nameof(offset), offset.ToString() } });
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="clientId"></param>
        /// <param name="directoryUserId"></param>
        /// <param name="email"></param>
        /// <param name="displayName"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public async Task<LicenseServerPreTokenIssuanceResponse?> DoAzurePreTokenIssuance(string clientId,
                                                                                          Guid directoryUserId, 
                                                                                          string email,
                                                                                          string displayName)
        {
            ThrowIfGuidEmpty(directoryUserId, nameof(directoryUserId));
            if (string.IsNullOrEmpty(clientId))
                throw new ArgumentException("Client ID is required.", nameof(clientId));
            var result = await PostJsonAsync(CreateUri($"{ApiUri}/{licenses}"),
                                            new
                                            {
                                                email = string.IsNullOrEmpty(email) ? "<EMAIL>" : email,
                                                displayName = string.IsNullOrEmpty(displayName) ? "Display Name" : displayName,
                                                client_id = clientId,
                                                step = PreTokenIssuance,
                                                ui_locales = "en-US",
                                                objectId = directoryUserId.ToString(),
                                                identities = new object[] {
                                                     new {
                                                              signInType = "federated",
                                                              issuer = "ethosbridge.com",
                                                              issuerAssignedId = "**********"
                                                         }
                                                },   
                                            });

            return JsonSerializer.Deserialize<LicenseServerPreTokenIssuanceResponse>(await result.Content.ReadAsStringAsync(), JsonOptions);
        }

        /// <summary>
        /// 
        /// </summary>
        ~LicenseServerClient()
        {
            Dispose(false);
        }
    }
}
