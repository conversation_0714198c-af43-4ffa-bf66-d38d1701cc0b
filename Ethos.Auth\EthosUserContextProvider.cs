using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;

namespace Ethos.Auth;

public interface IEthosUserContextProvider
{
    ClaimsPrincipal? GetUser();
    
    string? TenantId { get; }
    string? UserId { get; }
    string? Email { get; }
    string? FirstName { get; }
    string? LastName { get; }
    string? FullName { get; }
}

public class EthosUserContextProvider(IServiceProvider serviceProvider) : IEthosUserContextProvider
{
    public ClaimsPrincipal? GetUser() {
        using var scope = serviceProvider.CreateScope();
        var sp = scope.ServiceProvider;
        var accessor = sp.GetRequiredService<IHttpContextAccessor>();
        var context = accessor.HttpContext;
        var user = context?.User;
        return user;
    }

    public string? TenantId => GetUser()?.FindFirstValue(JwtRegisteredClaimNames.Aud);
    public string? UserId => GetUser()?.FindFirstValue(JwtRegisteredClaimNames.Sub);
    public string? Email => GetUser()?.FindFirstValue(ClaimTypes.Email);
    public string? FirstName => GetUser()?.FindFirstValue(ClaimTypes.GivenName);
    public string? LastName => GetUser()?.FindFirstValue(ClaimTypes.Surname);
    public string? FullName => $"{FirstName} {LastName}";
}