import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CityRepository } from '@app/modules/city/city.repository';
import { CityService } from '@app/modules/city/city.service';
import { CityController } from '@app/modules/city/city.controller';
import { StateModule } from '@app/modules/state/state.module';

@Module({
  imports: [
    StateModule,
    TypeOrmModule.forFeature([CityRepository]),
  ],
  providers: [CityService],
  controllers: [CityController],
  exports: [CityService],
})
export class CityModule {}
