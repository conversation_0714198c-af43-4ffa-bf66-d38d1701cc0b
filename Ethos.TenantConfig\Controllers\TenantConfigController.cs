﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.JsonPatch;
using Newtonsoft.Json;
using System.Text;
using System.Security.Cryptography;

namespace Ethos.TenantConfig.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("api/tenants/{tenantId}/config")]
    public class TenantConfigController : ControllerBase
    {
        private readonly ILogger<TenantConfigController> Logger;
        readonly IServiceScopeFactory scopeFactory;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="scopeFactory"></param>
        public TenantConfigController(ILogger<TenantConfigController> logger, IServiceScopeFactory scopeFactory)
        {
            Logger = logger;
            this.scopeFactory = scopeFactory;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        [HttpGet]
        public ActionResult<List<TenantConfigSection>> GetConfig([FromRoute] Guid tenantId)
        {
            using (var scope = scopeFactory.CreateScope())
            {
                var dbContext = scope.ServiceProvider.GetRequiredService<ConfigDbContext>();

                var tenantConfig = dbContext.Configurations.Where(c => c.TenantId == tenantId).ToList();

                if (tenantConfig is null || tenantConfig.Count == 0)
                    return Ok(Array.Empty<object>());

                foreach (var c in tenantConfig)
                    c.Etag = GenerateEtag(c);

                return Ok(tenantConfig);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="section"></param>
        /// <returns></returns>
        static string GenerateEtag(TenantConfigSection section)
        {
            section.Etag = null;
            var hashBytes = SHA256.HashData(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(section)));
            return Convert.ToBase64String(hashBytes);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="currentTag"></param>
        /// <param name="requestTag"></param>
        /// <returns></returns>
        static bool IfMatch(string currentTag, string requestTag)
        {
            if (currentTag.StartsWith("W/"))
                currentTag = currentTag.Substring(2);
            if (requestTag.StartsWith("W/"))
                requestTag = requestTag.Substring(2);
            return string.Equals(requestTag.Trim('"'), currentTag.Trim('"'));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="section"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{section}")]
        public ActionResult<TenantConfigSection> GetConfigSection([FromRoute] Guid tenantId, [FromRoute] string section)
        {
            using (var scope = scopeFactory.CreateScope())
            {
                var dbContext = scope.ServiceProvider.GetRequiredService<ConfigDbContext>();

                var tenantConfig = dbContext.Configurations.FirstOrDefault(c => c.TenantId == tenantId && string.Equals(section.ToLower(), c.Name.ToLower()));

                if (tenantConfig is null)
                    return Problem($"No such configuration section: {section}", null, 404);

                tenantConfig.Etag = GenerateEtag(tenantConfig);
                this.WithEtag(tenantConfig.Etag ?? string.Empty, false);
                return Ok(tenantConfig);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="config"></param>
        /// <returns></returns>
        [HttpPost]
        public ActionResult<TenantConfigSection> CreateConfig([FromRoute] Guid tenantId, [FromBody] TenantConfigSection config)
        {
            if (string.IsNullOrEmpty(config.Name))
                return ValidationProblem("Section name is required.", null, 422);

            config.TenantId = tenantId;

            using (var scope = scopeFactory.CreateScope())
            {
                var dbContext = scope.ServiceProvider.GetRequiredService<ConfigDbContext>();

                var tenantConfig = dbContext.Configurations.FirstOrDefault(c => c.TenantId == tenantId && string.Equals(config.Name.ToLower(), c.Name.ToLower()));

                if (tenantConfig is not null && tenantConfig.TenantId != Guid.Empty)
                    return Problem($"Configuration already exists for tenant: {config.Name}", null, 409);

                dbContext.Configurations.Add(config);
                dbContext.SaveChanges(true);

                var etag = GenerateEtag(config);
                config.Etag = etag;
                this.WithEtag(etag, false);
                return CreatedAtAction(nameof(GetConfigSection), new { tenantId, section = config.Name }, config);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="updatedConfig"></param>
        /// <param name="ifMatch"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{section}")]
        public ActionResult<TenantConfigSection> ReplaceConfig([FromRoute] Guid tenantId, [FromRoute] string section, [FromBody] TenantConfigSection config, [FromHeader(Name = "If-Match")] string? ifMatch)
        {
            using (var scope = scopeFactory.CreateScope())
            {
                var dbContext = scope.ServiceProvider.GetRequiredService<ConfigDbContext>();

                var tenantConfig = dbContext.Configurations.FirstOrDefault(c => c.TenantId == tenantId && string.Equals(section.ToLower(), c.Name.ToLower()));
                var hasExisting = tenantConfig is not null && tenantConfig.TenantId != Guid.Empty;

                if (!hasExisting)
                {
                    if (string.IsNullOrEmpty(config.Name) || !config.Name.Equals(section, StringComparison.OrdinalIgnoreCase))
                        config.Name = section;
                    config.TenantId = tenantId;
                    return CreateConfig(tenantId, config);
                }

                // this should never happen
                if (tenantConfig is null)
                    return Problem("Unknown error: tenant config exists but was null.", null, 500);

                tenantConfig.TenantId = tenantId;

                if (!string.IsNullOrEmpty(ifMatch))
                {
                    var currentEtag = GenerateEtag(tenantConfig);
                    if (!IfMatch(currentEtag, ifMatch))
                        return Problem("Cannot update tenant configuration: object has changed since last retrieval.", null, 412);
                }

                tenantConfig.Values = config.Values;
                tenantConfig.Secrets = config.Secrets;
                tenantConfig.Storage = config.Storage;

                dbContext.Configurations.Update(tenantConfig);
                dbContext.SaveChanges(true);

                var etag = GenerateEtag(tenantConfig);
                tenantConfig.Etag = etag;
                this.WithEtag(etag, false);
                return Ok(tenantConfig);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="patchDocument"></param>
        /// <param name="ifMatch"></param>
        /// <returns></returns>
        [HttpPatch]
        [Route("{section}")]
        public ActionResult<TenantConfigSection> PatchConfig([FromRoute] Guid tenantId, [FromRoute] string section, [FromBody] JsonPatchDocument<TenantConfigSection> patchDocument, [FromHeader(Name = "If-Match")] string? ifMatch)
        {
            using (var scope = scopeFactory.CreateScope())
            {
                var dbContext = scope.ServiceProvider.GetRequiredService<ConfigDbContext>();

                var tenantConfig = dbContext.Configurations.FirstOrDefault(c => c.TenantId == tenantId && string.Equals(section.ToLower(), c.Name.ToLower()));

                if (tenantConfig is null)
                    return Problem($"No such configuration section: {section}", null, 404);

                if (!string.IsNullOrEmpty(ifMatch))
                {
                    var currentEtag = GenerateEtag(tenantConfig);
                    if (!IfMatch(currentEtag, ifMatch))
                        return Problem("Cannot update tenant configuration: object has changed since last retrieval.", null, 412);
                }

                patchDocument.ApplyTo(tenantConfig);
                tenantConfig.TenantId = tenantId;
                if (string.IsNullOrEmpty(tenantConfig.Name) || !tenantConfig.Name.Equals(section, StringComparison.OrdinalIgnoreCase))
                    tenantConfig.Name = section;
                dbContext.Configurations.Update(tenantConfig);
                dbContext.SaveChanges(true);

                var etag = GenerateEtag(tenantConfig);
                tenantConfig.Etag = etag;
                this.WithEtag(etag, false);
                return Ok(tenantConfig);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="keys"></param>
        /// <param name="ifMatch"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{section}")]
        public ActionResult DeleteConfig([FromRoute] Guid tenantId, [FromRoute] string section, [FromHeader(Name = "If-Match")] string? ifMatch)
        {
            using (var scope = scopeFactory.CreateScope())
            {
                var dbContext = scope.ServiceProvider.GetRequiredService<ConfigDbContext>();

                var tenantConfig = dbContext.Configurations.FirstOrDefault(c => c.TenantId == tenantId && string.Equals(section.ToLower(), c.Name.ToLower()));

                if (tenantConfig is null)
                    return Problem($"No such configuration section: {section}", null, 404);

                if (!string.IsNullOrEmpty(ifMatch))
                {
                    var currentEtag = GenerateEtag(tenantConfig);
                    if (!IfMatch(currentEtag, ifMatch))
                        return Problem("Cannot delete tenant configuration: object has changed since last retrieval.", null, 412);
                }

                dbContext.Configurations.Remove(tenantConfig);
                dbContext.SaveChanges(true);
                return NoContent();
            }
        }
    }
}
