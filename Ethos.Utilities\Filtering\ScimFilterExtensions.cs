﻿using System.Collections;
using System.Linq.Expressions;
using System.Reflection;
using System.Text.RegularExpressions;

namespace Ethos.Utilities.Filtering
{
    /// <summary>
    /// 
    /// </summary>
    public static class ScimFilterExtensions
    {
        // Define supported SCIM operators and their corresponding C# expression types/methods
        static readonly Dictionary<string, ExpressionType> ExpressionOperatorMap = new()
        {
            {"eq", ExpressionType.Equal},
            {"ne", ExpressionType.NotEqual},
            {"gt", ExpressionType.GreaterThan},
            {"ge", ExpressionType.GreaterThanOrEqual},
            {"lt", ExpressionType.LessThan},
            {"le", ExpressionType.LessThanOrEqual}
        };

        static readonly Dictionary<string, string> StringMethodMap = new()
        {
            {"co", "Contains"}, // Contains
            {"sw", "StartsWith"}, // Starts With
            {"ew", "EndsWith"}, // Ends With
            {"has", "Contains"} // 'has' operator for collections, interpreted as 'Any(element.Contains(value))'
        };

        // Regex for single condition (moved here to be reused by the parser)
        static readonly Regex ConditionRegex = new Regex(@"^([\w\.]+)\s+(eq|ne|gt|ge|lt|le|co|sw|ew|pr|has)\s*(?:'(.*?)'|(\S+))?$", RegexOptions.IgnoreCase);

        /// <summary>
        /// Filters a collection of generic objects (class or struct) based on a SCIM-style filter string.
        /// Supports basic SCIM operators: eq, ne, gt, ge, lt, le, co, sw, ew, pr.
        /// Supports logical operators 'and', 'or' and parenthetical grouping.
        /// Automatically converts camelCase property names in the filter to PascalCase for C# properties.
        /// Supports nested properties using dot notation (e.g., "homeAddress.city eq 'London'").
        /// Handles null propagation for nested properties.
        /// </summary>
        /// <typeparam name="T">The type of objects in the collection (can be a class or struct).</typeparam>
        /// <param name="source">The collection of objects to filter.</param>
        /// <param name="filterString">The SCIM-style filter string (e.g., "userName eq 'john.doe'", "homeAddress.city eq 'London'", "age gt 30", "email co 'example.com'", "title pr").</param>
        /// <returns>A filtered IEnumerable of T.</returns>
        /// <exception cref="ArgumentException">Thrown if the filter string is invalid or properties/operators are not found.</exception>
        public static IQueryable<T> Filter<T>(this IQueryable<T> source, string? filterString)
        {
            if (string.IsNullOrWhiteSpace(filterString))
                return source;

            var type = typeof(T);
            var parameter = Expression.Parameter(type, "x");

            // Start the recursive parsing
            var predicate = ParseFilterExpression(filterString.Trim(), type, parameter);

            // Create and compile the final lambda expression for filtering
            var lambda = Expression.Lambda<Func<T, bool>>(predicate, parameter);

            // Apply the compiled filter to the source collection
            return source.AsQueryable().Where(lambda);
        }

        // --- Recursive Parser Methods ---

        /// <summary>
        /// Parses the top-level filter string, handling 'or' logical operations.
        /// 'or' has lower precedence than 'and'.
        /// </summary>
        private static Expression ParseFilterExpression(string filterString, Type type, ParameterExpression parameter)
        {
            // Find the outermost 'or' operator, respecting parentheses
            int orIndex = FindOuterKeyword(filterString, "or");
            if (orIndex != -1)
            {
                string leftPart = filterString.Substring(0, orIndex).Trim();
                string rightPart = filterString.Substring(orIndex + "or".Length).Trim();

                if (string.IsNullOrWhiteSpace(leftPart) || string.IsNullOrWhiteSpace(rightPart))
                {
                    throw new ArgumentException($"Malformed 'or' expression in filter string: '{filterString}'");
                }

                Expression left = ParseAndExpression(leftPart, type, parameter);
                Expression right = ParseFilterExpression(rightPart, type, parameter); // Right side can still contain 'or'
                return Expression.OrElse(left, right);
            }

            // If no 'or', proceed to parse 'and' expressions
            return ParseAndExpression(filterString, type, parameter);
        }

        /// <summary>
        /// Parses an expression, handling 'and' logical operations.
        /// 'and' has higher precedence than 'or'.
        /// </summary>
        private static Expression ParseAndExpression(string filterString, Type type, ParameterExpression parameter)
        {
            // Find the outermost 'and' operator, respecting parentheses
            int andIndex = FindOuterKeyword(filterString, "and");
            if (andIndex != -1)
            {
                string leftPart = filterString.Substring(0, andIndex).Trim();
                string rightPart = filterString.Substring(andIndex + "and".Length).Trim();

                if (string.IsNullOrWhiteSpace(leftPart) || string.IsNullOrWhiteSpace(rightPart))
                {
                    throw new ArgumentException($"Malformed 'and' expression in filter string: '{filterString}'");
                }

                var left = ParsePrimaryExpression(leftPart, type, parameter);
                var right = ParseAndExpression(rightPart, type, parameter); // Right side can still contain 'and'
                return Expression.AndAlso(left, right);
            }

            // If no 'and', proceed to parse primary expressions (single conditions or parenthesized groups)
            return ParsePrimaryExpression(filterString, type, parameter);
        }

        /// <summary>
        /// Parses a primary expression, which can be a single condition or a parenthesized group.
        /// </summary>
        private static Expression ParsePrimaryExpression(string filterString, Type type, ParameterExpression parameter)
        {
            filterString = filterString.Trim(); // Trim whitespace around factors

            // Handle parenthetical grouping
            if (filterString.StartsWith('(') && filterString.EndsWith(')'))
            {
                // Check for correctly matched parentheses
                int parenthesisCount = 0;
                bool validParens = true;
                string innerExpression = filterString.Substring(1, filterString.Length - 2).Trim();

                for (int i = 0; i < filterString.Length; i++)
                {
                    if (filterString[i] == '(') parenthesisCount++;
                    else if (filterString[i] == ')') parenthesisCount--;

                    // If count drops to 0 before the end, it's not the outermost pair
                    if (parenthesisCount == 0 && i < filterString.Length - 1)
                    {
                        validParens = false;
                        break;
                    }
                }
                if (parenthesisCount != 0 || !validParens)
                {
                    throw new ArgumentException($"Mismatched or invalid parentheses in filter string: '{filterString}'");
                }

                return ParseFilterExpression(innerExpression, type, parameter); // Recursively parse the inner expression
            }

            // If not parenthesized, it must be a single condition
            return CreatePropertyPredicate(filterString, type, parameter);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        static Type? GetUnderlyingCollectionType(Type type)
        {
            // If we have a plain array
            if (type.IsArray)
                return type.GetElementType();

            // Generic type that implements IEnumerable<T>
            // We need to find the IEnumerable<T> interface it implements.
            // This handles List<T>, HashSet<T>, T[], and custom collections.
            if (type.IsGenericType && type.GetGenericArguments().Length > 0)
            {
                // Check if it's a direct generic IEnumerable<T>
                if (type.GetGenericTypeDefinition() == typeof(IEnumerable<>))
                    return type.GetGenericArguments()[0];

                // Check if it implements IEnumerable<T> (e.g., List<T> implements IEnumerable<T>)
                var enumerableInterface = type.GetInterfaces()
                                            .FirstOrDefault(i => i.IsGenericType &&
                                                                 i.GetGenericTypeDefinition() == typeof(IEnumerable<>));
                if (enumerableInterface != null)
                    return enumerableInterface.GetGenericArguments()[0];
            }

            // If it's not an array and doesn't implement generic IEnumerable<T>,
            // it's not a collection type we can easily get an element type for.
            return null;
        }

        /// <summary>
        /// Creates an Expression Tree predicate for a single SCIM filter condition.
        /// This method contains the logic for property access, value conversion, and operator mapping.
        /// </summary>
        private static Expression CreatePropertyPredicate(string conditionString, Type type, ParameterExpression parameter)
        {
            var match = ConditionRegex.Match(conditionString);
            if (!match.Success)
                throw new ArgumentException($"Invalid filter condition: '{conditionString}'");

            string propertyNameCamel = match.Groups[1].Value;
            string op = match.Groups[2].Value.ToLowerInvariant();
            string filterValueString = match.Groups[3].Success ? match.Groups[3].Value : match.Groups[4].Value;

            // Split property path into segments (e.g., "homeAddress.city" -> ["homeAddress", "city"])
            string[] propertyPathSegments = propertyNameCamel.Split('.');
            Expression currentPropertyAccess = parameter;
            var currentPropertyType = type;
            Expression? accumulatedNullChecks = null; // Chain of null checks for intermediate properties

            // Build the property access chain for nested properties
            for (int i = 0; i < propertyPathSegments.Length; i++)
            {
                string segment = propertyPathSegments[i];
                string pascalSegment = char.ToUpperInvariant(segment[0]) + segment.Substring(1);

                var prop = currentPropertyType.GetProperty(pascalSegment, BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                if (prop == null)
                {
                    throw new ArgumentException($"Property '{pascalSegment}' not found on type '{currentPropertyType.Name}' in filter path '{propertyNameCamel}'.");
                }

                // If this is not the last segment AND the current property is a reference type or nullable value type,
                // add a null check for this level to the accumulatedNullChecks chain.
                // We do this for all but the last property because 'pr' handles the null check for the final property itself.
                if (i < propertyPathSegments.Length - 1 && (!currentPropertyType.IsValueType || Nullable.GetUnderlyingType(currentPropertyType) != null))
                {
                    Expression segmentNullCheck = Expression.NotEqual(currentPropertyAccess, Expression.Constant(null, currentPropertyAccess.Type));
                    if (accumulatedNullChecks == null)
                    {
                        accumulatedNullChecks = segmentNullCheck;
                    }
                    else
                    {
                        accumulatedNullChecks = Expression.AndAlso(accumulatedNullChecks, segmentNullCheck);
                    }
                }

                currentPropertyAccess = Expression.Property(currentPropertyAccess, prop);
                currentPropertyType = prop.PropertyType;
            }

            Expression? predicate = null;

            if (op == "pr") // "present" operator
            {
                // Refined 'pr' for collections: not null AND has any elements
                if (typeof(IEnumerable).IsAssignableFrom(currentPropertyType) && currentPropertyType != typeof(string))
                {
                    // Check if collection is not null AND has any elements
                    // Equivalent to: collection != null && collection.Any()
                    var elementType = GetUnderlyingCollectionType(currentPropertyType) ?? 
                        throw new ArgumentException($"Cannot determine element type for collection property '{propertyNameCamel}' for 'pr' operator.");

                    var anyMethod = (typeof(Enumerable).GetMethods()
                        .Where(m => m.Name == "Any" && m.IsGenericMethodDefinition && m.GetParameters().Length == 1)
                        .FirstOrDefault()
                        ?.MakeGenericMethod(elementType)) ?? throw new InvalidOperationException($"Enumerable.Any<{elementType.Name}>() method not found.");

                    Expression collectionNotNull = Expression.NotEqual(currentPropertyAccess, Expression.Constant(null, currentPropertyType));
                    Expression collectionHasElements = Expression.Call(anyMethod, currentPropertyAccess);
                    predicate = Expression.AndAlso(collectionNotNull, collectionHasElements);
                }
                else if (currentPropertyType.IsValueType && Nullable.GetUnderlyingType(currentPropertyType) == null)
                {
                    // For non-nullable value types (e.g., int, bool), "pr" means it's not the default value.
                    ConstantExpression defaultValue = Expression.Constant(Activator.CreateInstance(currentPropertyType), currentPropertyType);
                    predicate = Expression.NotEqual(currentPropertyAccess, defaultValue);
                }
                else
                {
                    // For reference types (e.g., string, Address) or nullable value types (e.g., int?), "pr" means not null.
                    predicate = Expression.NotEqual(currentPropertyAccess, Expression.Constant(null, currentPropertyType));
                }
            }
            else // Handling comparison operators (eq, ne, gt, ge, lt, le) and string methods (co, sw, ew)
            {
                object? convertedValue = null;
                Type targetPropertyType = Nullable.GetUnderlyingType(currentPropertyType) ?? currentPropertyType;

                if (targetPropertyType.IsArray || typeof(IEnumerable).IsAssignableFrom(targetPropertyType))
                {
                    if (targetPropertyType != typeof(string))
                    {
                        targetPropertyType = GetUnderlyingCollectionType(currentPropertyType) ?? 
                            throw new ArgumentException($"Cannot get underlying type of collection on property '{propertyNameCamel}' of type '{currentPropertyType.Name}'");
                    }
                }

                try
                {
                    if (filterValueString.Equals("null", StringComparison.OrdinalIgnoreCase))
                    {
                        if (currentPropertyType.IsValueType && Nullable.GetUnderlyingType(currentPropertyType) == null)
                        {
                            throw new ArgumentException($"Cannot set property '{propertyNameCamel}' of type '{currentPropertyType.Name}' to null as it is a non-nullable value type.");
                        }
                        convertedValue = null;
                    }
                    else if (targetPropertyType == typeof(bool))
                    {
                        convertedValue = bool.Parse(filterValueString);
                    }
                    else if (targetPropertyType == typeof(int))
                    {
                        convertedValue = int.Parse(filterValueString);
                    }
                    else if (targetPropertyType == typeof(long))
                    {
                        convertedValue = long.Parse(filterValueString);
                    }
                    else if (targetPropertyType == typeof(ulong))
                    {
                        convertedValue = ulong.Parse(filterValueString);
                    }
                    else if (targetPropertyType == typeof(ushort))
                    {
                        convertedValue = ushort.Parse(filterValueString);
                    }
                    else if (targetPropertyType == typeof(uint))
                    {
                        convertedValue = uint.Parse(filterValueString);
                    }
                    else if (targetPropertyType == typeof(short))
                    {
                        convertedValue = short.Parse(filterValueString);
                    }
                    else if (targetPropertyType == typeof(byte))
                    {
                        convertedValue = byte.Parse(filterValueString);
                    }
                    else if (targetPropertyType == typeof(sbyte))
                    {
                        convertedValue = sbyte.Parse(filterValueString);
                    }
                    else if (targetPropertyType == typeof(double))
                    {
                        convertedValue = double.Parse(filterValueString);
                    }
                    else if (targetPropertyType == typeof(float))
                    {
                        convertedValue = float.Parse(filterValueString);
                    }
                    else if (targetPropertyType == typeof(decimal))
                    {
                        convertedValue = decimal.Parse(filterValueString);
                    }
                    else if (targetPropertyType == typeof(Guid))
                    {
                        convertedValue = Guid.Parse(filterValueString);
                    }
                    else if (targetPropertyType == typeof(DateTimeOffset))
                    {
                        convertedValue = DateTimeOffset.Parse(filterValueString);
                    }
                    else if (targetPropertyType == typeof(DateTime))
                    {
                        convertedValue = DateTime.Parse(filterValueString);
                    }
                    else if (targetPropertyType == typeof(TimeSpan))
                    {
                        convertedValue = TimeSpan.Parse(filterValueString);
                    }
                    else if (targetPropertyType == typeof(string))
                    {
                        convertedValue = filterValueString;
                    }
                    else
                    {
                        convertedValue = Convert.ChangeType(filterValueString, targetPropertyType);
                    }
                }
                catch (Exception ex)
                {
                    throw new ArgumentException($"Failed to convert filter value '{filterValueString}' to type '{currentPropertyType.Name}' for property '{propertyNameCamel}'. {ex.Message}", ex);
                }

                ConstantExpression valueConstant = Expression.Constant(convertedValue, currentPropertyType);

                // Handle string-specific operators (co, sw, ew)
                if (StringMethodMap.TryGetValue(op, out var methodName))
                {
                    if (op == "has")
                    {
                        var elementType = GetUnderlyingCollectionType(currentPropertyType);

                        if (elementType == null || elementType != typeof(string))
                            throw new ArgumentException($"Operator '{op}' is only applicable to collections of strings. Property '{propertyNameCamel}' is of type '{currentPropertyType.Name}'.");

                        // Create a parameter for the inner lambda (e.g., 'item' in emails.Any(item => ...))
                        ParameterExpression itemParameter = Expression.Parameter(elementType, "item");

                        var toLowerMethod = typeof(string).GetMethod("ToLower", Type.EmptyTypes) 
                            ?? throw new Exception("ToLower method not found on string type.");

                        Expression itemToLower = Expression.Call(itemParameter, toLowerMethod);
                        Expression valueToLower = Expression.Call(valueConstant, toLowerMethod);

                        var stringContainsMethod = typeof(string).GetMethod("Contains", [typeof(string)]) 
                            ?? throw new InvalidOperationException("String.Contains(string) method not found.");

                        Expression innerPredicateBody = Expression.Call(itemToLower, stringContainsMethod, valueToLower);
                        var innerLambda = Expression.Lambda(innerPredicateBody, itemParameter);

                        // Get the Enumerable.Any<TSource>(this IEnumerable<TSource> source, Func<TSource, bool> predicate) method
                        var anyWithPredicateMethod = (typeof(Enumerable).GetMethods()
                            .Where(m => m.Name == "Any" && m.IsGenericMethodDefinition && m.GetParameters().Length == 2)
                            .FirstOrDefault()
                            ?.MakeGenericMethod(elementType)) ?? throw new InvalidOperationException($"Enumerable.Any<{elementType.Name}>(Func<{elementType.Name}, bool>) method not found.");

                        // Create the call to Enumerable.Any()
                        predicate = Expression.Call(anyWithPredicateMethod, currentPropertyAccess, innerLambda);

                        // Also ensure the collection itself is not null before calling Any()
                        Expression collectionNotNull = Expression.NotEqual(currentPropertyAccess, Expression.Constant(null, currentPropertyType));
                        predicate = Expression.AndAlso(collectionNotNull, predicate);
                    }
                    else
                    {
                        if (currentPropertyType != typeof(string))
                            throw new ArgumentException($"Operator '{op}' is only applicable to string properties. Property '{propertyNameCamel}' is of type '{currentPropertyType.Name}'.");

                        // For case-insensitive comparison, convert both property and value to lower
                        var toLowerMethod = typeof(string).GetMethod("ToLower", Type.EmptyTypes) ?? throw new Exception("ToLower method not found on string type.");

                        var propertyToLower = Expression.Call(currentPropertyAccess, toLowerMethod);
                        var valueToLower = Expression.Call(valueConstant, toLowerMethod);

                        var stringMethod = typeof(string).GetMethod(methodName, [typeof(string)])
                            ?? throw new InvalidOperationException($"String method '{methodName}' not found on string type.");

                        predicate = Expression.Call(propertyToLower, stringMethod, valueToLower);
                    }
                }
                else if (ExpressionOperatorMap.TryGetValue(op, out ExpressionType expressionType))
                {
                    Expression left = currentPropertyAccess;
                    Expression right = valueConstant;

                    // Handle comparison between Nullable<T> and T, or Nullable<T> and null
                    // If one side is Nullable<T> and the other is T, try to unwrap the Nullable<T>
                    if (Nullable.GetUnderlyingType(left.Type) == right.Type && Nullable.GetUnderlyingType(left.Type) != null)
                    {
                        left = Expression.Property(left, "Value"); // Access the .Value of the Nullable<T>
                    }
                    else if (Nullable.GetUnderlyingType(right.Type) == left.Type && Nullable.GetUnderlyingType(right.Type) != null)
                    {
                        right = Expression.Property(right, "Value"); // Access the .Value of the Nullable<T>
                    }

                    // If types are still not directly compatible (e.g., int vs. double after parsing),
                    // attempt to convert the constant value to the property's type.
                    if (left.Type != right.Type && !left.Type.IsAssignableFrom(right.Type))
                    {
                        try
                        {
                            right = Expression.Constant(Convert.ChangeType(convertedValue, left.Type), left.Type);
                        }
                        catch (Exception ex)
                        {
                            throw new ArgumentException($"Incompatible types for comparison between property '{propertyNameCamel}' ({left.Type.Name}) and filter value '{filterValueString}' ({right.Type.Name}). Conversion failed.", ex);
                        }
                    }

                    predicate = Expression.MakeBinary(expressionType, left, right);
                }
                else
                    throw new ArgumentException($"Unsupported operator: '{op}'");
            }

            // Combine the main predicate with the accumulated null checks.
            // This ensures that if any intermediate property in a nested path is null,
            // the entire filter condition for that object evaluates to false.
            if (accumulatedNullChecks != null)
            {
                predicate = Expression.AndAlso(accumulatedNullChecks, predicate);
            }

            return predicate;
        }

        /// <summary>
        /// Helper method to find the first occurrence of a keyword outside of any parentheses.
        /// </summary>
        private static int FindOuterKeyword(string s, string keyword, int startIndex = 0)
        {
            int parenthesisCount = 0;
            for (int i = startIndex; i <= s.Length - keyword.Length; i++)
            {
                if (s[i] == '(')
                {
                    parenthesisCount++;
                }
                else if (s[i] == ')')
                {
                    parenthesisCount--;
                    if (parenthesisCount < 0) // Unmatched closing parenthesis
                    {
                        throw new ArgumentException($"Mismatched closing parenthesis in filter string: '{s}'");
                    }
                }
                // Check if the current substring matches the keyword and is outside parentheses
                else if (parenthesisCount == 0 &&
                         s.Substring(i, keyword.Length).Equals(keyword, StringComparison.OrdinalIgnoreCase))
                {
                    // Ensure it's a whole word to prevent "operand" matching "and"
                    bool pre = (i == 0 || !Char.IsLetterOrDigit(s[i - 1]));
                    bool post = (i + keyword.Length == s.Length || !Char.IsLetterOrDigit(s[i + keyword.Length]));
                    if (pre && post)
                    {
                        return i;
                    }
                }
            }
            if (parenthesisCount != 0) // Unmatched opening parenthesis
            {
                throw new ArgumentException($"Mismatched opening parenthesis in filter string: '{s}'");
            }
            return -1;
        }
    }
}
