import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { BaseFiltersDto } from '@app/common/dto/base.filters.dto';
import { EStudySort } from '@app/modules/study/enums';

export class StudyFiltersDto extends BaseFiltersDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ enum: EStudySort })
  @IsOptional()
  @IsEnum(EStudySort)
  orderField?: EStudySort = EStudySort.Name;
}
