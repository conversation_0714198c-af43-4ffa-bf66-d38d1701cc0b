﻿
using System.Text.Json.Serialization;

namespace Ethos.ReferenceData.Client
{

    /// <summary>
    /// 
    /// </summary>
    public enum ReferenceDataValidationType
    {
        Key,
        Value,
        Id
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataValidationError
    {
        public string Instance { get; set; } = null!;
        public string Title { get; set; } = null!;
        public Dictionary<string, string[]> Errors { get; set; } = [];
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataValidationDto
    {
        public string SetName { get; set; } = null!;
        public string? Version { get; set; }
        [JsonConverter(typeof(ReferenceDataValueConverter))]
        public object? Value { get; set; }
        public ReferenceDataValidationType ValidationType { get; set; }
        public string? PropertyName { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class ReferenceDataSetValue<T> : BaseReferenceDataSetValue
    {
        public T Values { get; set; } = default!;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class ReferenceDataSetValue : BaseReferenceDataSetValue
    {
        [JsonConverter(typeof(ReferenceDataValueConverter))]
        public object Values { get; set; } = default!;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class BaseReferenceDataSetValue
    {
        public long Id { get; set; }
        public ReferenceDataSetKeyDefinitionValue Key { get; set; } = default!;
        public string SetName { get; set; } = null!;
        public long SetId { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataSetAlternateKeyDto
    {
        public long SetId { get; set; }
        public string Name { get; set; } = null!;
        public string? Version { get; set; }
        [JsonConverter(typeof(ReferenceDataValueConverter))]
        public object Value { get; set; } = null!;
        public ReferenceDataSetAlternateKeyBehaviorDto? Behavior { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class ReferenceDataSetAlternateKeyDto<T> : ReferenceDataSetAlternateKeyDto
    {
        public ReferenceDataSetValue<T> Data { get; set; } = default!;
        public List<ReferenceDataSetSchemaDto> CreationSchema { get; set; } = [];
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataSetAlternateKeyBehaviorDto
    {
        public bool OnSelectRequireNewValue { get; set; }
        public bool OnSelectAllowNewValue { get; set; }
        public bool OnNewValueRequireAllFields { get; set; }
        public bool OnNewValueRequireKeyValueOnly { get; set; }
    }

    public class ReferenceDataSetSchemaDto
    {
        public string DisplayName { get; set; } = null!;
        public string Type { get; set; } = null!;
        public string Source { get; set; } = null!;
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataSetValueDefinitionDto
    {
        public string Name { get; set; } = null!;
        [JsonConverter(typeof(ReferenceDataValueConverter))]
        public object Value { get; set; } = null!;

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public ReferenceDataSetMappingDto? InSet { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataSetMappingDto
    {
        public long? SetId { get; set; }
        public string? Name {  get; set; }
        public string? Version { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataSetMappingDetailDto
    {
        public string SetName { get; set; } = null!;
        public string? Version { get; set; }
        public string? Alias { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataImportJobDto
    {
        public Guid JobId { get; set; }
        public string? Error { get; set; }
        public ReferenceDataSetDto? Set { get; set; }
        public bool IsComplete { get; set; }
        public DateTimeOffset StartTime { get; set; }
        public DateTimeOffset? EndTime { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataSetKeyDefinitionValue
    {
        public string Name { get; set; } = null!;
        [JsonConverter(typeof(ReferenceDataValueConverter))]
        public object Value { get; set; } = null!;
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataSetDto
    {
        public long? SetId { get; set; }
        public string Name { get; set; } = null!;
        public string? Version { get; set; }
        public string? Authority { get; set; }
        public string? Source { get; set; }
        public int Count { get; set; }
        public string? Key {  get; set; }   
    }
}
