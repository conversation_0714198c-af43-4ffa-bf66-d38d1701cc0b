﻿namespace Ethos.ReferenceData.Client
{
    /// <summary>
    /// 
    /// </summary>
    internal class ReferenceDataPagedResult : ReferenceDataPagedResult<object>
    {
    }

    /// <summary>
    /// 
    /// </summary>
    /// <typeparam name="T"></typeparam>
    internal class ReferenceDataPagedResult<T> where T : class, new()
    {
        public List<T> Items { get; set; } = [];
        public int TotalCount { get; set; }
        public int Limit { get; set; }
        public int Offset { get; set; }
        public string? Previous { get; set; }
        public string? Next { get; set; }
    }
}