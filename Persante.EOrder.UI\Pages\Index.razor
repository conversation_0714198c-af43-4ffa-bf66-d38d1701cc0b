﻿@page "/"
@using DoctorsApplication.Interface;
@using Persante.Blazor.SharedUI.Configuration;

@code {
    [Inject]
    protected NavigationManager NavigationManager { get; set; } = null!;
    [Inject]
    public IStudyService _studyService { get; set; } = default!;
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _studyService.checkDefaultFilter();
            NavigationManager.NavigateTo("/Dashboard");
        }
    }
}
