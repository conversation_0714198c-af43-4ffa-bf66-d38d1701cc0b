apiVersion: apps/v1
kind: Deployment
metadata:
  name: ethos-roles
  namespace: ethos-ns-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ethos-roles
  template:
    metadata:
      labels:
        app: ethos-roles
    spec:
      imagePullSecrets:
      - name: acr-secret
      containers:
      - name: ethos-roles
        image: ethoscrdev.azurecr.io/ethos-roles:2025.06.1
        imagePullPolicy: Always
        ports:
        - containerPort: 8080 
        env:
        - name: ConnectionStrings__DefaultConnection
          valueFrom:
            secretKeyRef:
              key: connection-string
              name: db-connection-secret
              optional: false
        - name: ASPNETCORE_URLS
          value: "http://*:8080"
        - name: Auth__Authorization__DebugScopes_JSON
          value: "[ \"*.*.Admin\" ]"
        - name: Events__BaseUri
          value: "https://goodspace.org/_billing.php"
        
      restartPolicy: Always 