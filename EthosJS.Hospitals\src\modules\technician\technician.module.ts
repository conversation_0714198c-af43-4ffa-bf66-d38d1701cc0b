import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TechnicianCredentialRepository, TechnicianRepository } from '@app/modules/technician/technician.repository';
import { TechnicianService } from '@app/modules/technician/technician.service';
import { TechnicianController } from '@app/modules/technician/technician.controller';
import { ClinicModule } from '@app/modules/clinic/clinic.module';
import { FacilityModule } from '@app/modules/facility/facility.module';
import { CredentialModule } from '@app/modules/credential/credential.module';
import { TechnicianScheduleRepository } from '@app/modules/technicianSchedule/technician.schedule.repository';

@Module({
  imports: [
    ClinicModule,
    CredentialModule,
    FacilityModule,
    TypeOrmModule.forFeature([TechnicianRepository, TechnicianCredentialRepository, TechnicianScheduleRepository]),
  ],
  providers: [TechnicianService],
  controllers: [TechnicianController],
  exports: [TechnicianService],
})
export class TechnicianModule {}
