using Ethos.Workflows.Api.Analysis;
using Microsoft.AspNetCore.Mvc;

namespace Ethos.Workflows.Controllers;

[ApiController]
public sealed class PyApiController : ControllerBase
{
    /// <summary>GET /pyapi.zip – returns all Python clients as a single archive.</summary>
    [HttpGet("/pyapi.zip")]
    public async Task<IActionResult> GetPythonClientBundle()
    {
        var apiModel = ApiAnalyzer.Analyze(typeof(ProviderController).Assembly, s => { });
        var pythonFiles = PythonGen.Build(apiModel);
        var zipBytes = ZipBuilder.BuildZip(pythonFiles);
        return File(zipBytes,
                    contentType: "application/zip",
                    fileDownloadName: "pyapi.zip");
    }
    
    [HttpGet("/tsapi.zip")]
    public async Task<IActionResult> GetTypescriptClientBundle()
    {
        var apiModel = ApiAnalyzer.Analyze(typeof(ProviderController).Assembly, s => { });
        var pythonFiles = TypeScriptGen.Build(apiModel);
        var zipBytes = ZipBuilder.BuildZip(pythonFiles);
        return File(zipBytes,
            contentType: "application/zip",
            fileDownloadName: "tsapi.zip");
    }
}