using Ethos.Model;
using Ethos.Workflows.Api;
using Ethos.Workflows.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class PhysicianController(DbContext dbContext)
    : EntityControllerBase<PhysicianDbo, CreatePhysicianDto, PhysicianDto, PhysicianQ>(dbContext)
{
    protected override IQueryable<PhysicianDbo> ApplyIncludes(IQueryable<PhysicianDbo> query)
    {
        return query
            .Include(p => p.Names);
    }

    protected override PhysicianDto MapToDto(PhysicianDbo dbo)
    {
        return new PhysicianDto
        {
            Id = dbo.Id,
            Demographics = dbo.Demographics?.ToDto(),
            ContactInformation = dbo.ContactDetail?.ToDto(),
            Names = dbo.Names.Select(n => n.ToDto()).ToList(),
            Identifiers = dbo.Identifiers.Select(i => i.ToDto()).ToList(),
            CareLocationIds = _dbContext.Set<PhysicianCareLocationRelationDbo>()
                .Where(r => r.PhysicianId == dbo.Id)
                .Select(r => r.CareLocationId)
                .ToList()
        };
    }

    protected override PhysicianDbo CreateOrUpdateEntity(PhysicianDbo? entity, CreatePhysicianDto input, Guid? requiredId = null)
    {
        if (entity == null)
        {
            entity = new PhysicianDbo
            {
                Id = requiredId ?? Guid.NewGuid(),
                Names = input.Names.Select(n => n.ToEntity()).ToList(),
                Identifiers = input.Identifiers?.Select(i => i.ToEntity()).ToList() ?? new List<IdentifierDbo>(),
            };
            
            if (input.CareLocationIds != null)
            {
                foreach (var careLocationId in input.CareLocationIds)
                {
                    _dbContext.Set<PhysicianCareLocationRelationDbo>().Add(new PhysicianCareLocationRelationDbo
                    {
                        PhysicianId = entity.Id,
                        CareLocationId = careLocationId
                    });
                }
            }
        }
        else
        {
            entity.Names = input.Names.Select(n => new PersonNameDbo
            {
                LastName = n.LastName!,
                MiddleName = n.MiddleName,
                FirstName = n.FirstName!,
                SuffixId = n.Suffix,
                PrefixId = n.Prefix
            }).ToList();

            if (input.Identifiers != null)
            {
                entity.Identifiers = input.Identifiers?.Select(i => i.ToEntity()).ToList() 
                    ?? new List<IdentifierDbo>();
            }

            var existingRelations = _dbContext.Set<PhysicianCareLocationRelationDbo>()
                .Where(r => r.PhysicianId == entity.Id)
                .ToList();

            _dbContext.Set<PhysicianCareLocationRelationDbo>().RemoveRange(existingRelations);

            if (input.CareLocationIds != null)
            {
                foreach (var careLocationId in input.CareLocationIds)
                {
                    _dbContext.Set<PhysicianCareLocationRelationDbo>().Add(new PhysicianCareLocationRelationDbo
                    {
                        PhysicianId = entity.Id,
                        CareLocationId = careLocationId
                    });
                }
            }
        }

        return entity;
    }
}