import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BaseCollectionDto } from '@app/common/dto/base.collection.dto';
import { BedScheduleDto } from '@app/modules/bedSchedule/dto/bed.schedule.dto';

export class BedScheduleCollectionDto extends BaseCollectionDto {
  @ApiProperty({ type: BedScheduleDto, isArray: true })
  @Expose()
  data: BedScheduleDto[]
}
