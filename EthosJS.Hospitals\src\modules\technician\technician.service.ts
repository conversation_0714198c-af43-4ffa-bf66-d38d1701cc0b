import _ from 'lodash';
import moment from 'moment';
import { Transactional } from 'typeorm-transactional-cls-hooked';
import { BadRequestException, Injectable } from '@nestjs/common';
import { TechnicianCredentialRepository, TechnicianRepository } from '@app/modules/technician/technician.repository';
import { TechnicianEntity } from '@app/modules/technician/technician.entity';
import { IListResult } from '@app/common/types';
import { TechnicianFiltersDto } from '@app/modules/technician/dto/technician.filters.dto';
import { CreateTechnicianDto } from '@app/modules/technician/dto/create.technician.dto';
import { UpdateTechnicianDto } from '@app/modules/technician/dto/update.technician.dto';
import { ClinicService } from '@app/modules/clinic/clinic.service';
import { CredentialService } from '@app/modules/credential/credential.service';
import { TechnicianStandardScheduleDto } from '@app/modules/technician/dto/technician.standard.schedule.dto';
import { ClinicEntity } from '@app/modules/clinic/clinic.entity';
import { ETechnicianScheduleShift } from '@app/modules/technicianSchedule/enums';
import { WEEK } from '@app/common/constants';
import { TechnicianScheduleRepository } from '@app/modules/technicianSchedule/technician.schedule.repository';
import { TechnicianDto } from '@app/modules/technician/dto/technician.dto';
import { TechnicianCredentialDto } from '@app/modules/technician/dto/technician.credential.dto';
import { getWeekDay } from '@app/common/helpers';
import { FacilityEntity } from '@app/modules/facility/facility.entity';
import { ScheduleDto } from '@app/modules/schedule/dto/schedule.dto';
import { mapToScheduleDto } from '@app/modules/schedule/helpers';
import { EDayWeek, EShift } from '@app/common/enums';
import { ISchedule } from '@app/modules/schedule/types';
import { ITechnicianStandardSchedule } from '@app/modules/technician/types';

@Injectable()
export class TechnicianService {
  constructor(
    private readonly repository: TechnicianRepository,
    private readonly technicianCredentialRepository: TechnicianCredentialRepository,
    private readonly clinicService: ClinicService,
    private readonly credentialService: CredentialService,
    private readonly technicianScheduleRepository: TechnicianScheduleRepository,
  ) {}

  async list(filters: TechnicianFiltersDto): Promise<IListResult<TechnicianEntity>> {
    return this.repository.list(filters);
  }

  async getByIdOrFail(technicianId: number): Promise<TechnicianEntity> {
    const technician = await this.repository.findOne({
      where: {
        id: technicianId,
      },
      relations: ['clinic', 'credentials', 'credentials.credential'],
    });

    if (!technician) {
      throw new BadRequestException(`Technician ${technicianId} not found`);
    }

    return technician;
  }

  @Transactional()
  async create(technician: CreateTechnicianDto): Promise<TechnicianEntity> {
    const clinic = await this.clinicService.getByIdOrFail(technician.clinicId);
    technician.standardSchedule = this.getStandardSchedule(clinic, technician.standardSchedule);

    if (technician.credentials?.length) {
      const credentialIds = technician.credentials.map(({ credentialId }) => credentialId);
      await this.credentialService.checkExistence(credentialIds);
    }

    const entity = this.repository.create(technician);
    const created = await this.repository.save(entity);

    if (technician.credentials?.length) {
      const entities = this.technicianCredentialRepository.create(technician.credentials.map((item) => ({ ...item, technicianId: created.id })));
      await this.technicianCredentialRepository.save(entities);
    }

    return this.getByIdOrFail(created.id);
  }

  @Transactional()
  async update({ id, credentials, ...update }: UpdateTechnicianDto): Promise<TechnicianEntity> {
    const technician = await this.getByIdOrFail(id);
    const conflicts = await this.getConflicts({ id, credentials, ...update }, technician);

    if (conflicts.length) {
      throw new BadRequestException(`${conflicts.length} conflicts in schedule. Check it through special method`);
    }

    const entity = this.repository.merge(technician, update);
    const updated = await this.repository.save(entity);

    if (credentials) {
      await this.technicianCredentialRepository.delete({ technicianId: updated.id });

      if (credentials.length) {
        const entities = this.technicianCredentialRepository.create(credentials.map((item) => ({ ...item, technicianId: updated.id })));
        await this.technicianCredentialRepository.save(entities);
      }
    }

    if (update.capacity) {
      await this.repository.updateSchedulesCapacity(updated.id, updated.capacity);
    }

    if (update.name) {
      await this.repository.updateTechnicianName(updated.id, updated.name);
    }

    return this.getByIdOrFail(technician.id);
  }

  async getConflicts({ id, credentials, capacity, standardSchedule }: UpdateTechnicianDto, technician?: TechnicianEntity): Promise<ScheduleDto[]> {
    if (!technician) {
      technician = await this.getByIdOrFail(id);
    }
    const conflicts: ISchedule[] = [];
    const schedules = await this.repository.getTechnicianSchedules(technician.id);

    if (capacity && capacity < technician.capacity) {
      const schedulesByDates = schedules.reduce((acc, item) => {
        if (!acc[item.date]) {
          acc[item.date] = [];
        }
        acc[item.date].push(item);

        return acc;
      }, {} as Record<string, ISchedule[]>);

      Object.values(schedulesByDates).forEach((item) => {
        if (schedules.length > capacity) {
          conflicts.push(...item);
        }
      });
    }

    if (credentials) {
      const credentialIds = technician.credentials.map(({ credentialId }) => credentialId);

      if (credentials.length) {
        await this.credentialService.checkExistence(credentialIds);
      }

      const conflictedSchedules = schedules.filter((item) => _.difference(item.credentials, credentialIds).length > 0);
      conflicts.push(...conflictedSchedules);
    }

    if (standardSchedule) {
      const clinic = await this.clinicService.getByIdOrFail(technician.clinicId);
      const newStandardSchedule = this.getStandardSchedule(clinic, standardSchedule);
      const schedulesByWeekDays = schedules.reduce((acc, schedule) => {
        if (!acc[schedule.weekday]) {
          acc[schedule.weekday] = {};
        }

        if (!acc[schedule.weekday][schedule.date]) {
          acc[schedule.weekday][schedule.date] = [];
        }

        acc[schedule.weekday][schedule.date].push(schedule);

        return acc;
      }, {} as Record<string, Record<string, ISchedule[]>>);

      for (const weekday of Object.values(EDayWeek)) {
        const newDay = newStandardSchedule[weekday];
        const oldDay = technician.standardSchedule[weekday];

        const newCapacity = newDay.capacity ?? 0;
        const schedulesByWeekday = schedulesByWeekDays[weekday];
        if (!schedulesByWeekday) {
          continue;
        }
        for (const schedules of Object.values(schedulesByWeekday)) {
          if (schedules.length > newCapacity) {
            conflicts.push(...schedules);
          }

          if (newDay.shift === ETechnicianScheduleShift.DayOff) {
            conflicts.push(...schedules);
          }
        }

        if (newDay.shift !== ETechnicianScheduleShift.DayOff && newDay.shift !== oldDay.shift) {
          const conflictedByShift = schedules.filter((item) => item.shift !== newDay.shift as unknown as EShift);
          conflicts.push(...conflictedByShift);
        }

        if (newDay.facilityId !== oldDay.facilityId) {
          const conflictedByFacility = schedules.filter((item) => item.facilityId !== newDay.facilityId);
          conflicts.push(...conflictedByFacility);
        }
      }

      return conflicts.map(mapToScheduleDto);
    }

    return conflicts.map(mapToScheduleDto);
  }

  @Transactional()
  async delete(technicianId: number): Promise<TechnicianEntity> {
    const technician = await this.getByIdOrFail(technicianId);

    await this.repository.checkHasSchedules(technicianId);
    await technician.softRemove();
    await this.technicianCredentialRepository.softDelete({ technicianId });
    await this.technicianScheduleRepository.softDelete({ technicianId });

    return technician;
  }

  async getTechniciansToDate(facility: FacilityEntity, date: string): Promise<TechnicianEntity[]> {
    const day = moment(date).weekday();
    const weekday = getWeekDay(day);

    const technicians = await this.repository.find({
      where: {
        clinicId: facility.clinicId,
      },
      relations: ['credentials'],
    });

    return technicians.filter((technician) => {
      const schedule = technician.standardSchedule[weekday];

      return schedule?.facilityId === facility.id;
    });
  }

  private getStandardSchedule(clinic: ClinicEntity, standardSchedule: TechnicianStandardScheduleDto = {}): ITechnicianStandardSchedule {
    const schedule: any = {};

    for (const day of WEEK) {
      const scheduleDay = standardSchedule[day];

      if (!scheduleDay) {
        schedule[day] = { shift: ETechnicianScheduleShift.DayOff };
        continue;
      }

      if (scheduleDay.shift !== ETechnicianScheduleShift.DayOff) {
        if (!scheduleDay.facilityId) {
          throw new BadRequestException('Schedule without day-off shift must have facilityId');
        }

        if (!scheduleDay.capacity) {
          throw new BadRequestException('Schedule without day-off shift must have capacity');
        }

        if (!clinic.facilities.find(({ id }) => id === scheduleDay.facilityId)) {
          throw new BadRequestException(`Technician is not related to facility ${scheduleDay.facilityId}`);
        }
      }

      schedule[day] = scheduleDay;
    }

    return schedule as ITechnicianStandardSchedule;
  }

  static mapToDto(entity: TechnicianEntity): TechnicianDto {
    const dto = new TechnicianDto();

    dto.id = entity.id;
    dto.createdAt = entity.createdAt;
    dto.updatedAt = entity.updatedAt;
    dto.name = entity.name;
    dto.capacity = entity.capacity;
    dto.standardSchedule = entity.standardSchedule;
    dto.clinicId = entity.clinicId;
    dto.clinicName = entity.clinic.name;
    dto.credentials = entity.credentials.map((credential) => {
      const dto = new TechnicianCredentialDto();

      dto.credentialId = credential.credentialId;
      dto.technicianId = credential.technicianId;
      dto.credentialName = credential.credential.name;
      dto.validUntil = credential.validUntil;

      return dto;
    });

    return dto;
  }
}
