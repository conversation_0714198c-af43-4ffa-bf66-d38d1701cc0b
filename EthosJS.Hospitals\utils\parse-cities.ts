import '../src/bootstrap';
import { createReadStream } from 'fs';
import csvParser from 'csv-parser';
import path from 'path';
import { createConnection } from 'typeorm';
import { CityEntity } from '../src/modules/city/city.entity';
import { StateEntity } from '../src/modules/state/state.entity';
import { config } from '../src/config';

interface CityData {
    city: string;
    state_id: string;
    state_name: string;
}

async function insertCitiesAndStates(cities: CityData[]) {
  const connection = await createConnection(config.typeorm);

  for (const cityData of cities) {
    const { city, state_id, state_name } = cityData;

    let state = await connection.getRepository(StateEntity).findOne({ code: state_id });

    if (!state) {
      state = new StateEntity();
      state.code = state_id;
      state.name = state_name;
      await connection.getRepository(StateEntity).save(state);
    }

    const cityEntity = new CityEntity();
    cityEntity.name = city;
    cityEntity.stateId = state.id;

    await connection.getRepository(CityEntity).save(cityEntity);
  }

  console.log('Cities and states inserted successfully!');
}

const cities: CityData[] = [];

createReadStream(path.resolve('utils', 'uscities.csv'))
  .pipe(csvParser())
  .on('data', (row: CityData) => {
    cities.push(row);
  })
  .on('end', () => {
    insertCitiesAndStates(cities);
  });