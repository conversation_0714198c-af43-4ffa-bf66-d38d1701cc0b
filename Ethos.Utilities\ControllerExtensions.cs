﻿using System.Net;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;

namespace Ethos.Utilities
{
    public class EthosErrorResponse
    {
        public int? HttpStatus { get; set; }
        public string Message { get; set; } = null!;
        public string Instance { get; set; } = null!;
        public Dictionary<string, string[]>? Detail { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public static class ControllerExtensions
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="controller"></param>
        /// <param name="detail"></param>
        /// <returns></returns>
        public static ObjectResult EthosErrorForbidden(this ControllerBase controller, string detail)
        {
            return GetResult(controller, HttpStatusCode.Forbidden, detail);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="controller"></param>
        /// <param name="detail"></param>
        /// <returns></returns>
        public static ObjectResult EthosErrorBadRequest(this ControllerBase controller, string detail)
        {
            return GetResult(controller, HttpStatusCode.BadRequest, detail);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="controller"></param>
        /// <param name="httpStatus"></param>
        /// <param name="detail"></param>
        /// <param name="details"></param>
        /// <returns></returns>
        static ObjectResult GetResult(ControllerBase controller, HttpStatusCode httpStatus, string detail, Dictionary<string, string[]>? details = null)
        {
            return new ObjectResult(new EthosErrorResponse()
            {
                Message = detail,
                HttpStatus = (int)httpStatus,
                Instance = controller.HttpContext.Request.Path.Value,
                Detail = details,
            });
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="controller"></param>
        /// <param name="detail"></param>
        /// <returns></returns>
        public static ObjectResult EthosErrorConflict(this ControllerBase controller, string detail)
        {
            return GetResult(controller, HttpStatusCode.Conflict, detail);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="controller"></param>
        /// <param name="detail"></param>
        /// <param name="details"></param>
        /// <returns></returns>
        public static ObjectResult EthosErrorBadRequest(this ControllerBase controller, string detail, Dictionary<string, string[]> details)
        {
            return GetResult(controller, HttpStatusCode.BadRequest, detail, details);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="controller"></param>
        /// <param name="detail"></param>
        /// <returns></returns>
        public static ObjectResult EthosErrorNotFound(this ControllerBase controller, string detail)
        {
            return GetResult(controller, HttpStatusCode.NotFound, detail);
        }
    }
}
