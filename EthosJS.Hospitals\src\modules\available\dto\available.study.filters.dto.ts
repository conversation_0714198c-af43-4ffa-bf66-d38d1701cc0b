import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsPositive } from 'class-validator';
import { EShift } from '@app/common/enums';
import { IsDateOnly } from '@app/common/decorators/validators.decorator';

export class AvailableStudyFiltersDto {
  @ApiProperty()
  @IsInt()
  @IsPositive()
  facilityId: number;

  @ApiProperty({ format: 'date-time', example: '2020-01-01' })
  @IsDateOnly()
  date: string;

  @ApiProperty({ enum: EShift })
  @IsEnum(EShift)
  shift: EShift;
}
