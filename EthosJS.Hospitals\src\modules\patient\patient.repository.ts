import moment from 'moment/moment';
import { EntityRepository } from 'typeorm';
import { BaseRepository } from '@app/common/base.repository';
import { PatientEntity } from '@app/modules/patient/patient.entity';

@EntityRepository(PatientEntity)
export class PatientRepository extends BaseRepository<PatientEntity> {
  async updatePatientName(patientId: number, name: string): Promise<void> {
    const today = moment().startOf('day');
    await this.query('UPDATE schedules SET patient_name = $3 WHERE date >= $1 AND patient_id = $2 AND deleted_at IS NULL', [today, patientId, name]);
  }
}
