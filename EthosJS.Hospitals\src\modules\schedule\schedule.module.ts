import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleRepository } from '@app/modules/schedule/schedule.repository';
import { ScheduleService } from '@app/modules/schedule/schedule.service';
import { ScheduleController } from '@app/modules/schedule/schedule.controller';
import { PatientModule } from '@app/modules/patient/patient.module';
import { TechnicianScheduleModule } from '@app/modules/technicianSchedule/technician.schedule.module';
import { FacilityModule } from '@app/modules/facility/facility.module';
import { StudyModule } from '@app/modules/study/study.module';
import { BedScheduleModule } from '@app/modules/bedSchedule/bed.schedule.module';
import { EquipmentModule } from '@app/modules/equipment/equipment.module';

@Module({
  imports: [
    BedScheduleModule,
    PatientModule,
    TechnicianScheduleModule,
    FacilityModule,
    StudyModule,
    EquipmentModule,
    TypeOrmModule.forFeature([
      ScheduleRepository,
    ]),
  ],
  providers: [ScheduleService],
  controllers: [ScheduleController],
  exports: [ScheduleService],
})
export class ScheduleModule {}
