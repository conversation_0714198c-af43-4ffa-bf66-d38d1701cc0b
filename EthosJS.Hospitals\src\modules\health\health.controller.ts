import { Controller, Get, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiOkResponse, ApiTags, ApiExcludeEndpoint } from '@nestjs/swagger';

@Controller('healthz')
@ApiTags('health')
export class HealthController {
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Returns HTTP 200 when api service is alive',
  })
  public async checkHealth(): Promise<void> {}

  @Get('error')
  @ApiExcludeEndpoint()
  alwaysError(): never {
    throw new Error('Always error');
  }
}
