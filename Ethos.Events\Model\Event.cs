﻿
namespace Ethos.Events
{
    /// <summary>
    /// 
    /// </summary>
    public class Event
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = null!;
        public DateTimeOffset EventTime { get; set; }
        public string Type { get; set; } = null!;
        public Guid? TenantId { get; set; }
        public string GeneratedBy { get; set; } = null!;
        public string? Code { get; set; }
        public object? EventData { get; set; }
        public ICollection<EventCorrelationId> CorrelationIds { get; set; } = [];
    }

    /// <summary>
    /// 
    /// </summary>
    public class EventCorrelationId
    {
        public Guid EventId { get; set; }
        public Event Event { get; set; } = null!;
        public string Type { get; set; } = null!;
        public string Value { get; set; } = null!;
    }

    /// <summary>
    /// 
    /// </summary>
    public class CorrelationIdDto
    {
        public string Type { get; set; } = null!;
        public string Value { get; set; } = null!;
    }

    /// <summary>
    /// 
    /// </summary>
    public class EventDto
    {
        public Guid? Id { get; set; }
        public string Name { get; set; } = null!;
        public DateTimeOffset? EventTime { get; set; }
        public string Type { get; set; } = null!;
        public Guid? TenantId { get; set; }
        public string? GeneratedBy { get; set; }
        public string? Code { get; set; }
        public object? EventData { get; set; }
        public ICollection<CorrelationIdDto> CorrelationIds { get; set; } = [];
    }
}