﻿
namespace Ethos.ThirdParty.Tennr
{
    /// <summary>
    /// 
    /// </summary>
    public class TennrResponse
    {
        /// <summary>
        /// 
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int HttpStatusCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? Output { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? Error { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? Raw { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public Dictionary<string, string[]> Details { get; set; } = [];
    }
}
