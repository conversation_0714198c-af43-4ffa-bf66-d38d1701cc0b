import { Column, Entity, OneToMany } from 'typeorm';
import { Expose } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from '@app/common/base.entity';
import { FacilityEntity } from '@app/modules/facility/facility.entity';
import { TechnicianEntity } from '@app/modules/technician/technician.entity';

@Entity({ name: 'clinics' })
export class ClinicEntity extends BaseEntity {
  @Column()
  @ApiProperty()
  @Expose()
  name: string;

  @OneToMany(
    () => TechnicianEntity,
    technician => technician.clinic,
    {
      onDelete: 'CASCADE',
    }
  )
  technicians: TechnicianEntity[];

  @OneToMany(
    () => FacilityEntity,
    facility => facility.clinic,
    {
      cascade: true,
    }
  )
  facilities: FacilityEntity[];
}
