using System.IO.Compression;
using System.Reflection;
using System.Text;

namespace Ethos.Workflows.Api.Analysis;

public class ZipBuilder
{
     public static byte[] BuildZip(Dictionary<string, string> files)
     {
         using var ms  = new MemoryStream();
         using (var zip = new ZipArchive(ms, ZipArchiveMode.Create, leaveOpen: true))
         {
             foreach (var (fileName, content) in files) {
                 var entry = zip.CreateEntry(fileName, CompressionLevel.Optimal);
                 using (var target = entry.Open())
                 using (var writer = new StreamWriter(target, Encoding.UTF8))
                 {
                     writer.Write(content);
                 }
             }
         }
     
         return ms.ToArray();
     }
    
    public static string GetEmbedded(Assembly asm, string file)
    {
        var resourcePath = file.Replace('/', '.');
        var res = asm.GetManifestResourceNames()
            .FirstOrDefault(n => n.EndsWith(resourcePath, StringComparison.OrdinalIgnoreCase));
        if (res is null) throw new InvalidOperationException(
            $"Embedded resource '{file}' not found in assembly '{asm.GetName().Name}'.");
    
        using var resStream = asm.GetManifestResourceStream(res)!;
        using var reader = new StreamReader(resStream, Encoding.UTF8);
        return reader.ReadToEnd();
    }
    
    private static void AddEmbedded(ZipArchive zip, Assembly asm, string file)
    {
        var res = asm.GetManifestResourceNames()
            .FirstOrDefault(n => n.EndsWith($".py.{file}", StringComparison.OrdinalIgnoreCase));
        if (res is null) return; // silently skip if missing
    
        using var resStream = asm.GetManifestResourceStream(res)!;
        var entry = zip.CreateEntry(file, CompressionLevel.Optimal);
        using var target = entry.Open();
        resStream.CopyTo(target);
    }
}