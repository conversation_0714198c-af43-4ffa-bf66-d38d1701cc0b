﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class PersonalContactDetailDbo : IOwnedEntity<PersonalContactDetailDbo>
{
    public ICollection<PersonalEmailDbo> Emails { get; set; } = new List<PersonalEmailDbo>();
    public ICollection<PersonalPhoneNumberDbo> PhoneNumbers { get; set; } = new List<PersonalPhoneNumberDbo>();
    public ICollection<PersonalEmergencyContactDbo> EmergencyContacts { get; set; } = new List<PersonalEmergencyContactDbo>();
    public ICollection<PersonalAddressDbo> Addresses { get; set; } = new List<PersonalAddressDbo>();
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<PersonalContactDetailDbo>(Register);

    public new static void Register(EntityTypeBuilder<PersonalContactDetailDbo> entity)
    {
        IOwnedEntity<PersonalContactDetailDbo>.Register(entity);
        
        entity.HasMany(p => p.Emails)
            .WithOne(e => e.Parent)
            .HasForeignKey(e => e.ParentId)
            .OnDelete(DeleteBehavior.Cascade);

        entity.HasMany(p => p.PhoneNumbers)
            .WithOne(e => e.Parent)
            .HasForeignKey(e => e.ParentId)
            .OnDelete(DeleteBehavior.Cascade);

        entity.HasMany(p => p.Addresses)
            .WithOne(e => e.Parent)
            .HasForeignKey(e => e.ParentId)
            .OnDelete(DeleteBehavior.Cascade);

        entity.HasMany(p => p.EmergencyContacts)
            .WithOne(e => e.Parent)
            .HasForeignKey(e => e.ParentId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}