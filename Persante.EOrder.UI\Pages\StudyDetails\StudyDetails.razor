﻿@page "/StudyDetails"
@page "/StudyDetails/{pName}/{Id}"
@page "/StudyDetails/{Id}"
@using BlazorPro.Spinkit
@using DoctorsApplication.Shared.Component
@using Persante.Blazor.SharedUI.Models
@using Persante.Identity.Iam.Blazor.Components
@using System.Net.Http.Json
@using DoctorsApplication.Models
@inject AppStateService appStateService
@inject IDialogService DialogService
@inject NavigationManager _navigationManager
@using DoctorsApplication.Pages.StudyDetails.StudyDetailsTabs
@using System.Drawing.Printing;
@inherits PersanteComponentBase<StudyDetails>

<SpinLoader IsLoading="@isLoad" IsFaulted="true">
    <LoadingTemplate>
        <PersanteLoader IsVisible="@isLoad" />
    </LoadingTemplate>
</SpinLoader>

<HeaderBar IsOutlined="true"
           IsSticky="true" Styles="margin-top:-15px !important;">
    <LeftSideContent>
        <PageStack FlowDirectionIsRow="true" Gap="2">
            <MudIcon Icon="@Icons.Material.TwoTone.BarChart"></MudIcon>
            <MudText>Chart</MudText>
        </PageStack>
        <MudDivider Vertical="true" DividerType="DividerType.Middle" FlexItem="true" />
        <MudText Color="Color.Primary">@studyDetails.patientName</MudText>
        </LeftSideContent>
        <RightSideContent>
            <MudIconButton Icon="@Icons.Material.TwoTone.Close" Color="Color.Dark" />
        </RightSideContent>
    </HeaderBar>
    <PageBody ToolbarSticky>
    <ChildContent>
        <MudExpansionPanels>
            <MudExpansionPanel IsInitiallyExpanded="@isExpanded">
                <TitleContent>
                    <PageStack FlowDirectionIsRow="true" Class="ml-8">
                        <MudText Class="pt-12"><b>Patient :</b> @studyDetails.patientId</MudText>
                            <PageButtons GroupVariant="Variant.Outlined"
                                         Buttons="@(new List<PageButtons.ActionButton>() {
                                    new ()
                                  {
                                      Variant = Variant.Text,
                                      ButtonColor = Color.Primary,
                                      Size = Size.Medium,
                                      StartIcon = Icons.Material.Filled.ContentCopy,
                                      Tooltip = "Copy",
                                      OnClick=(()=>CopyTextBoxData("**********"))
                                  },
                              })" />

                            <MudDivider Vertical="true" DividerType="DividerType.Middle" FlexItem="true" />

                            <MudText Class="pt-12 m-rgt-14"><b>Study :</b> @studyDetails.studyId</MudText>
                            <PageButtons GroupVariant="Variant.Outlined"
                                         Buttons="@(new List<PageButtons.ActionButton>() {
                                    new ()
                                  {
                                      Variant = Variant.Text,
                                      ButtonColor = Color.Primary,
                                      Size = Size.Medium,
                                      StartIcon = Icons.Material.Filled.ContentCopy,
                                      Tooltip = "Copy",
                                      OnClick=(()=>CopyTextBoxData(@studyDetails.studyId))
                                  },
                              })" />

                            <MudDivider Vertical="true" DividerType="DividerType.Middle" FlexItem="true" />

                            <MudText Class="pt-12"><b>Study Date :</b> @studyDetails.studyDate</MudText>

                            <MudDivider Vertical="true" DividerType="DividerType.Middle" FlexItem="true" />
                            <MudText Class="pt-12"><b>Study Type :</b> @studyDetails.studyType</MudText>
                        </PageStack>
                    </TitleContent>
                    <ChildContent>
                        <PageStack FlowDirectionIsRow="true" Class="mb-20 mt-5">
                            <PageCard IsOutlined Header="" BorderHexColor="#2196f3ff"
                                   Class="mud-primary-text">
                             <PageList>
                                 <PageListItem Label="Patient Name" Value="@studyDetails.patientName" />
                                 <PageListItem Label="StudyType" Value="@studyDetails.studyType" />
                                 <PageListItem Label="StudyDate" Value="@studyDetails.studyDate" />
                             </PageList>
                         </PageCard>
                         <PageCard IsOutlined Header="Partener EMR Fields" Class="mud-primary-text"
                                   BorderHexColor="#2196f3ff">
                             <PageList>
                                 <PageListItem Label="Location" Value="@studyDetails.location" />
                                 <PageListItem Label="MRN" Value="@studyDetails.mrn" />
                                 <PageListItem Label="FIN" Value="@studyDetails.fin" />
                                 <PageListItem>
                                     <PageStack FlowDirectionIsRow="true">
                                         <MudText>
                                             <PageListItem Label="OrderNum" Value="@studyDetails.orderNumber"
                                                           Class="pl-0">
                                             </PageListItem>
                                         </MudText>
                                         <MudText Class="wt-43">
                                             <PageButtons Class="right" Styles="float:right !important;" Buttons="@(new List<PageButtons.ActionButton>() {
                                                            new ()
                                                            {
                                                                StartIcon=Icons.Material.Outlined.EditNote,
                                                                Variant = Variant.Filled,
                                                                ButtonColor = Color.Info,
                                                                ButtonType = ButtonType.Submit,
                                                                Size = Size.Small,
                                                                Tooltip="Update Partner EMR Field",
                                                                OnClick = (()=>(OpenPartnerEMRDialogue()))
                                                            },
                                                     })" />
                                         </MudText>
                                     </PageStack>
                                 </PageListItem>
                             </PageList>
                         </PageCard>
                         <PageCard IsOutlined Header="Physicians" Class="mud-primary-text"
                                   BorderHexColor="#2196f3ff">
                             <PageList>
                                 <PageListItem Label="Primary Physician" Value="@studyDetails.primaryPhysician" />
                                 <PageListItem Label="Referring Physician" Value="@studyDetails.referringPhysician" />
                                 <PageListItem Label="Interpreting Physician" Value="@studyDetails.interpretingPhysician" />
                             </PageList>
                         </PageCard>
                     </PageStack>
                     <MudTabs Elevation="0" @bind-ActivePanelIndex="@_activePanel" Class="">
                         <MudTabPanel Text="Demographics" ID="1">
                             <Demographics DemographicsModel="@studyDetails.DemographicsModel"></Demographics>
                         </MudTabPanel>

                         <MudTabPanel Text="Doc Review" ID="2">
                             <DocReviewTab></DocReviewTab>
                         </MudTabPanel>

                         <MudTabPanel Text="Questionnaires" ID="3">
                             <QuestionariesTab></QuestionariesTab>
                         </MudTabPanel>

                         <MudTabPanel Text="Tech Impressions" ID="4">
                             <TechImpressionTab TechImpressionModel="@studyDetails.TechImpressionModel"></TechImpressionTab>
                         </MudTabPanel>

                         <MudTabPanel Text="Comments" ID="5">
                             <CommentsTab AcquisitionNoteModel="@studyDetails.TechImpressionModel.AcquisitionNoteModel"></CommentsTab>
                         </MudTabPanel>

                         <MudTabPanel Text="Sent Documents" ID="6">
                             <SentDocumentHistoryDetail DocumentHistoryDetailModel="@studyDetails.DocumentHistoryDetailModel"></SentDocumentHistoryDetail>
                         </MudTabPanel>

                         <MudTabPanel Text="FollowUp" ID="7">
                             <FollowUp FollowupModel="@studyDetails.FollowupModel"></FollowUp>
                         </MudTabPanel>

                         <MudTabPanel Text="DME Rx" ID="8">
                         </MudTabPanel>

                         <MudTabPanel Text="Documents" ID="9">
                             <DocumentsTab></DocumentsTab>
                         </MudTabPanel>
                     </MudTabs>
                 </ChildContent>
             </MudExpansionPanel>
         </MudExpansionPanels>
     </ChildContent>
 </PageBody>