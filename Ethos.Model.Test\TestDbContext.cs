using Microsoft.EntityFrameworkCore;

namespace Ethos.Model.Test;

public class TestDbContext : DbContextBase
{
    public TestDbContext(DbContextOptions<TestDbContext> options)
        : base(options, null) { }

    // -- You can add DbSet<T> properties if you want typed LINQ access:
    public DbSet<AddressDbo> Addresses => Set<AddressDbo>();
    public DbSet<PatientDbo> Patients => Set<PatientDbo>();
    public DbSet<OrderDbo> Orders => Set<OrderDbo>();
}