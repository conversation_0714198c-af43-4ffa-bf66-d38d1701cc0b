using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class CareLocationShiftDbo : IAuditableEntity<CareLocationShiftDbo>
{
    public Guid CareLocationId { get; set; }
    public virtual CareLocationDbo CareLocation { get; set; } = null!;
    
    public required string Name { get; set; }
    public required TimeOnly FromTime { get; set; }
    public required TimeOnly ToTime { get; set; }
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<CareLocationShiftDbo>(Register);

    public new static void Register(EntityTypeBuilder<CareLocationShiftDbo> entity)
    {
        IAuditableEntity<CareLocationShiftDbo>.Register(entity);
        
        entity.Property(e => e.Name).HasMaxLength(200).IsRequired();
        
        entity.HasOne(e => e.CareLocation)
            .WithMany()
            .HasForeignKey(e => e.CareLocationId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}