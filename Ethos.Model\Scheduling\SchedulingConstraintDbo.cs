using System.ComponentModel.DataAnnotations.Schema;
using System.Linq.Expressions;
using System.Text.Json;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model.Scheduling;

public class SchedulingConstraintDbo : IAuditableEntity<SchedulingConstraintDbo>
{
    public required string Name { get; set; }
    public required string Description { get; set; }
    public required bool IsHardConstraint { get; set; }
    public required ICollection<string> TopLevelVariables { get; set; } = new List<string>();
    public required Expr Expression { get; set; }

    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<SchedulingConstraintDbo>(Register);
    
    public new static void Register(EntityTypeBuilder<SchedulingConstraintDbo> entity)
    {
        IAuditableEntity<SchedulingConstraintDbo>.Register(entity);
        
        entity.Property(e => e.IsHardConstraint)
            .IsRequired();
        
        entity.Property(e => e.Name)
            .IsRequired()
            .HasMaxLength(100);
        
        entity.Property(e => e.Description)
            .IsRequired()
            .HasMaxLength(500);
        
        entity.Property(e => e.TopLevelVariables)
            .IsRequired()
            .HasConversion(
                v => string.Join(',', v),
                v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList())
            .HasMaxLength(1000);
        
        entity.Property(e => e.Expression)
            .IsRequired()
            .HasConversion(
                // Serialize: Handle null JsonNode
                v => JsonSerializer.Serialize(v, JsonSerializerOptions.Default),
                // Deserialize: Handle null/empty string from DB
                v => JsonSerializer.Deserialize<Expr>(v!, JsonSerializerOptions.Default)!
            )
            .HasColumnType("jsonb");
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(SchedulingConstraintQ.WithId), "WithId")]
[JsonDerivedType(typeof(SchedulingConstraintQ.WithName), "WithName")]
[JsonDerivedType(typeof(SchedulingConstraintQ.WithTopLevelVariable), "WithTopLevelVariable")]
public abstract record SchedulingConstraintQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : SchedulingConstraintQ;
    public sealed record WithName(string Name) : SchedulingConstraintQ;
    public sealed record WithTopLevelVariable(string TopLevelVariable) : SchedulingConstraintQ;

    public static SchedulingConstraintQ HasId(Guid id) => new WithId(id);
    public static SchedulingConstraintQ HasName(string name) => new WithName(name);
    public static SchedulingConstraintQ HasTopLevelVariable(string topLevelVariable) => new WithTopLevelVariable(topLevelVariable);

    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithId id => Expression.Equal(
                Expression.Property(self, nameof(SchedulingConstraintDbo.Id)),
                Expression.Constant(id.Id, typeof(Guid))),
            WithName externalId => Expression.Equal(
                Expression.Property(self, nameof(SchedulingConstraintDbo.Name)),
                Expression.Constant(externalId.Name, typeof(string))),
            WithTopLevelVariable topLevelVariable => Expression.Call(
                Expression.Property(self, nameof(SchedulingConstraintDbo.TopLevelVariables)),
                typeof(ICollection<string>).GetMethod("Contains")!,
                Expression.Constant(topLevelVariable.TopLevelVariable, typeof(string))),
            _ => throw new NotSupportedException($"Unsupported query type: {this.GetType().Name}")
        };
    }
}