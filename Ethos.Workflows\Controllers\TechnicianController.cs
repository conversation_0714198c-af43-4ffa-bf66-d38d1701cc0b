namespace Ethos.Workflows.Controllers;

using Ethos.Model;
using Ethos.Workflows.Api;
using Ethos.Workflows.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;



[Authorize]
[ApiController]
[Route("api/[controller]")]
public class TechnicianController(DbContext dbContext)
    : EntityControllerBase<TechnicianDbo, CreateTechnicianDto, TechnicianDto, TechnicianQ>(dbContext)
{
    protected override IQueryable<TechnicianDbo> ApplyIncludes(IQueryable<TechnicianDbo> query)
    {
        return query
            .Include(t => t.Names);
    }

    protected override TechnicianDto MapToDto(TechnicianDbo dbo)
    {
        return new TechnicianDto
        {
            Id = dbo.Id,
            Demographics = dbo.Demographics?.ToDto(),
            ContactInformation = dbo.ContactDetail?.ToDto(),
            Names = dbo.Names.Select(n => n.ToDto()).ToList(),
            Qualifications = dbo.Qualifications?.Select(q => q.ToDto()).ToList() ?? new List<TechnicianQualificationDto>(),
        };
    }

    protected override TechnicianDbo CreateOrUpdateEntity(TechnicianDbo? entity, CreateTechnicianDto input,
        Guid? requiredId = null)
    {
        entity ??= new TechnicianDbo { Id = requiredId ?? Guid.NewGuid() };

        if (input.Names != null) entity.Names = input.Names!.Select(n => n.ToEntity()).ToList();
        
        if (input.Qualifications != null)
        {
            entity.Qualifications = input.Qualifications.Select(n => n.ToEntity()).ToList();
        }

        return entity;
    }
}

