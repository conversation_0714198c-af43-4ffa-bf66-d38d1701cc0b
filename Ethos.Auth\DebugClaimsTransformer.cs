﻿using System.Security.Claims;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Ethos.Auth
{
    /// <summary>
    /// 
    /// </summary>
    public class DebugClaimsTransformer : IClaimsTransformation
    {
        readonly IHostEnvironment _env;
        readonly ILogger<DebugClaimsTransformer> _logger;
        readonly IConfiguration _config;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="env"></param>
        /// <param name="logger"></param>
        public DebugClaimsTransformer(IHostEnvironment env, ILogger<DebugClaimsTransformer> logger, IConfiguration configuration)
        {
            _env = env;
            _logger = logger;
            _config = configuration;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="principal"></param>
        /// <returns></returns>
        public Task<ClaimsPrincipal> TransformAsync(ClaimsPrincipal principal)
        {
            // only transform the claim if we are in the dev environment
            if (!_env.IsDevelopment())
                return Task.FromResult(principal);

            // only use authenticated identity
            if (principal.Identity is ClaimsIdentity identity && identity.IsAuthenticated)
            {
                var options = _config.GetEthosAuthorizationOptions();
                var scopeClaim = options?.ClaimNames?.Scope ?? EthosClaimNames.MicrosoftScope;

                if (options?.DebugScopes.Length > 0)
                {
                    var scopeStr = string.Join(' ', options.DebugScopes.Select(s => s.ToString()));

                    var scopeClaimClaim = identity.FindFirst(c => c.Type.Equals(scopeClaim, StringComparison.OrdinalIgnoreCase));
                    if (scopeClaimClaim is null)
                    {
                        identity.AddClaim(new Claim(scopeClaim, scopeStr));
                        _logger.LogInformation("Applied debug scopes for authenticated user: {UserName}", principal.Identity.Name);
                        _logger.LogDebug("Debug scopes applied: {Scopes}", scopeStr);
                    }
                    else
                    {
                        var currentScopes = scopeClaimClaim.Value;
                        identity.RemoveClaim(scopeClaimClaim);
                        identity.AddClaim(new Claim(scopeClaim, currentScopes + ' ' + scopeStr));
                        _logger.LogDebug("Debug scopes applied to existing claim: {Scopes}", scopeStr);
                    }
                }

                // Change user ID 
                // var userIdClaim = identity.FindFirst(ClaimTypes.NameIdentifier);
                // if (userIdClaim != null)
                // {
                //     identity.RemoveClaim(userIdClaim);
                //     identity.AddClaim(new Claim(ClaimTypes.NameIdentifier, "debug-user-id-123"));
                //     _logger.LogDebug("Changed NameIdentifier to 'debug-user-id-123'.");
                // }

                return Task.FromResult(principal);
            }

            // Return the original principal if not authenticated or not in development
            return Task.FromResult(principal);
        }
    }
}