using System.Text.Json.Nodes;
using Ethos.Model;
using Ethos.Workflows.Api;
using Ethos.Workflows.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class EquipmentController(DbContext dbContext)
    : EntityControllerBase<EquipmentDbo, CreateEquipmentDto, EquipmentDto, EquipmentQ>(dbContext)
{
    protected override EquipmentDto MapToDto(EquipmentDbo dbo) => dbo.ToDto();

    protected override EquipmentDbo CreateOrUpdateEntity(EquipmentDbo? entity, CreateEquipmentDto input,
        Guid? requiredId = null)
    {
        if (entity == null)
        {
            if (input.CareLocationId == null)
            {
                throw new ArgumentNullException(nameof(input.CareLocationId), "CareLocationId is required for a new Equipment.");
            }
            
            var careLocation = _dbContext.Set<CareLocationDbo>()
                .FirstOrDefault(p => p.Id == input.CareLocationId);

            if (careLocation == null)
            {
                throw new RequiredEntityDoesNotExistException(EntityType.CareLocation, input.CareLocationId.Value);
            }

            entity = new EquipmentDbo
            {
                Id = requiredId ?? Guid.NewGuid(),
                CareLocationId = input.CareLocationId ?? throw new ArgumentNullException(nameof(input.CareLocationId)),
                RoomId = input.RoomId,
                EquipmentTypeId = input.EquipmentTypeId ??
                                  throw new ArgumentNullException(nameof(input.EquipmentTypeId)),
                EquipmentData = input.EquipmentData
            };
        }
        else
        {
            entity.EquipmentData = input.EquipmentData;
        }

        return entity;
    }
}