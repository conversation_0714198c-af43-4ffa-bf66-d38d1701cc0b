﻿using System.Text.RegularExpressions;

namespace Ethos.Auth
{

    /// <summary>
    /// 
    /// </summary>
    public partial class EthosScope : IParsable<EthosScope>
    {
        /// <summary>
        /// 
        /// </summary>
        public const string All = "*";
        public const string None = "-";

        /// <summary>
        /// 
        /// </summary>
        public string? Feature { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Entity { get; set; } = null!;

        /// <summary>
        /// 
        /// </summary>
        public string Permission { get; set; } = null!;

        /// <summary>
        /// 
        /// </summary>
        public static readonly EthosScope Default = new(All, All, None);

        /// <summary>
        /// 
        /// </summary>
        public EthosScope()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="fullyQualifiedScope"></param>
        public EthosScope(string fullyQualifiedScope)
        {
            var scope = Parse(fullyQualifiedScope, null);
            Feature = scope.Feature;
            Entity = scope.Entity;
            Permission = scope.Permission;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="feature"></param>
        /// <param name="entity"></param>
        /// <param name="permission"></param>
        public EthosScope(string feature, string entity, string permission)
        {
            Feature = feature;
            Entity = entity;
            Permission = permission;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="permission"></param>
        public EthosScope(string entity, string permission)
        {
            Entity = entity;
            Permission = permission;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public bool IsValid() => !string.IsNullOrEmpty(Entity?.Trim()) && TryGetPermission(out _);

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public bool IsFullyQualified() => IsValid() && !string.IsNullOrEmpty(Feature?.Trim());

        /// <summary>
        /// 
        /// </summary>
        /// <param name="scope"></param>
        /// <returns></returns>
        public bool Is(EthosScope? scope)
        {
            if (scope is null)
                return false;

            if (!IsValid() || !scope.IsValid())
                return false;

            if (!scope.TryGetPermission(out var scopePermission))
                return false;

            if (!TryGetPermission(out var thisPermission))
                return false;

            if (scopePermission is null || thisPermission is null)
                return false;

            if (!Equals(scopePermission, thisPermission))
                return false;

            if (!string.Equals(Entity, scope.Entity, StringComparison.OrdinalIgnoreCase))
                return false;

            if (!string.Equals(Feature ?? string.Empty, scope.Feature ?? string.Empty, StringComparison.OrdinalIgnoreCase))
                return false;

            return true;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="scope"></param>
        /// <returns></returns>
        public bool IsAuthorized(EthosScope? scope)
        {
            if (scope is null)
                return false;

            if (!IsFullyQualified() || !scope.IsFullyQualified())
                return false;

            return ((Feature ?? string.Empty).Equals(scope.Feature ?? string.Empty, StringComparison.OrdinalIgnoreCase) || scope.IsAllFeatures()) &&
                   (Entity.Equals(scope.Entity, StringComparison.OrdinalIgnoreCase) || scope.IsAllEntities()) &&
                   IsAllowed(scope.GetPermission(), GetPermission());
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public bool IsAllPermissions() => All.Equals(Permission) ||
                                          nameof(All).Equals(Permission, StringComparison.OrdinalIgnoreCase);

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public bool IsAllEntities() => All.Equals(Entity);

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public bool IsAllFeatures() => All.Equals(Feature);

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public EthosPermission GetPermission()
        {
            if (IsAllPermissions())
                return EthosPermission.All;

            if (None.Equals(Permission) || string.IsNullOrEmpty(Permission))
                return EthosPermission.None;

            if (!Enum.TryParse(Permission, true, out EthosPermission permission))
                throw new Exception($"Invalid permission: {Permission}");

            return permission;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="permission"></param>
        /// <returns></returns>
        public bool TryGetPermission(out EthosPermission? permission)
        {
            permission = null;
            try
            {
                permission = GetPermission();
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userPermission"></param>
        /// <param name="requiredPermission"></param>
        /// <returns></returns>
        public static bool IsAllowed(EthosPermission? userPermission, EthosPermission? requiredPermission)
        {
            if (!userPermission.HasValue || !requiredPermission.HasValue)
                return false;

            if (userPermission == EthosPermission.None || requiredPermission == EthosPermission.None)
                return false;

            bool allowed;

            if ((requiredPermission & EthosPermission.Admin) == EthosPermission.Admin)
            {
                allowed = (userPermission & EthosPermission.Admin) == EthosPermission.Admin;
                if (!allowed)
                    return false;
            }

            if ((requiredPermission & EthosPermission.ReadAll) == EthosPermission.ReadAll)
            {
                allowed = (userPermission & EthosPermission.ReadAll) == EthosPermission.ReadAll;
                if (!allowed)
                    return false;
            }

            if ((userPermission & EthosPermission.All) == EthosPermission.All)
                return true;

            if ((requiredPermission & EthosPermission.Read) == EthosPermission.Read)
            {
                allowed = (userPermission & EthosPermission.Write) == EthosPermission.Write ||
                           (userPermission & EthosPermission.Delete) == EthosPermission.Delete ||
                           (userPermission & EthosPermission.Audit) == EthosPermission.Audit ||
                           (userPermission & EthosPermission.Read) == EthosPermission.Read;

                if (!allowed)
                    return false;
            }

            if ((requiredPermission & EthosPermission.Write) == EthosPermission.Write)
            {
                allowed = (userPermission & EthosPermission.Write) == EthosPermission.Write;

                if (!allowed)
                    return false;
            }

            //if ((requiredPermission & EthosPermission.Create) == EthosPermission.Create)
            //{
            //    allowed = (userPermission & EthosPermission.Create) == EthosPermission.Create;

            //    if (!allowed)
            //        return false;
            //}

            if ((requiredPermission & EthosPermission.Delete) == EthosPermission.Delete)
            {
                allowed = (userPermission & EthosPermission.Delete) == EthosPermission.Delete;

                if (!allowed)
                    return false;
            }

            if ((requiredPermission & EthosPermission.Audit) == EthosPermission.Audit)
            {
                allowed = (userPermission & EthosPermission.Audit) == EthosPermission.Audit;

                if (!allowed)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            var strVal = string.Empty;

            if (string.IsNullOrEmpty(Entity) && string.IsNullOrEmpty(Permission))
                return strVal;

            if (!TryGetPermission(out var permissionEnum))
                return strVal;

            var entity = string.IsNullOrEmpty(Entity) ? All : Entity;
            var permission = permissionEnum == EthosPermission.All ? All : permissionEnum == EthosPermission.None ? None : permissionEnum.ToString();

            if (!string.IsNullOrEmpty(Feature))
                return string.Join('.', [ Feature, entity, permission ]);

            return string.Join('.', [ entity, permission ]);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="s"></param>
        /// <param name="formatProvider"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public static EthosScope Parse(string? s, IFormatProvider? formatProvider)
        {
            s = s?.Trim();
            if (string.IsNullOrEmpty(s))
                throw new ArgumentException("Scope cannot be null or empty string.", nameof(s));

            var matches = ScopeRegex().Matches(s);

            if (matches.Count == 0 || matches.Count > 1)
                throw new ArgumentException($"Invalid scope: {s}", nameof(s));

            var scope = new EthosScope();

            var groups = matches.First().Groups;

            if (groups.Count < 5)
                throw new ArgumentException($"Invalid scope: {s}", nameof(s));

            if (string.IsNullOrEmpty(groups[4].Value))
            {
                if (string.IsNullOrEmpty(groups[2].Value))
                {
                    if (string.IsNullOrEmpty(groups[1].Value))
                        throw new ArgumentException($"Invalid scope: {s}", nameof(s));

                    scope.Entity = groups[1].Value;
                    scope.Permission = None;
                }
                else
                {
                    scope.Entity = groups[1].Value;
                    scope.Permission = groups[2].Value;
                }
            }
            else
            {
                scope.Feature = groups[1].Value;
                scope.Entity = groups[2].Value;
                scope.Permission = groups[4].Value;
            }

            if (string.IsNullOrEmpty(scope.Entity))
                throw new ArgumentException($"Invalid scope: {s}", nameof(s));

            try
            {
                scope.GetPermission();
            }
            catch (Exception e)
            {
                throw new ArgumentException($"Invalid permission in scope: {s}", nameof(s), e);
            }
            return scope;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="s"></param>
        /// <param name="formatProvider"></param>
        /// <param name="scope"></param>
        /// <returns></returns>
        public static bool TryParse(string? s, IFormatProvider? formatProvider, out EthosScope scope)
        {
            scope = Default;
            try
            {
                scope = Parse(s, formatProvider);
                return true;
            }
            catch
            {
                return false;
            }
        }

        [GeneratedRegex(@"^([^\.]+)\.([^\.]+)(\.([^\.]+))?$")]
        private static partial Regex ScopeRegex();
    }
}
