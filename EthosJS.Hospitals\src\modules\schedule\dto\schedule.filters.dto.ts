import { Transform } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, IsPositive } from 'class-validator';
import { BaseFiltersDto } from '@app/common/dto/base.filters.dto';
import { EScheduleSort } from '@app/modules/schedule/enums';
import { EShift } from '@app/common/enums';
import { IsDateOnly, IsDatesList, IsIntegerList } from '@app/common/decorators/validators.decorator';

export class ScheduleFiltersDto extends BaseFiltersDto {
  @ApiPropertyOptional({
    isArray: false,
    type: 'string',
    description: 'List of integers concatenated through ,',
    example: '1,2,3',
  })
  @IsOptional()
  @IsIntegerList()
  @Transform((value) => value?.split(',').map((item: string) => Number(item)))
  facilityIds?: number[];

  @ApiPropertyOptional({
    isArray: false,
    type: 'string',
    description: 'List of dates concatenated through ,',
    example: '2020-01-01,2021-02-02,2022-03-03',
  })
  @IsOptional()
  @IsDatesList()
  @Transform((value) => value?.split(','))
  dates?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  @IsPositive()
  clinicId?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  @IsPositive()
  technicianId?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  @IsPositive()
  patientId?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  @IsPositive()
  studyId?: number;

  @ApiPropertyOptional({ enum: EShift })
  @IsOptional()
  @IsEnum(EShift)
  shift?: EShift;

  @ApiPropertyOptional({ format: 'date-time', example: '2020-01-01' })
  @IsOptional()
  @IsDateOnly()
  dateFrom?: string;

  @ApiPropertyOptional({ format: 'date-time', example: '2024-01-01' })
  @IsOptional()
  @IsDateOnly()
  dateTo?: string;

  @ApiPropertyOptional({ enum: EScheduleSort })
  @IsOptional()
  @IsEnum(EScheduleSort)
  orderField?: EScheduleSort = EScheduleSort.CreatedAt;
}
