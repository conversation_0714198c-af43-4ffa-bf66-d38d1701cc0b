﻿using System.Collections.ObjectModel;
using System.Text.RegularExpressions;
using MudBlazor;
using Microsoft.AspNetCore.Components;
using Persante.Blazor.SharedUI.Components;

namespace DoctorsApplication.Pages.StudyDetails
{
    public partial class PartnerEMRFieldDialogue
    {
        [CascadingParameter] MudDialogInstance MudDialog { get; set; }

        [Parameter]
        public Data PassedData { get; set; }

        public Data lastData { get; set; }

        List<PartnerEMRFieldType> partnerEMRFieldType = new List<PartnerEMRFieldType>(){
    new PartnerEMRFieldType() {
        Id = 1, EmrFieldTypeName="MRN",
        EmrFieldTypeValue="",FieldValueValidationRegEx="^[a-zA-Z0-9]*$",
        FieldValueValidationErrorMessage="MRN must be alphanumeric."
    },
    new PartnerEMRFieldType() {
        Id = 1, EmrFieldTypeName="FIN",
        EmrFieldTypeValue="",FieldValueValidationRegEx=@"\b[FIN]\w+",
        FieldValueValidationErrorMessage="Value must starts with 'FIN'."
    },
    new PartnerEMRFieldType() {
        Id = 1, EmrFieldTypeName="Order Num",
        EmrFieldTypeValue="",FieldValueValidationRegEx="^[0-9]*$",
        FieldValueValidationErrorMessage="Only Numbers are allowed."
    }
    };

        public string UpdatedOn { get; set; } = "05/21/2024 @ 11:03 AM";

        NotificationToast toaster;

        void Submit() => MudDialog.Close(DialogResult.Ok(true));
        async Task Cancel() => MudDialog.Cancel();
        //  MudDialog.Close(DialogResult.Ok(this.input));

        ObservableCollection<PartnerEMRFields> PartnerEMRFieldsList = new ObservableCollection<PartnerEMRFields>()
    {
    new PartnerEMRFields()
        {
            Id = 1, EmrFieldTypeName = "Order Num",
        EmrFieldTypeValue = "512981465",UpdatedDate = "05/21/2024 @ 11:03 AM",
        UpdatedBy = "dmcclintock"
    },
    new PartnerEMRFields()
        {
            Id = 2, EmrFieldTypeName = "Order Num",
        EmrFieldTypeValue = "51208146",UpdatedDate = "05/21/2024 @ 10:58 AM",
        UpdatedBy = "dmcclintock"
    },
    new PartnerEMRFields()
        {
            Id = 3, EmrFieldTypeName = "MRN",
        EmrFieldTypeValue = "M501234",UpdatedDate = "05/21/2024 @ 03:19 AM",
        UpdatedBy = "pjohnson"
    },
    new PartnerEMRFields()
        {
            Id = 4, EmrFieldTypeName = "FIN",
        EmrFieldTypeValue = "F10456",UpdatedDate = "05/21/2024 @ 03:19 AM",
        UpdatedBy = "pjohnson"
    },
    new PartnerEMRFields()
        {
            Id = 5, EmrFieldTypeName = "Order Num",
        EmrFieldTypeValue = "5120000000",UpdatedDate = "05/21/2024 @ 03:19 AM",
        UpdatedBy = "pjohnson"
    },
    };

        public async Task SavePartenerEMRField()
        {
            if (!partnerEMRFieldType.Exists(a => a.isNotValid))
            {
                if (partnerEMRFieldType[0].EmrFieldTypeValue != "")
                {
                    PartnerEMRFieldsList.Insert(0, new PartnerEMRFields()
                    {
                        Id = 5,
                        EmrFieldTypeName = "MRN",
                        EmrFieldTypeValue = partnerEMRFieldType[0].EmrFieldTypeValue,
                        UpdatedDate = DateTime.Now.ToString("MM/dd/yyyy @ hh:mm tt"),
                        UpdatedBy = "pjohnson"
                    });

                    PassedData.currMRNValue = partnerEMRFieldType[0].EmrFieldTypeValue;
                }

                if (partnerEMRFieldType[1].EmrFieldTypeValue != "")
                {
                    PartnerEMRFieldsList.Insert(0, new PartnerEMRFields()
                    {
                        Id = 5,
                        EmrFieldTypeName = "FIN",
                        EmrFieldTypeValue = partnerEMRFieldType[1].EmrFieldTypeValue,
                        UpdatedDate = DateTime.Now.ToString("MM/dd/yyyy @ hh:mm tt"),
                        UpdatedBy = "pjohnson"
                    });

                    PassedData.currFINValue = partnerEMRFieldType[1].EmrFieldTypeValue;
                }

                if (partnerEMRFieldType[2].EmrFieldTypeValue != "")
                {
                    PartnerEMRFieldsList.Insert(0, new PartnerEMRFields()
                    {
                        Id = 5,
                        EmrFieldTypeName = "OrderNum",
                        EmrFieldTypeValue = partnerEMRFieldType[2].EmrFieldTypeValue,
                        UpdatedDate = DateTime.Now.ToString("MM/dd/yyyy @ hh:mm tt"),
                        UpdatedBy = "pjohnson"
                    });

                    PassedData.currOrdNumValue = partnerEMRFieldType[2].EmrFieldTypeValue;
                }

                if (partnerEMRFieldType[0].EmrFieldTypeValue != "" ||
                    partnerEMRFieldType[1].EmrFieldTypeValue != "" ||
                partnerEMRFieldType[2].EmrFieldTypeValue != "")
                {
                    await toaster.ShowToaster(new ToastModel("Success")
                    {
                        ToastType = Severity.Success,
                        Content = "Partner EMR fields Updated Successfully."
                    });

                    partnerEMRFieldType[0].EmrFieldTypeValue = partnerEMRFieldType[1].EmrFieldTypeValue =
                    partnerEMRFieldType[2].EmrFieldTypeValue = "";
                    UpdatedOn = DateTime.Now.ToString("MM/dd/yyyy @ hh:mm tt");
                }
            }
        }

        private void OnBlur(PartnerEMRFieldType model)
        {
            try
            {
                CheckRegex(model);
            }
            catch (Exception ex)
            {
                //  Logger.LogIfEnabled(LogLevel.Error, ex, ex.Message);
            }
        }

        void CheckRegex(PartnerEMRFieldType model)
        {
            Regex rg = new Regex(model.FieldValueValidationRegEx);
            if (!rg.IsMatch(model.EmrFieldTypeValue))
            {
                partnerEMRFieldType.Find(w => w.EmrFieldTypeName == model.EmrFieldTypeName).isNotValid = true;
            }
            else
            {
                partnerEMRFieldType.Find(w => w.EmrFieldTypeName == model.EmrFieldTypeName).isNotValid = false;
            }
        }

        public async Task CancelPartenerEMRField()
        {
            partnerEMRFieldType[0].EmrFieldTypeValue = partnerEMRFieldType[1].EmrFieldTypeValue =
                    partnerEMRFieldType[2].EmrFieldTypeValue = "";

            partnerEMRFieldType[0].isNotValid = partnerEMRFieldType[1].isNotValid =
                    partnerEMRFieldType[2].isNotValid = false;
        }

        public async Task Close()
        {
            MudDialog.Close(DialogResult.Ok(this.PassedData));
        }
    }
    public class PartnerEMRFieldType
    {
        public int? Id { get; set; }
        public string EmrFieldTypeName { get; set; }
        public string EmrFieldTypeValue { get; set; }
        public string FieldValueValidationRegEx { get; set; }
        public string FieldValueValidationErrorMessage { get; set; }
        public bool isNotValid { get; set; }
    }

    public class PartnerEMRFields
    {
        public int? Id { get; set; }
        public string EmrFieldTypeName { get; set; }
        public string EmrFieldTypeValue { get; set; }
        public string UpdatedBy { get; set; }
        public string UpdatedDate { get; set; }
    }
}
