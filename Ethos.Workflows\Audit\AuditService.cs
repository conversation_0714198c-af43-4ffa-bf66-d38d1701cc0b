using Ethos.Model;
using Ethos.Workflows.Database;
using Newtonsoft.Json;

namespace Ethos.Workflows.Audit;

public interface IAuditService
{
    Task LogAsync(Guid instanceId, string? oldState, string newState,
        string userId, string transitionName, object? metadata);
}

public class AuditService : IAuditService
{
    private readonly AppDbContext _dbContext;

    public AuditService(AppDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task LogAsync(Guid instanceId, string? oldState, string newState,
        string? userId, string transitionName, object? metadata)
    {
        var log = new AuditLogDbo
        {
            WorkflowInstanceId = instanceId,
            OldState = oldState,
            NewState = newState,
            UserId = userId,
            TransitionName = transitionName,
            Timestamp = DateTime.UtcNow,
            Metadata = metadata != null ? JsonConvert.SerializeObject(metadata) : null
        };
        _dbContext.AuditLogs.Add(log);
        await _dbContext.SaveChangesAsync();
    }
}