using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

[JsonConverter(typeof(StringEnumConverter<FileStatus>))]
public enum FileStatus
{
    PendingUpload = 0,      // File stream is being validated for upload
    Uploading = 1,          // File stream is being received/written
    ValidationFailed = 2,   // File stream received, but validation failed
    UploadFailed = 3,       // Storage write failed
    Processing = 4,         // Post-validation steps (thumbnail, scan) in progress
    ProcessingFailed = 5,   // Thumbnail/scan failed
    Ready = 6,              // File is validated, processed, and ready for association/use
    Deleted = 7             // Soft deleted
}

public class FileMetadataDbo : IEntity<FileMetadataDbo>
{
    private FileMetadataDbo() { }
    
    public required Guid Id { get; init; }
    public required string StoragePath { get; init; }
    public required string UserId { get; init; }
    public required EntityType ContextEntityType { get; init; }
    public required Guid ContextEntityId { get; init; }
    public required string Purpose { get; init; }
    public required DateTime UploadTimestamp { get; init; }
    public required string UploadFileName { get; init; }
    public required string UploadMimeType { get; init; }
    public required long UploadFileSize { get; init; }
    
    public FileStatus Status { get; set; }
    public JsonNode? Failure { get; set; }
    public DateTime? DeletedAt { get; set; }
    public required DateTime LastUpdatedAt { get; set; }
    
    public string? MimeType { get; set; }
    public string? ThumbnailPath { get; set; }
    public DateTime? ProcessingCompletedTimestamp { get; set; }
    public static FileMetadataDbo Initial(
        Guid id,
        DateTime uploadTimestamp,
        string storagePath,
        string originalFileName,
        long uploadFileSize,
        string mimeType,
        string userId,
        EntityType contextEntityType,
        Guid contextEntityId,
        string purpose)
    {
        return new FileMetadataDbo
        {
            Id = id,
            LastUpdatedAt = uploadTimestamp,
            UploadTimestamp = uploadTimestamp,
            StoragePath = storagePath,
            UploadFileName = originalFileName,
            UploadFileSize = uploadFileSize,
            UploadMimeType = mimeType,
            UserId = userId,
            ContextEntityType = contextEntityType,
            ContextEntityId = contextEntityId,
            Purpose = purpose,
            Status = FileStatus.PendingUpload
        };
    }
    
    public static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<FileMetadataDbo>(Register);
    
    public static void Register(EntityTypeBuilder<FileMetadataDbo> entity)
    {
        // Map to table named after the class
        entity.ToTable(nameof(FileMetadataDbo));

        // Composite primary key: (Id, ValidUntilEventId)
        entity.HasKey(e => e.Id);

        // Additional recommended configurations for production:
        entity.Property(e => e.StoragePath).IsRequired();
        entity.Property(e => e.UserId).IsRequired();
        entity.Property(e => e.ContextEntityType).IsRequired();
        entity.Property(e => e.ContextEntityId).IsRequired();
        entity.Property(e => e.Purpose).IsRequired();
        entity.Property(e => e.UploadTimestamp).IsRequired();
        entity.Property(e => e.UploadFileName).IsRequired();
        entity.Property(e => e.UploadMimeType).IsRequired();
        entity.Property(e => e.UploadFileSize).IsRequired();
        entity.Property(e => e.Status).IsRequired();
        entity.Property(e => e.LastUpdatedAt).IsRequired();
        
        entity.Property(e => e.Failure)
            .HasConversion(
                // Serialize: Handle null JsonNode
                v => v == null ? null : v.ToString(),
                // Deserialize: Handle null/empty string from DB
                v => string.IsNullOrEmpty(v) ? null : JsonNode.Parse(v, null, default)
            )
            .HasColumnType("jsonb")
            .IsRequired(false);
    }
}