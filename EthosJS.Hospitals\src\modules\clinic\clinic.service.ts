import { BadRequestException, Injectable } from '@nestjs/common';
import { ClinicRepository } from '@app/modules/clinic/clinic.repository';
import { ClinicEntity } from '@app/modules/clinic/clinic.entity';
import { ClinicFiltersDto } from '@app/modules/clinic/dto/clinic.filters.dto';
import { CreateClinicDto } from '@app/modules/clinic/dto/create.clinic.dto';
import { UpdateClinicDto } from '@app/modules/clinic/dto/update.clinic.dto';
import { IListResult } from '@app/common/types';
import { Transactional } from 'typeorm-transactional-cls-hooked';

@Injectable()
export class ClinicService {
  constructor(
    private readonly repository: ClinicRepository,
  ) {}

  async list(filters: ClinicFiltersDto): Promise<IListResult<ClinicEntity>> {
    return this.repository.list(filters);
  }

  async getByIdOrFail(clinicId: number): Promise<ClinicEntity> {
    const clinic = await this.repository.findOne({
      where: {
        id: clinicId,
      },
      relations: ['facilities'],
    });

    if (!clinic) {
      throw new BadRequestException(`Clinic ${clinicId} is not found`);
    }

    return clinic;
  }

  async create(clinic: CreateClinicDto): Promise<ClinicEntity> {
    const entity = this.repository.create(clinic);

    return this.repository.save(entity);
  }

  async update({ id, ...update }: UpdateClinicDto): Promise<ClinicEntity> {
    const clinic = await this.getByIdOrFail(id);
    const entity = this.repository.merge(clinic, update);

    return this.repository.save(entity);
  }

  @Transactional()
  async delete(clinicId: number): Promise<ClinicEntity> {
    const clinic = await this.repository.findOne({
      where: {
        id: clinicId,
      },
      relations: ['facilities', 'technicians', 'facilities.bedSchedules', 'facilities.technicianSchedules'],
    });

    if (!clinic) {
      throw new BadRequestException(`Clinic ${clinicId} is not found`);
    }

    await this.repository.checkHasSchedules(clinic.id);
    await this.repository.softRemove(clinic);
    await this.repository.softRemoveRelations(clinicId);

    return clinic;
  }
}
