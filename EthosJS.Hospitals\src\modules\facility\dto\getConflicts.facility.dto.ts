import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsInt, IsOptional, IsPositive, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateFacilityEquipmentDto } from '@app/modules/facility/dto/create.facility.equipment.dto';

export class GetConflictsFacilityDto {
  @ApiProperty()
  @IsInt()
  id: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  @IsPositive()
  capacity?: number;

  @ApiPropertyOptional({ type: CreateFacilityEquipmentDto, isArray: true })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateFacilityEquipmentDto)
  equipments?: CreateFacilityEquipmentDto[];
}
