import { EntityRepository } from 'typeorm';
import { IListResult } from '@app/common/types';
import { BaseRepository } from '@app/common/base.repository';
import { CityFiltersDto } from '@app/modules/city/dto/city.filters.dto';
import { CityEntity } from '@app/modules/city/city.entity';
import { CityCollectionDto } from '@app/modules/city/dto/city.collection.dto';

@EntityRepository(CityEntity)
export class CityRepository extends BaseRepository<CityEntity> {
  collectionDto = CityCollectionDto;

  async list(filters: CityFiltersDto): Promise<IListResult<CityEntity>> {
    return super.list(filters);
  }
}
