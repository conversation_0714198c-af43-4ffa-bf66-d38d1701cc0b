import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class StudyCredentialItemDto {
  @ApiProperty()
  @Expose()
  credentialId: number;

  @ApiProperty()
  @Expose()
  credentialName: string;

  @ApiProperty()
  @Expose()
  credentialCode: string;
}

export class StudyCredentialDto {
  @ApiProperty({ isArray: true, type: StudyCredentialItemDto })
  @Expose()
  credentials: StudyCredentialItemDto[];

  @ApiProperty()
  @Expose()
  studyId?: number;

  @ApiProperty()
  @Expose()
  studyName: string;

  @ApiPropertyOptional()
  @Expose()
  stateId?: number;

  @ApiPropertyOptional()
  @Expose()
  stateName?: string;
}
