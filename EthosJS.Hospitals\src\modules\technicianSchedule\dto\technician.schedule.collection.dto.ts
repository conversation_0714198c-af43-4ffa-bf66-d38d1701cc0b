import { Expose } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { BaseCollectionDto } from '@app/common/dto/base.collection.dto';
import { TechnicianScheduleDto } from '@app/modules/technicianSchedule/dto/technician.schedule.dto';

export class TechnicianScheduleCollectionDto extends BaseCollectionDto {
  @ApiProperty({ type: TechnicianScheduleDto, isArray: true })
  @Expose()
  data: TechnicianScheduleDto[]
}
