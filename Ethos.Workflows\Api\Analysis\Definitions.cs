using System.Text.Json.Serialization;
using Ethos.Model;

namespace Ethos.Workflows.Api.Analysis;

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Language-Generic Analysis Machinery
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

// Top-level container for the entire discovered API surface.
public sealed record ApiModel(
    IReadOnlyDictionary<string, EntityControllerDefinition> EntityControllers,
    IReadOnlyDictionary<string, ControllerDefinition> Controllers, // e.g., "ProviderController"
    IReadOnlyDictionary<string, TypeDefinition> AllTypes);

// Represents a single [ApiController].
public sealed record EntityControllerDefinition(
    string EntityName,       // e.g., "CareLocation"
    string Route,            // e.g., "api/carelocation"
    TypeRef InputDto,        // Reference to the Create DTO
    TypeRef OutputDto,       // Reference to the Read DTO
    TypeRef QueryType);      // Reference to the Query object

public sealed record EndpointDefinition(
    string Method,           // e.g., "Get", "Post", "Put", "Delete"
    string Route,            // e.g., "api/carelocation/{id}"
    TypeRef? BodyInputType,
    IReadOnlyDictionary<string, TypeRef> QueryParameters, // e.g., { "filter": TypeRef },
    IReadOnlyDictionary<string, TypeRef> OtherParameters,
    TypeRef OutputType);

public sealed record ControllerDefinition(
    string Name,
    string Route,
    IReadOnlyList<EndpointDefinition> Endpoints);

// A union for all possible type definitions we can discover.
[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(DataClassDefinition), "DataClass")]
[JsonDerivedType(typeof(AdtDefinition), "Adt")]
[JsonDerivedType(typeof(EnumDefinition), "Enum")]
public abstract record TypeDefinition;

public sealed record DataClassDefinition(
    string Name,
    IReadOnlyList<PropertyDefinition> Properties) : TypeDefinition;

public sealed record AdtDefinition(
    string Name,
    string DiscriminatorField, // The field used for type checking, e.g., "$type"
    IReadOnlyList<DataClassDefinition> Variants) : TypeDefinition;

public sealed record EnumDefinition(
    string Name,
    IReadOnlyList<string> Members) : TypeDefinition;

public sealed record PropertyDefinition(
    string Name, // The C# name, e.g., "FirstName"
    TypeRef Type);

[JsonConverter(typeof(StringEnumConverter<PrimitiveKind>))]
public enum PrimitiveKind
{
    String, Bool, Int, Float, Guid, 
    TimeOnly, DateOnly, DateTime, DateTimeOffset, 
    Json, JsonDict
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(TypeRef.Primitive), "Primitive")]
[JsonDerivedType(typeof(TypeRef.Nullable), "Nullable")]
[JsonDerivedType(typeof(TypeRef.List), "List")]
[JsonDerivedType(typeof(TypeRef.Dict), "Dict")]
[JsonDerivedType(typeof(TypeRef.Set), "Set")]
[JsonDerivedType(typeof(TypeRef.Custom), "Custom")]
public abstract record TypeRef
{
    public sealed record Primitive(PrimitiveKind Kind) : TypeRef;
    public sealed record Nullable(TypeRef InnerType) : TypeRef;
    public sealed record List(TypeRef ElementType) : TypeRef;
    public sealed record Dict(TypeRef KeyType, TypeRef ValueType) : TypeRef;
    public sealed record Set(TypeRef ElementType) : TypeRef;
    public sealed record Custom(string Name) : TypeRef;
}