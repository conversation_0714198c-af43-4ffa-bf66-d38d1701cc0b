import { Expose, Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsInt, IsOptional, IsPositive, Min, ValidateNested } from 'class-validator';
import { IsDateOnly } from '@app/common/decorators/validators.decorator';
import { CreateBedScheduleEquipmentDto } from '@app/modules/bedSchedule/dto/create.bed.schedule.equipment.dto';

export class UpsertBedScheduleItemDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  @IsPositive()
  id?: number;

  @ApiProperty()
  @IsInt()
  @IsPositive()
  facilityId: number;

  @ApiProperty()
  @IsInt()
  @Min(0)
  dayShiftBeds: number;

  @ApiProperty()
  @IsInt()
  @Min(0)
  nightShiftBeds: number;

  @ApiPropertyOptional({ type: CreateBedScheduleEquipmentDto, isArray: true })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateBedScheduleEquipmentDto)
  equipments: CreateBedScheduleEquipmentDto[];

  @ApiProperty({ format: 'date-time', example: '2020-01-01' })
  @IsDateOnly()
  date: string;
}

export class UpsertBedScheduleDto {
  @ApiProperty({ type: UpsertBedScheduleItemDto, isArray: true })
  @IsArray()
  @Type(() => UpsertBedScheduleItemDto)
  @ValidateNested({ each: true })
  @Expose()
  items: UpsertBedScheduleItemDto[];
}
