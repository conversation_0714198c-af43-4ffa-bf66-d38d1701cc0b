using Ethos.Model;

namespace Ethos.Workflows.Api;

public sealed record CreateTechnicianAppointmentDto : IInputDto
{
    public Guid StudyId { get; set; }
    public Guid TechnicianId { get; set; }
    public Guid RoomId { get; set; }
    public Guid CareLocationShiftId { get; set; }
    public DateOnly Date { get; set; }
}

public sealed record TechnicianAppointmentDto
{
    public Guid Id { get; set; }
    public Guid StudyId { get; set; }
    public Guid TechnicianId { get; set; }
    public Guid RoomId { get; set; }
    public Guid CareLocationShiftId { get; set; }
    public DateOnly Date { get; set; }
}

public interface ITechnicianAppointmentApi : IEntityHttpClient<CreateTechnicianAppointmentDto, TechnicianAppointmentDto, TechnicianAppointmentQ>;

public class TechnicianAppointmentHttpClient(System.Net.Http.HttpClient httpClient)
    : EntityHttpClientBase<CreateTechnicianAppointmentDto, TechnicianAppointmentDto, TechnicianAppointmentQ>(httpClient, "technicianappointment"),
        ITechnicianAppointmentApi;