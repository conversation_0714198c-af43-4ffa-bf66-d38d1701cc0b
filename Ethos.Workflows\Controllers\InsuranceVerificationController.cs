using System.Security.Cryptography;
using System.Text;
using System.Text.Json.Nodes;
using Ethos.Model;
using Ethos.Utilities.Pagination;
using Ethos.Workflows.Database;
using Json.Path;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Persante.Insurance.FastAuth.Client;
using Persante.Insurance.FastAuth.Client.Model;

namespace Ethos.Workflows.Controllers;

public class FastAuthConfiguration
{
    public string BaseUrl { get; set; }
    public string ApiKey { get; set; }
    public string ApiSecret { get; set; }
    public string WebhookId { get; set; }
    public string WebhookSigningSecret { get; set; }
    public int WebhookTimestampToleranceSeconds { get; set; } = 300;
    public string DefaultAssignedOwnerForAuthorization { get; set; }
    public string DefaultDiagnosisQualifierCode { get; set; }
    public int ProcessingTimeoutMinutes { get; set; }
    public int WebhookWaitTimeoutHours { get; set; }
    public int MaxRetriesForStep { get; set; }
    public int RetryDelayMinutes { get; set; }
}

public class NoSuchStudyException(Guid StudyId) : Exception($"No such study with ID {StudyId}.");
public class NoSuchInsuranceServiceException(Guid ServiceId) : Exception($"No such insurance service with ID {ServiceId}.");
public class NoSuchJobException(Guid JobId) : Exception($"No such job with ID {JobId}.");

public class JobAcquisitionFailedException : Exception
{
    public JobAcquisitionFailedException(Guid jobId, string message) : base($"Failed to acquire job {jobId}: {message}") { }
    public JobAcquisitionFailedException(Guid jobId, string message, Exception innerException) : base($"Failed to acquire job {jobId}: {message}", innerException) { }
}

public class InsuranceVerificationService
{
    private readonly ILogger<InsuranceVerificationService> _logger;
    private readonly AppDbContext _dbContext;
    private readonly Guid instanceId = Guid.NewGuid();
    private readonly FastAuthConfiguration _fastAuthConfig;
    private readonly FastAuthClient _fastAuthClient;
    
    private readonly Guid FastAuthServiceId = new Guid("00000000-0000-0000-0000-000000000001");

    public InsuranceVerificationService(
        ILogger<InsuranceVerificationService> logger,
        AppDbContext dbContext,
        IOptions<FastAuthConfiguration> fastAuthOptions)
    {
        this._logger = logger;
        this._dbContext = dbContext;
        this._fastAuthConfig = fastAuthOptions.Value;
        this._fastAuthClient = new FastAuthClient(_fastAuthConfig.BaseUrl, _fastAuthConfig.ApiKey, _fastAuthConfig.ApiSecret);
        _fastAuthClient.RequestSent += LogFastAuthRequest;
        _fastAuthClient.ResponseReceived += LogFastAuthResponse;
    }
    
    public async Task<Guid> StartVerification(InsuranceVerificationRequestDto request)
    {
        // Check if the study exists
        var study = _dbContext.Set<StudyDbo>()
            .FirstOrDefaultAsync(s => s.Id == request.StudyId);
        if (study == null) 
            throw new NoSuchStudyException(request.StudyId);
        
        if (request.ServiceId != FastAuthServiceId) 
            throw new NoSuchInsuranceServiceException(request.ServiceId);
        
        var job = new InsuranceVerificationDbo
        {
            Id = Guid.NewGuid(),
            StudyId = request.StudyId,
            ServiceId = request.ServiceId,
            CreatedAt = DateTime.UtcNow,
        };
        job.UpdateFineGrainedState(new InsuranceVerificationJobState.VerificationStart());
        
        _dbContext.Set<InsuranceVerificationDbo>().Add(job);
        await _dbContext.SaveChangesAsync();
        return job.Id;
    }

    public async Task OnIdle()
    {
        await ProcessPendingJobsAsync();
        await HandleStuckProcessingJobsAsync();
        await HandleTimedOutExternalActionsAsync();
    }
    
    private async Task ProcessPendingJobsAsync()
    {
        var jobsToProcess = await _dbContext.Set<InsuranceVerificationDbo>()
            .Where(j => j.CoarseStateDiscriminator == nameof(CoarseJobState.Pending) &&
                        (j.WaitingUntil == null || j.WaitingUntil <= DateTime.UtcNow))
            .OrderBy(j => j.WaitingUntil ?? j.CreatedAt)
            .Take(10) // Batch size
            .ToListAsync();

        foreach (var job in jobsToProcess)
        {
            try
            {
                await AcquireJob(_dbContext, _logger, job.Id, instanceId, TimeSpan.FromMinutes(5), async acquiredJob =>
                {
                    // Main state machine dispatch
                    await DispatchJobStateActionAsync(acquiredJob);

                    // Release job after action (common save logic)
                    acquiredJob.ProcessingServiceInstanceId = null;
                    acquiredJob.ProcessingTimeoutTimestamp = null;
                    _dbContext.Update(acquiredJob);
                });
            }
            catch (JobAcquisitionFailedException ex)
            {
                _logger.LogInformation($"Job acquisition failed for {job.Id}: {ex.Message}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Unhandled error processing job {job.Id}. Job might be stuck if acquired.");
                // If AcquireJob doesn't auto-release on unhandled delegate exception, manual release might be needed or rely on timeout.
            }
        }
    }
    
    private async Task DispatchJobStateActionAsync(InsuranceVerificationDbo job)
    {
        var currentStateName = job.CurrentState.GetType().Name;
        _logger.LogInformation($"Processing JobId: {job.Id}, State: {currentStateName}");
        switch (job.CurrentState)
        {
            case InsuranceVerificationJobState.VerificationStart ji:
                await HandleVerificationStartAsync(job, ji);
                break;
            case InsuranceVerificationJobState.VerificationWebhookReceivedNeedsProcessing ji:
                await HandleVerificationWebhookReceivedNeedsProcessingAsync(job, ji);
                break;
            case InsuranceVerificationJobState.VerificationSuccessfulAuthRequired vsar: // Existing state, now triggers auth
                await HandleVerificationSuccessfulAuthRequiredAsync(job, vsar);
                break;
            case InsuranceVerificationJobState.AuthorizationStart as_state:
                await HandleAuthorizationStartAsync(job, as_state);
                break;
            case InsuranceVerificationJobState.AuthorizationWebhookReceivedNeedsProcessing awrnp:
                await HandleAuthorizationWebhookReceivedNeedsProcessingAsync(job, awrnp);
                break;
            default:
                _logger.LogWarning(
                    $"Job {job.Id} is in state {currentStateName} which has no direct processing action or is a waiting/error state handled elsewhere (e.g., webhook, timeout).");
                break;
        };
    }
    
    // ---- HANDLER METHODS ----
    private async Task HandleVerificationStartAsync(InsuranceVerificationDbo job, InsuranceVerificationJobState.VerificationStart ji)
    {
        var study = await _dbContext.Set<StudyDbo>() // Ensure study is loaded with related entities
            .Include(s => s.Order)
            .ThenInclude(o => o.Patient)
            .ThenInclude(p => p.Names)
            .Include(s => s.Order)
            .ThenInclude(o => o.ReferringPhysician)
            .ThenInclude(rp => rp.Names)
            .Include(s => s.Order)
            .ThenInclude(o => o.InterpretingPhysician)
            .ThenInclude(ip => ip.Names)
            .Include(s => s.Order)
            .ThenInclude(o => o.PrimaryCarePhysician)
            .ThenInclude(pcp => pcp.Names)
            .Include(s => s.Insurances)
            .FirstOrDefaultAsync(s => s.Id == job.StudyId);

        if (study == null)
        {
            _logger.LogError($"Job {job.Id}: Study {job.StudyId} not found during Handle_Job_InitializedAsync.");
            job.UpdateFineGrainedState(new InsuranceVerificationJobState.ErrorResourceNotFound("Study", job.StudyId, "Job study wasn't found"));
            return;
        }
        
        var order = job.Study.Order;
        var patient = job.Study.Order.Patient;
        var insurances = study.Insurances;
        var apptId = job.Id.ToString();
        
        var request = new FastAuthRequestData
        {
            ApptId = apptId,
            Cancelled = false,
            // FIXME: How do we get these?
            // EncounterType = request.EncounterType ?? defaultEncounterType ?? "outpatient",
            // PlaceOfServiceCode = defaultPlaceOfServiceCode,
            // AuthId = request.AuthorizationId,
            // CaseId = request.CaseId,
            // EffectiveDate = request.EffectiveDate,
            // ExpirationDate = request.ExpirationDate,
            // AuthRequired = request.AuthorizationRequired,
            // PrimaryServiceType = new FastAuthCodeValueData()
            // {
            //     Code = defaultPrimaryServiceTypeCode,
            //     Value = defaultPrimaryServiceTypeName
            // },
            Patient = new FastAuthPatientData
            {
                ApptId = apptId,
                Birth = patient.Demographics?.DateOfBirth?.ToDateTime(TimeOnly.Parse("12:00")),
                FirstName = patient.Names.FirstOrDefault()?.FirstName,
                LastName = patient.Names.FirstOrDefault()?.LastName,
                MiddleName = patient.Names.FirstOrDefault()?.MiddleName,
                // NOTE: Check if that's correct.
                MedicalRecord = patient.Id.ToString(),
                // NOTE: Check if that's correct.
                Payer = insurances.FirstOrDefault()?.InsuranceCarrier.ToString(),
                // NOTE: Check if that's correct.
                PayerId = insurances.FirstOrDefault()?.PatientId.ToString(),
                MemberId = insurances?.FirstOrDefault()?.MemberId,
                // FIXME: Figure out how to get that information.
                // RelationToSubscriber = !string.IsNullOrEmpty(request.Patient?.SubscriberRelationship) ?
                //                         new FastAuthCodeValueData()
                //                         {
                //                             Value = request.Patient?.SubscriberRelationship,
                //                         } : null,
            },
            OrderingProvider = new FastAuthProviderData()
            {
                FirstName = order.ReferringPhysician.Names.FirstOrDefault()?.FirstName,
                LastName = order.ReferringPhysician.Names.FirstOrDefault()?.LastName,
                MiddleName = order.ReferringPhysician.Names.FirstOrDefault()?.MiddleName,
                TaxId = order.ReferringPhysician?.Identifiers
                    .FirstOrDefault(i => i.System == IdentifierSystem.TaxId)?.Value,
                Npi = order.ReferringPhysician?.Identifiers
                    .FirstOrDefault(i => i.System == IdentifierSystem.NPI)?.Value,
                // SpecialtyCode = request.OrderingProvider.SpecialtyCode,
                // Specialty = new FastAuthCodeValueData()
                // {
                //     Code = request.OrderingProvider?.SpecialtyCode,
                //     Value = request.OrderingProvider?.SpecialtyName,
                // },
                // FacilityName = request.OrderingProvider.FacilityName,
                // City = request.OrderingProvider.City,
                // State = request.OrderingProvider.State,
                // Zip = request.OrderingProvider.Zip,
                // AddressLine1 = request.OrderingProvider.Address1,
                // AddressLine2 = request.OrderingProvider.Address2,
                // Phone = request.OrderingProvider.Phone,
                // Fax = request.OrderingProvider.Fax,
            }
        };

        foreach (var servicingProvider in (PhysicianDbo[])[order.InterpretingPhysician, order.PrimaryCarePhysician])
        {
            request.ServicingProviders.Add(new FastAuthProviderData()
            {
                FirstName = servicingProvider.Names.FirstOrDefault()?.FirstName,
                LastName = servicingProvider.Names.FirstOrDefault()?.LastName,
                MiddleName = servicingProvider.Names.FirstOrDefault()?.MiddleName,
                TaxId = servicingProvider.Identifiers
                    .FirstOrDefault(i => i.System == IdentifierSystem.TaxId)?.Value,
                Npi = servicingProvider.Identifiers
                    .FirstOrDefault(i => i.System == IdentifierSystem.NPI)?.Value,
                // FacilityName = servicingProvider.FacilityName,
                // City = servicingProvider.City,
                // State = servicingProvider.State,
                // Zip = servicingProvider.Zip,
                // AddressLine1 = servicingProvider.Address1,
                // AddressLine2 = servicingProvider.Address2,
                // Phone = servicingProvider.Phone,
                // Fax = servicingProvider.Fax,
                // SpecialtyCode = servicingProvider.SpecialtyCode ?? defaultServicingProviderSpecialtyCode,
                // Specialty = new FastAuthCodeValueData()
                // {
                //     Code = servicingProvider.SpecialtyCode ?? defaultServicingProviderSpecialtyCode,
                //     Value = servicingProvider.SpecialtyName ?? defaultServicingProviderSpecialty
                // }
            });
        }

        // FIXME: We don't really have this in the db.
        
        // foreach (var procedure in request.Procedures)
        // {
        //     result.Procedures.Add(new FastAuthProcedureData()
        //     {
        //         Code = procedure.Code,
        //         Quantity = procedure.Quantity ?? 1,
        //         From = procedure.Start,
        //         To = procedure.End,
        //         AuthRequired = procedure.AuthorizationRequired,
        //         Qualifier = new FastAuthCodeValueData()
        //         {
        //             Code = procedure.QualifierCode ?? defaultProcedureQualifierCode,
        //             Value = procedure.QualifierName ?? defaultProcedureQualifierName
        //         },
        //         ProcedureServiceQuantityType = new FastAuthCodeValueData()
        //         {
        //             Code = defaultProcedureQuantityQualifierCode,
        //             Value = defaultProcedureQuantityQualifierName,
        //         }
        //     });
        // }
        //
        // foreach (var diagnosis in request.Diagnoses)
        // {
        //     result.Diagnoses.Add(new FastAuthDiagnosisData()
        //     {
        //         Code = diagnosis.Code,
        //         Qualifier = new FastAuthCodeValueData()
        //         {
        //             Code = diagnosis.QualifierCode ?? defaultDiagnosisQualifierCode,
        //             Value = diagnosis.QualifierName ?? defaultDiagnosisQualifierName,
        //         }
        //     });
        // }
        
        // DefaultValues.TryGetValue("assignedOwnerAuthorization", out string? assignedOwnerAuthorization);
        //
        // // Assigning the "assignedOwner" field in FastAuth to the value "phc-support" will trigger the FastAuth
        // // service to perform authorization on their side with their staff & resources.
        // if (!string.IsNullOrEmpty(assignedOwnerAuthorization))
        //     authRequest.AssignedOwner = assignedOwnerAuthorization;

        try
        {
            _logger.LogInformation($"Job {job.Id}: Submitting verification request to FastAuth for ApptId {apptId}.");
            
            FastAuthRequestResponseData? faResult = await _fastAuthClient.Requests.CreateOrUpdate(request);
            
            _logger.LogInformation($"Job {job.Id}: FastAuth submission successful for ApptId {apptId}. FastAuth Status: {faResult?.Status}, AuthRequired: {faResult?.AuthRequired}");
            
            job.UpdateFineGrainedState(new InsuranceVerificationJobState.VerificationSubmittedAwaitingWebhook(
                CorrelationId: apptId, 
                WebhookTimeout: DateTime.UtcNow + TimeSpan.FromHours(_fastAuthConfig.WebhookWaitTimeoutHours)));
        }
        catch (Exception e)
        {
            _logger.LogError(e, $"Job {job.Id}: Error submitting verification request to FastAuth for ApptId {apptId}.");
            
            // TODO: add retry logic here if needed
            job.UpdateFineGrainedState(new InsuranceVerificationJobState.VerificationApiCallFailed(
                CorrelationId: apptId, 
                ErrorMessage: e.Message));
        }
    }

    private async Task HandleVerificationWebhookReceivedNeedsProcessingAsync(InsuranceVerificationDbo job, InsuranceVerificationJobState.VerificationWebhookReceivedNeedsProcessing ji)
    {
        _logger.LogInformation($"Job {job.Id}: Processing webhook data for verification.");

        var payloadNode = ji.LastWebhookPayload;
        
        // Assuming the actual data might be nested under a "data" property in the webhook
        var dataNode = payloadNode["data"] as JsonObject ?? payloadNode;
        // status: 'Auth Required' | 'No Auth Required' | 'Unknown', 
        // status: 'Approved - Partial' | 'Approved' | 'Auth Required' | 'Denied' | 'Expired' | 'In Review' | 'No Auth Required' | 'Unknown' | 'Withdrawn', 
        string? fastAuthStatus = dataNode["status"]?.GetValue<string>();
        const string FastAuthStatus_AuthRequired = "Auth Required";
        const string FastAuthStatus_AuthNotRequired = "No Auth Required";
        const string FastAuthStatus_Unknown = "Unknown";
        const string FastAuthStatus_ApprovedPartial = "Approved - Partial";
        const string FastAuthStatus_Approved = "Approved";
        const string FastAuthStatus_Denied = "Denied";
        const string FastAuthStatus_Expired = "Expired";
        const string FastAuthStatus_InReview = "In Review";
        const string FastAuthStatus_Withdrawn = "Withdrawn";
        
        //     authRequired: 'noData' | 'notRequired' | 'required',
        string? authRequired = dataNode["authRequired"]?.GetValue<string>(); // FastAuth client model uses PascalCase
        const string AuthRequired_NoData = "noData";
        const string AuthRequired_NotRequired = "notRequired";
        const string AuthRequired_Required = "required";
        
        // coverageStatus: 'active_Coverage' | 'coverage_Unknown' | 'inactive', 
        // coverageStatus: 'active_Coverage' | 'active_Services_Capitated_to_Primary_Care_Physician' | 'coverage_Unknown' | 'inactive', 
        string? coverageStatus = dataNode["coverageStatus"]?.GetValue<string>();
        const string CoverageStatus_Active = "active_Coverage";
        const string CoverageStatus_Unknown = "coverage_Unknown";
        const string CoverageStatus_Inactive = "inactive";
        const string CoverageStatus_ActiveServicesCapitatedToPrimaryCarePhysician = "active_Services_Capitated_to_Primary_Care_Physician";
        
        // string? apptIdFromWebhook = dataNode["apptId"]?.GetValue<string>(); // Already matched by controller

        _logger.LogInformation($"Job {job.Id}: Webhook details - FastAuthStatus: '{fastAuthStatus}', AuthRequired: '{authRequired}', CoverageStatus: '{coverageStatus}'");

        bool isActiveCoverage = CoverageStatus_Active.Equals(coverageStatus, StringComparison.OrdinalIgnoreCase) ||
                                CoverageStatus_ActiveServicesCapitatedToPrimaryCarePhysician.Equals(coverageStatus, StringComparison.OrdinalIgnoreCase);

        job.ExternalActionTimeoutTimestamp = null; // Webhook has been processed
        
        // Case 1: Success - Authorization is explicitly NOT required or has been Approved.
        // This is a terminal success state.
        if (FastAuthStatus_AuthNotRequired.Equals(fastAuthStatus, StringComparison.OrdinalIgnoreCase) ||
            AuthRequired_NotRequired.Equals(authRequired, StringComparison.OrdinalIgnoreCase) ||
            FastAuthStatus_Approved.Equals(fastAuthStatus, StringComparison.OrdinalIgnoreCase) ||
            FastAuthStatus_ApprovedPartial.Equals(fastAuthStatus, StringComparison.OrdinalIgnoreCase))
        {
            if (isActiveCoverage)
            {
                job.UpdateFineGrainedState(new InsuranceVerificationJobState.VerificationSuccessfulAuthNotRequired(payloadNode));
            }
            else
            {
                _logger.LogWarning($"Job {job.Id}: Auth not required by FastAuth, but coverage is not 'Active_Coverage' (is '{coverageStatus}'). Marking as NoCoverageOrDenied.");
                job.UpdateFineGrainedState(new InsuranceVerificationJobState.VerificationFailedNoCoverageOrDenied(
                    VerificationWebhookPayload: payloadNode,
                    CoverageStatus: coverageStatus));
            }
        }
        // Case 2: Success - Authorization IS required.
        // This is an intermediate success state that will likely lead to a new "Authorization" job.
        else if (FastAuthStatus_AuthRequired.Equals(fastAuthStatus, StringComparison.OrdinalIgnoreCase) ||
                 AuthRequired_Required.Equals(authRequired, StringComparison.OrdinalIgnoreCase))
        {
             if (isActiveCoverage)
            {
                // Pass CorrelationId and payload to the next state
                job.UpdateFineGrainedState(new InsuranceVerificationJobState.VerificationSuccessfulAuthRequired(
                    CorrelationId: ji.CorrelationId, 
                    VerificationWebhookPayload: payloadNode));
            }
            else
            {
                _logger.LogWarning($"Job {job.Id}: Auth required by FastAuth, but coverage is not 'Active_Coverage' (is '{coverageStatus}'). Marking as NoCoverageOrDenied.");
                job.UpdateFineGrainedState(new InsuranceVerificationJobState.VerificationFailedNoCoverageOrDenied(
                    VerificationWebhookPayload: payloadNode,
                    CoverageStatus: coverageStatus));
            }
        }
        // Case 3: Failure - The request was denied, coverage is inactive, or the request expired/was withdrawn.
        // This is a terminal failure state.
        else if (FastAuthStatus_Denied.Equals(fastAuthStatus, StringComparison.OrdinalIgnoreCase) ||
                 FastAuthStatus_Expired.Equals(fastAuthStatus, StringComparison.OrdinalIgnoreCase) ||
                 FastAuthStatus_Withdrawn.Equals(fastAuthStatus, StringComparison.OrdinalIgnoreCase) ||
                 CoverageStatus_Inactive.Equals(coverageStatus, StringComparison.OrdinalIgnoreCase))
        {
            job.UpdateFineGrainedState(new InsuranceVerificationJobState.VerificationFailedNoCoverageOrDenied(
                VerificationWebhookPayload: payloadNode,
                CoverageStatus: coverageStatus));
        }
        // Case 4: Indeterminate / Pending - The process is still ongoing or resulted in an unknown state.
        // This requires review or a timeout/retry mechanism.
        else if (FastAuthStatus_InReview.Equals(fastAuthStatus, StringComparison.OrdinalIgnoreCase) ||
                 FastAuthStatus_Unknown.Equals(fastAuthStatus, StringComparison.OrdinalIgnoreCase) ||
                 CoverageStatus_Unknown.Equals(coverageStatus, StringComparison.OrdinalIgnoreCase) ||
                 AuthRequired_NoData.Equals(authRequired, StringComparison.OrdinalIgnoreCase))
        {
            _logger.LogWarning($"Job {job.Id}: Webhook indicates still pending or unknown coverage (FastAuthStatus: '{fastAuthStatus}', CoverageStatus: '{coverageStatus}'). This might require manual review or re-triggering logic not yet implemented. Marking as indeterminate error.");
            // This could be a scenario where FastAuth sends an intermediate update.
            // For now, we'll treat it as an indeterminate outcome.
            job.UpdateFineGrainedState(new InsuranceVerificationJobState.VerificationIndeterminateWebhookVerification(
                VerificationWebhookPayload: payloadNode,
                CoverageStatus: coverageStatus,
                FastAuthStatus: fastAuthStatus,
                AuthRequired: authRequired));
        }
        // Case 5: Unhandled - A catch-all for any combination not explicitly handled above.
        // This protects against new, unexpected statuses from the external service.
        else
        {
            _logger.LogError($"Job {job.Id}: Unhandled FastAuth status combination from webhook. FastAuthStatus: '{fastAuthStatus}', AuthRequired: '{authRequired}', CoverageStatus: '{coverageStatus}'.");
            job.UpdateFineGrainedState(new InsuranceVerificationJobState.VerificationIndeterminateWebhookVerification(
                VerificationWebhookPayload: payloadNode,
                CoverageStatus: coverageStatus,
                FastAuthStatus: fastAuthStatus,
                AuthRequired: authRequired));
        }

        await Task.CompletedTask;
    }

    private async Task HandleVerificationSuccessfulAuthRequiredAsync(InsuranceVerificationDbo job,
        InsuranceVerificationJobState.VerificationSuccessfulAuthRequired ji)
    {
        job.UpdateFineGrainedState(new InsuranceVerificationJobState.AuthorizationStart(ji.CorrelationId, ji.VerificationWebhookPayload));
        await Task.CompletedTask; // No async operation here, just a state transition
    }
    
    private async Task HandleAuthorizationStartAsync(InsuranceVerificationDbo job, InsuranceVerificationJobState.AuthorizationStart authStartState)
    {
        _logger.LogInformation($"Job {job.Id}: Starting authorization process for CorrelationId: {authStartState.CorrelationId}.");

        // Re-use study loading logic if needed, or assume necessary info is in authStartState.VerificationWebhookPayload
        // For this example, we'll assume we patch the existing FastAuth request using the CorrelationId.
        var study = await _dbContext.Set<StudyDbo>()
            .FirstOrDefaultAsync(s => s.Id == job.StudyId); // Basic load, enhance if more details needed for auth request construction

        if (study == null)
        {
            _logger.LogError($"Job {job.Id}: Study {job.StudyId} not found during HandleAuthorizationStartAsync.");
            job.UpdateFineGrainedState(new InsuranceVerificationJobState.ErrorResourceNotFound("Study", job.StudyId, "Job study wasn't found for authorization."));
            return;
        }
        
        var fastAuthRequest = new FastAuthRequestData
        {
            ApptId = authStartState.CorrelationId, // This MUST be the same ApptId used for verification
            // Only include fields that need to be patched/updated for authorization.
            // For FastAuth, typically this involves setting the AssignedOwner to trigger their manual auth process.
            AssignedOwner = _fastAuthConfig.DefaultAssignedOwnerForAuthorization 
            // Potentially add/update other fields if FastAuth's authorization workflow requires it,
            // e.g., specific notes, or confirming procedure/diagnosis codes from vsar.VerificationWebhookPayload if they can change.
        };

        try
        {
            _logger.LogInformation($"Job {job.Id}: Submitting authorization update to FastAuth for ApptId {authStartState.CorrelationId}.");
            // Assuming PATCH is the correct method to update an existing request for authorization.
            // If FastAuth has a different mechanism or CreateOrUpdate is idempotent and handles this, adjust accordingly.
            FastAuthRequestResponseData? faResult = await _fastAuthClient.Requests.Patch(fastAuthRequest);
            
            _logger.LogInformation($"Job {job.Id}: FastAuth authorization submission successful for ApptId {authStartState.CorrelationId}. FastAuth Status: {faResult?.Status}");

            job.UpdateFineGrainedState(new InsuranceVerificationJobState.AuthorizationSubmittedAwaitingWebhook(
                CorrelationId: authStartState.CorrelationId,
                WebhookTimeout: DateTime.UtcNow + TimeSpan.FromHours(_fastAuthConfig.WebhookWaitTimeoutHours)
            ));
        }
        catch (Exception e)
        {
            _logger.LogError(e, $"Job {job.Id}: Error submitting authorization request to FastAuth for ApptId {authStartState.CorrelationId}.");
            job.UpdateFineGrainedState(new InsuranceVerificationJobState.AuthorizationApiCallFailed(
                CorrelationId: authStartState.CorrelationId,
                ErrorMessage: e.Message
            ));
        }
    }
    
    private async Task HandleAuthorizationWebhookReceivedNeedsProcessingAsync(InsuranceVerificationDbo job, InsuranceVerificationJobState.AuthorizationWebhookReceivedNeedsProcessing awrnp)
    {
        _logger.LogInformation($"Job {job.Id}: Processing webhook data for authorization for CorrelationId: {awrnp.CorrelationId}.");

        var payloadNode = awrnp.LastWebhookPayload;
        var dataNode = payloadNode["data"] as JsonObject ?? payloadNode;

        string? fastAuthStatus = dataNode["status"]?.GetValue<string>();
        string? authId = dataNode["authId"]?.GetValue<string>();
        DateTime? decisionDate = dataNode["decisionDate"]?.TryGetValue<DateTime>(out var dd) ?? false ? dd : null;
        DateTime? effectiveDate = dataNode["effectiveDate"]?.TryGetValue<DateTime>(out var ed) ?? false ? ed : null;
        DateTime? expirationDate = dataNode["expirationDate"]?.TryGetValue<DateTime>(out var exd) ?? false ? exd : null;
        string? denialReason = dataNode["denial"]?["reason"]?.GetValue<string>() ?? dataNode["statusNotes"]?.GetValue<string>(); // Example paths

        _logger.LogInformation($"Job {job.Id}: Auth Webhook details - FastAuthStatus: '{fastAuthStatus}', AuthId: '{authId}'.");
        
        job.ExternalActionTimeoutTimestamp = null; // Webhook processed

        if ("Approved".Equals(fastAuthStatus, StringComparison.OrdinalIgnoreCase) || 
            "Authorized".Equals(fastAuthStatus, StringComparison.OrdinalIgnoreCase)) // Common positive statuses
        {
            job.UpdateFineGrainedState(new InsuranceVerificationJobState.AuthorizationSuccessful(
                awrnp.CorrelationId,
                payloadNode,
                authId,
                effectiveDate ?? decisionDate, // Prefer effective, fallback to decision
                expirationDate
            ));
        }
        else if ("Denied".Equals(fastAuthStatus, StringComparison.OrdinalIgnoreCase))
        {
            job.UpdateFineGrainedState(new InsuranceVerificationJobState.AuthorizationFailedDenied(
                awrnp.CorrelationId,
                payloadNode,
                denialReason ?? "Denied by payer"
            ));
        }
        else if ("Pending".Equals(fastAuthStatus, StringComparison.OrdinalIgnoreCase) || 
                 "Submitted".Equals(fastAuthStatus, StringComparison.OrdinalIgnoreCase)) // Other non-terminal statuses
        {
            _logger.LogWarning($"Job {job.Id}: Authorization webhook indicates still pending (FastAuthStatus: '{fastAuthStatus}'). This might require manual review or further waiting logic not fully implemented yet. Marking as indeterminate.");
            job.UpdateFineGrainedState(new InsuranceVerificationJobState.AuthorizationIndeterminateWebhook(
                awrnp.CorrelationId,
                payloadNode,
                fastAuthStatus
            ));
        }
        else
        {
            _logger.LogError($"Job {job.Id}: Unhandled FastAuth status from authorization webhook: '{fastAuthStatus}'.");
            job.UpdateFineGrainedState(new InsuranceVerificationJobState.AuthorizationIndeterminateWebhook(
                 awrnp.CorrelationId,
                 payloadNode,
                 fastAuthStatus
            ));
        }
        await Task.CompletedTask;
    }
    
    // ---- TIMEOUT HANDLERS ----
    private async Task HandleStuckProcessingJobsAsync()
    {
        var stuckJobs = await _dbContext.Set<InsuranceVerificationDbo>()
            .Where(j => j.CoarseStateDiscriminator == nameof(CoarseJobState.Processing) &&
                        j.ProcessingServiceInstanceId != null && // Claimed by an instance
                        j.ProcessingTimeoutTimestamp != null &&
                        j.ProcessingTimeoutTimestamp <= DateTime.UtcNow)
            .ToListAsync();

        foreach (var job in stuckJobs)
        {
            _logger.LogWarning($"Job {job.Id} (State: {job.CoarseStateDiscriminator}) was stuck in Processing by instance {job.ProcessingServiceInstanceId}.");
            job.ProcessingServiceInstanceId = null;
            job.ProcessingTimeoutTimestamp = null;
            job.UpdateFineGrainedState(new InsuranceVerificationJobState.ErrorStuckProcessing(
                Message: $"Job was stuck in Processing by instance {job.ProcessingServiceInstanceId}."));
        }
        if (stuckJobs.Any()) await _dbContext.SaveChangesAsync();
    }

    private async Task HandleTimedOutExternalActionsAsync()
    {
        var timedOutJobs = await _dbContext.Set<InsuranceVerificationDbo>()
            .Where(j => j.CoarseStateDiscriminator == nameof(CoarseJobState.WaitingForExternalAction) &&
                        j.ExternalActionTimeoutTimestamp != null &&
                        j.ExternalActionTimeoutTimestamp <= DateTime.UtcNow)
            .ToListAsync();

        foreach (var job in timedOutJobs)
        {
            _logger.LogWarning($"Job {job.Id} (State: {job.CoarseStateDiscriminator}) timed out waiting for external action (e.g., webhook).");
            job.ExternalActionTimeoutTimestamp = null;
            job.UpdateFineGrainedState(new InsuranceVerificationJobState.ErrorWebhookTimeout(
                Message: $"Job timed out waiting for external action (e.g., webhook)."));
        }
        
        if (timedOutJobs.Any()) await _dbContext.SaveChangesAsync();
    }
    
    // ---- HELPER METHODS ----
    
    public static async Task AcquireJob(
        DbContext dbContext, ILogger logger,
        Guid jobId, Guid instanceId, TimeSpan processingTimeout,
        Func<InsuranceVerificationDbo, Task> onJobAcquired)
    {
        var job = await dbContext.Set<InsuranceVerificationDbo>()
            .FirstOrDefaultAsync(e => e.Id == jobId);
        if (job == null) 
            throw new NoSuchJobException(jobId);
        
        // **CHECK 1: Is the job in a suitable state to be acquired?**
        // We expect the job to be in 'Pending' state and not yet claimed by any processor.
        if (job.CoarseStateDiscriminator != nameof(CoarseJobState.Pending) || job.ProcessingServiceInstanceId != null)
        {
            string reason = $"Job is not in the expected state for acquisition. Current CoarseJobState: {job.CoarseStateDiscriminator}, ProcessingServiceInstanceId: {job.ProcessingServiceInstanceId}. Expected Initial state and no processor.";
            logger.LogWarning($"JobAcquisitionFailed for JobId {jobId}: {reason}");
            throw new JobAcquisitionFailedException(jobId, reason);
        }
        
        // **Attempt to claim the job**
        // If the checks above pass, this instance will try to claim the job.
        job.CoarseStateDiscriminator = nameof(CoarseJobState.Processing);
        job.LastCoarseStateUpdate = DateTime.UtcNow; // Use UtcNow for server-side timestamps
        job.ProcessingServiceInstanceId = instanceId;   // Assign current service instance ID
        job.ProcessingTimeoutTimestamp = DateTime.UtcNow + processingTimeout; // Set processing timeout (e.g., 5 minutes)

        try
        {
            // SaveChangesAsync is transactional. If a concurrency conflict occurs,
            // DbUpdateConcurrencyException will be thrown.
            await dbContext.SaveChangesAsync();
            logger.LogInformation($"Job {jobId} successfully acquired and set to Processing by instance {instanceId}.");
            await onJobAcquired(job);
            await dbContext.SaveChangesAsync();
        }
        catch (DbUpdateConcurrencyException ex)
        {
            // This exception means another process modified the job in the database
            // between the time we loaded it and tried to save our changes.
            // Our initial checks were based on stale data.
            logger.LogWarning(ex, $"Concurrency conflict for job {jobId} while attempting to set to Processing. Another instance likely claimed it.");

            // Optionally, reload the entity to log its current state after the conflict
            var entry = ex.Entries.SingleOrDefault(); // Should be the job entity
            if (entry != null)
            {
                await entry.ReloadAsync(); // Get the database's current version
                var currentDbJob = (InsuranceVerificationDbo)entry.Entity;
                logger.LogInformation($"Job {jobId} current DB state after concurrency conflict: CoarseJobState={currentDbJob.CoarseStateDiscriminator}, ProcessingServiceInstanceId={currentDbJob.ProcessingServiceInstanceId}.");
            }
            
            throw new JobAcquisitionFailedException(jobId, "Concurrency conflict: Job was modified or claimed by another process.", ex);
        }
    }
    
    
    // Logging helpers for FastAuth client
    private void LogFastAuthRequest(object? sender, RequestSentEventArgs e)
    {
        _logger.LogInformation("FastAuth Request Sent: URL: {Url}, Method: {Method}, Body: {Body}", e.Url, e.HttpMethod, e.Request);
        // Potentially log to a separate audit table or a more structured log
    }

    private void LogFastAuthResponse(object? sender, ResponseReceivedEventArgs e)
    {
        _logger.LogInformation("FastAuth Response Received: URL: {Url}, Status: {Status}, Body: {Body}", e.Url, e.HttpStatus, e.Response);
        // Potentially log to a separate audit table or a more structured log
        // If error status, log as warning/error
        if (e.HttpStatus.HasValue && (int)e.HttpStatus.Value >= 400)
        {
            _logger.LogWarning("FastAuth returned an error status: {Status} for URL: {Url}", e.HttpStatus, e.Url);
        }
    }
    
    // observability
    
    public async Task<InsuranceVerificationJobStatusDto?> GetJobStatusAsync(Guid jobId)
    {
        var job = await _dbContext.Set<InsuranceVerificationDbo>()
            .FirstOrDefaultAsync(j => j.Id == jobId);

        if (job == null)
        {
            return null; // Or throw NoSuchJobException(jobId) if preferred for controller to handle
        }

        JsonNode? fineGrainedStateDetails = null;
        try
        {
            if (!string.IsNullOrEmpty(job.StateJson))
            {
                fineGrainedStateDetails = JsonNode.Parse(job.StateJson);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to parse StateJson for job {JobId} into JsonNode.", jobId);
            // Optionally, include the raw string or an error message in the DTO
        }

        return new InsuranceVerificationJobStatusDto
        {
            JobId = job.Id,
            StudyId = job.StudyId,
            ServiceId = job.ServiceId,
            CreatedAt = job.CreatedAt,
            CurrentFineGrainedStateName = job.StateName,
            CurrentFineGrainedStateDetails = fineGrainedStateDetails, // The actual deserialized state object
            CurrentCoarseState = job.CoarseStateDiscriminator,
            CoarseStateMessage = job.CoarseStateMessage,
            CoarseStateSucceeded = job.CoarseStateSucceeded,
            LastCoarseStateUpdate = job.LastCoarseStateUpdate,
            WaitingUntil = job.WaitingUntil,
            ExternalActionTimeoutTimestamp = job.ExternalActionTimeoutTimestamp,
            ProcessingServiceInstanceId = job.ProcessingServiceInstanceId,
            ProcessingTimeoutTimestamp = job.ProcessingTimeoutTimestamp
        };
    }
}


public sealed record InsuranceVerificationRequestDto
{
    public Guid StudyId { get; set; }
    public Guid ServiceId { get; set; }
}

public sealed record InsuranceVerificationJobStatusDto
{
    public Guid JobId { get; set; }
    public Guid StudyId { get; set; }
    public Guid ServiceId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CurrentFineGrainedStateName { get; set; } = null!;
        
    // Represents the deserialized StateJson for more detailed insight
    public JsonNode? CurrentFineGrainedStateDetails { get; set; } 
        
    public string CurrentCoarseState { get; set; } = null!;
    public string? CoarseStateMessage { get; set; } // From Completed or Error states
    public bool? CoarseStateSucceeded { get; set; } // From Completed state
    public DateTime? LastCoarseStateUpdate { get; set; }
        
    public DateTime? WaitingUntil { get; set; } // If in Pending with a delay
    public DateTime? ExternalActionTimeoutTimestamp { get; set; } // If WaitingForExternalAction
    public Guid? ProcessingServiceInstanceId { get; set; } // If Processing
    public DateTime? ProcessingTimeoutTimestamp { get; set; } // If Processing
}


[ApiController]
[Route("api/[controller]")]
public class InsuranceVerificationController : ControllerBase
{
    private readonly ILogger<InsuranceController> _logger;
    private readonly InsuranceVerificationService _service;
    private readonly AppDbContext _dbContext;
    private readonly FastAuthConfiguration _fastAuthConfig;
    private const string FastAuthSignatureHeader = "x-fastauth-signature-256";
    
    public InsuranceVerificationController(
        ILogger<InsuranceController> logger,
        InsuranceVerificationService service,
        AppDbContext dbContext,
        IOptions<FastAuthConfiguration> fastAuthOptions)
    {
        _logger = logger;
        _service = service;
        _dbContext = dbContext;
        _fastAuthConfig = fastAuthOptions.Value;
    }
    
    private InsuranceVerificationJobStatusDto MapEntityToJobStatusDto(InsuranceVerificationDbo job)
    {
        JsonNode? fineGrainedStateDetails = null;
        try
        {
            if (!string.IsNullOrEmpty(job.StateJson))
            {
                fineGrainedStateDetails = JsonNode.Parse(job.StateJson);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to parse StateJson for job {JobId}.", job.Id);
        }

        return new InsuranceVerificationJobStatusDto
        {
            JobId = job.Id,
            StudyId = job.StudyId,
            ServiceId = job.ServiceId,
            CreatedAt = job.CreatedAt,
            CurrentFineGrainedStateName = job.StateName,
            CurrentFineGrainedStateDetails = fineGrainedStateDetails,
            CurrentCoarseState = job.CoarseStateDiscriminator,
            CoarseStateMessage = job.CoarseStateMessage,
            CoarseStateSucceeded = job.CoarseStateSucceeded,
            LastCoarseStateUpdate = job.LastCoarseStateUpdate,
            WaitingUntil = job.WaitingUntil,
            ExternalActionTimeoutTimestamp = job.ExternalActionTimeoutTimestamp,
            ProcessingServiceInstanceId = job.ProcessingServiceInstanceId,
            ProcessingTimeoutTimestamp = job.ProcessingTimeoutTimestamp
        };
    }

    private async Task<InsuranceVerificationJobStatusDto?> GetJobStatusAsync(Guid jobId)
    {
        var job = await _dbContext.Set<InsuranceVerificationDbo>()
            .AsNoTracking()
            .FirstOrDefaultAsync(j => j.Id == jobId);

        return job == null ? null : MapEntityToJobStatusDto(job);
    }

    /// <summary>
    /// Executes a pre-built query for insurance verification jobs, handles pagination, and maps to DTOs.
    /// </summary>
    /// <param name="queryable">The IQueryable with filters and includes already applied.</param>
    /// <param name="page">The page number.</param>
    /// <param name="pageSize">The number of items per page.</param>
    /// <returns>A paged result of job statuses.</returns>
    private async Task<PagedResponse<InsuranceVerificationJobStatusDto>> GetJobsAsync(IQueryable<InsuranceVerificationDbo> queryable, PagingParameters pagingParameters)
    {
        // Using the provided PaginateWithLinksAsync extension
        return await queryable.PaginateWithLinksAsync<InsuranceVerificationDbo, InsuranceVerificationJobStatusDto>(
            this, // Pass controller instance for link generation
            items => items.Select(MapEntityToJobStatusDto).ToList(), // Provide the mapping function
            pagingParameters.limit,
            pagingParameters.offset
        );
    }
    
    [HttpPost("start")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> StartVerification([FromBody] InsuranceVerificationRequestDto request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }
        try
        {
            var jobId = await _service.StartVerification(request);
            return Ok(new { jobId = jobId, message = "Insurance verification process started." });
        }
        catch (NoSuchStudyException ex)
        {
            return NotFound(new ProblemDetails { Title = "Study Not Found", Detail = ex.Message, Status = StatusCodes.Status404NotFound });
        }
        catch (NoSuchInsuranceServiceException ex)
        {
            return BadRequest(new ProblemDetails { Title = "Invalid Service", Detail = ex.Message, Status = StatusCodes.Status400BadRequest });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting insurance verification for StudyId {StudyId}", request.StudyId);
            return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails { Title = "An unexpected error occurred.", Detail = ex.Message });
        }
    }
    
    [HttpGet("status/{jobId:guid}")]
    [ProducesResponseType(typeof(InsuranceVerificationJobStatusDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetJobStatus(Guid jobId)
    {
        try
        {
            var statusDto = await _service.GetJobStatusAsync(jobId);
            if (statusDto == null)
            {
                return NotFound(new ProblemDetails { Title = "Job Not Found", Detail = $"No insurance verification job found with ID {jobId}.", Status = StatusCodes.Status404NotFound });
            }
            return Ok(statusDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving status for job {JobId}", jobId);
            return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails { Title = "An unexpected error occurred.", Detail = ex.Message});
        }
    }
    
    /// <summary>
    /// Searches for insurance verification jobs based on specified criteria.
    /// </summary>
    /// <param name="queryDto">The query criteria.</param>
    /// <param name="pagingParameters">Pagination parameters.</param>
    /// <returns>A paged list of insurance verification job statuses.</returns>
    [HttpPost("search")]
    [ProducesResponseType(typeof(PagedResponse<InsuranceVerificationJobStatusDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> SearchJobs(
        [FromBody] QueryDto<InsuranceVerificationQ> queryDto,
        [FromQuery] PagingParameters pagingParameters)
    {
        try
        {
            IQueryable<InsuranceVerificationDbo> queryable = _dbContext.Set<InsuranceVerificationDbo>().AsNoTracking();

            // Apply includes based on potential query types.
            // This is a broad approach; more targeted includes could be built if queryDto structure allows inspection.
            bool needsStudy = queryDto.Any(q => q is InsuranceVerificationQ.WithOrderId || q is InsuranceVerificationQ.WithPatientId);
            bool needsOrder = queryDto.Any(q => q is InsuranceVerificationQ.WithPatientId);

            if (needsOrder) // Implies needsStudy
            {
                queryable = queryable.Include(j => j.Study!).ThenInclude(s => s.Order);
            }
            else if (needsStudy)
            {
                queryable = queryable.Include(j => j.Study);
            }
            
            // Build and apply the predicate
            var predicate = queryDto.BuildPredicate<InsuranceVerificationDbo>();
            queryable = queryable.Where(predicate);
            
            // Order by creation date descending by default
            queryable = queryable.OrderByDescending(j => j.CreatedAt);

            var result = await GetJobsAsync(queryable, pagingParameters);
            return Ok(result);
        }
        catch (NotSupportedException ex) // Catch errors from BuildPredicateBody for unsupported query types
        {
            _logger.LogWarning(ex, "Unsupported query type used in search.");
            return BadRequest(new ProblemDetails { Title = "Unsupported Query", Detail = ex.Message, Status = StatusCodes.Status400BadRequest });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching insurance verification jobs.");
            return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails { Title = "An unexpected error occurred.", Detail = ex.Message });
        }
    }

    [HttpPost("fastauth-webhook")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> HandleFastAuthInsuranceUpdate()
    {
        string requestBody;
        using (var reader = new StreamReader(Request.Body, Encoding.UTF8))
        {
            requestBody = await reader.ReadToEndAsync();
        }

        if (string.IsNullOrEmpty(requestBody))
        {
            _logger.LogWarning("FastAuth Webhook: Received empty request body.");
            return BadRequest("Request body cannot be empty.");
        }

        if (!Request.Headers.TryGetValue(FastAuthSignatureHeader, out var signatureHeaderValues))
        {
            _logger.LogWarning("FastAuth Webhook: Missing '{HeaderName}' header.", FastAuthSignatureHeader);
            return BadRequest($"Missing '{FastAuthSignatureHeader}' header.");
        }
        var signatureHeader = signatureHeaderValues.FirstOrDefault();

        if (!IsValidFastAuthSignature(signatureHeader, requestBody, _fastAuthConfig.WebhookSigningSecret, _fastAuthConfig.WebhookTimestampToleranceSeconds, out string validationError))
        {
            _logger.LogWarning("FastAuth Webhook: Invalid signature. Error: {Error}. Header: {Header}", validationError, signatureHeader);
            return Unauthorized($"Invalid signature: {validationError}");
        }

        _logger.LogInformation("FastAuth Webhook: Signature validated successfully. Body: {Body}", requestBody);

        JsonNode? payloadNode;
        try
        {
            payloadNode = JsonNode.Parse(requestBody);
            if (payloadNode == null) throw new InvalidOperationException("Parsed JSON node is null.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "FastAuth Webhook: Failed to parse JSON payload. Body: {Body}", requestBody);
            return BadRequest("Invalid JSON payload.");
        }

        // FastAuth's `appt_id` is what we stored as `CorrelationId` (job.Id.ToString())
        // The actual data is often nested.
        var dataNodeForCorrelation = payloadNode["data"] as JsonObject ?? payloadNode;
        string? correlationId = dataNodeForCorrelation["apptId"]?.GetValue<string>();


        if (string.IsNullOrEmpty(correlationId))
        {
            _logger.LogWarning("FastAuth Webhook: Could not extract 'apptId' as CorrelationID from payload: {Payload}", requestBody);
            return BadRequest("CorrelationID (apptId) missing or not found in payload.");
        }

        // Convert correlationId (which is job.Id.ToString()) back to Guid to find the job
        if (!Guid.TryParse(correlationId, out Guid jobId))
        {
            _logger.LogWarning("FastAuth Webhook: Extracted apptId '{CorrelationID}' is not a valid Guid.", correlationId);
            return BadRequest("Invalid CorrelationID format in payload.");
        }


        var job = await _dbContext.Set<InsuranceVerificationDbo>()
            .FirstOrDefaultAsync(j =>
                j.Id == jobId && // Directly match on JobId
                j.CoarseStateDiscriminator == nameof(CoarseJobState.WaitingForExternalAction) &&
                (j.StateName == nameof(InsuranceVerificationJobState.VerificationSubmittedAwaitingWebhook) ||
                 j.StateName == nameof(InsuranceVerificationJobState.AuthorizationSubmittedAwaitingWebhook)) // Assuming Authorization flow has a similar waiting state
            );

        if (job == null)
        {
            _logger.LogWarning("FastAuth Webhook: No matching active job found for JobId (CorrelationID) '{JobId}' in an appropriate waiting state.", jobId);
            return Ok("Webhook acknowledged; no matching active job found or job not in a ready waiting state.");
        }

        _logger.LogInformation("FastAuth Webhook: Found matching JobId '{JobId}'. Current job state: {JobStateName}", job.Id, job.StateName);

        switch (job.CurrentState)
        {
            case InsuranceVerificationJobState.VerificationSubmittedAwaitingWebhook ji:
                job.UpdateFineGrainedState(new InsuranceVerificationJobState.VerificationWebhookReceivedNeedsProcessing(correlationId, payloadNode as JsonObject));
                break;
            case InsuranceVerificationJobState.AuthorizationSubmittedAwaitingWebhook ji:
                job.UpdateFineGrainedState(new InsuranceVerificationJobState.AuthorizationWebhookReceivedNeedsProcessing(correlationId, payloadNode as JsonObject));
                break;
            default:
                _logger.LogWarning("FastAuth Webhook: JobId '{JobId}' was found but is in an unexpected state '{CurrentState}' to receive this webhook.", job.Id, job.StateName);
                return Ok("Webhook acknowledged; job not in a state to process this specific webhook type at this moment.");
        }
        
        try
        {
            _dbContext.Update(job);
            await _dbContext.SaveChangesAsync();
            _logger.LogInformation("FastAuth Webhook: JobId '{JobId}' updated to state '{NewState}'. Will be picked up by job processor.", job.Id, job.StateName);
        }
        catch (DbUpdateConcurrencyException ex)
        {
            _logger.LogWarning(ex, "FastAuth Webhook: Concurrency conflict while updating JobId '{JobId}'.", job.Id);
            // It's possible another process (like a timeout handler) modified the job.
            // The webhook is acknowledged. The other process's change might take precedence or the job processor will sort it out.
            return Ok("Webhook acknowledged; concurrency conflict during job update."); 
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "FastAuth Webhook: Error saving updates for JobId '{JobId}'.", job.Id);
            return StatusCode(StatusCodes.Status500InternalServerError, "Internal error persisting webhook data after validation.");
        }

        return Ok("Webhook data received and queued for processing.");
    }

    private static bool IsValidFastAuthSignature(string? signatureHeader, string requestBody, string webhookSecret, int timestampToleranceSeconds, out string errorMessage)
    {
        errorMessage = "";
        if (string.IsNullOrEmpty(signatureHeader))
        {
            errorMessage = "Signature header is missing.";
            return false;
        }

        string? timestampStr = null;
        string? providedHash = null;

        var parts = signatureHeader.Split(',');
        foreach (var part in parts)
        {
            var kv = part.Split(new[] { '=' }, 2);
            if (kv.Length == 2)
            {
                if (kv[0].Trim() == "t") timestampStr = kv[1].Trim();
                if (kv[0].Trim().Equals("sha256", StringComparison.OrdinalIgnoreCase)) providedHash = kv[1].Trim();
            }
        }

        if (string.IsNullOrEmpty(timestampStr) || !long.TryParse(timestampStr, out long timestamp))
        {
            errorMessage = "Timestamp 't' is missing or invalid in signature header.";
            return false;
        }

        if (string.IsNullOrEmpty(providedHash))
        {
            errorMessage = "Hash 'sha256' is missing in signature header.";
            return false;
        }

        var currentUnixTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        if (Math.Abs(currentUnixTime - timestamp) > timestampToleranceSeconds)
        {
            errorMessage = $"Timestamp is outside the tolerance window. Current: {currentUnixTime}, Header: {timestamp}, Tolerance: {timestampToleranceSeconds}s.";
            return false;
        }

        // As per FastAuth docs: The `signed_payload_string` is "the unix timestamp as a string, a literal dot character, and then the raw request body string".
        var stringToSign = $"{timestamp}.{requestBody}";
        using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(webhookSecret)); // Secret must be for this specific webhook
        var computedHashBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(stringToSign));
        var computedHashHex = BitConverter.ToString(computedHashBytes).Replace("-", "").ToLowerInvariant();

        if (!computedHashHex.Equals(providedHash, StringComparison.OrdinalIgnoreCase))
        {
            errorMessage = "Computed hash does not match provided hash.";
            // For debugging: _logger.LogDebug("Hash mismatch. Expected: '{ProvidedHash}', Computed: '{ComputedHash}', StringToSign: '{StringToSign}' (length: {Length})", providedHash, computedHashHex, stringToSign, stringToSign.Length);
            return false;
        }
        errorMessage = "Signature validated.";
        return true;
    }
}