//@ts-nocheck
// import '@axmit/persante-hospitals';
import { IRequestSuccess } from 'common/models';

export interface IStudiesCollectionPayload {}
export interface IStudiesCollectionDto {}

export interface IStudiesCollection {
  data: IStudiesCollectionDto | null;
  loading: boolean;
}

export interface IStudyModelCreatePayload extends IRequestSuccess {}
export interface IStudyModelUpdatePayload extends IRequestSuccess {}
export interface IStudyModelDeletePayload extends IRequestSuccess {}
export interface IStudyModelDto {}

export interface IStudyModel {
  data: IStudyModelDto | null;
  loading: boolean;
}

export interface IStudyCredentialDto {
  id?: number;
}
export interface IStudyCredentialItemDto {}
