import moment from 'moment';
import { EntityRepository } from 'typeorm';
import { BadRequestException } from '@nestjs/common';
import { IListResult } from '@app/common/types';
import { FacilityFiltersDto } from '@app/modules/facility/dto/facility.filters.dto';
import { FacilityEntity } from '@app/modules/facility/facility.entity';
import { BaseRepository } from '@app/common/base.repository';
import { FacilityCollectionDto } from '@app/modules/facility/dto/facility.collection.dto';
import { ISchedule } from '@app/modules/schedule/types';
import { convertRawSchedule } from '@app/modules/schedule/helpers';

@EntityRepository(FacilityEntity)
export class FacilityRepository extends BaseRepository<FacilityEntity> {
  collectionDto = FacilityCollectionDto;

  async list(filters: FacilityFiltersDto): Promise<IListResult<FacilityEntity>> {
    return super.list(filters, ['city', 'clinic']);
  }

  async checkHasSchedules(facilityId: number): Promise<void> {
    const today = moment().startOf('day');

    const [{ count }] = await this.query('SELECT COUNT(*) as count FROM schedules WHERE date >= $1 AND facility_id = $2 AND deleted_at IS NULL', [today, facilityId]);

    if (Number(count) > 0) {
      throw new BadRequestException('Study has related schedules');
    }
  }

  async getFacilitySchedules(facilityId: number): Promise<ISchedule[]> {
    const today = moment().startOf('day');
    const result = await this.query('SELECT * FROM schedules WHERE date >= $1 AND facility_id = $2 AND deleted_at IS NULL', [today, facilityId]);

    return result.map(convertRawSchedule);
  }

  async updateSchedulesCapacity(facilityId: number, capacity: number): Promise<void> {
    const today = moment().startOf('day');
    await this.query('UPDATE bed_schedules SET day_shift_beds = $3 WHERE day_shift_beds IS NOT NULL AND day_shift_beds > $3 AND date >= $1 AND facility_id = $2 AND deleted_at IS NULL', [today, facilityId, capacity]);
    await this.query('UPDATE bed_schedules SET night_shift_beds = $3 WHERE night_shift_beds IS NOT NULL AND night_shift_beds > $3 AND date >= $1 AND facility_id = $2 AND deleted_at IS NULL', [today, facilityId, capacity]);
  }

  async updateFacilityName(facilityId: number, name: string): Promise<void> {
    const today = moment().startOf('day');
    await this.query('UPDATE schedules SET facility_name = $3 WHERE date >= $1 AND facility_id = $2 AND deleted_at IS NULL', [today, facilityId, name]);
  }

  async softRemoveRelations(facilityId: number): Promise<void> {
    await this.query('UPDATE "technician_schedules" SET "deleted_at" = CURRENT_TIMESTAMP, "updated_at" = CURRENT_TIMESTAMP WHERE "facility_id" = $1', [facilityId]);
    await this.query('UPDATE "bed_schedules" SET "deleted_at" = CURRENT_TIMESTAMP, "updated_at" = CURRENT_TIMESTAMP WHERE "facility_id" = $1', [facilityId]);
    await this.query('UPDATE "facility_equipments" SET "deleted_at" = CURRENT_TIMESTAMP, "updated_at" = CURRENT_TIMESTAMP WHERE "facility_id" = $1', [facilityId]);
  }
}