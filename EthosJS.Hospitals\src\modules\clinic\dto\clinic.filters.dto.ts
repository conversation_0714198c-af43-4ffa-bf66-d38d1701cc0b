import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { BaseFiltersDto } from '@app/common/dto/base.filters.dto';
import { EClinicSort } from '@app/modules/clinic/enums';

export class ClinicFiltersDto extends BaseFiltersDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ enum: EClinicSort })
  @IsOptional()
  @IsEnum(EClinicSort)
  orderField: EClinicSort = EClinicSort.CreatedAt;
}
