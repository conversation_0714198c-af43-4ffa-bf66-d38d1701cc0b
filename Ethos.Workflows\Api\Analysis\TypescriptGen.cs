using System.Text;
using System.Text.RegularExpressions;

namespace Ethos.Workflows.Api.Analysis;

public static class TypeScriptGen
{
    public static Dictionary<string, string> Build(ApiModel apiModel)
    {
        var result = new Dictionary<string, string>
        {
            ["ethos-common.ts"] = ZipBuilder.GetEmbedded(typeof(TypeScriptGen).Assembly, "/ts/ethos-common.ts"),
            ["login-api.ts"] = ZipBuilder.GetEmbedded(typeof(TypeScriptGen).Assembly, "/ts/login-api.ts"),
            ["test-script.ts"] = ZipBuilder.GetEmbedded(typeof(TypeScriptGen).Assembly, "/ts/test-script.ts"),
            ["ethos-common-types.ts"] = GenerateCommonTypes(apiModel)
        };
        foreach (var controller in apiModel.EntityControllers.Values)
        {
            var apiFile = GenerateEntityApi(controller, apiModel);
            result[$"{Naming.PascalToKebab(controller.EntityName)}-api.ts"] = apiFile;
        }
        return result;
    }

    public static string GenerateCommonTypes(ApiModel apiModel)
    {
        var controllerSpecificTypes = apiModel.EntityControllers.Values
            .SelectMany(c => new[] { c.InputDto, c.OutputDto, c.QueryType })
            .Distinct()
            .Select(c => (c as TypeRef.Custom)?.Name)
            .ToHashSet();

        var commonTypesBuffer = new StringBuilder(16_384);
        commonTypesBuffer.AppendLine("import { DateOnly, DateTime, DateTimeOffset, TimeOnly, UUID, Json, JsonDict } from './ethos-common';");
        commonTypesBuffer.AppendLine();

        foreach (var pair in apiModel.AllTypes.OrderBy(p => p.Key))
        {
            var typeName = pair.Key;
            var typeDef = pair.Value;

            if (controllerSpecificTypes.Contains(typeName)) continue;

            AppendTypeDefinition(commonTypesBuffer, typeName, typeDef, apiModel);
        }

        return commonTypesBuffer.ToString();
    }

    private static void AppendTypeDefinition(StringBuilder sb, string typeName, TypeDefinition typeDef, ApiModel apiModel)
    {
        if (typeDef is DataClassDefinition dataClass)
        {
            AppendDataClass(sb, typeName, dataClass);
        }
        else if (typeDef is AdtDefinition adt)
        {
            AppendAdt(sb, typeName, adt, apiModel);
        }
        else if (typeDef is EnumDefinition enumDef)
        {
            AppendEnum(sb, typeName, enumDef);
        }
        else
        {
            throw new NotSupportedException($"Unsupported type definition: {typeDef.GetType().Name}");
        }
    }

    private static void AppendAdt(StringBuilder sb, string typeName, AdtDefinition adt, ApiModel apiModel)
    {
        // Generate interfaces for each variant
        foreach (var variant in adt.Variants)
        {
            sb.AppendLine($"export interface {typeName}_{variant.Name} {{");
            sb.AppendLine($"  $type: '{variant.Name}';");
            foreach (var prop in variant.Properties)
            {
                sb.AppendLine($"  {Naming.PascalToCamel(prop.Name)}: {ToTypeScript(prop.Type)};");
            }
            sb.AppendLine("}");
            sb.AppendLine();
        }

        // Generate the discriminated union type
        sb.AppendLine($"export type {typeName} =");
        sb.AppendLine(string.Join(" |" + Environment.NewLine, adt.Variants.Select(v => $"  {typeName}_{v.Name}")));
        sb.AppendLine(";");
        sb.AppendLine();

        // Generate a namespace for helper functions
        sb.AppendLine($"export namespace {typeName} {{");

        // toJSON function
        sb.AppendLine("  export function toJSON(adt: " + typeName + "): any {");
        sb.AppendLine("    switch (adt.$type) {");
        foreach (var variant in adt.Variants)
        {
            sb.AppendLine($"      case '{variant.Name}': {{");
            sb.AppendLine("        return {");
            sb.AppendLine("          $type: adt.$type,");
            foreach(var prop in variant.Properties)
            {
                var camelName = Naming.PascalToCamel(prop.Name);
                sb.AppendLine($"          {camelName}: {TypeScriptJsonSerializer(prop.Type, $"adt.{camelName}")},");
            }
            sb.AppendLine("        };");
            sb.AppendLine("      }");
        }
        sb.AppendLine("    }");
        sb.AppendLine("  }");
        sb.AppendLine();

        // fromJSON function
        sb.AppendLine("  export function fromJSON(data: any): " + typeName + " {");
        sb.AppendLine("    if (!data || typeof data !== 'object' || !data.$type) {");
        sb.AppendLine("        throw new Error('Invalid data format for ADT deserialization.');");
        sb.AppendLine("    }");
        sb.AppendLine("    switch (data.$type) {");
        foreach (var variant in adt.Variants)
        {
            sb.AppendLine($"      case '{variant.Name}': {{");
            sb.AppendLine($"        const variantData: {typeName}_{variant.Name} = {{");
            sb.AppendLine($"          $type: '{variant.Name}',");
             foreach(var prop in variant.Properties)
            {
                var camelName = Naming.PascalToCamel(prop.Name);
                sb.AppendLine($"          {camelName}: {TypeScriptJsonDeserializer(prop.Type, $"data.{camelName}")},");
            }
            sb.AppendLine("        };");
            sb.AppendLine("        return variantData;");
            sb.AppendLine("      }");
        }
        sb.AppendLine("      default: throw new Error(`Unknown ADT variant: ${data.$type}`);");
        sb.AppendLine("    }");
        sb.AppendLine("  }");

        sb.AppendLine("}");
        sb.AppendLine();
    }


    private static void AppendDataClass(StringBuilder sb, string typeName, DataClassDefinition dataClass)
    {
        sb.AppendLine($"export interface {typeName} {{");
        foreach (var prop in dataClass.Properties)
        {
            sb.AppendLine($"  {Naming.PascalToCamel(prop.Name)}: {ToTypeScript(prop.Type)};");
        }
        sb.AppendLine("}");
        sb.AppendLine();

        // Generate a namespace for helper functions
        sb.AppendLine($"export namespace {typeName} {{");

        // toJSON function
        sb.AppendLine($"  export function toJSON(obj: {typeName}): any {{");
        sb.AppendLine("    return {");
        foreach (var prop in dataClass.Properties)
        {
            var camelName = Naming.PascalToCamel(prop.Name);
            sb.AppendLine($"      {camelName}: {TypeScriptJsonSerializer(prop.Type, $"obj.{camelName}")},");
        }
        sb.AppendLine("    };");
        sb.AppendLine("  }");
        sb.AppendLine();

        // fromJSON function
        sb.AppendLine($"  export function fromJSON(data: any): {typeName} {{");
        sb.AppendLine("    if (!data || typeof data !== 'object') {");
        sb.AppendLine($"        throw new Error('Invalid data format for {typeName} deserialization.');");
        sb.AppendLine("    }");
        sb.AppendLine($"    const obj: {typeName} = {{");
        foreach (var prop in dataClass.Properties)
        {
            var camelName = Naming.PascalToCamel(prop.Name);
            sb.AppendLine($"      {camelName}: {TypeScriptJsonDeserializer(prop.Type, $"data.{camelName}")},");
        }
        sb.AppendLine("    };");
        sb.AppendLine("    return obj;");
        sb.AppendLine("  }");

        sb.AppendLine("}");
        sb.AppendLine();
    }

    private static void AppendEnum(StringBuilder sb, string typeName, EnumDefinition enumDef)
    {
        sb.AppendLine($"export enum {typeName} {{");
        foreach (var member in enumDef.Members)
        {
            sb.AppendLine($"  {member} = '{member}',");
        }
        sb.AppendLine("}");
        sb.AppendLine();
    }

    private static string ToTypeScript(this TypeRef type) => type switch
    {
        TypeRef.Primitive(var kind) => kind switch
        {
            PrimitiveKind.Bool => "boolean",
            PrimitiveKind.Int => "number",
            PrimitiveKind.Float => "number",
            PrimitiveKind.String => "string",
            PrimitiveKind.Guid => "UUID",
            PrimitiveKind.TimeOnly => "TimeOnly",
            PrimitiveKind.DateOnly => "DateOnly",
            PrimitiveKind.DateTime => "DateTime",
            PrimitiveKind.DateTimeOffset => "DateTimeOffset",
            PrimitiveKind.Json => "Json",
            PrimitiveKind.JsonDict => "JsonDict",
            _ => "any"
        },
        TypeRef.Nullable(var innerType) => $"{innerType.ToTypeScript()} | null",
        TypeRef.List(var elementType) => $"Array<{elementType.ToTypeScript()}>",
        TypeRef.Dict(var keyType, var valueType) => $"Record<{keyType.ToTypeScript()}, {valueType.ToTypeScript()}>",
        TypeRef.Set(var elementType) => $"Set<{elementType.ToTypeScript()}>",
        TypeRef.Custom(var name) => name,
        _ => "any"
    };

    private static string TypeScriptJsonSerializer(this TypeRef type, string varName) => type switch
    {
        TypeRef.Primitive => varName,
        TypeRef.Nullable(var inner) => $"{varName} === null ? null : {TypeScriptJsonSerializer(inner, varName)}",
        TypeRef.List(var element) => $"{varName}.map(x => {TypeScriptJsonSerializer(element, "x")})",
        TypeRef.Set(var element) => $"Array.from({varName}).map(x => {TypeScriptJsonSerializer(element, "x")})",
        TypeRef.Dict(var key, var value) => $"{varName}", // Dictionaries/Records are directly serializable if values are
        TypeRef.Custom(var name) when IsEnum(name) => varName,
        TypeRef.Custom(var name) => $"{name}.toJSON({varName})",
        _ => varName
    };

    private static string TypeScriptJsonDeserializer(this TypeRef type, string varName) => type switch
    {
        TypeRef.Primitive => varName,
        TypeRef.Nullable(var inner) => $"{varName} === null ? null : {TypeScriptJsonDeserializer(inner, varName)}",
        TypeRef.List(var element) => $"{varName}.map(x => {TypeScriptJsonDeserializer(element, "x")})",
        TypeRef.Set(var element) => $"new Set({varName}.map(x => {TypeScriptJsonDeserializer(element, "x")}))",
        TypeRef.Dict(var key, var value) => $"{varName}", // Dictionaries/Records are directly deserializable
        TypeRef.Custom(var name) when IsEnum(name) => $"{varName} as {name}",
        TypeRef.Custom(var name) => $"{name}.fromJSON({varName})",
        _ => varName
    };

    private static bool IsEnum(string typeName) => typeName.EndsWith("Enum"); // Simplified check

    public static string GenerateEntityApi(EntityControllerDefinition iface, ApiModel apiModel)
    {
        var entityName = iface.EntityName;
        var kebabName = Naming.PascalToKebab(entityName);
        var apiFileBuffer = new StringBuilder(16_384);

        // Imports
        apiFileBuffer.AppendLine("import { AxiosInstance } from 'axios';");
        apiFileBuffer.AppendLine("import { EntityHttpClientBase, PagedResponse, PagingParameters, QueryDto, UUID } from './ethos-common';");
        apiFileBuffer.AppendLine("import * as Common from './ethos-common-types';");
        apiFileBuffer.AppendLine();

        // DTOs specific to this API
        var inputDto = (iface.InputDto as TypeRef.Custom)!;
        var outputDto = (iface.OutputDto as TypeRef.Custom)!;
        var queryType = (iface.QueryType as TypeRef.Custom)!;

        AppendTypeDefinition(apiFileBuffer, inputDto.Name, apiModel.AllTypes[inputDto.Name], apiModel);
        AppendTypeDefinition(apiFileBuffer, outputDto.Name, apiModel.AllTypes[outputDto.Name], apiModel);
        AppendTypeDefinition(apiFileBuffer, queryType.Name, apiModel.AllTypes[queryType.Name], apiModel);


        // API Class
        apiFileBuffer.AppendLine($"export class {entityName}Api extends EntityHttpClientBase<{inputDto.Name}, {outputDto.Name}, {queryType.Name}> {{");
        apiFileBuffer.AppendLine("  constructor(session: AxiosInstance) {");
        apiFileBuffer.AppendLine($"    super(session, '{entityName}');");
        apiFileBuffer.AppendLine("  }");
        apiFileBuffer.AppendLine("}");
        apiFileBuffer.AppendLine();

        return apiFileBuffer.ToString();
    }
}
