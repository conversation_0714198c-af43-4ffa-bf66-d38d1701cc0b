import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BaseCollectionDto } from '@app/common/dto/base.collection.dto';
import { StudyCollectionItemDto } from '@app/modules/study/dto/study.collection.item.dto';

export class StudyCollectionDto extends BaseCollectionDto {
  @ApiProperty({ type: StudyCollectionItemDto, isArray: true })
  @Expose()
  data: StudyCollectionItemDto[]
}
