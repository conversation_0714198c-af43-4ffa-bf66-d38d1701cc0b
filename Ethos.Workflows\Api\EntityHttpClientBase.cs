using Ethos.Model;

namespace Ethos.Workflows.Api;

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Web; // For HttpUtility used in query string building
using Microsoft.Net.Http.Headers; // For HeaderNames constants
// Assuming these namespaces exist and contain the relevant types
using Ethos.Utilities.Pagination;
// using Ethos.Workflows.Controllers; // Contains QueryDto definition conceptually

// Define the interface for the client
public interface IEntityHttpClient<TInputDto, TOutputDto, TQuery>
    where TQuery : IPrimitiveQuery // Assuming IPrimitiveQuery is a class/interface
{
    void SetBearerToken(string token);

    Task<(TOutputDto? Dto, string? Etag)> GetByIdAsync(Guid id);

    Task<PagedResponse<TOutputDto>?> SearchAsync(QueryDto<TQuery>? query, PagingParameters? paging = null);

    Task<PagedResponse<TOutputDto>?> GetAsync(QueryDto<TQuery>? query, PagingParameters? paging = null);

    public async Task<List<TOutputDto>> GetListAsync(QueryDto<TQuery>? query)
    {
        var paging = new PagingParameters
        {
            offset = 0,
            limit = 100 // Default limit, can be adjusted
        };
        
        var result = new List<TOutputDto>();

        while (true)
        {
            var response = await GetAsync(query, paging);
            if (response == null || response.Items.Count == 0)
            {
                return result; // No items found
            }

            if (response.Items.Count < paging.limit)
            {
                // If we received fewer items than the limit, this is the last page
                result.AddRange(response.Items);
                return result; // Return the accumulated results
            }

            // If we have more items, increase the offset for the next request
            paging.offset += paging.limit;
            result.AddRange(response.Items);
        }
    }

    Task<(TOutputDto? Dto, string? Etag)> CreateAsync(TInputDto input);

    Task<(TOutputDto? Dto, string? Etag, bool Created)> PutAsync(Guid id, TInputDto input, string? etag = null);

    Task<(TOutputDto? Dto, string? Etag)> PatchAsync(Guid id, TInputDto input, string etag);
}

// --- Base Implementation ---

public abstract class EntityHttpClientBase<TInputDto, TOutputDto, TQuery> : IEntityHttpClient<TInputDto, TOutputDto, TQuery>
    where TInputDto : IInputDto // Assuming IInputDto is implemented by classes
    where TOutputDto : class
    where TQuery : IPrimitiveQuery // Assuming IPrimitiveQuery is implemented by classes
{
    protected readonly HttpClient _httpClient;
    protected readonly string _controllerBasePath; // e.g., "api/patient"

    /// <summary>
    /// Default JsonSerializerOptions. Override ConfigureJsonSerializerOptions to customize.
    /// </summary>
    protected JsonSerializerOptions JsonSerializerOptions => ConfigureJsonSerializerOptions();

    /// <summary>
    /// Initializes a new instance of the EntityHttpClientBase.
    /// </summary>
    /// <param name="httpClient">The HttpClient instance. Ensure BaseAddress is configured.</param>
    /// <param name="controllerName">The name of the controller used in the route (e.g., "patient").</param>
    protected EntityHttpClientBase(HttpClient httpClient, string controllerName)
    {
        _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
        if (string.IsNullOrWhiteSpace(controllerName))
            throw new ArgumentNullException(nameof(controllerName));
        if (_httpClient.BaseAddress == null)
            Console.WriteLine($"Warning: HttpClient provided to {GetType().Name} has no BaseAddress set."); // Or throw exception

        _controllerBasePath = $"api/{controllerName.Trim('/')}";
    }

    /// <summary>
    /// Configures the JsonSerializerOptions used for requests and responses.
    /// Defaults to case-insensitive property names.
    /// Override to provide custom options or converters.
    /// </summary>
    protected virtual JsonSerializerOptions ConfigureJsonSerializerOptions()
    {
        return new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            // Add any custom converters if needed
        };
    }

    /// <summary>
    /// Sets the Bearer token for subsequent requests.
    /// </summary>
    /// <param name="token">The bearer token.</param>
    public virtual void SetBearerToken(string token)
    {
        _httpClient.DefaultRequestHeaders.Authorization =
            new AuthenticationHeaderValue("Bearer", token);
    }

    /// <summary>
    /// Throws an HttpRequestException if the response status code is not successful.
    /// </summary>
    protected static async Task ThrowIfNotSuccess(HttpResponseMessage response)
    {
        if (response.IsSuccessStatusCode) return;

        string body = "N/A";
        try
        {
            body = await response.Content.ReadAsStringAsync();
        }
        catch { /* Ignore errors reading body for error reporting */ }

        var ex = new HttpRequestException(
            $"API Request Failed: [{(int)response.StatusCode}] {response.ReasonPhrase}\nURL: {response.RequestMessage?.RequestUri}\nResponse Body:\n{body}", null, response.StatusCode);

        // Optionally add response headers or other info to ex.Data
        ex.Data.Add("ResponseHeaders", response.Headers.ToString());
        ex.Data.Add("ResponseBody", body);

        throw ex;
    }

    /// <summary>
    /// Extracts the ETag header value (without quotes).
    /// </summary>
    protected string? GetEtagFromResponse(HttpResponseMessage response)
    {
        return response.Headers.ETag?.Tag?.Trim('"');
    }

    /// <summary>
    /// Builds a query string from PagingParameters and optionally a simple query object.
    /// Note: Complex query objects might not serialize well into a GET query string.
    /// </summary>
    protected virtual string BuildQueryString(PagingParameters? paging = null, QueryDto<TQuery>? query = null)
    {
        var queryParams = HttpUtility.ParseQueryString(string.Empty); // Requires System.Web

        if (paging != null)
        {
            queryParams["offset"] = paging.offset.ToString();
            queryParams["limit"] = paging.limit.ToString();
        }

        if (query != null)
        {
             string jsonQuery = JsonSerializer.Serialize(query, JsonSerializerOptions);
             queryParams["queryBase64"] = Convert.ToBase64String(Encoding.UTF8.GetBytes(jsonQuery));
        }
        
        return queryParams.ToString() ?? string.Empty;
    }

    public virtual async Task<(TOutputDto? Dto, string? Etag)> GetByIdAsync(Guid id)
    {
        var path = $"{_controllerBasePath}/{id}";
        using var response = await _httpClient.GetAsync(path);
        await ThrowIfNotSuccess(response);

        var dto = await response.Content.ReadFromJsonAsync<TOutputDto>(JsonSerializerOptions);
        var etag = GetEtagFromResponse(response);
        return (dto, etag);
    }

    public virtual async Task<PagedResponse<TOutputDto>?> SearchAsync(QueryDto<TQuery>? query, PagingParameters paging)
    {
        var queryString = BuildQueryString(paging); // Only paging for POST search query string
        var path = $"{_controllerBasePath}/search?{queryString}";

        // POST request with query in body, paging in query string
        using var response = await _httpClient.PostAsJsonAsync(path, query, JsonSerializerOptions);
        await ThrowIfNotSuccess(response);

        return await response.Content.ReadFromJsonAsync<PagedResponse<TOutputDto>>(JsonSerializerOptions);
    }

    public virtual async Task<PagedResponse<TOutputDto>?> GetAsync(QueryDto<TQuery>? query, PagingParameters paging)
    {
        // Builds query string including paging and potentially simple query params (see BuildQueryString comments)
        var queryString = BuildQueryString(paging, query);
        var path = $"{_controllerBasePath}?{queryString}";

        using var response = await _httpClient.GetAsync(path);
        await ThrowIfNotSuccess(response);

        return await response.Content.ReadFromJsonAsync<PagedResponse<TOutputDto>>(JsonSerializerOptions);
    }

    public virtual async Task<(TOutputDto? Dto, string? Etag)> CreateAsync(TInputDto input)
    {
        using var response = await _httpClient.PostAsJsonAsync(_controllerBasePath, input, JsonSerializerOptions);
        // Expect 201 Created
        await ThrowIfNotSuccess(response);

        var dto = await response.Content.ReadFromJsonAsync<TOutputDto>(JsonSerializerOptions);
        var etag = GetEtagFromResponse(response);
        return (dto, etag);
    }

    public virtual async Task<(TOutputDto? Dto, string? Etag, bool Created)> PutAsync(Guid id, TInputDto input, string? etag = null)
    {
        var path = $"{_controllerBasePath}/{id}";
        using var request = new HttpRequestMessage(HttpMethod.Put, path);
        request.Content = JsonContent.Create(input, options: JsonSerializerOptions);

        if (!string.IsNullOrEmpty(etag))
        {
            // Use strong ETag format for If-Match
            request.Headers.TryAddWithoutValidation(HeaderNames.IfMatch, $"\"{etag}\"");
        }

        using var response = await _httpClient.SendAsync(request);
        // Expect 200 OK or 201 Created
        await ThrowIfNotSuccess(response);

        var dto = await response.Content.ReadFromJsonAsync<TOutputDto>(JsonSerializerOptions);
        var responseEtag = GetEtagFromResponse(response);
        bool created = response.StatusCode == System.Net.HttpStatusCode.Created;

        return (dto, responseEtag, created);
    }

    public virtual async Task<(TOutputDto? Dto, string? Etag)> PatchAsync(Guid id, TInputDto input, string etag)
    {
        if (string.IsNullOrEmpty(etag))
        {
            throw new ArgumentNullException(nameof(etag), "ETag is required for PATCH operations.");
        }

        var path = $"{_controllerBasePath}/{id}";
        using var request = new HttpRequestMessage(HttpMethod.Patch, path);
        request.Content = JsonContent.Create(input, options: JsonSerializerOptions);
        // Use strong ETag format for If-Match
        request.Headers.TryAddWithoutValidation(HeaderNames.IfMatch, $"\"{etag}\"");

        using var response = await _httpClient.SendAsync(request);
        // Expect 200 OK
        await ThrowIfNotSuccess(response);

        var dto = await response.Content.ReadFromJsonAsync<TOutputDto>(JsonSerializerOptions);
        var responseEtag = GetEtagFromResponse(response);

        return (dto, responseEtag);
    }
}


// --- Example Usage ---
/*
// Assuming these types exist:
public class PatientInputDto : IInputDto { public Guid? Id { get; set; } //... other props }
public class PatientOutputDto { public Guid Id { get; set; } public string? Etag { get; set; } //... other props }
public class PatientQuery : IPrimitiveQuery { //... query props }
public class PatientQueryDto : QueryDto<PatientQuery> { //... potentially specific properties }


public interface IPatientApi : IEntityHttpClient<PatientInputDto, PatientOutputDto, PatientQuery>
{
    // Add any patient-specific methods here if needed
}

public class PatientHttpClient : EntityHttpClientBase<PatientInputDto, PatientOutputDto, PatientQuery>, IPatientApi
{
    public PatientHttpClient(HttpClient httpClient)
        : base(httpClient, "patient") // Pass controller name "patient"
    {
    }

    // Implement patient-specific methods here if any

    // Example overriding JsonSerializerOptions if needed
    // protected override JsonSerializerOptions ConfigureJsonSerializerOptions()
    // {
    //     var options = base.ConfigureJsonSerializerOptions();
    //     // options.Converters.Add(new CustomPatientConverter());
    //     return options;
    // }
}

// --- Dependency Injection Setup (Example for ASP.NET Core) ---
// In Program.cs or Startup.cs

// builder.Services.AddHttpClient<IPatientApi, PatientHttpClient>(client =>
// {
//     client.BaseAddress = new Uri("https://your-api-base-url.com/");
//     // Add other default headers if needed
// });

*/