using Ethos.Model;

namespace Ethos.Workflows.Api;

public sealed record CreateNoteDto : IInputDto
{
    public required EntityType EntityType { get; set; }
    public required Guid EntityId { get; set; }
    public required string Content { get; set; }
}

public sealed record NoteDto
{
    public required Guid Id { get; set; }
    public required EntityType EntityType { get; set; }
    public required Guid EntityId { get; set; }
    public required string Content { get; set; }
}

public interface INoteApi : IEntityHttpClient<CreateNoteDto, NoteDto, NoteQ>;

public class NoteHttpClient(System.Net.Http.HttpClient httpClient)
    : EntityHttpClientBase<CreateNoteDto, NoteDto, NoteQ>(httpClient, "note"),
        INoteApi;