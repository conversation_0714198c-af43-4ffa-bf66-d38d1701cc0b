import { Module } from '@nestjs/common';
import { FacilityModule } from '@app/modules/facility/facility.module';
import { StudyModule } from '@app/modules/study/study.module';
import { ScheduleModule } from '@app/modules/schedule/schedule.module';
import { AvailableController } from '@app/modules/available/available.controller';
import { AvailableService } from '@app/modules/available/available.service';
import { EquipmentModule } from '@app/modules/equipment/equipment.module';

@Module({
  imports: [
    FacilityModule,
    EquipmentModule,
    StudyModule,
    ScheduleModule,
  ],
  providers: [AvailableService],
  controllers: [AvailableController],
})
export class AvailableModule {}
