import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsEnum, IsInt, IsOptional, IsPositive, ValidateIf, ValidateNested } from 'class-validator';
import { Expose, Type } from 'class-transformer';
import { ETechnicianScheduleShift } from '@app/modules/technicianSchedule/enums';
import { IsDateOnly } from '@app/common/decorators/validators.decorator';

export class UpsertTechnicianScheduleItemDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  @IsPositive()
  @Expose()
  id?: number;

  @ApiProperty()
  @IsInt()
  @IsPositive()
  @Expose()
  technicianId: number;

  @ApiPropertyOptional()
  @ValidateIf((obj) => obj.shift !== ETechnicianScheduleShift.DayOff)
  @IsInt()
  @IsPositive()
  @Expose()
  facilityId?: number;

  @ApiProperty({ enum: ETechnicianScheduleShift })
  @IsEnum(ETechnicianScheduleShift)
  @Expose()
  shift: ETechnicianScheduleShift;

  @ApiPropertyOptional()
  @ValidateIf((obj) => obj.shift !== ETechnicianScheduleShift.DayOff)
  @IsInt()
  @IsPositive()
  @Expose()
  capacity?: number;

  @ApiProperty({ format: 'date-time', example: '2020-01-01' })
  @IsDateOnly()
  @Expose()
  date: string;
}


export class UpsertTechnicianScheduleDto {
  @ApiProperty({ type: UpsertTechnicianScheduleItemDto, isArray: true })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpsertTechnicianScheduleItemDto)
  @Expose()
  items: UpsertTechnicianScheduleItemDto[];
}
