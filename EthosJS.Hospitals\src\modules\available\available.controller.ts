import { AuthGuard } from '@app/common/guards/auth.guard';
import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { AvailableScheduleFiltersDto } from '@app/modules/available/dto/available.schedule.filters.dto';
import { AvailableScheduleCollectionDto } from '@app/modules/available/dto/available.schedule.collection.dto';
import { AvailableService } from '@app/modules/available/available.service';
import { AvailableStudyCollectionDto } from '@app/modules/available/dto/available.study.collection.dto';
import { AvailableStudyFiltersDto } from '@app/modules/available/dto/available.study.filters.dto';
import { AvailableStudyDayFiltersDto } from '@app/modules/available/dto/available.study.day.filters.dto';
import { AvailableStudyDayCollectionDto } from '@app/modules/available/dto/available.study.day.collection.dto';

@Controller('available')
@ApiTags('Available entities')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class AvailableController {
  constructor(private readonly service: AvailableService) {
  }

  @Post('/schedules')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: AvailableScheduleCollectionDto,
    description: 'Get list of available schedule slots',
  })
  async getAvailableSchedules(@Body() filters: AvailableScheduleFiltersDto): Promise<AvailableScheduleCollectionDto> {
    return this.service.getAvailableSchedules(filters);
  }

  @Post('/studies')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: AvailableStudyCollectionDto,
    description: 'Get available studies at specific date',
  })
  async getAvailableStudies(@Body() filters: AvailableStudyFiltersDto): Promise<AvailableStudyCollectionDto> {
    return this.service.getAvailableStudies(filters);
  }

  @Post('/study-days')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: AvailableStudyDayCollectionDto,
    description: 'Get available study days',
  })
  async getAvailableStudyDays(@Body() filters: AvailableStudyDayFiltersDto): Promise<AvailableStudyDayCollectionDto> {
    return this.service.getAvailableStudyDays(filters);
  }
}
