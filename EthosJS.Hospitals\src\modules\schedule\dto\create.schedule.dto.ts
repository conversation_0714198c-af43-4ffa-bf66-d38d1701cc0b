import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsDefined, IsEnum, IsInt, IsOptional, IsPositive, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateScheduleEquipmentDto } from '@app/modules/schedule/dto/create.schedule.equipment.dto';
import { IsDateOnly } from '@app/common/decorators/validators.decorator';
import { SchedulePatientDto } from '@app/modules/schedule/dto/schedule.patient.dto';
import { EShift } from '@app/common/enums';

export class CreateScheduleDto {
  @ApiProperty()
  @IsInt()
  @IsPositive()
  facilityId: number;

  @ApiProperty()
  @IsDefined()
  @Type(() => SchedulePatientDto)
  @ValidateNested()
  patient: SchedulePatientDto;

  @ApiProperty()
  @IsInt()
  @IsPositive()
  studyId: number;

  @ApiProperty()
  @IsEnum(EShift)
  shift: EShift;

  @ApiProperty({ format: 'date-time', example: '2020-01-01' })
  @IsDateOnly()
  date: string;

  @ApiPropertyOptional({ type: () => CreateScheduleEquipmentDto, isArray: true })
  @IsOptional()
  @IsArray()
  @Type(() => CreateScheduleEquipmentDto)
  @ValidateNested({ each: true })
  equipments: CreateScheduleEquipmentDto[] = [];
}
