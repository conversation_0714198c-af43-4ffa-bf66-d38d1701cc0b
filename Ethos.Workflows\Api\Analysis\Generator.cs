using System.Collections;
using System.Collections.Concurrent;
using System.Reflection;
using System.Text.Json.Nodes;

namespace Ethos.Workflows.Api.Analysis;

using Factory = Func<Generator.GenCtx, object?>;

public static class Generator
{
    
    // Per-type factory cache: avoids reflection after the first call.
    private static readonly ConcurrentDictionary<Type, Lazy<Factory>> _factories = new();
    
    public sealed class GenCtx
    {
        public required Random Rng;
        public int Depth;                     // current recursion depth
        public int RemainingNodes;            // how many nodes can we still generate?
        private readonly Dictionary<Type, List<object>> _pool = new();
        public GenerationOptions Opts { get; init; } = new();
        
        public bool TryAcquireNode()
        {
            if (RemainingNodes <= 0) return false;
            RemainingNodes -= 1;
            return true;
        }
        
        public bool AreNewNodesAllowed => RemainingNodes > 0 && Depth < Opts.MaxDepth;
        public bool ShouldReuse => !AreNewNodesAllowed || Rng.NextDouble() < Opts.ReuseChancePercent / 100.0;
        
        public bool TryTakeFromPool(Type t, out object? value)
        {
            value = null;
            if (!_pool.TryGetValue(t, out var list) || list.Count == 0) return false;
            value = list[Rng.Next(list.Count)];
            return true;
        }

        public void AddToPool(Type t, object instance)
        {
            if (!_pool.TryGetValue(t, out var list))
            {
                list = new List<object>();
                _pool[t] = list;
            }
            list.Add(instance);
        }
    }

    public sealed record GenerationOptions(
        int Seed = 0,            // 0 means “use a random seed”
        int MaxDepth = 5,
        int MaxNodes = 128,
        int MaxCollectionLen = 5,
        int ReuseChancePercent = 55,          // --> 0 means “never reuse”
        IReadOnlyDictionary<Type, Func<GenCtx, object?>>? CustomGenerators = null,
        Action<Generator.GenCtx, Type, object>? OnInstanceCreated = null);

    public static TResult Generate<TResult>(GenerationOptions? opts = null) => 
        (TResult)Generate(typeof(TResult), opts);

    public static object? Generate(Type type, GenerationOptions? opts = null)
    {
        opts ??= new GenerationOptions();
        var rng = opts.Seed == 0 ? new Random() : new Random(opts.Seed);
        var ctx = new GenCtx { Rng = rng, Opts = opts };
        ctx.RemainingNodes = opts.MaxNodes;
        return GetGenerator(ctx, type)(ctx);
    }

    /* ----------------------------------------------------------------- */

    #region Factory builder

    private static Func<GenCtx, object?> GetGenerator(GenCtx ctx, Type t)
    {
        if (ctx.Opts.CustomGenerators != null && ctx.Opts.CustomGenerators.TryGetValue(t, out var custom))
        {
            return custom;
        }
        
        // One Lazy per Type; Lazy ensures nobody observes a half-built delegate.
        return _factories.GetOrAdd(t, static t =>
        {
            // ‘wrapper’ is immediately publishable and forwards to ‘realFactory’
            Factory? realFactory = null!;
            Factory wrapper = ctx => realFactory(ctx);

            // Build the real delegate *after* wrapper exists (breaks self-recursion).
            realFactory = Guard(t, ctx => BuildFactory0(t)(ctx));

            return new Lazy<Factory>(() => wrapper,         // the delegate others will see
                LazyThreadSafetyMode.ExecutionAndPublication);
        }).Value;
    }
    
    private static readonly object _CantGenerateSentinel = new object();
    
    private static object? GetTrivialValueOrNull(Type t)
    {
        if (t.IsArray)
            return Array.CreateInstance(t.GetElementType()!, 0);

        if (t == typeof(string))
            return string.Empty;
        
        /*  ICollection / IEnumerable → empty List<T>  */
        if (t.IsGenericType)
        {
            var gtd = t.GetGenericTypeDefinition();
            if (gtd == typeof(List<>)   || gtd == typeof(IReadOnlyList<>) ||
                gtd == typeof(IList<>)  || gtd == typeof(ICollection<>)  ||
                gtd == typeof(IEnumerable<>))
            {
                var elemType = t.GetGenericArguments()[0];
                return Activator.CreateInstance(typeof(List<>).MakeGenericType(elemType));
            }

            if (gtd == typeof(HashSet<>) || gtd == typeof(ISet<>))
            {
                var elemType = t.GetGenericArguments()[0];
                return Activator.CreateInstance(typeof(HashSet<>).MakeGenericType(elemType));
            }

            if (gtd == typeof(Dictionary<,>) ||
                gtd == typeof(IDictionary<,>) ||
                gtd == typeof(IReadOnlyDictionary<,>))
            {
                var args = t.GetGenericArguments();
                return Activator.CreateInstance(typeof(Dictionary<,>).MakeGenericType(args));
            }

            if (Nullable.GetUnderlyingType(t) != null)
                return null;                        // Nullable<T> → null is OK here
        }

        if (t.IsValueType)
            return Activator.CreateInstance(t);
        
        return _CantGenerateSentinel;
    }

    private static bool IsPoolable(Type t) =>
        !typeof(JsonNode).IsAssignableFrom(t) && !t.IsPrimitive && !t.IsEnum && !t.IsValueType;

    private static Func<GenCtx, object?> Guard(Type type, Func<GenCtx, object?> inner) => ctx =>
    {
        ctx.Depth++;
        try
        {
            if (ctx.Depth > ctx.Opts.MaxDepth || !ctx.TryAcquireNode())
            {
                var result = GetTrivialValueOrNull(type);
                if (result != _CantGenerateSentinel)
                    return result; // return trivial value or null
                // else: we HAVE to generate something, so continue
            }
            if (ctx.ShouldReuse && IsPoolable(type) && ctx.TryTakeFromPool(type, out var reused))
                return reused;
            var instance = inner(ctx);
            if (instance != null)
            {
                if (IsPoolable(type)) ctx.AddToPool(type, instance);
                ctx.Opts.OnInstanceCreated?.Invoke(ctx, type, instance);
            }

            return instance;
        }
        finally
        {
            ctx.Depth--;
        }
    };
    
    private static Func<GenCtx, object?> BuildFactory(Type type)
    {
        // Guard the factory to handle recursion and depth limits
        return Guard(type, ctx => BuildFactory0(type)(ctx));
    }
    
    private static readonly NullabilityInfoContext NCtx = new();
    
    private static Func<GenCtx, object?> BuildFactory0(Type type)
    {
        /* 1. Primitives & well-known BCL structs */
        if (type == typeof(bool))
            return ctx => ctx.Rng.Next(2) == 0;
        if (type == typeof(byte))
            return ctx => (byte)ctx.Rng.Next(byte.MinValue, byte.MaxValue + 1);
        if (type == typeof(sbyte))
            return ctx => (sbyte)ctx.Rng.Next(sbyte.MinValue, sbyte.MaxValue + 1);
        if (type == typeof(short))
            return ctx => (short)ctx.Rng.Next(short.MinValue, short.MaxValue + 1);
        if (type == typeof(ushort))
            return ctx => (ushort)ctx.Rng.Next(ushort.MinValue, ushort.MaxValue + 1);
        if (type == typeof(int))
            return ctx => ctx.Rng.Next(int.MinValue, int.MaxValue);
        if (type == typeof(uint))
            return ctx => (uint)ctx.Rng.NextInt64(uint.MinValue, (long)uint.MaxValue + 1);
        if (type == typeof(long))
            return ctx => ctx.Rng.NextInt64(long.MinValue, long.MaxValue);
        if (type == typeof(ulong))
            return ctx => (ulong)ctx.Rng.NextInt64(long.MinValue, long.MaxValue); // may wrap
        if (type == typeof(float))
            return ctx => (float)ctx.Rng.NextDouble();
        if (type == typeof(double))
            return ctx => ctx.Rng.NextDouble();
        if (type == typeof(decimal))
            return ctx => new decimal(ctx.Rng.Next(), ctx.Rng.Next(), ctx.Rng.Next(), 
                ctx.Rng.Next(2) == 0, (byte)ctx.Rng.Next(0, 28));
        if (type == typeof(char))
            return ctx => (char)ctx.Rng.Next(32, 127);             // printable ASCII
        if (type == typeof(string))
            return ctx =>
            {
                var len = ctx.Rng.Next(0, 24);
                Span<char> buf = stackalloc char[len];
                for (var i = 0; i < len; i++) buf[i] = (char)ctx.Rng.Next(32, 127);
                return new string(buf);
            };
        if (type == typeof(Guid))
            return _ => Guid.NewGuid();
        if (type == typeof(DateOnly))
            return ctx => DateOnly.FromDateTime(new DateTime(ctx.Rng.NextInt64(DateTime.MinValue.Ticks, DateTime.MaxValue.Ticks)));
        if (type == typeof(TimeOnly))
            return ctx => TimeOnly.FromTimeSpan(new TimeSpan(ctx.Rng.NextInt64(TimeOnly.MinValue.Ticks, TimeOnly.MaxValue.Ticks)));
        if (type == typeof(DateTime))
            return ctx => new DateTime(ctx.Rng.NextInt64(DateTime.MinValue.Ticks, DateTime.MaxValue.Ticks));
        if (type == typeof(DateTimeOffset))
            return ctx =>
            {
                var offest = TimeSpan.FromMinutes(ctx.Rng.Next(-12 * 60, 14 * 60)); // UTC-12 to UTC+14
                return new DateTimeOffset(
                    new DateTime(ctx.Rng.NextInt64(DateTime.MinValue.Ticks, DateTime.MaxValue.Ticks)),
                    offest
                );
            };
        if (type == typeof(TimeSpan))
            return ctx => new TimeSpan(ctx.Rng.NextInt64(TimeSpan.MinValue.Ticks, TimeSpan.MaxValue.Ticks));
        if (type == typeof(JsonNode))
        {
            return ctx =>
            {
                var nodes = new JsonNode[]
                {
                    JsonValue.Create(Guid.NewGuid()),
                    JsonValue.Create(ctx.Rng.Next(0, 100)),
                    JsonValue.Create(ctx.Rng.NextDouble()),
                    JsonValue.Create(ctx.Rng.Next(0, 100) == 0),
                    new JsonObject { ["key"] = "value" },
                    new JsonArray { "item1", "item2" }
                };
                return nodes[ctx.Rng.Next(nodes.Length)];
            };
        }
        else if (type == typeof(JsonObject))
        {
            return ctx =>
            {
                var jsonNodeFactory = GetGenerator(ctx, typeof(JsonNode));
                var obj = new JsonObject();
                var len = ctx.Rng.Next(1, 5); // at least one property
                for (int i = 0; i < len; i++)
                {
                    var key = $"key{i}";
                    var value = (JsonNode) jsonNodeFactory(ctx)!;
                    obj[key] = value;
                }
                return obj;
            };
        }

        if (type.IsArray)                                         // int[] …
        {
            var elemType     = type.GetElementType()!;
            return ctx =>
            {
                var elemFactory  = GetGenerator(ctx, elemType);
                var len = ctx.Rng.Next(0, ctx.Opts.MaxCollectionLen + 1);
                var arr = Array.CreateInstance(elemType, len);
                for (int i = 0; i < len; i++) arr.SetValue(elemFactory(ctx), i);
                return arr;
            };
        }
        
        if (type.IsGenericType)
        {
            var gtd = type.GetGenericTypeDefinition();

            /* 2a. List‑like ---------------------------------------------------- */
            if (gtd == typeof(List<>) ||
                gtd == typeof(IReadOnlyList<>) ||
                gtd == typeof(IList<>) ||
                gtd == typeof(ICollection<>) ||
                gtd == typeof(IEnumerable<>))
            {
                var elemType    = type.GetGenericArguments()[0];

                return ctx =>
                {
                    var elemFactory = GetGenerator(ctx, elemType);
                    var len  = ctx.Rng.Next(0, ctx.Opts.MaxCollectionLen + 1);
                    var list = (IList)Activator.CreateInstance(typeof(List<>).MakeGenericType(elemType))!;
                    for (int i = 0; i < len; i++) list.Add(elemFactory(ctx));
                    return list;                                 // List<T> satisfies every interface above
                };
            }

            /* 2b. HashSet ------------------------------------------------------ */
            if (gtd == typeof(HashSet<>) || gtd == typeof(ISet<>))
            {
                var elemType    = type.GetGenericArguments()[0];

                return ctx =>
                {
                    var elemFactory = GetGenerator(ctx, elemType);
                    var len  = ctx.Rng.Next(0, ctx.Opts.MaxCollectionLen + 1);
                    var set  = (ISet<object>)Activator.CreateInstance(typeof(HashSet<>).MakeGenericType(elemType))!;
                    for (int i = 0; set.Count < len && i < 2 * len && ctx.AreNewNodesAllowed; i++)
                        set.Add(elemFactory(ctx));
                    return set;
                };
            }

            /* 2c. Dictionary‑like --------------------------------------------- */
            if (gtd == typeof(Dictionary<,>) ||
                gtd == typeof(IDictionary<,>) ||
                gtd == typeof(IReadOnlyDictionary<,>))
            {
                var args          = type.GetGenericArguments();

                return ctx =>
                {
                    var keyFactory    = GetGenerator(ctx, args[0]);
                    var valueFactory  = GetGenerator(ctx, args[1]);
                    var len = ctx.Rng.Next(0, ctx.Opts.MaxCollectionLen + 1);
                    var dict = (IDictionary)Activator.CreateInstance(typeof(Dictionary<,>).MakeGenericType(args))!;
                    
                    for (int i = 0; dict.Count < len && i < 2 * len && ctx.AreNewNodesAllowed; i++)
                    {
                        var key = keyFactory(ctx);
                        if (key == null || dict.Contains(key)) continue; // skip nulls or duplicates
                        dict[key] = valueFactory(ctx);
                    }

                    return dict;
                };
            }
        }
        
        /* 2. Nullable<T>  ------------------------------------------------ */
        if (Nullable.GetUnderlyingType(type) is { } underlying)
        {
            return ctx =>
            {
                var innerFactory = GetGenerator(ctx, underlying);
                return ctx.Rng.Next(10) == 0 ? null : innerFactory(ctx);
            };
        }

        /* 3. Enums ------------------------------------------------------- */
        if (type.IsEnum)
        {
            var values = Enum.GetValues(type);
            return ctx => values.GetValue(ctx.Rng.Next(values.Length));
        }

        /* 4. Abstract root? => sealed ADT pick-a-child ------------------- */
        if (type.IsAbstract || type.IsInterface)
        {
            var concrete = type.Assembly
                               .GetTypes()
                               .Where(c => type.IsAssignableFrom(c) &&
                                           !c.IsAbstract &&
                                           !c.ContainsGenericParameters)
                               .OrderBy(c => c.GetConstructor(Type.EmptyTypes) == null ? 1 : 0)
                               .ToArray();

            if (concrete.Length == 0)
                throw new NotSupportedException($"No concrete implementations of {type.FullName} found.");
            
            return ctx =>
            {
                var factories = concrete.Select(t => GetGenerator(ctx, t)).ToArray();
                if (ctx.AreNewNodesAllowed)
                    return factories[ctx.Rng.Next(factories.Length)](ctx);
                
                // Try to choose a parameterless ctor
                var factory = factories.FirstOrDefault(f => f.Method.GetParameters().Length == 0);
                if (factory != null)
                    return factory(ctx);
                
                // If no parameterless ctor, just pick one
                return factories[ctx.Rng.Next(factories.Length)](ctx);
            };
        }

        /* 5. Records / POCO classes ------------------------------------- */
        if ((type.IsClass && !type.IsAbstract) || (type.IsValueType && !type.IsPrimitive && !type.IsEnum))
        {
            // Choose the public ctor with the most parameters
            var ctor = type.GetConstructors(BindingFlags.Public | BindingFlags.Instance)
                .OrderByDescending(c => c.GetParameters().Length)
                .FirstOrDefault();

            if (ctor != null)
            {
                var paramInfos = ctor.GetParameters();

                return ctx =>
                {
                    var paramFactories = paramInfos.Select(p =>
                        GetGenerator(ctx, p.ParameterType)).ToArray();

                    var args = new object?[paramFactories.Length];
                    for (int i = 0; i < args.Length; i++)
                    {
                        if (ctx.AreNewNodesAllowed)
                            args[i] = paramFactories[i](ctx);
                        else
                        {
                            // If the param is nullable, we can skip it
                            if (NCtx.Create(paramInfos[i]).ReadState == NullabilityState.Nullable)
                            {
                                args[i] = null;
                            }
                            else
                            {
                                // Otherwise, we must generate a value
                                args[i] = paramFactories[i](ctx);
                            }
                        }
                    }

                    var instance = ctor.Invoke(args);

                    // For records with init-only props not in the primary ctor,
                    // set them here if possible.
                    foreach (var p in type.GetProperties(BindingFlags.Public | BindingFlags.Instance))
                    {
                        if (!p.CanWrite || p.GetSetMethod() == null) continue;

                        // Skip if set by constructor
                        var ctorParam = paramInfos.FirstOrDefault(pi =>
                            string.Equals(pi.Name, p.Name, StringComparison.OrdinalIgnoreCase));
                        if (ctorParam != null) continue;

                        var f = GetGenerator(ctx, p.PropertyType);
                        if (ctx.AreNewNodesAllowed)
                        {
                            p.SetValue(instance, f(ctx));
                        }
                        else
                        {
                            // If the prop is nullable, we can skip it
                            if (NCtx.Create(p).ReadState == NullabilityState.Nullable)
                            {
                                p.SetValue(instance, null);
                            }
                            else
                            {
                                // Otherwise, we must generate a value
                                p.SetValue(instance, f(ctx));
                            }
                        }
                    }

                    return instance!;
                };
            }
            
            if (type.IsValueType)
            {
                // Create default instance and set properties
                return ctx =>
                {
                    var instance = Activator.CreateInstance(type);
                    foreach (var field in type.GetFields(BindingFlags.Public | BindingFlags.Instance))
                    {
                        var f = GetGenerator(ctx, field.FieldType);
                        field.SetValue(instance, f(ctx));
                    }
                    return instance;
                };
            }
        }

        throw new NotSupportedException($"Type {type.FullName} is not supported.");
    }

    #endregion
}