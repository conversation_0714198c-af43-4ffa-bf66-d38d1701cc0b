import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  StudyCredentialRepository,
  StudyRepository,
} from '@app/modules/study/study.repository';
import { StudyService } from '@app/modules/study/study.service';
import { StudyController } from '@app/modules/study/study.controller';
import { EquipmentModule } from '@app/modules/equipment/equipment.module';
import { CredentialModule } from '@app/modules/credential/credential.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      StudyRepository,
      StudyCredentialRepository,
    ]),
    CredentialModule,
    EquipmentModule,
  ],
  providers: [StudyService],
  controllers: [StudyController],
  exports: [StudyService],
})
export class StudyModule {}
