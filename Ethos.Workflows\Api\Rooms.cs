using Ethos.Model;

namespace Ethos.Workflows.Api;

public sealed record RoomDto
{
    public required Guid Id { get; set; }
    [CheckString(100)]
    public required string? Name { get; set; }
    public required Guid CareLocationId { get; set; }
    public required IReadOnlyList<long>? SupportedStudyTypes { get; set; }
}

public sealed record CreateRoomDto : IInputDto
{
    [CheckString(100)]
    public required string? Name { get; set; }
    public required Guid? CareLocationId { get; set; }
    public required IReadOnlyList<long>? SupportedStudyTypes { get; set; }
}

public interface IRoomApi : IEntityHttpClient<CreateRoomDto, RoomDto, RoomQ>;

public class RoomHttpClient(System.Net.Http.HttpClient httpClient)
    : EntityHttpClientBase<CreateRoomDto, RoomDto, RoomQ>(httpClient, "room"),
        IRoomApi;