import { Column, Entity, ManyToOne, OneToMany } from 'typeorm';
import { BaseEntity } from '@app/common/base.entity';
import { ClinicEntity } from '@app/modules/clinic/clinic.entity';
import { TechnicianScheduleEntity } from '@app/modules/technicianSchedule/technician.schedule.entity';
import { ScheduleEntity } from '@app/modules/schedule/schedule.entity';
import { EDayWeek } from '@app/common/enums';
import { ETechnicianScheduleShift } from '@app/modules/technicianSchedule/enums';
import { TechnicianCredentialEntity } from '@app/modules/technician/technician.credential.entity';
import { ITechnicianStandardSchedule } from '@app/modules/technician/types';

@Entity({ name: 'technicians' })
export class TechnicianEntity extends BaseEntity {
  @Column()
  name: string;

  @Column()
  capacity: number;

  @Column({
    type: 'jsonb',
    default: {
      [EDayWeek.Monday]: { shift: ETechnicianScheduleShift.DayOff },
      [EDayWeek.Tuesday]: { shift: ETechnicianScheduleShift.DayOff },
      [EDayWeek.Wednesday]: { shift: ETechnicianScheduleShift.DayOff },
      [EDayWeek.Thursday]: { shift: ETechnicianScheduleShift.DayOff },
      [EDayWeek.Friday]: { shift: ETechnicianScheduleShift.DayOff },
      [EDayWeek.Saturday]: { shift: ETechnicianScheduleShift.DayOff },
      [EDayWeek.Sunday]: { shift: ETechnicianScheduleShift.DayOff },
    },
  })
  standardSchedule: ITechnicianStandardSchedule;

  @Column()
  clinicId: number;

  @ManyToOne(
    () => ClinicEntity,
    clinic => clinic.technicians,
    { onDelete: 'CASCADE' }
  )
  clinic: ClinicEntity;

  @OneToMany(
    () => ScheduleEntity,
    schedule => schedule.technician,
  )
  schedules: ScheduleEntity[];

  @OneToMany(
    () => TechnicianScheduleEntity,
    schedule => schedule.technician,
  )
  technicianSchedules: TechnicianScheduleEntity[];

  @OneToMany(
    () => TechnicianCredentialEntity,
    credential => credential.technician,
  )
  credentials: TechnicianCredentialEntity[];
}
