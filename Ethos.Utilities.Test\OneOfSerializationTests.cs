using System.Text.Json;

namespace Ethos.Utilities.Test;

public class OneOfSerializationTests
{
    private static readonly JsonSerializerOptions _options = new()
    {
        // If you have global converters, add them here if needed
        // e.g. Converters = { new OneOfConverter<int,string>() } 
        // But note the classes already use [JsonConverter(...)] attributes
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false
    };

    [Fact]
    public void Serialize_OneOfT1_CreatesExpectedJson()
    {
        // Arrange
        var oneOf = new OneOf<int, string>(123);

        // Act
        string json = JsonSerializer.Serialize(oneOf, _options);

        // Assert
        // Expected structure:
        // {
        //   "index": 1,
        //   "value": 123
        // }
        Assert.Equal("{\"index\":1,\"value\":123}", json);
    }

    [Fact]
    public void Serialize_OneOfT2_CreatesExpectedJson()
    {
        // Arrange
        var oneOf = new OneOf<int, string>("Hello");

        // Act
        string json = JsonSerializer.Serialize(oneOf, _options);

        // Assert
        // Expected structure:
        // {
        //   "index": 2,
        //   "value": "Hello"
        // }
        Assert.Equal("{\"index\":2,\"value\":\"Hello\"}", json);
    }

    [Fact]
    public void Deserialize_OneOfT1_ReturnsExpectedObject()
    {
        // Arrange
        string json = "{\"index\":1,\"value\":123}";

        // Act
        var deserialized = JsonSerializer.Deserialize<OneOf<int, string>>(json, _options);

        // Assert
        Assert.NotNull(deserialized);
        Assert.Equal(123, deserialized.Item1);
        Assert.Throws<InvalidOperationException>(() => deserialized.Item2);
    }

    [Fact]
    public void Deserialize_OneOfT2_ReturnsExpectedObject()
    {
        // Arrange
        string json = "{\"index\":2,\"value\":\"World\"}";

        // Act
        var deserialized = JsonSerializer.Deserialize<OneOf<int, string>>(json, _options);

        // Assert
        Assert.NotNull(deserialized);
        Assert.Equal("World", deserialized.Item2);
        Assert.Throws<InvalidOperationException>(() => deserialized.Item1);
    }

    [Fact]
    public void Serialize_OneOfT1T2T3_WithItem1_CreatesExpectedJson()
    {
        // Arrange
        var oneOf = new OneOf<int, string, bool>(777);

        // Act
        string json = JsonSerializer.Serialize(oneOf, _options);

        // Assert
        // {
        //   "index": 1,
        //   "value": 777
        // }
        Assert.Equal("{\"index\":1,\"value\":777}", json);
    }

    [Fact]
    public void Serialize_OneOfT1T2T3_WithItem2_CreatesExpectedJson()
    {
        // Arrange
        var oneOf = new OneOf<int, string, bool>("xUnit");

        // Act
        string json = JsonSerializer.Serialize(oneOf, _options);

        // Assert
        // {
        //   "index": 2,
        //   "value": "xUnit"
        // }
        Assert.Equal("{\"index\":2,\"value\":\"xUnit\"}", json);
    }

    [Fact]
    public void Serialize_OneOfT1T2T3_WithItem3_CreatesExpectedJson()
    {
        // Arrange
        var oneOf = new OneOf<int, string, bool>(true);

        // Act
        string json = JsonSerializer.Serialize(oneOf, _options);

        // Assert
        // {
        //   "index": 3,
        //   "value": true
        // }
        Assert.Equal("{\"index\":3,\"value\":true}", json);
    }

    [Fact]
    public void Deserialize_OneOfT1T2T3_Item1_ReturnsExpectedObject()
    {
        // Arrange
        string json = "{\"index\":1,\"value\":1234}";

        // Act
        var deserialized = JsonSerializer.Deserialize<OneOf<int, string, bool>>(json, _options);

        // Assert
        Assert.NotNull(deserialized);
        Assert.Equal(1234, deserialized.Item1);
        Assert.Throws<InvalidOperationException>(() => deserialized.Item2);
        Assert.Throws<InvalidOperationException>(() => deserialized.Item3);
    }

    [Fact]
    public void Deserialize_OneOfT1T2T3_Item2_ReturnsExpectedObject()
    {
        // Arrange
        string json = "{\"index\":2,\"value\":\"Hello T2\"}";

        // Act
        var deserialized = JsonSerializer.Deserialize<OneOf<int, string, bool>>(json, _options);

        // Assert
        Assert.NotNull(deserialized);
        Assert.Equal("Hello T2", deserialized.Item2);
        Assert.Throws<InvalidOperationException>(() => deserialized.Item1);
        Assert.Throws<InvalidOperationException>(() => deserialized.Item3);
    }

    [Fact]
    public void Deserialize_OneOfT1T2T3_Item3_ReturnsExpectedObject()
    {
        // Arrange
        string json = "{\"index\":3,\"value\":true}";

        // Act
        var deserialized = JsonSerializer.Deserialize<OneOf<int, string, bool>>(json, _options);

        // Assert
        Assert.NotNull(deserialized);
        Assert.True(deserialized.Item3);
        Assert.Throws<InvalidOperationException>(() => deserialized.Item1);
        Assert.Throws<InvalidOperationException>(() => deserialized.Item2);
    }

    [Fact]
    public void Deserialize_InvalidIndex_ThrowsJsonException()
    {
        // Arrange
        string json = "{\"index\":99,\"value\":\"Invalid Index\"}";

        // Act & Assert
        Assert.ThrowsAny<JsonException>(() =>
        {
            JsonSerializer.Deserialize<OneOf<int, string>>(json, _options);
        });
    }

    [Fact]
    public void Serialize_OneOfWithNoValue_ThrowsJsonException()
    {
        // Arrange
        // Manually create an "empty" OneOf (not typical usage).
        // This object has no valid _index or _item, so the converter should fail on writing.
        var oneOf = new OneOf<int, string>();

        // Act & Assert
        Assert.ThrowsAny<JsonException>(() =>
        {
            JsonSerializer.Serialize(oneOf, _options);
        });
    }
}