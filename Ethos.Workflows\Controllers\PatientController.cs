using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Ethos.Model;
using Ethos.Workflows.Api;
using Ethos.Workflows.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Controllers;


[Authorize]
[ApiController]
[Route("api/[controller]")]
public class PatientController(DbContext dbContext) : EntityControllerBase<PatientDbo, CreatePatientInputDto, PatientDto, PatientQ>(dbContext)
{
    // For patients, we probably have some includes:
    protected override PatientDto MapToDto(PatientDbo dbo)
    {
        // Find the earliest future study date across all studies for this patient
        var nextStudyDate = dbo.Orders
            .SelectMany(o => o.Studies)
            .SelectMany(s => s.Appointments)
            .Where(a => a.Date >= DateOnly.FromDateTime(DateTime.UtcNow))
            .OrderBy(a => a.Date)
            .Select(a => (DateOnly?)a.Date)
            .FirstOrDefault();

        return new PatientDto
        {
            Id = dbo.Id,
            Names = dbo.Names.Select(n => n.ToDto()).ToList(),
            Identifiers = dbo.Identifiers.Select(i => i.ToDto()).ToList(),
            OrderIds = dbo.Orders.Select(o => o.Id).ToList(),

            // Deterministic calculation of last study location
            LastStudyLocation = dbo.Orders
                .SelectMany(o => o.Studies)
                .OrderByDescending(s => s.Appointments.MaxBy(a => a.Date)) // Order by the latest appointment date
                .Select(s => s.Location)
                .FirstOrDefault(),
            
            NextStudyDate = nextStudyDate,
            Demographics = dbo.Demographics?.ToDto(),
            PhysicalMeasurements = dbo.PhysicalMeasurements?.ToDto(),
            ClinicalConsiderations = dbo.ClinicalConsiderations.ToList(),
            SchedulingPreferences = new SchedulingPreferencesDto(
                dbo.TechnicianPreference,
                dbo.PreferredWeekdays.ToList()
            ),
            ContactInformation = dbo.ContactDetail?.ToDto(),

            // Map full Insurance DTOs
            Insurances = dbo.Insurances.Select(i => new InsuranceDto(
                i.InsuranceCarrier ?? -1,
                i.InsuranceId,
                i.PolicyId,
                i.GroupNumber,
                i.MemberId ?? "",
                i.InsuranceHolder != null ? new InsuranceHolderDto
                {
                    Relationship = -1,
                    Name = "",
                    DateOfBirth = DateOnly.MinValue
                } : null,
                null, // PhoneNumber
                null, // Email
                null  // Address
            )).ToList(),

            // Map Guardian
            Guardian = null
        };
    }

    protected override PatientDbo CreateOrUpdateEntity(PatientDbo? entity, CreatePatientInputDto input, Guid? requiredId = null)
    {
        entity ??= new PatientDbo { Id = requiredId ?? Guid.NewGuid() };
        
        // == Basic Information & Demographics ==
        if (input.PatientInformation != null)
        {
            // This replaces the entire collection, which is fine for a simple "patient name" scenario.
            entity.Names = new List<PersonNameDbo>
            {
                new()
                {
                    LastName = input.PatientInformation.LastName,
                    MiddleName = input.PatientInformation.MiddleName,
                    FirstName = input.PatientInformation.FirstName,
                    SuffixId = input.PatientInformation.Suffix,
                    PrefixId = input.PatientInformation.Prefix
                }
            };
        }
        
        if (input.Identifiers != null)
        {
            // EF Core's OwnsMany handles the complexity of diffing/updating this collection.
            entity.Identifiers = input.Identifiers.Select(i => i.ToEntity()).ToList();
        }
        
        if (input.Demographics != null)
        {
            entity.Demographics = input.Demographics.ToEntity();
        }
        
        if (input.PhysicalMeasurements != null)
        {
            entity.PhysicalMeasurements = input.PhysicalMeasurements.ToEntity();
        }
        
        // == Contact Information (Complex Owned Type) ==
        if (input.ContactInformation != null)
        {
            entity.ContactDetail = input.ContactInformation.ToEntity();
        }
        
        if (input.Insurances != null)
        {
            // EF Core will handle Add/Update/Delete based on comparing the existing collection with the new one.
            entity.Insurances = input.Insurances?.Select(i => new InsuranceDbo
            {
                InsuranceCarrier = i.InsuranceCarrier,
                InsuranceId = i.InsuranceId,
                PolicyId = i.PolicyId,
                GroupNumber = i.GroupNumber,
                MemberId = i.MemberId,
                InsuranceHolder = new InsuranceHolderDataDbo()
                {
                },
                PhoneNumber = new PhoneNumberWithUseDataDbo()
                {
                    PhoneNumber = i.PhoneNumber?.PhoneNumber ?? "",
                    PhoneNumberTypeId = 0,
                },
            }).ToList() ?? new List<InsuranceDbo>();
        }
        
        if (input.ClinicalConsiderations != null)
        {
            entity.ClinicalConsiderations = input.ClinicalConsiderations.ToList();
        }
        
        if (input.SchedulingPreferences != null)
        {
            entity.TechnicianPreference = input.SchedulingPreferences.TechnicianPreference;
            entity.PreferredWeekdays = input.SchedulingPreferences.PreferredDayOfWeek.ToList();
        }

        return entity;
    }

    protected override IQueryable<PatientDbo> ApplyIncludes(IQueryable<PatientDbo> query)
    {
        return query
            .Include(p => p.Names)
            .Include(p => p.Identifiers)
            .Include(p => p.Demographics)
            .Include(p => p.PhysicalMeasurements)
            .Include(p => p.Insurances)
            .Include(p => p.Guardian)

            .Include(p => p.ContactDetail)
            .ThenInclude(cd => cd.Addresses)
            .ThenInclude(a => a.Address)
            .Include(p => p.ContactDetail)
            .ThenInclude(cd => cd.EmergencyContacts)
            .Include(p => p.ContactDetail)
            .ThenInclude(cd => cd.PhoneNumbers)
            .Include(p => p.ContactDetail)
            .ThenInclude(cd => cd.Emails)

            .Include(p => p.Orders)
            .ThenInclude(o => o.Studies)
            .ThenInclude(s => s.Appointments);
    }
}
