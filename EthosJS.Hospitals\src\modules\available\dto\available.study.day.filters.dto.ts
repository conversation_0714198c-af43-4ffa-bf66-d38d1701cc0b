import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsPositive } from 'class-validator';
import { EShift } from '@app/common/enums';
import { IsDateOnly } from '@app/common/decorators/validators.decorator';

export class AvailableStudyDayFiltersDto {
  @ApiProperty()
  @IsInt()
  @IsPositive()
  facilityId: number;

  @ApiProperty({ format: 'date-time', example: '2020-01-01' })
  @IsDateOnly()
  dateFrom: string;

  @ApiProperty({ format: 'date-time', example: '2024-01-01' })
  @IsDateOnly()
  dateTo: string;

  @ApiProperty({ enum: EShift })
  @IsEnum(EShift)
  shift: EShift;
}
