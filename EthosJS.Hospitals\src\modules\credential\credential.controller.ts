import { AuthGuard } from '@app/common/guards/auth.guard';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { CredentialService } from '@app/modules/credential/credential.service';
import { CredentialCollectionDto } from '@app/modules/credential/dto/credential.collection.dto';
import { CredentialFiltersDto } from '@app/modules/credential/dto/credential.filters.dto';
import { CredentialEntity } from '@app/modules/credential/credential.entity';
import { CreateCredentialDto } from '@app/modules/credential/dto/create.credential.dto';
import { UpdateCredentialDto } from '@app/modules/credential/dto/update.credential.dto';
import { DeleteCredentialDto } from '@app/modules/credential/dto/delete.credential.dto';

@Controller('credential')
@ApiTags('Credentials')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class CredentialController {
  constructor(private readonly service: CredentialService) {
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: CredentialCollectionDto,
    description: 'Get list of credentials',
  })
  async list(@Query() filters: CredentialFiltersDto): Promise<CredentialCollectionDto> {
    return this.service.list(filters);
  }

  @Get('/:credentialId')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: CredentialEntity,
    description: 'Get credential by id',
  })
  async getById(@Param('credentialId', new ParseIntPipe()) credentialId: number): Promise<CredentialEntity> {
    return this.service.getByIdOrFail(credentialId);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOkResponse({
    type: CredentialEntity,
    description: 'Create credential',
  })
  async create(@Body() credential: CreateCredentialDto): Promise<CredentialEntity> {
    return this.service.create(credential);
  }

  @Put()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: CredentialEntity,
    description: 'Update credential',
  })
  async update(@Body() update: UpdateCredentialDto): Promise<CredentialEntity> {
    return this.service.update(update);
  }

  @Delete()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: CredentialEntity,
    description: 'Delete credential',
  })
  async delete(@Body() { id }: DeleteCredentialDto): Promise<CredentialEntity> {
    return this.service.delete(id);
  }
}
