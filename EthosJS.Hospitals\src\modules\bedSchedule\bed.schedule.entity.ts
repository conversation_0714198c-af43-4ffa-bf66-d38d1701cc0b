import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { BaseEntity } from '@app/common/base.entity';
import { FacilityEntity } from '@app/modules/facility/facility.entity';
import { IBedScheduleEquipment } from '@app/modules/bedSchedule/types';

@Entity({ name: 'bed_schedules' })
export class BedScheduleEntity extends BaseEntity {
  @Column()
  facilityId: number;

  @ManyToOne(
    () => FacilityEntity,
    facility => facility.bedSchedules,
  )
  @JoinColumn()
  facility: FacilityEntity;

  @Column()
  dayShiftBeds: number;

  @Column()
  nightShiftBeds: number;

  @Column({ type: 'date' })
  date: string;

  @Column({ type: 'jsonb', default: '{}' })
  equipments: Record<number, IBedScheduleEquipment>
}
