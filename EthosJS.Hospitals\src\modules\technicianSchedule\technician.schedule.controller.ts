import { AuthGuard } from '@app/common/guards/auth.guard';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { TechnicianScheduleService } from '@app/modules/technicianSchedule/technician.schedule.service';
import { TechnicianScheduleCollectionDto } from '@app/modules/technicianSchedule/dto/technician.schedule.collection.dto';
import { TechnicianScheduleFiltersDto } from '@app/modules/technicianSchedule/dto/technician.schedule.filters.dto';
import { CreateTechnicianScheduleDto } from '@app/modules/technicianSchedule/dto/create.technician.schedule.dto';
import { DeleteTechnicianScheduleDto } from '@app/modules/technicianSchedule/dto/delete.technicianSchedule.dto';
import { TechnicianScheduleDto } from '@app/modules/technicianSchedule/dto/technician.schedule.dto';
import { UpsertTechnicianScheduleDto } from '@app/modules/technicianSchedule/dto/upsert.technician.schedule.dto';
import {
  UpsertTechnicianScheduleResultDto,
} from '@app/modules/technicianSchedule/dto/upsert.technician.schedule.result.dto';

@Controller('technician-schedule')
@ApiTags('Technician Schedules')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class TechnicianScheduleController {
  constructor(private readonly service: TechnicianScheduleService) {
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: TechnicianScheduleCollectionDto,
    description: 'Get list of technician schedules',
  })
  async list(@Query() filters: TechnicianScheduleFiltersDto): Promise<TechnicianScheduleCollectionDto> {
    const { data, count } = await this.service.list(filters);

    return {
      data: data.map(TechnicianScheduleService.mapToDto),
      count,
    };
  }

  @Get('/:technicianScheduleId')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: TechnicianScheduleDto,
    description: 'Get technician schedule by id',
  })
  async getById(@Param('technicianScheduleId', new ParseIntPipe()) technicianScheduleId: number): Promise<TechnicianScheduleDto> {
    const item = await this.service.getByIdOrFail(technicianScheduleId);

    return TechnicianScheduleService.mapToDto(item);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOkResponse({
    type: TechnicianScheduleDto,
    description: 'Create technician schedule',
  })
  async create(@Body() schedule: CreateTechnicianScheduleDto): Promise<TechnicianScheduleDto> {
    const item = await this.service.create(schedule);

    return TechnicianScheduleService.mapToDto(item);
  }

  @Post('/upsert')
  @HttpCode(HttpStatus.CREATED)
  @ApiOkResponse({
    type: UpsertTechnicianScheduleResultDto,
    description: 'Upsert list of technician schedules',
  })
  async upsert(@Body() dto: UpsertTechnicianScheduleDto): Promise<UpsertTechnicianScheduleResultDto> {
    return this.service.upsert(dto);
  }

  @Delete()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: TechnicianScheduleDto,
    description: 'Delete technician schedule',
  })
  async delete(@Body() { id }: DeleteTechnicianScheduleDto): Promise<TechnicianScheduleDto> {
    const item = await this.service.delete(id);

    return TechnicianScheduleService.mapToDto(item);
  }
}
