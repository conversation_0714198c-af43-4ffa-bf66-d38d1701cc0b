﻿using System.Net.Http.Headers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Primitives;

namespace Ethos.TenantConfig
{
    /// <summary>
    /// 
    /// </summary>
    public static class ControllerExtensions
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="controller"></param>
        /// <param name="headerName"></param>
        /// <param name="values"></param>
        /// <returns></returns>
        public static ControllerBase WithHeader(this ControllerBase controller, string headerName, IEnumerable<string?> values, 
            IEqualityComparer<string?>? stringComparer = null)
        {
            stringComparer ??= StringComparer.CurrentCulture;
            values ??= [];
            values = values.Where(v => !string.IsNullOrEmpty(v));

            if (controller.HttpContext.Response.Headers.TryGetValue(headerName, out var headerVal))
            {
                var appended = values.Union(headerVal, stringComparer);
                controller.HttpContext.Response.Headers[headerName] = new StringValues([.. appended]);
            }
            else
                controller.HttpContext.Response.Headers.Append(headerName, new StringValues([.. values]));
            return controller;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="controller"></param>
        /// <param name="links"></param>
        /// <returns></returns>
        public static ControllerBase WithEtag(this ControllerBase controller, string etag, bool isWeak = true)
        {
            if (string.IsNullOrEmpty(etag))
                return controller;

            if (!isWeak)
            {
                if (!etag.StartsWith('"'))
                    etag = $"\"{etag}";

                if (!etag.EndsWith('"'))
                    etag = $"{etag}\"";
            }

            var etagObj = new EntityTagHeaderValue(etag, isWeak);
            return WithHeader(controller, "ETag", [etagObj.ToString()]);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="controller"></param>
        /// <param name="links"></param>
        /// <returns></returns>
        public static ControllerBase WithLinkHeader(this ControllerBase controller, IDictionary<string, string?> links)
        {
            var linkValues = links?.Where(kvp => !string.IsNullOrEmpty(kvp.Value))
                                   .Select(kvp => $"<{kvp.Value}>; rel=\"{kvp.Key}\"");

            if (linkValues is null || !linkValues.Any())
                return controller;

            return WithHeader(controller, "Link", linkValues);
        }
    }
}
