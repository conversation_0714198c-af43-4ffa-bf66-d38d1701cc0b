import { AuthGuard } from '@app/common/guards/auth.guard';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { BedScheduleService } from '@app/modules/bedSchedule/bed.schedule.service';
import { BedScheduleCollectionDto } from '@app/modules/bedSchedule/dto/bed.schedule.collection.dto';
import { BedScheduleFiltersDto } from '@app/modules/bedSchedule/dto/bed.schedule.filters.dto';
import { CreateBedScheduleDto } from '@app/modules/bedSchedule/dto/create.bed.schedule.dto';
import { DeleteBedScheduleDto } from '@app/modules/bedSchedule/dto/delete.bed.schedule.dto';
import { UpsertBedScheduleDto } from '@app/modules/bedSchedule/dto/upsert.bed.schedule.dto';
import { UpsertBedScheduleResultDto } from '@app/modules/bedSchedule/dto/upsert.bed.schedule.result.dto';
import { BedScheduleDto } from '@app/modules/bedSchedule/dto/bed.schedule.dto';

@Controller('bed-schedule')
@ApiTags('Bed Schedules')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class BedScheduleController {
  constructor(private readonly service: BedScheduleService) {
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: BedScheduleCollectionDto,
    description: 'Get list of bed schedules',
  })
  async list(@Query() filters: BedScheduleFiltersDto): Promise<BedScheduleCollectionDto> {
    return this.service.list(filters);
  }

  @Get('/:bedScheduleId')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: BedScheduleDto,
    description: 'Get bed schedule by id',
  })
  async getById(@Param('bedScheduleId', new ParseIntPipe()) bedScheduleId: number): Promise<BedScheduleDto> {
    const item = await this.service.getByIdOrFail(bedScheduleId);

    return BedScheduleService.mapToDto(item);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOkResponse({
    type: BedScheduleDto,
    description: 'Create bed schedule',
  })
  async create(@Body() schedule: CreateBedScheduleDto): Promise<BedScheduleDto> {
    const item = await this.service.create(schedule);

    return BedScheduleService.mapToDto(item);
  }

  @Post('/upsert')
  @HttpCode(HttpStatus.CREATED)
  @ApiOkResponse({
    type: UpsertBedScheduleResultDto,
    description: 'Upsert bed schedules',
  })
  async upsert(@Body() dto: UpsertBedScheduleDto): Promise<UpsertBedScheduleResultDto> {
    return this.service.upsert(dto);
  }

  @Delete()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: BedScheduleDto,
    description: 'Delete bed schedule',
  })
  async delete(@Body() { id }: DeleteBedScheduleDto): Promise<BedScheduleDto> {
    const item = await this.service.delete(id);

    return BedScheduleService.mapToDto(item);
  }
}
