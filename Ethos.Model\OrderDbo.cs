using System.Linq.Expressions;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class OrderDbo : IAuditableEntity<OrderDbo>
{
    public Guid PatientId { get; set; }
    public virtual PatientDbo Patient { get; set; } = null!;
    
    public Guid? PrimaryCarePhysicianId { get; set; }
    public virtual PhysicianDbo? PrimaryCarePhysician { get; set; }
    
    public Guid? ReferringPhysicianId { get; set; }
    public virtual PhysicianDbo? ReferringPhysician { get; set; }
    
    public Guid? InterpretingPhysicianId { get; set; }
    public virtual PhysicianDbo? InterpretingPhysician { get; set; }
    
    public Guid CareLocationId { get; set; }
    public virtual CareLocationDbo CareLocation { get; set; } = null!;
    
    public virtual ICollection<StudyDbo> Studies { get; set; } = new List<StudyDbo>();
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<OrderDbo>(Register);

    public new static void Register(EntityTypeBuilder<OrderDbo> entity)
    {
        IAuditableEntity<OrderDbo>.Register(entity);
        
        entity.HasOne(o => o.Patient)
            .WithMany(p => p.Orders)
            .HasForeignKey(o => o.PatientId)
            .HasPrincipalKey(p => p.Id)
            .OnDelete(DeleteBehavior.Cascade);
        entity.HasOne(o => o.PrimaryCarePhysician)
            .WithMany()
            .HasForeignKey(o => o.PrimaryCarePhysicianId)
            .HasPrincipalKey(p => p.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Cascade);
        entity.HasOne(o => o.ReferringPhysician)
            .WithMany()
            .HasForeignKey(o => o.ReferringPhysicianId)
            .HasPrincipalKey(p => p.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Cascade);
        entity.HasOne(o => o.InterpretingPhysician)
            .WithMany()
            .HasForeignKey(o => o.InterpretingPhysicianId)
            .HasPrincipalKey(p => p.Id)
            .OnDelete(DeleteBehavior.Cascade);
        entity.HasOne(o => o.CareLocation)
            .WithMany()
            .HasForeignKey(o => o.CareLocationId)
            .HasPrincipalKey(p => p.Id)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(OrderQ.WithId), "WithId")]
[JsonDerivedType(typeof(OrderQ.WithPatientId), "WithPatientId")]
public abstract record OrderQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : OrderQ;
    public sealed record WithPatientId(Guid PatientId) : OrderQ;
    
    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithId id => Expression.Equal(Expression.Property(self, nameof(OrderDbo.Id)), Expression.Constant(id.Id)),
            WithPatientId patientId => Expression.Equal(Expression.Property(self, nameof(OrderDbo.PatientId)), Expression.Constant(patientId.PatientId)),
            _ => throw new NotImplementedException()
        };
    }
}