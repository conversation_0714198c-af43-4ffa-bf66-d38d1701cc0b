import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BaseCollectionDto } from '@app/common/dto/base.collection.dto';
import { AvailableStudyDto } from '@app/modules/available/dto/available.study.dto';

export class AvailableStudyCollectionDto extends BaseCollectionDto {
  @ApiProperty({ type: AvailableStudyDto, isArray: true })
  @Expose()
  data: AvailableStudyDto[]
}
