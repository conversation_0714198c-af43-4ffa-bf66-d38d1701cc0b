using Ethos.Model;

namespace Ethos.Workflows.Api;


public sealed record CreateInsuranceDto : IInputDto
{
    public required long InsuranceCarrier { get; set; } // Changed from string to long?
    public required string? InsuranceId { get; set; }
    public required string PolicyId { get; set; }
    public required string GroupNumber { get; set; }
    public required string? MemberId { get; set; }
}

public sealed record InsuranceOutputDto
{
    public required Guid Id { get; set; }
    public required long? InsuranceCarrier { get; set; } // Changed from string to long?
    public required string? InsuranceId { get; set; }
    public required string PolicyId { get; set; }
    public required string GroupNumber { get; set; }
    public required string? MemberId { get; set; }
}

public interface IInsuranceApi : IEntityHttpClient<CreateInsuranceDto, InsuranceOutputDto, InsuranceQ>;

public class InsuranceHttpClient(System.Net.Http.HttpClient httpClient)
    : EntityHttpClientBase<CreateInsuranceDto, InsuranceOutputDto, InsuranceQ>(httpClient, "insurance"),
        IInsuranceApi;
