{"openapi": "3.0.0", "info": {"title": "Hospital beds API", "description": "Hospital Beds API Documentation", "version": "0.0.1", "contact": {}}, "tags": [], "servers": [], "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"LoginRequest": {"type": "object", "properties": {"email": {"type": "string"}, "password": {"type": "string"}}, "required": ["email", "password"]}, "LoginResponse": {"type": "object", "properties": {"id": {"type": "string"}, "uid": {"type": "number"}, "access_token": {"type": "string"}, "token_type": {"type": "string"}, "rights": {"type": "array", "items": {"type": "string"}}, "name": {"type": "string"}, "expires_in": {"type": "object"}, "refresh_token": {"type": "object"}}, "required": ["id", "uid", "access_token", "token_type", "rights", "name"]}, "RefreshRequest": {"type": "object", "properties": {"refreshToken": {"type": "string"}}, "required": ["refreshToken"]}, "CreateScheduleEquipmentDto": {"type": "object", "properties": {"equipmentId": {"type": "number"}, "count": {"type": "number"}}, "required": ["equipmentId", "count"]}, "AvailableScheduleFiltersDto": {"type": "object", "properties": {"facilityId": {"type": "number"}, "studyId": {"type": "number"}, "dateFrom": {"type": "string", "format": "date-time", "example": "2020-01-01"}, "dateTo": {"type": "string", "format": "date-time", "example": "2024-01-01"}, "limit": {"type": "number"}, "shift": {"type": "string", "enum": ["day", "night"]}, "equipments": {"type": "array", "items": {"$ref": "#/components/schemas/CreateScheduleEquipmentDto"}}}, "required": ["facilityId", "studyId", "dateFrom", "dateTo", "shift"]}, "AvailableScheduleDto": {"type": "object", "properties": {"facilityId": {"type": "number"}, "studyId": {"type": "number"}, "date": {"type": "string", "format": "date-time", "example": "2020-01-01"}, "shift": {"type": "string", "enum": ["day", "night"]}}, "required": ["facilityId", "studyId", "date", "shift"]}, "AvailableScheduleCollectionDto": {"type": "object", "properties": {"count": {"type": "number"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AvailableScheduleDto"}}}, "required": ["count", "data"]}, "AvailableStudyFiltersDto": {"type": "object", "properties": {"facilityId": {"type": "number"}, "date": {"type": "string", "format": "date-time", "example": "2020-01-01"}, "shift": {"type": "string", "enum": ["day", "night"]}}, "required": ["facilityId", "date", "shift"]}, "AvailableStudyDto": {"type": "object", "properties": {"studyId": {"type": "number"}, "name": {"type": "string"}, "date": {"type": "string", "format": "date-time", "example": "2020-01-01"}, "shift": {"type": "string", "enum": ["day", "night"]}, "isAvailable": {"type": "boolean"}}, "required": ["studyId", "name", "date", "shift", "isAvailable"]}, "AvailableStudyCollectionDto": {"type": "object", "properties": {"count": {"type": "number"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AvailableStudyDto"}}}, "required": ["count", "data"]}, "AvailableStudyDayFiltersDto": {"type": "object", "properties": {"facilityId": {"type": "number"}, "dateFrom": {"type": "string", "format": "date-time", "example": "2020-01-01"}, "dateTo": {"type": "string", "format": "date-time", "example": "2024-01-01"}, "shift": {"type": "string", "enum": ["day", "night"]}}, "required": ["facilityId", "dateFrom", "dateTo", "shift"]}, "AvailableStudyDayDto": {"type": "object", "properties": {"date": {"type": "string", "format": "date-time", "example": "2020-01-01"}, "isAvailable": {"type": "boolean"}}, "required": ["date", "isAvailable"]}, "AvailableStudyDayCollectionDto": {"type": "object", "properties": {"count": {"type": "number"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AvailableStudyDayDto"}}}, "required": ["count", "data"]}, "FacilityEquipmentDto": {"type": "object", "properties": {"equipmentId": {"type": "number"}, "facilityId": {"type": "number"}, "equipmentName": {"type": "string"}, "count": {"type": "number"}}, "required": ["equipmentId", "facilityId", "equipmentName", "count"]}, "FacilityDto": {"type": "object", "properties": {"id": {"type": "number"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}, "name": {"type": "string"}, "addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "zip": {"type": "string"}, "phone": {"type": "string"}, "fax": {"type": "string"}, "capacity": {"type": "number"}, "cityId": {"type": "number"}, "cityName": {"type": "string"}, "clinicId": {"type": "number"}, "clinicName": {"type": "string"}, "equipments": {"type": "array", "items": {"$ref": "#/components/schemas/FacilityEquipmentDto"}}}, "required": ["id", "createdAt", "updatedAt", "name", "capacity", "cityId", "cityName", "clinicId", "clinicName", "equipments"]}, "FacilityCollectionDto": {"type": "object", "properties": {"count": {"type": "number"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/FacilityDto"}}}, "required": ["count", "data"]}, "CreateFacilityEquipmentDto": {"type": "object", "properties": {"equipmentId": {"type": "number"}, "count": {"type": "number"}}, "required": ["equipmentId", "count"]}, "CreateFacilityDto": {"type": "object", "properties": {"name": {"type": "string"}, "clinicId": {"type": "number"}, "cityId": {"type": "number"}, "capacity": {"type": "number"}, "addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "zip": {"type": "string"}, "phone": {"type": "string"}, "fax": {"type": "string"}, "equipments": {"type": "array", "items": {"$ref": "#/components/schemas/CreateFacilityEquipmentDto"}}}, "required": ["name", "clinicId", "cityId", "capacity"]}, "UpdateFacilityDto": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "capacity": {"type": "number"}, "cityId": {"type": "number"}, "addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "zip": {"type": "string"}, "phone": {"type": "string"}, "fax": {"type": "string"}, "equipments": {"type": "array", "items": {"$ref": "#/components/schemas/CreateFacilityEquipmentDto"}}}, "required": ["id"]}, "GetConflictsFacilityDto": {"type": "object", "properties": {"id": {"type": "number"}, "capacity": {"type": "number"}, "equipments": {"type": "array", "items": {"$ref": "#/components/schemas/CreateFacilityEquipmentDto"}}}, "required": ["id"]}, "DeleteFacilityDto": {"type": "object", "properties": {"id": {"type": "number"}}, "required": ["id"]}, "ClinicEntity": {"type": "object", "properties": {"id": {"type": "number"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}, "name": {"type": "string"}}, "required": ["id", "createdAt", "updatedAt", "name"]}, "ClinicCollectionDto": {"type": "object", "properties": {"count": {"type": "number"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ClinicEntity"}}}, "required": ["count", "data"]}, "CreateClinicDto": {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"]}, "UpdateClinicDto": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}}, "required": ["id", "name"]}, "DeleteClinicDto": {"type": "object", "properties": {"id": {"type": "number"}}, "required": ["id"]}, "CityEntity": {"type": "object", "properties": {"id": {"type": "number"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}, "name": {"type": "string"}, "stateId": {"type": "number"}}, "required": ["id", "createdAt", "updatedAt", "name", "stateId"]}, "CityCollectionDto": {"type": "object", "properties": {"count": {"type": "number"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CityEntity"}}}, "required": ["count", "data"]}, "CreateCityDto": {"type": "object", "properties": {"name": {"type": "string"}, "stateId": {"type": "number"}}, "required": ["name", "stateId"]}, "StateEntity": {"type": "object", "properties": {"id": {"type": "number"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}}, "required": ["id", "createdAt", "updatedAt", "name", "code"]}, "StateCollectionDto": {"type": "object", "properties": {"count": {"type": "number"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/StateEntity"}}}, "required": ["count", "data"]}, "EquipmentEntity": {"type": "object", "properties": {"id": {"type": "number"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}, "name": {"type": "string"}}, "required": ["id", "createdAt", "updatedAt", "name"]}, "EquipmentCollectionDto": {"type": "object", "properties": {"count": {"type": "number"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/EquipmentEntity"}}}, "required": ["count", "data"]}, "CreateEquipmentDto": {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"]}, "UpdateEquipmentDto": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}}, "required": ["id", "name"]}, "DeleteEquipmentDto": {"type": "object", "properties": {"id": {"type": "number"}}, "required": ["id"]}, "BedScheduleEquipmentDto": {"type": "object", "properties": {"equipmentId": {"type": "number"}, "bedScheduleId": {"type": "number"}, "equipmentName": {"type": "string"}, "count": {"type": "number"}}, "required": ["equipmentId", "bedScheduleId", "equipmentName", "count"]}, "BedScheduleDto": {"type": "object", "properties": {"id": {"type": "number", "format": "int"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}, "facilityId": {"type": "number"}, "facilityName": {"type": "string"}, "dayShiftBeds": {"type": "number"}, "nightShiftBeds": {"type": "number"}, "date": {"type": "string"}, "equipments": {"type": "array", "items": {"$ref": "#/components/schemas/BedScheduleEquipmentDto"}}}, "required": ["id", "createdAt", "updatedAt", "facilityId", "facilityName", "dayShiftBeds", "nightShiftBeds", "date", "equipments"]}, "BedScheduleCollectionDto": {"type": "object", "properties": {"count": {"type": "number"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/BedScheduleDto"}}}, "required": ["count", "data"]}, "CreateBedScheduleEquipmentDto": {"type": "object", "properties": {"equipmentId": {"type": "number"}, "count": {"type": "number"}}, "required": ["equipmentId", "count"]}, "CreateBedScheduleDto": {"type": "object", "properties": {"facilityId": {"type": "number"}, "dayShiftBeds": {"type": "number"}, "nightShiftBeds": {"type": "number"}, "equipments": {"type": "array", "items": {"$ref": "#/components/schemas/CreateBedScheduleEquipmentDto"}}, "date": {"type": "string", "format": "date-time", "example": "2020-01-01"}}, "required": ["facilityId", "dayShiftBeds", "nightShiftBeds", "date"]}, "UpsertBedScheduleItemDto": {"type": "object", "properties": {"id": {"type": "number"}, "facilityId": {"type": "number"}, "dayShiftBeds": {"type": "number"}, "nightShiftBeds": {"type": "number"}, "equipments": {"type": "array", "items": {"$ref": "#/components/schemas/CreateBedScheduleEquipmentDto"}}, "date": {"type": "string", "format": "date-time", "example": "2020-01-01"}}, "required": ["facilityId", "dayShiftBeds", "nightShiftBeds", "date"]}, "UpsertBedScheduleDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/UpsertBedScheduleItemDto"}}}, "required": ["items"]}, "UpsertBedScheduleResultItemDto": {"type": "object", "properties": {"item": {"$ref": "#/components/schemas/BedScheduleDto"}, "sourceItem": {"$ref": "#/components/schemas/UpsertBedScheduleItemDto"}, "error": {"type": "string"}}}, "UpsertBedScheduleResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/UpsertBedScheduleResultItemDto"}}}, "required": ["items"]}, "DeleteBedScheduleDto": {"type": "object", "properties": {"id": {"type": "number"}}, "required": ["id"]}, "StudyCollectionItemDto": {"type": "object", "properties": {"id": {"type": "number", "format": "int"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}, "name": {"type": "string"}}, "required": ["id", "createdAt", "updatedAt", "name"]}, "StudyCollectionDto": {"type": "object", "properties": {"count": {"type": "number"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/StudyCollectionItemDto"}}}, "required": ["count", "data"]}, "StudyCredentialItemDto": {"type": "object", "properties": {"credentialId": {"type": "number"}, "credentialName": {"type": "string"}, "credentialCode": {"type": "string"}}, "required": ["credentialId", "credentialName", "credentialCode"]}, "StudyCredentialDto": {"type": "object", "properties": {"credentials": {"type": "array", "items": {"$ref": "#/components/schemas/StudyCredentialItemDto"}}, "studyId": {"type": "number"}, "studyName": {"type": "string"}, "stateId": {"type": "number"}, "stateName": {"type": "string"}}, "required": ["credentials", "studyId", "studyName"]}, "StudyEquipmentDto": {"type": "object", "properties": {"studyId": {"type": "number"}, "equipmentId": {"type": "number"}, "equipmentName": {"type": "string"}, "count": {"type": "number"}}, "required": ["studyId", "equipmentId", "equipmentName", "count"]}, "StudyDto": {"type": "object", "properties": {"id": {"type": "number", "format": "int"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}, "name": {"type": "string"}, "credentials": {"type": "array", "items": {"$ref": "#/components/schemas/StudyCredentialDto"}}, "equipments": {"type": "array", "items": {"$ref": "#/components/schemas/StudyEquipmentDto"}}}, "required": ["id", "createdAt", "updatedAt", "name", "credentials", "equipments"]}, "CreateStudyCredentialDto": {"type": "object", "properties": {"credentials": {"type": "array", "items": {"type": "integer"}}, "stateId": {"type": "number"}}, "required": ["credentials"]}, "CreateStudyEquipmentDto": {"type": "object", "properties": {"equipmentId": {"type": "number", "description": "id equipment "}, "count": {"type": "number"}}, "required": ["equipmentId", "count"]}, "CreateStudyDto": {"type": "object", "properties": {"name": {"type": "string"}, "credentials": {"type": "array", "items": {"$ref": "#/components/schemas/CreateStudyCredentialDto"}}, "equipments": {"type": "array", "items": {"$ref": "#/components/schemas/CreateStudyEquipmentDto"}}}, "required": ["name"]}, "UpdateStudyDto": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "credentials": {"type": "array", "items": {"$ref": "#/components/schemas/CreateStudyCredentialDto"}}, "equipments": {"type": "array", "items": {"$ref": "#/components/schemas/CreateStudyEquipmentDto"}}}, "required": ["id"]}, "DeleteStudyDto": {"type": "object", "properties": {"id": {"type": "number"}}, "required": ["id"]}, "CredentialEntity": {"type": "object", "properties": {"id": {"type": "number"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}, "issuedBy": {"type": "string"}}, "required": ["id", "createdAt", "updatedAt", "name", "code"]}, "CredentialCollectionDto": {"type": "object", "properties": {"count": {"type": "number"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CredentialEntity"}}}, "required": ["count", "data"]}, "CreateCredentialDto": {"type": "object", "properties": {"name": {"type": "string"}, "code": {"type": "string"}, "issuedBy": {"type": "string"}}, "required": ["name", "code"]}, "UpdateCredentialDto": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "code": {"type": "string"}, "issuedBy": {"type": "string"}}, "required": ["id"]}, "DeleteCredentialDto": {"type": "object", "properties": {"id": {"type": "number"}}, "required": ["id"]}, "ScheduleEquipmentDto": {"type": "object", "properties": {"scheduleId": {"type": "number"}, "equipmentId": {"type": "number"}, "equipmentName": {"type": "string"}, "count": {"type": "number"}, "studyCount": {"type": "number"}}, "required": ["scheduleId", "equipmentId", "equipmentName", "count", "studyCount"]}, "ScheduleDto": {"type": "object", "properties": {"id": {"type": "number", "format": "int"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}, "technicianId": {"type": "number"}, "technicianName": {"type": "string"}, "patientId": {"type": "number"}, "patientName": {"type": "string"}, "studyId": {"type": "number"}, "studyName": {"type": "string"}, "facilityId": {"type": "number"}, "facilityName": {"type": "string"}, "shift": {"type": "string", "enum": ["day", "night"]}, "date": {"type": "string"}, "equipments": {"type": "array", "items": {"$ref": "#/components/schemas/ScheduleEquipmentDto"}}}, "required": ["id", "createdAt", "updatedAt", "technicianId", "<PERSON><PERSON><PERSON>", "patientId", "patientName", "studyId", "studyName", "facilityId", "facilityName", "shift", "date", "equipments"]}, "ScheduleCollectionDto": {"type": "object", "properties": {"count": {"type": "number"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ScheduleDto"}}}, "required": ["count", "data"]}, "SchedulePatientDto": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}}, "required": ["name"]}, "CreateScheduleDto": {"type": "object", "properties": {"facilityId": {"type": "number"}, "patient": {"$ref": "#/components/schemas/SchedulePatientDto"}, "studyId": {"type": "number"}, "shift": {"type": "string"}, "date": {"type": "string", "format": "date-time", "example": "2020-01-01"}, "equipments": {"type": "array", "items": {"$ref": "#/components/schemas/CreateScheduleEquipmentDto"}}}, "required": ["facilityId", "patient", "studyId", "shift", "date"]}, "DeleteScheduleDto": {"type": "object", "properties": {"id": {"type": "number"}}, "required": ["id"]}, "TechnicianScheduleDto": {"type": "object", "properties": {"id": {"type": "number", "format": "int"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}, "technicianId": {"type": "number"}, "technicianName": {"type": "string"}, "facilityId": {"type": "number"}, "shift": {"type": "string", "enum": ["day", "night", "day_off"]}, "capacity": {"type": "number"}, "date": {"type": "string"}}, "required": ["id", "createdAt", "updatedAt", "technicianId", "<PERSON><PERSON><PERSON>", "shift", "date"]}, "TechnicianScheduleCollectionDto": {"type": "object", "properties": {"count": {"type": "number"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TechnicianScheduleDto"}}}, "required": ["count", "data"]}, "CreateTechnicianScheduleDto": {"type": "object", "properties": {"technicianId": {"type": "number"}, "facilityId": {"type": "number"}, "shift": {"type": "string", "enum": ["day", "night", "day_off"]}, "capacity": {"type": "number"}, "date": {"type": "string", "format": "date-time", "example": "2020-01-01"}}, "required": ["technicianId", "shift", "date"]}, "UpsertTechnicianScheduleItemDto": {"type": "object", "properties": {"id": {"type": "number"}, "technicianId": {"type": "number"}, "facilityId": {"type": "number"}, "shift": {"type": "string", "enum": ["day", "night", "day_off"]}, "capacity": {"type": "number"}, "date": {"type": "string", "format": "date-time", "example": "2020-01-01"}}, "required": ["technicianId", "shift", "date"]}, "UpsertTechnicianScheduleDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/UpsertTechnicianScheduleItemDto"}}}, "required": ["items"]}, "UpsertTechnicianScheduleResultItemDto": {"type": "object", "properties": {"item": {"$ref": "#/components/schemas/TechnicianScheduleDto"}, "sourceItem": {"$ref": "#/components/schemas/UpsertTechnicianScheduleItemDto"}, "error": {"type": "string"}}}, "UpsertTechnicianScheduleResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/UpsertTechnicianScheduleResultItemDto"}}}, "required": ["items"]}, "DeleteTechnicianScheduleDto": {"type": "object", "properties": {"id": {"type": "number"}}, "required": ["id"]}, "TechnicianStandardScheduleItemDto": {"type": "object", "properties": {"shift": {"type": "string", "enum": ["day", "night", "day_off"]}, "capacity": {"type": "number"}, "facilityId": {"type": "number"}}, "required": ["shift"]}, "TechnicianStandardScheduleDto": {"type": "object", "properties": {"mon": {"$ref": "#/components/schemas/TechnicianStandardScheduleItemDto"}, "tue": {"$ref": "#/components/schemas/TechnicianStandardScheduleItemDto"}, "wed": {"$ref": "#/components/schemas/TechnicianStandardScheduleItemDto"}, "thu": {"$ref": "#/components/schemas/TechnicianStandardScheduleItemDto"}, "fri": {"$ref": "#/components/schemas/TechnicianStandardScheduleItemDto"}, "sat": {"$ref": "#/components/schemas/TechnicianStandardScheduleItemDto"}, "sun": {"$ref": "#/components/schemas/TechnicianStandardScheduleItemDto"}}}, "TechnicianCredentialDto": {"type": "object", "properties": {"credentialId": {"type": "number"}, "credentialName": {"type": "string"}, "technicianId": {"type": "number"}, "validUntil": {"type": "string"}}, "required": ["credentialId", "credentialName", "technicianId"]}, "TechnicianDto": {"type": "object", "properties": {"id": {"type": "number", "format": "int"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}, "name": {"type": "string"}, "capacity": {"type": "number"}, "standardSchedule": {"$ref": "#/components/schemas/TechnicianStandardScheduleDto"}, "clinicId": {"type": "number"}, "clinicName": {"type": "string"}, "credentials": {"type": "array", "items": {"$ref": "#/components/schemas/TechnicianCredentialDto"}}}, "required": ["id", "createdAt", "updatedAt", "name", "capacity", "standardSchedule", "clinicId", "clinicName", "credentials"]}, "TechnicianCollectionDto": {"type": "object", "properties": {"count": {"type": "number"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TechnicianDto"}}}, "required": ["count", "data"]}, "CreateTechnicianCredentialDto": {"type": "object", "properties": {"credentialId": {"type": "number"}, "validUntil": {"type": "string"}}, "required": ["credentialId"]}, "CreateTechnicianDto": {"type": "object", "properties": {"name": {"type": "string"}, "clinicId": {"type": "number"}, "capacity": {"type": "number"}, "credentials": {"type": "array", "items": {"$ref": "#/components/schemas/CreateTechnicianCredentialDto"}}, "standardSchedule": {"$ref": "#/components/schemas/TechnicianStandardScheduleDto"}}, "required": ["name", "clinicId", "capacity"]}, "UpdateTechnicianDto": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "capacity": {"type": "number"}, "credentials": {"type": "array", "items": {"$ref": "#/components/schemas/CreateTechnicianCredentialDto"}}, "standardSchedule": {"$ref": "#/components/schemas/TechnicianStandardScheduleDto"}}, "required": ["id"]}, "GetConflictsTechnicianDto": {"type": "object", "properties": {"id": {"type": "number"}, "capacity": {"type": "number"}, "credentials": {"type": "array", "items": {"$ref": "#/components/schemas/CreateTechnicianCredentialDto"}}, "standardSchedule": {"$ref": "#/components/schemas/TechnicianStandardScheduleDto"}}, "required": ["id"]}, "DeleteTechnicianDto": {"type": "object", "properties": {"id": {"type": "number"}}, "required": ["id"]}}}, "paths": {"/api/auth": {"post": {"operationId": "AuthController_userLogin", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "User info with access token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "403": {"description": "Username or password is wrong."}, "406": {"description": "Email is not confirmed"}}, "tags": ["<PERSON><PERSON>"]}, "delete": {"operationId": "AuthController_userLogout", "parameters": [], "responses": {"204": {"description": "Logout"}, "401": {"description": "Unauthorized"}}, "tags": ["<PERSON><PERSON>"], "security": [{"bearer": []}]}, "put": {"operationId": "AuthController_refreshTokens", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshRequest"}}}}, "responses": {"200": {"description": "Refresh user token pair by refresh token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": ""}}, "tags": ["<PERSON><PERSON>"]}}, "/api/auth/login": {"post": {"operationId": "AuthController_login", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "User info with access token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "403": {"description": "Username or password is wrong."}, "406": {"description": "Email is not confirmed"}}, "tags": ["<PERSON><PERSON>"]}}, "/api/available/schedules": {"post": {"operationId": "AvailableController_getAvailableSchedules", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AvailableScheduleFiltersDto"}}}}, "responses": {"200": {"description": "Get list of available schedule slots", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AvailableScheduleCollectionDto"}}}}}, "tags": ["Available entities"], "security": [{"bearer": []}]}}, "/api/available/studies": {"post": {"operationId": "AvailableController_getAvailableStudies", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AvailableStudyFiltersDto"}}}}, "responses": {"200": {"description": "Get available studies at specific date", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AvailableStudyCollectionDto"}}}}}, "tags": ["Available entities"], "security": [{"bearer": []}]}}, "/api/available/study-days": {"post": {"operationId": "AvailableController_getAvailableStudyDays", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AvailableStudyDayFiltersDto"}}}}, "responses": {"200": {"description": "Get available study days", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AvailableStudyDayCollectionDto"}}}}}, "tags": ["Available entities"], "security": [{"bearer": []}]}}, "/api/facility": {"get": {"operationId": "FacilityController_list", "parameters": [{"name": "offset", "required": false, "in": "query", "schema": {"default": 0, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"default": 20, "type": "number"}}, {"name": "orderDirection", "required": false, "in": "query", "schema": {"default": "DESC", "enum": ["ASC", "DESC"], "type": "string"}}, {"name": "name", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "clinicId", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "orderField", "required": false, "in": "query", "schema": {"enum": ["createdAt", "updatedAt", "name"], "type": "string"}}], "responses": {"200": {"description": "Get list of facilities", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FacilityCollectionDto"}}}}}, "tags": ["Facilities"], "security": [{"bearer": []}]}, "post": {"operationId": "FacilityController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFacilityDto"}}}}, "responses": {"200": {"description": "Create facility", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FacilityDto"}}}}}, "tags": ["Facilities"], "security": [{"bearer": []}]}, "put": {"operationId": "FacilityController_update", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFacilityDto"}}}}, "responses": {"200": {"description": "Update facility", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FacilityDto"}}}}}, "tags": ["Facilities"], "security": [{"bearer": []}]}, "delete": {"operationId": "FacilityController_delete", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteFacilityDto"}}}}, "responses": {"200": {"description": "Delete facility", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FacilityDto"}}}}}, "tags": ["Facilities"], "security": [{"bearer": []}]}}, "/api/facility/{facilityId}": {"get": {"operationId": "FacilityController_getById", "parameters": [{"name": "facilityId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "Get facility by id", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FacilityDto"}}}}}, "tags": ["Facilities"], "security": [{"bearer": []}]}}, "/api/facility/conflicts": {"post": {"operationId": "FacilityController_getConflicts", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetConflictsFacilityDto"}}}}, "responses": {"200": {"description": "Get list of conflicted schedules", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FacilityDto"}}}}}, "tags": ["Facilities"], "security": [{"bearer": []}]}}, "/api/clinic": {"get": {"operationId": "ClinicController_list", "parameters": [{"name": "offset", "required": false, "in": "query", "schema": {"default": 0, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"default": 20, "type": "number"}}, {"name": "orderDirection", "required": false, "in": "query", "schema": {"default": "DESC", "enum": ["ASC", "DESC"], "type": "string"}}, {"name": "name", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "orderField", "required": false, "in": "query", "schema": {"enum": ["createdAt", "updatedAt", "name"], "type": "string"}}], "responses": {"200": {"description": "Get list of clinics", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicCollectionDto"}}}}}, "tags": ["Clinics"], "security": [{"bearer": []}]}, "post": {"operationId": "ClinicController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateClinicDto"}}}}, "responses": {"200": {"description": "Create clinic", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicEntity"}}}}}, "tags": ["Clinics"], "security": [{"bearer": []}]}, "put": {"operationId": "ClinicController_update", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateClinicDto"}}}}, "responses": {"200": {"description": "Update clinic", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicEntity"}}}}}, "tags": ["Clinics"], "security": [{"bearer": []}]}, "delete": {"operationId": "ClinicController_delete", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteClinicDto"}}}}, "responses": {"200": {"description": "Delete clinic", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicEntity"}}}}}, "tags": ["Clinics"], "security": [{"bearer": []}]}}, "/api/clinic/{clinicId}": {"get": {"operationId": "ClinicController_getById", "parameters": [{"name": "clinicId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "Get clinic by id", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicEntity"}}}}}, "tags": ["Clinics"], "security": [{"bearer": []}]}}, "/api/city": {"get": {"operationId": "CityController_list", "parameters": [{"name": "offset", "required": false, "in": "query", "schema": {"default": 0, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"default": 20, "type": "number"}}, {"name": "orderDirection", "required": false, "in": "query", "schema": {"default": "DESC", "enum": ["ASC", "DESC"], "type": "string"}}, {"name": "name", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "stateId", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "orderField", "required": false, "in": "query", "schema": {"enum": ["createdAt", "updatedAt", "name"], "type": "string"}}], "responses": {"200": {"description": "Get list of cities", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CityCollectionDto"}}}}}, "tags": ["Cities"], "security": [{"bearer": []}]}, "post": {"operationId": "CityController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCityDto"}}}}, "responses": {"200": {"description": "Create city", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CityEntity"}}}}}, "tags": ["Cities"], "security": [{"bearer": []}]}}, "/api/city/{cityId}": {"get": {"operationId": "CityController_getById", "parameters": [{"name": "cityId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "Get city by id", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CityEntity"}}}}}, "tags": ["Cities"], "security": [{"bearer": []}]}}, "/api/state": {"get": {"operationId": "StateController_list", "parameters": [{"name": "offset", "required": false, "in": "query", "schema": {"default": 0, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"default": 20, "type": "number"}}, {"name": "orderDirection", "required": false, "in": "query", "schema": {"default": "DESC", "enum": ["ASC", "DESC"], "type": "string"}}, {"name": "name", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "orderField", "required": false, "in": "query", "schema": {"enum": ["createdAt", "updatedAt", "name"], "type": "string"}}], "responses": {"200": {"description": "Get list of states", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StateCollectionDto"}}}}}, "tags": ["States"], "security": [{"bearer": []}]}}, "/api/state/{stateId}": {"get": {"operationId": "StateController_getById", "parameters": [{"name": "stateId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "Get state by id", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StateEntity"}}}}}, "tags": ["States"], "security": [{"bearer": []}]}}, "/api/equipment": {"get": {"operationId": "EquipmentController_list", "parameters": [{"name": "offset", "required": false, "in": "query", "schema": {"default": 0, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"default": 20, "type": "number"}}, {"name": "orderDirection", "required": false, "in": "query", "schema": {"default": "DESC", "enum": ["ASC", "DESC"], "type": "string"}}, {"name": "name", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "orderField", "required": false, "in": "query", "schema": {"enum": ["createdAt", "updatedAt", "name"], "type": "string"}}], "responses": {"200": {"description": "Get list of equipments", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentCollectionDto"}}}}}, "tags": ["Equipments"], "security": [{"bearer": []}]}, "post": {"operationId": "EquipmentController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateEquipmentDto"}}}}, "responses": {"200": {"description": "Create equipment", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentEntity"}}}}}, "tags": ["Equipments"], "security": [{"bearer": []}]}, "put": {"operationId": "EquipmentController_update", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateEquipmentDto"}}}}, "responses": {"200": {"description": "Update equipment", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentEntity"}}}}}, "tags": ["Equipments"], "security": [{"bearer": []}]}, "delete": {"operationId": "EquipmentController_delete", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteEquipmentDto"}}}}, "responses": {"200": {"description": "Delete equipment", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentEntity"}}}}}, "tags": ["Equipments"], "security": [{"bearer": []}]}}, "/api/equipment/{equipmentId}": {"get": {"operationId": "EquipmentController_getById", "parameters": [{"name": "equipmentId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "Get equipment by id", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentEntity"}}}}}, "tags": ["Equipments"], "security": [{"bearer": []}]}}, "/api/bed-schedule": {"get": {"operationId": "BedScheduleController_list", "parameters": [{"name": "offset", "required": false, "in": "query", "schema": {"default": 0, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"default": 20, "type": "number"}}, {"name": "orderDirection", "required": false, "in": "query", "schema": {"default": "DESC", "enum": ["ASC", "DESC"], "type": "string"}}, {"name": "facilityIds", "required": false, "in": "query", "description": "List of integers concatenated through ,", "example": "1,2,3", "schema": {"type": "string"}}, {"name": "clinicId", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "shift", "required": false, "in": "query", "schema": {"enum": ["day", "night"], "type": "string"}}, {"name": "dateFrom", "required": false, "in": "query", "example": "2020-01-01", "schema": {"format": "date-time", "type": "string"}}, {"name": "dateTo", "required": false, "in": "query", "example": "2024-01-01", "schema": {"format": "date-time", "type": "string"}}, {"name": "orderField", "required": false, "in": "query", "schema": {"enum": ["createdAt", "updatedAt", "date"], "type": "string"}}], "responses": {"200": {"description": "Get list of bed schedules", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BedScheduleCollectionDto"}}}}}, "tags": ["Bed Schedules"], "security": [{"bearer": []}]}, "post": {"operationId": "BedScheduleController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBedScheduleDto"}}}}, "responses": {"200": {"description": "Create bed schedule", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BedScheduleDto"}}}}}, "tags": ["Bed Schedules"], "security": [{"bearer": []}]}, "delete": {"operationId": "BedScheduleController_delete", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteBedScheduleDto"}}}}, "responses": {"200": {"description": "Delete bed schedule", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BedScheduleDto"}}}}}, "tags": ["Bed Schedules"], "security": [{"bearer": []}]}}, "/api/bed-schedule/{bedScheduleId}": {"get": {"operationId": "BedScheduleController_getById", "parameters": [{"name": "bedScheduleId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "Get bed schedule by id", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BedScheduleDto"}}}}}, "tags": ["Bed Schedules"], "security": [{"bearer": []}]}}, "/api/bed-schedule/upsert": {"post": {"operationId": "BedScheduleController_upsert", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertBedScheduleDto"}}}}, "responses": {"200": {"description": "Upsert bed schedules", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertBedScheduleResultDto"}}}}}, "tags": ["Bed Schedules"], "security": [{"bearer": []}]}}, "/api/study": {"get": {"operationId": "StudyController_list", "parameters": [{"name": "offset", "required": false, "in": "query", "schema": {"default": 0, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"default": 20, "type": "number"}}, {"name": "orderDirection", "required": false, "in": "query", "schema": {"default": "DESC", "enum": ["ASC", "DESC"], "type": "string"}}, {"name": "name", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "orderField", "required": false, "in": "query", "schema": {"enum": ["createdAt", "updatedAt", "name"], "type": "string"}}], "responses": {"200": {"description": "Get list of studies", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyCollectionDto"}}}}}, "tags": ["Studies"], "security": [{"bearer": []}]}, "post": {"operationId": "StudyController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStudyDto"}}}}, "responses": {"200": {"description": "Create study", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyDto"}}}}}, "tags": ["Studies"], "security": [{"bearer": []}]}, "put": {"operationId": "StudyController_update", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStudyDto"}}}}, "responses": {"200": {"description": "Update study", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyDto"}}}}}, "tags": ["Studies"], "security": [{"bearer": []}]}, "delete": {"operationId": "StudyController_delete", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteStudyDto"}}}}, "responses": {"200": {"description": "Delete study", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyDto"}}}}}, "tags": ["Studies"], "security": [{"bearer": []}]}}, "/api/study/{studyId}": {"get": {"operationId": "StudyController_getById", "parameters": [{"name": "studyId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "Get study by id", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyDto"}}}}}, "tags": ["Studies"], "security": [{"bearer": []}]}}, "/api/credential": {"get": {"operationId": "CredentialController_list", "parameters": [{"name": "offset", "required": false, "in": "query", "schema": {"default": 0, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"default": 20, "type": "number"}}, {"name": "orderDirection", "required": false, "in": "query", "schema": {"default": "DESC", "enum": ["ASC", "DESC"], "type": "string"}}, {"name": "name", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "orderField", "required": false, "in": "query", "schema": {"enum": ["createdAt", "updatedAt", "name"], "type": "string"}}], "responses": {"200": {"description": "Get list of credentials", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CredentialCollectionDto"}}}}}, "tags": ["Credentials"], "security": [{"bearer": []}]}, "post": {"operationId": "CredentialController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCredentialDto"}}}}, "responses": {"200": {"description": "Create credential", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CredentialEntity"}}}}}, "tags": ["Credentials"], "security": [{"bearer": []}]}, "put": {"operationId": "CredentialController_update", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCredentialDto"}}}}, "responses": {"200": {"description": "Update credential", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CredentialEntity"}}}}}, "tags": ["Credentials"], "security": [{"bearer": []}]}, "delete": {"operationId": "CredentialController_delete", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteCredentialDto"}}}}, "responses": {"200": {"description": "Delete credential", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CredentialEntity"}}}}}, "tags": ["Credentials"], "security": [{"bearer": []}]}}, "/api/credential/{credentialId}": {"get": {"operationId": "CredentialController_getById", "parameters": [{"name": "credentialId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "Get credential by id", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CredentialEntity"}}}}}, "tags": ["Credentials"], "security": [{"bearer": []}]}}, "/api/schedule": {"get": {"operationId": "ScheduleController_list", "parameters": [{"name": "offset", "required": false, "in": "query", "schema": {"default": 0, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"default": 20, "type": "number"}}, {"name": "orderDirection", "required": false, "in": "query", "schema": {"default": "DESC", "enum": ["ASC", "DESC"], "type": "string"}}, {"name": "facilityIds", "required": false, "in": "query", "description": "List of integers concatenated through ,", "example": "1,2,3", "schema": {"type": "string"}}, {"name": "dates", "required": false, "in": "query", "description": "List of dates concatenated through ,", "example": "2020-01-01,2021-02-02,2022-03-03", "schema": {"type": "string"}}, {"name": "clinicId", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "technicianId", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "patientId", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "studyId", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "shift", "required": false, "in": "query", "schema": {"enum": ["day", "night"], "type": "string"}}, {"name": "dateFrom", "required": false, "in": "query", "example": "2020-01-01", "schema": {"format": "date-time", "type": "string"}}, {"name": "dateTo", "required": false, "in": "query", "example": "2024-01-01", "schema": {"format": "date-time", "type": "string"}}, {"name": "orderField", "required": false, "in": "query", "schema": {"enum": ["createdAt", "updatedAt", "date"], "type": "string"}}], "responses": {"200": {"description": "Get list of schedules", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduleCollectionDto"}}}}}, "tags": ["Schedules"], "security": [{"bearer": []}]}, "post": {"operationId": "ScheduleController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateScheduleDto"}}}}, "responses": {"200": {"description": "Create schedule", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduleDto"}}}}}, "tags": ["Schedules"], "security": [{"bearer": []}]}, "delete": {"operationId": "ScheduleController_delete", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteScheduleDto"}}}}, "responses": {"200": {"description": "Delete schedule", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduleDto"}}}}}, "tags": ["Schedules"], "security": [{"bearer": []}]}}, "/api/schedule/{scheduleId}": {"get": {"operationId": "ScheduleController_getById", "parameters": [{"name": "scheduleId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "Get schedule by id", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduleDto"}}}}}, "tags": ["Schedules"], "security": [{"bearer": []}]}}, "/api/technician-schedule": {"get": {"operationId": "TechnicianScheduleController_list", "parameters": [{"name": "offset", "required": false, "in": "query", "schema": {"default": 0, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"default": 20, "type": "number"}}, {"name": "orderDirection", "required": false, "in": "query", "schema": {"default": "DESC", "enum": ["ASC", "DESC"], "type": "string"}}, {"name": "technicianId", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "facilityIds", "required": false, "in": "query", "description": "List of integers concatenated through ,", "example": "1,2,3", "schema": {"type": "string"}}, {"name": "clinicId", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "shift", "required": false, "in": "query", "schema": {"enum": ["day", "night", "day_off"], "type": "string"}}, {"name": "dateFrom", "required": false, "in": "query", "example": "2020-01-01", "schema": {"format": "date-time", "type": "string"}}, {"name": "dateTo", "required": false, "in": "query", "example": "2024-01-01", "schema": {"format": "date-time", "type": "string"}}, {"name": "orderField", "required": false, "in": "query", "schema": {"enum": ["createdAt", "updatedAt"], "type": "string"}}], "responses": {"200": {"description": "Get list of technician schedules", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TechnicianScheduleCollectionDto"}}}}}, "tags": ["Technician Schedules"], "security": [{"bearer": []}]}, "post": {"operationId": "TechnicianScheduleController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTechnicianScheduleDto"}}}}, "responses": {"200": {"description": "Create technician schedule", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TechnicianScheduleDto"}}}}}, "tags": ["Technician Schedules"], "security": [{"bearer": []}]}, "delete": {"operationId": "TechnicianScheduleController_delete", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteTechnicianScheduleDto"}}}}, "responses": {"200": {"description": "Delete technician schedule", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TechnicianScheduleDto"}}}}}, "tags": ["Technician Schedules"], "security": [{"bearer": []}]}}, "/api/technician-schedule/{technicianScheduleId}": {"get": {"operationId": "TechnicianScheduleController_getById", "parameters": [{"name": "technicianScheduleId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "Get technician schedule by id", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TechnicianScheduleDto"}}}}}, "tags": ["Technician Schedules"], "security": [{"bearer": []}]}}, "/api/technician-schedule/upsert": {"post": {"operationId": "TechnicianScheduleController_upsert", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertTechnicianScheduleDto"}}}}, "responses": {"200": {"description": "Upsert list of technician schedules", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertTechnicianScheduleResultDto"}}}}}, "tags": ["Technician Schedules"], "security": [{"bearer": []}]}}, "/api/technician": {"get": {"operationId": "TechnicianController_list", "parameters": [{"name": "offset", "required": false, "in": "query", "schema": {"default": 0, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"default": 20, "type": "number"}}, {"name": "orderDirection", "required": false, "in": "query", "schema": {"default": "DESC", "enum": ["ASC", "DESC"], "type": "string"}}, {"name": "name", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "clinicId", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "orderField", "required": false, "in": "query", "schema": {"enum": ["createdAt", "updatedAt", "name"], "type": "string"}}], "responses": {"200": {"description": "Get list of technicians", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TechnicianCollectionDto"}}}}}, "tags": ["Technicians"], "security": [{"bearer": []}]}, "post": {"operationId": "TechnicianController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTechnicianDto"}}}}, "responses": {"200": {"description": "Create technician", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TechnicianDto"}}}}}, "tags": ["Technicians"], "security": [{"bearer": []}]}, "put": {"operationId": "TechnicianController_update", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTechnicianDto"}}}}, "responses": {"200": {"description": "Update technician", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TechnicianDto"}}}}}, "tags": ["Technicians"], "security": [{"bearer": []}]}, "delete": {"operationId": "TechnicianController_delete", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteTechnicianDto"}}}}, "responses": {"200": {"description": "Delete technician", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TechnicianDto"}}}}}, "tags": ["Technicians"], "security": [{"bearer": []}]}}, "/api/technician/{technicianId}": {"get": {"operationId": "TechnicianController_getById", "parameters": [{"name": "technicianId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "Get technician by id", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TechnicianDto"}}}}}, "tags": ["Technicians"], "security": [{"bearer": []}]}}, "/api/technician/conflicts": {"post": {"operationId": "TechnicianController_getConflicts", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetConflictsTechnicianDto"}}}}, "responses": {"200": {"description": "Get list of conflicted schedules", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TechnicianDto"}}}}}, "tags": ["Technicians"], "security": [{"bearer": []}]}}, "/api/healthz": {"get": {"operationId": "HealthController_checkHealth", "parameters": [], "responses": {"200": {"description": "Returns HTTP 200 when api service is alive"}}, "tags": ["health"]}}}}