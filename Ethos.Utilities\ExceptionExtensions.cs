namespace Ethos.Utilities;

using System;
using System.Text.Json.Nodes;
using System.Collections;

public static class ExceptionExtensions
{
    public static JsonObject ToJsonObject(this Exception ex)
    {
        var result = new JsonObject
        {
            ["Type"] = ex.GetType().FullName,
            ["Message"] = ex.Message,
            ["StackTrace"] = ex.StackTrace
        };

        if (ex.HResult != 0)
        {
            result["HResult"] = ex.HResult;
        }

        if (!string.IsNullOrEmpty(ex.Source))
        {
            result["Source"] = ex.Source;
        }

        if (ex.TargetSite != null)
        {
            result["TargetSite"] = ex.TargetSite.ToString();
        }

        // Capture the Data property (arbitrary key-value pairs).
        if (ex.Data?.Count > 0)
        {
            var dataJson = new JsonObject();
            foreach (DictionaryEntry kvp in ex.Data)
            {
                var key = kvp.Key?.ToString() ?? "null";
                dataJson[key] = kvp.Value?.ToString();
            }
            result["Data"] = dataJson;
        }

        // Special handling for AggregateException which can have multiple inner exceptions.
        if (ex is AggregateException aggEx)
        {
            var innerArray = new JsonArray();
            foreach (var inner in aggEx.InnerExceptions)
            {
                if (inner != null)
                {
                    innerArray.Add(inner.ToJsonObject());
                }
            }
            result["InnerExceptions"] = innerArray;
        }
        else if (ex.InnerException != null)
        {
            // Standard single inner exception.
            result["InnerException"] = ex.InnerException.ToJsonObject();
        }

        return result;
    }
}