import dotenv from 'dotenv';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';

const nodeEnv = process.env.NODE_ENV || 'development';
let path = `.env.${nodeEnv}`;

if (nodeEnv === 'development') {
  path = '.env';
}

dotenv.config({ path });

// Replace \\n with \n to support multiline strings in AWS
for (const envName of Object.keys(process.env)) {
  process.env[envName] = process.env[envName]?.replace(/\\n/g, '\n') || '';
}

export const config = {
  nodeEnv,
  baseUri: process.env.BASE_URI || 'https://dev.regenesis-crm.axmit.com',
  api: {
    port: process.env.PORT || 3000,
    transportPort: process.env.TRANSPORT_PORT || 4000,
  },
  logger: {
    pid: process.pid,
    globalLevel: process.env.LOG_LEVEL,
  },
  typeorm: {
    type: 'postgres',
    host: process.env.POSTGRES_HOST,
    port: process.env.POSTGRES_PORT ? +process.env.POSTGRES_PORT : 5432,
    //username: process.env.POSTGRES_USERNAME,
    username: 'postgres',
    password: process.env.POSTGRES_PASSWORD,
    database: process.env.POSTGRES_DATABASE,
    ssl: process.env.POSTGRES_SSL === 'true' ? { rejectUnauthorized: false } : false,
    namingStrategy: new SnakeNamingStrategy(),
    entities: [__dirname + '/modules/**/*.entity{.ts,.js}'],
    migrations: [__dirname + '/migrations/*{.ts,.js}'],
    cli: {
      migrationsDir: 'src/migrations',
    },
    migrationsRun: false,
    logging: process.env.POSTGRES_LOGGING === 'true',
  } as PostgresConnectionOptions,
};

console.log('Config loaded:', config);
