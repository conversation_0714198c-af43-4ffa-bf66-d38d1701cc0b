import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsInt, IsOptional, IsPositive, ValidateNested } from 'class-validator';
import { IsDateOnly } from '@app/common/decorators/validators.decorator';
import { CreateBedScheduleEquipmentDto } from '@app/modules/bedSchedule/dto/create.bed.schedule.equipment.dto';

export class CreateBedScheduleDto {
  @ApiProperty()
  @IsInt()
  @IsPositive()
  facilityId: number;

  @ApiProperty()
  @IsInt()
  @IsPositive()
  dayShiftBeds: number;

  @ApiProperty()
  @IsInt()
  @IsPositive()
  nightShiftBeds: number;

  @ApiPropertyOptional({ type: CreateBedScheduleEquipmentDto, isArray: true })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateBedScheduleEquipmentDto)
  equipments: CreateBedScheduleEquipmentDto[];

  @ApiProperty({ format: 'date-time', example: '2020-01-01' })
  @IsDateOnly()
  date: string;
}
