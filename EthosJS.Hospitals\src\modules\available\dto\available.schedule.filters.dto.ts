import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsEnum, IsInt, IsOptional, IsPositive, ValidateNested } from 'class-validator';
import { EShift } from '@app/common/enums';
import { IsDateOnly } from '@app/common/decorators/validators.decorator';
import { CreateScheduleEquipmentDto } from '@app/modules/schedule/dto/create.schedule.equipment.dto';

export class AvailableScheduleFiltersDto {
  @ApiProperty()
  @IsInt()
  @IsPositive()
  facilityId: number;

  @ApiProperty()
  @IsInt()
  @IsPositive()
  studyId: number;

  @ApiProperty({ format: 'date-time', example: '2020-01-01' })
  @IsDateOnly()
  dateFrom: string;

  @ApiProperty({ format: 'date-time', example: '2024-01-01' })
  @IsDateOnly()
  dateTo: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  @IsPositive()
  limit?: number;

  @ApiProperty({ enum: EShift })
  @IsEnum(EShift)
  shift: EShift;

  @ApiPropertyOptional({ type: () => CreateScheduleEquipmentDto, isArray: true })
  @IsOptional()
  @IsArray()
  @Type(() => CreateScheduleEquipmentDto)
  @ValidateNested({ each: true })
  equipments: CreateScheduleEquipmentDto[] = [];
}
