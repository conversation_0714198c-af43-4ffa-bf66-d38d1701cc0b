import { Expose } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity, ManyToMany } from 'typeorm';
import { BaseEntity } from '@app/common/base.entity';
import { TechnicianEntity } from '@app/modules/technician/technician.entity';

@Entity({ name: 'credentials' })
export class CredentialEntity extends BaseEntity {
  @Column()
  @ApiProperty()
  @Expose()
  name: string;

  @Column()
  @ApiProperty()
  @Expose()
  code: string;

  @Column({ nullable: true })
  @ApiPropertyOptional()
  @Expose()
  issuedBy?: string;

  @ManyToMany(
    () => TechnicianEntity,
    technician => technician.credentials,
  )
  technicians: TechnicianEntity[];
}
