using System.Reflection;
using Ethos.Auth;
using Ethos.Model.Scheduling;
using Ethos.ReferenceData.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class DbContextBase : DbContext
{
    private readonly IEthosUserContextProvider? _currentSession; // provides current user ID

    public DbContextBase(DbContextOptions options, 
        IEthosUserContextProvider? currentSessionProvider)
        : base(options)
    {
        _currentSession = currentSessionProvider;
    }
    
    const string systemUser = "system";

    private void RecordEdits()
    {
        var now = DateTime.UtcNow;
        string? userId = _currentSession?.UserId;  // get current user (or null/system)

		// Get all added, modified, or deleted entries of auditable entities
        var entries = ChangeTracker.Entries<IAuditableEntity>()
            .Where(x => x.State == EntityState.Added
                        || x.State == EntityState.Deleted
                        || x.State == EntityState.Modified);
	    
		if (!entries.Any())
        {
            return;
        }

        var editRecords = new List<EditRecordDbo>();
        // 1. Stamp auditable entities with Created/Updated info:
        foreach (var entry in ChangeTracker.Entries<IAuditableEntity>())
        {
            var entity = entry.Entity;
            
            EditType? editType = entry.State switch
            {
                EntityState.Added => EditType.Create,
                EntityState.Modified => EditType.Update,
                EntityState.Deleted => EditType.Delete,
                EntityState.Detached => null,
                EntityState.Unchanged => null
            };
            
            if (editType == null)
            {
                continue;
            }
            
            var trail = new EditRecordDbo {
                EntityName = entity.GetType().Name,
                EditType = editType.Value,
                UserId = userId ?? systemUser,
                Timestamp = now,
                OldValues = new Dictionary<string, object?>(),
                NewValues = new Dictionary<string, object?>(),
                ChangedColumns = new List<string>(),
                PrimaryKey = null,
            };
            
            foreach (var prop in entry.Properties.Where(p => !p.IsTemporary))
            {
                string propName = prop.Metadata.Name;
                if (prop.Metadata.IsPrimaryKey())
                {
                    trail.PrimaryKey = prop.CurrentValue?.ToString();
                    continue;
                }

                // Capture Old and New values based on state
                if (entry.State == EntityState.Added)
                {
                    trail.NewValues[propName] = prop.CurrentValue;
                }
                else if (entry.State == EntityState.Deleted)
                {
                    trail.OldValues[propName] = prop.OriginalValue;
                }
                else if (entry.State == EntityState.Modified)
                {
                    // Only log if the value actually changed (and wasn’t just marked unchanged)
                    if (prop.IsModified && !Equals(prop.OriginalValue, prop.CurrentValue))
                    {
                        trail.ChangedColumns.Add(propName);
                        trail.OldValues[propName] = prop.OriginalValue;
                        trail.NewValues[propName] = prop.CurrentValue;
                    }
                }
            }

            switch (entry.State)
            {
                case EntityState.Added:
                    entity.CreateEvent = trail;
                    break;
                case EntityState.Modified:
                    entity.UpdateEvent = trail;
                    break;
            }
            
            editRecords.Add(trail);
        }
        
        // 2. Add audit records to the context
        Set<EditRecordDbo>().AddRange(editRecords);
    }
    
    public override async Task<int> SaveChangesAsync(CancellationToken cancel = default)
    {
        RecordEdits();
        // Save everything (both real entity changes and audit records) atomically
        return await base.SaveChangesAsync(cancel);
    }
    
    public override int SaveChanges()
    {
        RecordEdits();
        // Save everything (both real entity changes and audit records) atomically
        return base.SaveChanges();
    }

    //public void AddDefaultReferenceDataValues<T>() where T : ReferenceDataEntityBase<T>
    //{
    //    // find any attributes of type DefaultReferenceDataAttribute
    //    var type = typeof(T);
    //    var attributes = type.GetCustomAttributes(typeof(DefaultReferenceDataAttribute), true);
    //    if (attributes.Length == 0)
    //    {
    //        return;
    //    }

    //    var defaultReferenceDataAttribute = (DefaultReferenceDataAttribute)attributes[0];
    //    var defaultValues = defaultReferenceDataAttribute.Values!;
    //    foreach (var value in defaultValues)
    //    {
    //        // Create an instance of the entity with Name set to the value
    //        var entity = Activator.CreateInstance<T>();
    //        entity.Name = value;
    //        // Add the entity to the DbContext
    //        this.Set<T>().Add(entity);
    //    }
    //}
    
    private HashSet<EntityType> _registeredEntities = new HashSet<EntityType>();
    
    private void AddTable<T>(ModelBuilder modelBuilder) where T : class, IEntity, IEntity<T>
    {
        // public new static void Register(ModelBuilder modelBuilder)
        // public new static void Register(EntityTypeBuilder<AddressEntity> entity)
        var entityType = typeof(T);
        var entityName = entityType.Name;
        
        if (!entityType.IsClass || entityType.IsAbstract)
        {
            throw new InvalidOperationException($"Type {entityType.Name} must be a non-abstract class.");
        }
        if (!entityType.IsPublic)
        {
            throw new InvalidOperationException($"Type {entityType.Name} must be a public class.");
        }
        if (!entityType.Name.EndsWith("Dbo"))
        {
            throw new InvalidOperationException($"Type {entityType.Name} must end with 'Dbo'.");
        }
        entityName = entityName.Substring(0, entityName.Length - 3); // Remove "Dbo" suffix
        
        if (!Enum.TryParse<EntityType>(entityName, out var entityTypeValue))
        {
            throw new InvalidOperationException($"Type {entityType.Name} does not have a valid EntityType enum value.");
        }

        var _ = IEntity.GetEntityType(entityType); // Ensure the entity type is registered in IEntity
        
        if (_registeredEntities.Contains(entityTypeValue))
        {
            throw new InvalidOperationException($"Entity type {entityTypeValue} has already been registered.");
        }
        
        _registeredEntities.Add(entityTypeValue);
        
        // Get the second Register method (the one that takes EntityTypeBuilder)
        var argType = typeof(EntityTypeBuilder<>).MakeGenericType(entityType);
        var registerMethod = entityType.GetMethod("Register", 
            BindingFlags.Public | BindingFlags.Static, 
            new[] { argType }) 
            ?? throw new InvalidOperationException($"Type {entityType.Name} does not have a static Register method with the correct signature.");
        
        // Call the Register method with the modelBuilder
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema)
            .Entity<T>((builder) => registerMethod.Invoke(null, new object[] { builder }));
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Audit
        AddTable<EditRecordDbo>(modelBuilder);
        
        // File uploading
        AddTable<FileMetadataDbo>(modelBuilder);
        
        // Workflows
        AddTable<WorkflowDraftTransitionDbo>(modelBuilder);
        AddTable<WorkflowEntityLinkDbo>(modelBuilder);
        AddTable<WorkflowInstanceDbo>(modelBuilder);
        AddTable<WorkflowTransitionDbo>(modelBuilder);
        
        // Base types
        AddTable<DraftDbo>(modelBuilder);
        AddTable<NoteDbo>(modelBuilder);
        AddTable<AddressDbo>(modelBuilder);
        AddTable<InsuranceDbo>(modelBuilder);
        AddTable<InsuranceHolderDataDbo>(modelBuilder);
        AddTable<PhoneNumberWithUseDataDbo>(modelBuilder);
        
        // Patients
        AddTable<PatientDbo>(modelBuilder);
        AddTable<PatientGuardianDbo>(modelBuilder);
        AddTable<PatientAppointmentDbo>(modelBuilder);
        AddTable<PatientAppointmentConfirmationDbo>(modelBuilder);
        
        AddTable<PersonalContactDetailDbo>(modelBuilder);
        AddTable<PersonalEmailDbo>(modelBuilder);
        AddTable<PersonalEmergencyContactDbo>(modelBuilder);
        AddTable<PersonalPhoneNumberDbo>(modelBuilder);
        AddTable<PersonalAddressDbo>(modelBuilder);
        
        AddTable<OrganizationContactDetailDbo>(modelBuilder);
        AddTable<OrganizationEmailDbo>(modelBuilder);
        AddTable<OrganizationPhoneNumberDbo>(modelBuilder);
        AddTable<OrganizationAddressDbo>(modelBuilder);

        AddTable<OrderDbo>(modelBuilder);
        AddTable<StudyDbo>(modelBuilder);
        
        AddTable<ProviderDbo>(modelBuilder);
        AddTable<CareLocationDbo>(modelBuilder);
        AddTable<RoomDbo>(modelBuilder);
        AddTable<EquipmentDbo>(modelBuilder);
        
        // Physicians
        AddTable<PhysicianDbo>(modelBuilder);
        AddTable<PhysicianCareLocationRelationDbo>(modelBuilder);
        
        // Technicians
        AddTable<TechnicianDbo>(modelBuilder);
        AddTable<TechnicianRoleDbo>(modelBuilder);
        AddTable<TechnicianShiftPreferenceDbo>(modelBuilder);
        AddTable<TechnicianCareLocationRelationDbo>(modelBuilder);
        AddTable<TechnicianAppointmentDbo>(modelBuilder);
        
        // Insurance verification
        AddTable<InsuranceVerificationDbo>(modelBuilder);
        
        AddTable<SchedulingConstraintDbo>(modelBuilder);
        
        foreach (var enumValue in Enum.GetValues(typeof(EntityType)))
        {
            // Get the entity type from the enum value
            var entityType = (EntityType)enumValue;
            
            if (!_registeredEntities.Contains(entityType))
            {
                throw new InvalidOperationException($"Entity type {entityType} has not been registered.");
            }
        }
        
        base.OnModelCreating(modelBuilder);
    }
}