using Microsoft.Extensions.Configuration;

namespace Ethos.Auth;

public interface IEthosAuthConfigProvider
{  
    bool IsLocalAuthEnabled { get; }
    string? LocalJwtKey { get; }
    string? LocalJwtIssuer { get; }
    string? LocalJwtAudience { get; }
    bool IsAzureAuthEnabled { get; }
    string? AzureClientId { get; }
    string? AzureTenantId { get; }
    string? AzureInstance { get; }
    string? AzureIssuer { get; }
    string? AzurePolicy { get; }
}

public class EthosAuthConfigProvider(IConfiguration configuration) : IEthosAuthConfigProvider
{
    public bool IsLocalAuthEnabled => configuration.GetValue<bool>("Auth:Local:Enabled");
    public string? LocalJwtKey => configuration["Auth:Local:Key"];
    public string? LocalJwtIssuer => configuration["Auth:Local:Issuer"];
    public string? LocalJwtAudience => configuration["Auth:Local:Audience"];
    
    public bool IsAzureAuthEnabled => configuration.GetValue<bool>("Auth:AzureAD:Enabled");
    public string? AzureClientId => configuration["Auth:AzureAD:ClientId"];
    public string? AzureTenantId => configuration["Auth:AzureAD:TenantId"];
    public string? AzureInstance => configuration["Auth:AzureAD:Instance"];
    public string? AzureIssuer => configuration["Auth:AzureAD:Issuer"];
    public string? AzurePolicy => configuration["Auth:AzureAD:Policy"];
}