using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class OrganizationEmailDbo : IAuditableEntity<OrganizationEmailDbo>
{
    public Guid ParentId { get; set; }
    public virtual OrganizationContactDetailDbo Parent { get; set; } = null!;

    public string Email { get; set; } = null!;
    // public long Use { get; set; }

    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<OrganizationEmailDbo>(Register);
    
    public new static void Register(EntityTypeBuilder<OrganizationEmailDbo> entity)
    {
        IAuditableEntity<OrganizationEmailDbo>.Register(entity);

        entity.Property(e => e.Email).IsRequired().HasMaxLength(254);
        // entity.Property(e => e.Use).IsRequired();
        entity.HasIndex(e => new { e.ParentId, e.Email }).IsUnique(); // Ensure unique email per patient

        entity.HasOne(e => e.Parent)
            .WithMany(p => p.Emails)
            .HasForeignKey(e => e.ParentId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}