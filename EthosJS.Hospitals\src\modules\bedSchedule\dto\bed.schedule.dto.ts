import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BaseDto } from '@app/common/dto/base.dto';
import { BedScheduleEquipmentDto } from '@app/modules/bedSchedule/dto/bed.schedule.equipment.dto';

export class BedScheduleDto extends BaseDto {
  @ApiProperty()
  @Expose()
  facilityId: number;

  @ApiProperty()
  @Expose()
  facilityName: string;

  @ApiProperty()
  @Expose()
  dayShiftBeds: number;

  @ApiProperty()
  @Expose()
  nightShiftBeds: number;

  @ApiProperty()
  @Expose()
  date: string;

  @ApiProperty({ type: BedScheduleEquipmentDto, isArray: true })
  @Expose()
  equipments: BedScheduleEquipmentDto[];
}
