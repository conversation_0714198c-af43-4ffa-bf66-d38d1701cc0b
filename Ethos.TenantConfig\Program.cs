using System.Diagnostics;
using Ethos.TenantConfig;
using Microsoft.EntityFrameworkCore;
using Npgsql;
using Newtonsoft.Json.Serialization;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers(options =>
{
    options.InputFormatters.Insert(0, JsonPatchFormatter.GetJsonPatchInputFormatter());
}).AddNewtonsoftJson(options =>
{
    options.SerializerSettings.Converters.Add(new NewtonsoftJsonDynamicObjectConverter());
    options.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();
    options.SerializerSettings.Formatting = Newtonsoft.Json.Formatting.Indented;
    options.SerializerSettings.NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore;
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
builder.Services.AddProblemDetails();

builder.Configuration.SetBasePath(Directory.GetCurrentDirectory());
builder.Configuration.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
builder.Configuration.AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", optional: true);
builder.Configuration.AddEnvironmentVariables();

builder.Services.AddSingleton(builder.Configuration);

builder.Services.AddDbContext<ConfigDbContext>(options =>
{
    var dataSourceBuilder = new NpgsqlDataSourceBuilder(builder.Configuration.GetConnectionString("DefaultConnection"));
    //dataSourceBuilder.EnableDynamicJson();
    var dataSource = dataSourceBuilder.Build();
    options.UseNpgsql(dataSource);
}, contextLifetime: ServiceLifetime.Scoped, optionsLifetime: ServiceLifetime.Singleton);

var app = builder.Build();

// Do EF migrations
using (var scope = app.Services.CreateScope())
{
    try
    {
        var context = scope.ServiceProvider.GetRequiredService<ConfigDbContext>();

        if (app.Environment.IsDevelopment())
        {
            //var pendingMigrations = context.Database.GetPendingMigrations();

            //if (context.Database.HasPendingModelChanges())
            //{
            //    var migrationName = "DynamicMigration_" + DateTime.Now.Ticks;
            //    var projectDir = Directory.GetCurrentDirectory();
            //    var efTool = "dotnet";
            //    var arguments = $"ef migrations add {migrationName} --project \"{projectDir}\"";

            //    ProcessStartInfo processInfo = new(efTool, arguments)
            //    {
            //        RedirectStandardOutput = true,
            //        RedirectStandardError = true,
            //        UseShellExecute = false,
            //        CreateNoWindow = true
            //    };

            //    Process process = new()
            //    {
            //        StartInfo = processInfo
            //    };
            //    process.Start();

            //    process.WaitForExit();

            //    var output = process.StandardOutput.ReadToEnd();
            //    var error = process.StandardError.ReadToEnd();
            //    Console.WriteLine($"Migration created: {output}");
            //    if (!string.IsNullOrEmpty(error))
            //        Console.WriteLine($"Migration Errors: {error}");

            //    if (process.ExitCode != 0)
            //        throw new Exception("Migration Creation Failed");

            //    Thread.Sleep(10000);
            //}
        }
        //context.Database.Migrate();
        //context.Database.EnsureCreated();
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Migration Errors: {ex.Message}");
        throw;
    }
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

//app.UseHttpsRedirection();
app.UseExceptionHandler();
app.UseStatusCodePages();

app.UseAuthorization();

app.MapControllers();

app.Run();
