using System.Net.Http.Headers;
using System.Net.Http.Json;
using Ethos.Model;
using Ethos.Utilities.Pagination;
using Ethos.Workflows.Controllers;

namespace Ethos.Workflows.Api;

public sealed record OrganizationPhoneNumberDto
{
    [CheckString(30)]
    public required string PhoneNumber { get; set; }
}

public sealed record OrganizationEmailDto
{
    [CheckString(100)]
    public required string Email { get; set; }
}

public sealed record OrganizationAddressDto
{
    public required long Use { get; set; }
    public required long Type { get; set; }
    public required AddressDto Address { get; set; }
}

public sealed record OrganizationContactPersonDto
{
    public required PersonNameDto Name { get; set; }
    public required PersonalContactDetailDto ContactDetail { get; set; }
}

public sealed record OrganizationContactDetailDto
{
    public required IReadOnlyList<OrganizationPhoneNumberDto> PhoneNumbers { get; set; }
    public required IReadOnlyList<OrganizationEmailDto> Emails { get; set; }
    public required IReadOnlyList<OrganizationAddressDto> Addresses { get; set; }
    public required IReadOnlyList<OrganizationContactPersonDto> ContactPersons { get; set; }
}

// Input DTO
public sealed record CreateProviderDto : IInputDto
{
    [CheckString(100)]
    public string? Name { get; set; }
    public Guid? ParentProviderId { get; set; }
    public IReadOnlyList<IdentifierDto>? Identifiers { get; set; }
    public OrganizationContactDetailDto? ContactDetail { get; set; }
}

// Output DTO
public sealed record ProviderDto
{
    public required Guid Id { get; set; }
    [CheckString(100)]
    public required string Name { get; set; }
    public required Guid? ParentProviderId { get; set; }
    public required IReadOnlyList<IdentifierDto> Identifiers { get; set; } = new List<IdentifierDto>();
    public required OrganizationContactDetailDto? ContactDetail { get; set; }
}

public interface IProviderApi : IEntityHttpClient<CreateProviderDto, ProviderDto, ProviderQ>;

public class ProviderHttpClient(System.Net.Http.HttpClient httpClient)
    : EntityHttpClientBase<CreateProviderDto, ProviderDto, ProviderQ>(httpClient, "provider"),
        IProviderApi;