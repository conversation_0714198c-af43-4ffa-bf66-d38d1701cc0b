using Ethos.Workflows.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Controllers;

using System.Net.Http;
using System.Threading.Tasks;
using Ethos.Model;
using Ethos.Workflows.Api;
using Microsoft.AspNetCore.Mvc;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class NoteController(DbContext dbContext)
    : EntityControllerBase<NoteDbo, CreateNoteDto, NoteDto, NoteQ>(dbContext)
{
    protected override NoteDto MapToDto(NoteDbo dbo)
    {
        return new NoteDto
        {
            Id = dbo.Id,
            EntityType = dbo.EntityType,
            EntityId = dbo.EntityId,
            Content = dbo.Content,
        };
    }

    protected override NoteDbo CreateOrUpdateEntity(NoteDbo? entity, CreateNoteDto input, Guid? requiredId = null)
    {
        if (entity == null)
        {
            entity = new NoteDbo
            {
                Id = requiredId ?? Guid.NewGuid(),
                EntityType = input.EntityType,
                EntityId = input.EntityId,
                Content = input.Content
            };
        }
        else
        {
            entity.EntityType = input.EntityType;
            entity.EntityId = input.EntityId;
            entity.Content = input.Content;
        }
        return entity;
    }
}