using Ethos.Model;

namespace Ethos.Workflows.Api;

public sealed record CreateCareLocationDto : IInputDto 
{
    [CheckString(hardMaxLength: 100)]
    public required string Name { get; set; }
    public required Guid? ParentServiceLocationId { get; set; }
    public required Guid? ParentProviderId { get; set; }
    public required OrganizationContactDetailDto? ContactDetail { get; set; }
    public required IReadOnlyList<long>? SupportedEncounterTypes { get; set; }
    public required IReadOnlyList<long>? SupportedStudyTypes { get; set; }
}

public sealed record CareLocationDto
{
    public required Guid Id { get; set; }
    [CheckString(hardMaxLength: 100)]
    public required string Name { get; set; }
    public required Guid? ParentServiceLocationId { get; set; }
    public required Guid? ParentProviderId { get; set; }
    public required OrganizationContactDetailDto? ContactDetail { get; set; }
    public required IReadOnlyList<long>? SupportedEncounterTypes { get; set; }
    public required IReadOnlyList<long>? SupportedStudyTypes { get; set; }
}

public interface ICareLocationApi : IEntityHttpClient<CreateCareLocationDto, CareLocationDto, CareLocationQ>;

public class CareLocationHttpClient(System.Net.Http.HttpClient httpClient)
    : EntityHttpClientBase<CreateCareLocationDto, CareLocationDto, CareLocationQ>(httpClient, "carelocation"),
        ICareLocationApi;