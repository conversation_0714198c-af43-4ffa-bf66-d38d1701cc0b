﻿using System.IdentityModel.Tokens.Jwt;
using System.Reflection;
using System.Security.Claims;
using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;

namespace Ethos.Auth
{
    /// <summary>
    /// 
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Struct | AttributeTargets.Method)]
    public class EthosAuthSatisfyAnyAttribute : Attribute { }

    /// <summary>
    /// 
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Struct | AttributeTargets.Method)]
    public class EthosAuthSatisfyAllAttribute : Attribute { }

    /// <summary>
    /// 
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Struct | AttributeTargets.Method)]
    public class EthosAuthFeatureAttribute : Attribute
    {
        public string? Name { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Struct | AttributeTargets.Method, AllowMultiple = true)]
    public partial class EthosAuthScopeAttribute : Attribute
    {
        /// <summary>
        /// 
        /// </summary>
        readonly List<EthosScope> scopes = [];

        public EthosScope[] Scopes => [..scopes];

        /// <summary>
        /// 
        /// </summary>
        /// <param name="scopes"></param>
        public EthosAuthScopeAttribute(params string[] scopes)
        {
            foreach (var scope in scopes)
            {
                if (string.IsNullOrEmpty(scope))
                    continue;

                if (!EthosScope.TryParse(scope, null, out EthosScope? _scope) || _scope is null)
                    throw new ArgumentException($"Invalid scope: {scope}", nameof(scopes));

                this.scopes.Add(_scope);
            }
        }
    }

    /// <summary>
    /// 
    /// </summary>
    [Flags]
    public enum EthosPermission
    {
        /// <summary>
        /// No permission.
        /// </summary>
        None = 0,

        /// <summary>
        /// Read permission.
        /// </summary>
        Read = 1,

        /// <summary>
        /// Write permission.
        /// </summary>
        Write = 2,

        /// <summary>
        /// Read all permission.
        /// </summary>
        ReadAll = Read | 4,

        /// <summary>
        /// Delete permission.
        /// </summary>
        Delete = 8,

        /// <summary>
        /// Audit permission.
        /// </summary>
        Audit = 16,

        /// <summary>
        /// Administrative permission.
        /// </summary>
        Admin = Read | Write | Delete |  Audit | ReadAll | 4096,

        /// <summary>
        /// All permissions (except administrative permission and read all).
        /// </summary>
        All = Read | Write | Delete | Audit,

        /// <summary>
        /// This is here until we remove Patient.Create from Azure
        /// </summary>
        Create = 256
    }

    /// <summary>
    /// 
    /// </summary>
    public static class EthosAuthAttributeExtensions
    {
        const string Controller = nameof(Controller);

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="claim"></param>
        /// <param name="jsonOptions"></param>
        /// <returns></returns>
        public static T? GetProducts<T>(this Claim claim, JsonSerializerOptions? jsonOptions = null)
        {
            if (claim is null || string.IsNullOrEmpty(claim.Value))
                return default;
            return JsonSerializer.Deserialize<T>(claim.Value, jsonOptions ?? JsonSerializerOptions.Default);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="claim"></param>
        /// <param name="jsonOptions"></param>
        /// <returns></returns>
        public static string[] GetClaimValuesByType(this ClaimsPrincipal principal, StringComparison stringComparison, params string?[] claimNames)
        {
            if (principal is null || claimNames.Length == 0 || !claimNames.Any(cn => !string.IsNullOrEmpty(cn)))
                return [];
            return [.. principal.Claims.Where(c => claimNames.Any(cn => string.Equals(c.Type, cn, stringComparison)))
                                       .Select(c => c.Value)];
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="principal"></param>
        /// <param name="stringComparison"></param>
        /// <param name="delimiter"></param>
        /// <param name="claimNames"></param>
        /// <returns></returns>
        public static string[] GetDelimitedClaimValuesByType(this ClaimsPrincipal principal, StringComparison stringComparison, char delimiter, params string?[] claimNames)
        {
            var values = principal.GetClaimValuesByType(stringComparison, claimNames);
            var valsToSplit = values.Where(e => e.Contains(delimiter));
            if (valsToSplit.Any())
            {
                var splitVals = valsToSplit.SelectMany(e => e.Split(delimiter, StringSplitOptions.RemoveEmptyEntries));
                values = [.. values.Except(valsToSplit).Union(splitVals)];
            }
            return values;

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="principal"></param>
        /// <returns></returns>
        public static string[] GetGivenNames(this ClaimsPrincipal principal, params string?[] givenNameClaimNames)
        {
            if (givenNameClaimNames.Length == 0 || !givenNameClaimNames.Any(g => !string.IsNullOrEmpty(g)))
                givenNameClaimNames = [EthosClaimNames.GivenName, JwtRegisteredClaimNames.GivenName];
            return [.. principal.GetClaimValuesByType(StringComparison.OrdinalIgnoreCase, givenNameClaimNames)];
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="principal"></param>
        /// <returns></returns>
        public static bool HasScope(this ClaimsPrincipal principal, string scope, params string?[] scopeClaimNames)
        {
            if (string.IsNullOrEmpty(scope))
                return false;

            if (!EthosScope.TryParse(scope, null, out var ethosScope))
                return false;

            return principal.HasScope(ethosScope, scopeClaimNames);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="principal"></param>
        /// <param name="scope"></param>
        /// <param name="scopeClaimNames"></param>
        /// <returns></returns>
        public static bool IsAllowedScope(this ClaimsPrincipal principal, string scope, params string?[] scopeClaimNames)
        {
            if (string.IsNullOrEmpty(scope))
                return false;

            if (!EthosScope.TryParse(scope, null, out var ethosScope))
                return false;

            return principal.GetEthosScopes(scopeClaimNames).Any(s => ethosScope.IsAuthorized(s));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="principal"></param>
        /// <param name="scope"></param>
        /// <param name="scopeClaimNames"></param>
        /// <returns></returns>
        public static bool HasScope(this ClaimsPrincipal principal, EthosScope scope, params string?[] scopeClaimNames)
        {
            if (scope is null)
                return false;

            if (!scope.IsFullyQualified())
                return false;

            var scopes = principal.GetEthosScopes(scopeClaimNames);

            if (scopes.Length == 0)
                return false;

            return scopes.Any(es => es.Is(scope));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="principal"></param>
        /// <param name="scopeClaimNames"></param>
        /// <returns></returns>
        public static EthosScope[] GetEthosScopes(this ClaimsPrincipal principal, params string?[] scopeClaimNames)
        {
            var scopes = principal.GetScopes(scopeClaimNames);
            if (scopes.Length == 0)
                return [];
            return [..scopes.Select(s => new EthosScope(s))];
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="principal"></param>
        /// <param name="scopeClaimNames"></param>
        /// <returns></returns>
        public static string[] GetScopes(this ClaimsPrincipal principal, params string?[] scopeClaimNames)
        {
            if (scopeClaimNames.Length == 0 || !scopeClaimNames.Any(s => !string.IsNullOrEmpty(s)))
                scopeClaimNames = [EthosClaimNames.MicrosoftScope, EthosClaimNames.Scopes];
            return principal.GetDelimitedClaimValuesByType(StringComparison.OrdinalIgnoreCase, ' ', scopeClaimNames);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="principal"></param>
        /// <returns></returns>
        public static string[] GetSurnames(this ClaimsPrincipal principal, params string?[] surnameClaimNames)
        {
            if (surnameClaimNames.Length == 0 || !surnameClaimNames.Any(s => !string.IsNullOrEmpty(s)))
                surnameClaimNames = [EthosClaimNames.Surname, JwtRegisteredClaimNames.FamilyName];
            return [.. principal.GetClaimValuesByType(StringComparison.OrdinalIgnoreCase, surnameClaimNames)];
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="principal"></param>
        /// <param name="surnameClaimNames"></param>
        /// <returns></returns>
        public static string? GetSurname(this ClaimsPrincipal principal, params string?[] surnameClaimNames)
        {
            return principal.GetSurnames(surnameClaimNames).FirstOrDefault();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="principal"></param>
        /// <param name="surnameClaimNames"></param>
        /// <returns></returns>
        public static string? GetGivenName(this ClaimsPrincipal principal, params string?[] givenNameClaimNames)
        {
            return principal.GetGivenNames(givenNameClaimNames).FirstOrDefault();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="principal"></param>
        /// <param name="emailClaimNames"></param>
        /// <returns></returns>
        public static string[] GetEmails(this ClaimsPrincipal principal, params string?[] emailClaimNames)
        {
            if (emailClaimNames.Length == 0 || !emailClaimNames.Any(e => !string.IsNullOrEmpty(e)))
                emailClaimNames = [ EthosClaimNames.Emails, JwtRegisteredClaimNames.Email ];
            return principal.GetDelimitedClaimValuesByType(StringComparison.OrdinalIgnoreCase, ' ', emailClaimNames);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="principal"></param>
        /// <returns></returns>
        public static string? GetEmail(this ClaimsPrincipal principal, params string?[] emailClaimNames)
        {
            return principal.GetEmails(emailClaimNames).FirstOrDefault();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="principal"></param>
        /// <returns></returns>
        public static string[] GetUniqueIds(this ClaimsPrincipal principal, params string?[] uuidClaimNames)
        {
            if (uuidClaimNames.Length == 0 || !uuidClaimNames.Any(i => !string.IsNullOrEmpty(i)))
                uuidClaimNames = [EthosClaimNames.MicrosoftObjectIdentifier, EthosClaimNames.MicrosoftNameIdentifier];
            return [.. principal.GetClaimValuesByType(StringComparison.OrdinalIgnoreCase, uuidClaimNames)];
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="principal"></param>
        /// <param name="uuidClaimNames"></param>
        /// <returns></returns>
        public static string? GetUniqueId(this ClaimsPrincipal principal, params string?[] uuidClaimNames)
        {
            return principal.GetUniqueIds(uuidClaimNames).FirstOrDefault();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="principal"></param>
        /// <param name="uuidClaimNames"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static Guid GetRequiredUniqueId(this ClaimsPrincipal principal, params string?[] uuidClaimNames)
        {
            var uniqId = principal.GetUniqueId(uuidClaimNames);
            if (string.IsNullOrEmpty(uniqId))
                throw new Exception();
            if (!Guid.TryParse(uniqId, out var uniqueId))
                throw new Exception();
            return uniqueId;        
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static string[] GetFeatures(this Type type)
        {
            return [.. type.GetCustomAttributes<EthosAuthFeatureAttribute>(true).Select(a => a.Name)];
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="principal"></param>
        /// <param name="tenantIdClaimNames"></param>
        /// <returns></returns>
        public static string[] GetTenantIdStrings(this ClaimsPrincipal principal, params string?[] tenantIdClaimNames)
        {
            if (tenantIdClaimNames.Length == 0 || !tenantIdClaimNames.Any(i => !string.IsNullOrEmpty(i)))
                tenantIdClaimNames = [EthosClaimNames.TenantId];
            return [.. principal.GetClaimValuesByType(StringComparison.OrdinalIgnoreCase, tenantIdClaimNames)];
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="principal"></param>
        /// <param name="tenantIdClaimNames"></param>
        /// <returns></returns>
        public static string? GetTenantIdString(this ClaimsPrincipal principal, params string?[] tenantIdClaimNames)
        {
            return principal.GetTenantIdStrings(tenantIdClaimNames).FirstOrDefault();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="principal"></param>
        /// <param name="tenantIdClaimNames"></param>
        /// <returns></returns>
        public static Guid[] GetTenantIds(this ClaimsPrincipal principal, params string?[] tenantIdClaimNames)
        {
            var tenantIds = principal.GetTenantIdStrings(tenantIdClaimNames);
            return [.. tenantIds.Select(t => new Guid(t))];
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="principal"></param>
        /// <param name="tenantIdClaimNames"></param>
        /// <returns></returns>
        public static Guid? GetTenantId(this ClaimsPrincipal principal, params string?[] tenantIdClaimNames)
        {
            var tenantId = principal.GetTenantIds(tenantIdClaimNames).FirstOrDefault();
            return tenantId == Guid.Empty ? null : tenantId;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="controller"></param>
        /// <param name="tenantIdClaimNames"></param>
        /// <returns></returns>
        public static Guid? GetTenantId(this ControllerBase controller, params string?[] tenantIdClaimNames)
        {
            return controller.HttpContext.User.GetTenantId(tenantIdClaimNames);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="controller"></param>
        /// <param name="tenantIdClaimNames"></param>
        /// <returns></returns>
        public static Guid GetRequiredTenantId(this ControllerBase controller, params string?[] tenantIdClaimNames)
        {
            var tenantId = controller.GetTenantId(tenantIdClaimNames) ?? Guid.Empty;
            if (tenantId == Guid.Empty)
                throw new Exception("No tenant ID available on controller.");
            return tenantId;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="controller"></param>
        /// <param name="tenantIdClaimNames"></param>
        /// <returns></returns>
        public static Guid GetTenantIdOrDefault(this ControllerBase controller, params string?[] tenantIdClaimNames)
        {
            return controller.GetTenantId(tenantIdClaimNames) ?? Guid.Empty;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static string? GetFeature(this Type type)
        {
            return type.GetFeatures().FirstOrDefault();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="returnType"></param>
        /// <param name="assignableFrom"></param>
        /// <returns></returns>
        internal static Type? GetUnderlyingType(this Type returnType, Type assignableFrom)
        {
            if (returnType is null)
                return null;

            // Check if it's a generic type and assignable to our type
            if (returnType.IsGenericType && assignableFrom.IsAssignableFrom(returnType.GetGenericTypeDefinition()))
            {
                // Get the generic type arguments
                Type[] genericArgs = returnType.GetGenericArguments();
                return genericArgs.FirstOrDefault(); // should not be null, but we are being defensive
            }
            return returnType;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="returnType"></param>
        /// <param name="assignableFrom"></param>
        /// <returns></returns>
        internal static Type? GetUnderlyingType(this Type returnType, params Type[] assignableFrom)
        {
            var tmpReturnType = returnType;

            foreach (var assignable in assignableFrom)
            {
                if (tmpReturnType is null)
                    return null;

                tmpReturnType = tmpReturnType.GetUnderlyingType(assignable);
            }
            return tmpReturnType;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="endpoint"></param>
        /// <returns></returns>
        public static bool SatisfyAny(this Endpoint endpoint)
        {
            var actionAtts = endpoint.GetMethodInfoCustomAttributes<EthosAuthSatisfyAnyAttribute, EthosAuthSatisfyAllAttribute>();
            if (actionAtts.Any(a => a is EthosAuthSatisfyAnyAttribute))
                return !actionAtts.Any(a => a is EthosAuthSatisfyAllAttribute);
            return endpoint.GetCustomAttributes<EthosAuthSatisfyAnyAttribute>().Count > 0;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="endpoint"></param>
        /// <returns></returns>
        public static bool SatisfyAll(this Endpoint endpoint)
        {
            return !endpoint.SatisfyAny();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="endpoint"></param>
        /// <returns></returns>
        public static ControllerActionDescriptor? GetActionDescriptor(this Endpoint endpoint)
        {
            var descriptor = endpoint.Metadata.FirstOrDefault(m => m is ControllerActionDescriptor);
            if (descriptor is ControllerActionDescriptor _descriptor)
                return _descriptor;
            return default;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="endpoint"></param>
        /// <returns></returns>
        public static string? GetControllerName(this Endpoint endpoint)
        {
            var descriptor = endpoint.GetActionDescriptor();
            if (descriptor is not null)
                return descriptor.ControllerName;
            return default;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="endpoint"></param>
        /// <returns></returns>
        public static MethodInfo? GetActionMethodInfo(this Endpoint endpoint)
        {
            var descriptor = endpoint.GetActionDescriptor();
            if (descriptor is not null)
                return descriptor.MethodInfo;
            return default;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="endpoint"></param>
        /// <returns></returns>
        public static Type? GetActionReturnType(this Endpoint endpoint)
        {
            var methodInfo = endpoint.GetActionMethodInfo();
            if (methodInfo is not null)
                return methodInfo.ReturnType;
            return default;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TAttribute"></typeparam>
        /// <param name="endpoint"></param>
        /// <returns></returns>
        public static List<TAttribute> GetMethodInfoCustomAttributes<TAttribute>(this Endpoint endpoint) where TAttribute : Attribute
        {
            var methodInfo = endpoint.GetActionMethodInfo();
            if (methodInfo is not null)
                return [.. methodInfo.GetCustomAttributes<TAttribute>()];
            return [];
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TAttribute"></typeparam>
        /// <typeparam name="UAttribute"></typeparam>
        /// <param name="endpoint"></param>
        /// <returns></returns>
        public static List<Attribute> GetMethodInfoCustomAttributes<TAttribute, UAttribute>(this Endpoint endpoint) where TAttribute : Attribute
            where UAttribute : Attribute
        {
            return endpoint.GetMethodInfoCustomAttributes([ typeof(TAttribute), typeof(UAttribute) ]);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="endpoint"></param>
        /// <param name="types"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public static List<Attribute> GetMethodInfoCustomAttributes(this Endpoint endpoint, params Type[] types) 
        {
            if (types.Any(t => !typeof(Attribute).IsAssignableFrom(t)))
                throw new ArgumentException($"Types must inherit from '{nameof(Attribute)}'");

            var methodInfo = endpoint.GetActionMethodInfo();
            if (methodInfo is not null)
                return [.. methodInfo.GetCustomAttributes().Where(a => types.Contains(a.GetType()))];
            return [];
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TAttribute"></typeparam>
        /// <param name="endpoint"></param>
        /// <returns></returns>
        public static List<TAttribute> GetCustomAttributes<TAttribute>(this Endpoint endpoint) where TAttribute : Attribute
        {
            return new List<TAttribute>(endpoint.Metadata.GetOrderedMetadata<TAttribute>() ?? []);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="endpoint"></param>
        /// <returns></returns>
        public static List<T> GetMetadata<T>(this Endpoint endpoint) where T : class, new()
        {
            return new List<T>(endpoint.Metadata.GetOrderedMetadata<T>() ?? []);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="endpoint"></param>
        /// <returns></returns>
        public static string[] GetFeatures(this Endpoint endpoint)
        {
            var features = endpoint.GetCustomAttributes<EthosAuthFeatureAttribute>();

            if (features.Any(d => string.IsNullOrEmpty(d.Name)))
            {
                var name = endpoint.GetControllerName();
                if (!string.IsNullOrEmpty(name))
                    features.Add(new EthosAuthFeatureAttribute() { Name = name });
            }

            return [.. features.Where(d => !string.IsNullOrEmpty(d.Name))
                               .Select(f => f.Name)
                               .Distinct()];
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="endpoint"></param>
        /// <returns></returns>
        public static string? GetFeature(this Endpoint endpoint)
        {
            return endpoint.GetFeatures().FirstOrDefault();
        }
    }
}
