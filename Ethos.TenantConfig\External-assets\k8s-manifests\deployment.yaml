apiVersion: apps/v1
kind: Deployment
metadata:
  name: ethos-tenantconfig
  namespace: ethos-ns-test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ethos-tenantconfig
  template:
    metadata:
      labels:
        app: ethos-tenantconfig
    spec:
      containers:
      - name: ethos-tenantconfig
        image: ethoscrdev.azurecr.io/ethos-tenantconfig:2025.05.1
        imagePullPolicy: Always
        ports:
        - containerPort: 8080 
        env:
        - name: ConnectionStrings__DefaultConnection
          valueFrom:
            secretKeyRef:
              key: connection-string
              name: db-connection-secret
              optional: false
        - name: ASPNETCORE_URLS
          value: "http://*:8080"
        - name: VITE_API_URL
          value: "http://localhost:4000"
      restartPolicy: Always