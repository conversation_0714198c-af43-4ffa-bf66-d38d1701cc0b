import { Injectable, NotFoundException } from '@nestjs/common';
import { UserRepository } from '@app/modules/user/user.repository';
import { UserEntity } from '@app/modules/user/user.entity';
import { FindConditions } from 'typeorm';

@Injectable()
export class UserService {
  constructor(
      private readonly userRepository: UserRepository,
  ) {}

  async findByToken(token: string): Promise<UserEntity | undefined> {
    return this.userRepository.findOne({
      where: { token },
    });
  }

  async findOneOrFail(filter: FindConditions<UserEntity>): Promise<UserEntity> {
    const user = await this.userRepository.findOne(filter);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async upsertToken(email: string, token: string): Promise<UserEntity> {
    let user = await this.userRepository.findOne({ where: { email } });

    if (!user) {
      user = await this.userRepository.create({ email, name: 'persante-user' });
    }

    user.token = token;
    await user.save();

    return user;
  }

  async logout(userId: number): Promise<void> {
    await this.userRepository.update({ id: userId }, { token: undefined });
  }
}
