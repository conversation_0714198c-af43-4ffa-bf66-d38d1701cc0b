import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { EShift } from '@app/common/enums';
import { ScheduleEquipmentDto } from '@app/modules/schedule/dto/schedule.equipment.dto';
import { BaseDto } from '@app/common/dto/base.dto';

export class ScheduleDto extends BaseDto {
  @ApiProperty()
  @Expose()
  technicianId: number;

  @ApiProperty()
  @Expose()
  technicianName: string;

  @ApiProperty()
  @Expose()
  patientId: number;

  @ApiProperty()
  @Expose()
  patientName: string;

  @ApiProperty()
  @Expose()
  studyId: number;

  @ApiProperty()
  @Expose()
  studyName: string;

  @ApiProperty()
  @Expose()
  facilityId: number;

  @ApiProperty()
  @Expose()
  facilityName: string;

  @ApiProperty({ enum: EShift })
  @Expose()
  shift: EShift;

  @ApiProperty()
  @Expose()
  date: string;

  @ApiProperty({ type: ScheduleEquipmentDto, isArray: true })
  @Expose()
  equipments: ScheduleEquipmentDto[];
}
