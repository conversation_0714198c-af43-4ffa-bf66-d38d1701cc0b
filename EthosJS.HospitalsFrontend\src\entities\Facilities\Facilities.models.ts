//@ts-nocheck
// import '@axmit/persante-hospitals';
import { IOption, IRequestSuccess } from 'common/models';

export interface IFacilityCollectionPayload {}
export interface IFacilityCollectionDto  {}

export interface IFacilityCollection {
  data: IFacilityCollectionDto | null;
  loading: boolean;
}

export interface IFacilityModelCreatePayload extends IRequestSuccess {}
export interface IFacilityModelUpdatePayload extends  IRequestSuccess {}
export interface IFacilityModelDeletePayload extends  IRequestSuccess {}
export interface IFacilityModelDto  {}
export interface IFacilityEquipmentDto {}

export interface IFacilityModel {
  data: IFacilityModelDto | null;
  loading: boolean;
}

export interface IFacilityEquipmentEntity {
  key: number;
  id: number;
  equipmentId: number;
  name: string;
  count: number;
}

export interface IFacilityEquipmentCreatePayload {
  equipment: IOption;
  count: number;
}

export interface IFacilityEquipmentsCollection {
  data: IFacilityEquipmentEntity[] | null;
}
