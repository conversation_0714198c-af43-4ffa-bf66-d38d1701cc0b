// THIS FILE IS AUTO-GENERATED
// DO NOT EDIT

using System.Collections.Immutable;
using System.Text.RegularExpressions;
using Ethos.Utilities;

namespace Ethos.Model.Types;

public sealed record Insurance(
    int InsuranceCarrier,
    int PlanType,
    string? InsuranceId,
    string PolicyId,
    string GroupNumber,
    string MemberId,
    InsuranceHolder? InsuranceHolder,
    PhoneNumberWithUse? PhoneNumber,
    EmailWithUse? Email,
    AddressWithUse? Address);
