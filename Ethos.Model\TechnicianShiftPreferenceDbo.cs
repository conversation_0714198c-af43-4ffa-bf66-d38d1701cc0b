using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class TechnicianShiftPreferenceDbo : IAuditableEntity<TechnicianShiftPreferenceDbo>
{
    public Guid TechnicianId { get; set; }
    public virtual TechnicianDbo Technician { get; set; } = null!;
    
    public Guid ShiftId { get; set; }
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<TechnicianShiftPreferenceDbo>(Register);

    public new static void Register(EntityTypeBuilder<TechnicianShiftPreferenceDbo> entity)
    {
        IAuditableEntity<TechnicianShiftPreferenceDbo>.Register(entity);
        
        entity.HasOne(e => e.Technician)
            .WithMany()
            .HasForeignKey(e => e.TechnicianId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}