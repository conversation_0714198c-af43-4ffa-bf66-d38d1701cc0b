using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class TechnicianRoleDbo : IAuditableEntity<TechnicianRoleDbo>
{
    public Guid TechnicianId { get; set; }
    public virtual TechnicianDbo Technician { get; set; } = null!;
    
    public required long RoleId { get; set; }
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<TechnicianRoleDbo>(Register);

    public new static void Register(EntityTypeBuilder<TechnicianRoleDbo> entity)
    {
        IAuditableEntity<TechnicianRoleDbo>.Register(entity);
        
        entity.HasOne(e => e.Technician)
            .WithMany()
            .HasForeignKey(e => e.TechnicianId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}