using System.Collections.Immutable;
using System.Text.Json.Nodes;

namespace Ethos.Utilities;

public record Issue(ImmutableHashSet<string> Paths, string Message, Guid? IssueId, JsonObject? Data)
{
    public Issue(string path, string message, Guid? issueId = null, JsonObject? data = null) 
        : this(ImmutableHashSet.Create(path), message, issueId, data) { }

    public Issue PrependPath(string field)
    {
        var newPaths = Paths.Select(p =>
        {
            if (p.StartsWith("[")) // If the path starts with a bracket, it's an index
                return $"{field}{p}";
            else // Otherwise, it's a property name
                return $"{field}.{p}";
        });
        return this with { Paths = ImmutableHashSet.CreateRange(newPaths) };
    }

    public Issue PrependIndex(int index)
    {
        var newPaths = Paths.Select(p => $"[{index}]{p}");
        return this with { Paths = ImmutableHashSet.CreateRange(newPaths) };
    }
}

public class Validated<TResult>
{
    public ImmutableList<Issue>? Errors { get; }
    public ImmutableList<Issue>? Warnings { get; }
    public TResult? Value { get; }
    
    public bool IsSuccess => Errors is null;
    public bool IsFailure => !IsSuccess;
    public bool HasWarnings => Warnings is not null && Warnings.Any();
    
    public Validated<TNewResult> Map<TNewResult>(Func<TResult, TNewResult> map)
    {
        if (IsFailure)
            return Validated<TNewResult>.Failure(Errors!, Warnings);
        
        if (Value is null)
            throw new InvalidOperationException("Invalid State: Cannot map a null value");
        
        return Validated<TNewResult>.Success(map(Value), Warnings);
    }
    
    private Validated(ImmutableList<Issue>? errors, ImmutableList<Issue>? warnings, TResult? value)
    {
        if ((errors is null || errors.IsEmpty) && value is null)
            throw new InvalidOperationException("Value cannot be null if there are no errors");
        Errors = errors;
        Warnings = warnings;
        Value = value;
    }
    
    public static Validated<TResult> Success(TResult value, ImmutableList<Issue>? warnings = null) =>
        new(null, warnings, value);
    public static Validated<TResult> Failure(ImmutableList<Issue> errors, ImmutableList<Issue>? warnings = null) =>
        new(errors, warnings, default);
}