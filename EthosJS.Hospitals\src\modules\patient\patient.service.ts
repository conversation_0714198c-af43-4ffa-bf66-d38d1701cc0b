import { Transactional } from 'typeorm-transactional-cls-hooked';
import { BadRequestException, Injectable } from '@nestjs/common';
import { PatientEntity } from '@app/modules/patient/patient.entity';
import { PatientRepository } from '@app/modules/patient/patient.repository';
import { CreatePatientDto } from '@app/modules/patient/dto/create.patient.dto';

@Injectable()
export class PatientService {
  constructor(
    private readonly repository: PatientRepository,
  ) {}

  async getByIdOrFail(patientId: number): Promise<PatientEntity> {
    const patient = await this.repository.findOne({
      where: {
        id: patientId,
      },
    });

    if (!patient) {
      throw new BadRequestException('Study is not found');
    }

    return patient;
  }

  @Transactional()
  async upsert({ externalId, name }: CreatePatientDto): Promise<PatientEntity> {
    let patient = await this.repository.findOne({ externalId });

    const hasSameName = patient?.name === name;

    if (!patient) {
      patient = this.repository.create({ externalId, name });
    }

    patient.name = name;

    await patient.save();

    if (hasSameName) {
      await this.repository.updatePatientName(patient.id, patient.name);
    }

    return patient;
  }
}
