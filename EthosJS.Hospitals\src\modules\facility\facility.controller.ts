import { AuthGuard } from '@app/common/guards/auth.guard';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { FacilityService } from '@app/modules/facility/facility.service';
import { FacilityCollectionDto } from '@app/modules/facility/dto/facility.collection.dto';
import { FacilityFiltersDto } from '@app/modules/facility/dto/facility.filters.dto';
import { UpdateFacilityDto } from '@app/modules/facility/dto/update.facility.dto';
import { CreateFacilityDto } from '@app/modules/facility/dto/create.facility.dto';
import { DeleteFacilityDto } from '@app/modules/facility/dto/delete.facility.dto';
import { FacilityDto } from '@app/modules/facility/dto/facility.dto';
import { FacilityConflictsDto } from '@app/modules/facility/dto/facility.conflicts.dto';
import { GetConflictsFacilityDto } from '@app/modules/facility/dto/getConflicts.facility.dto';

@Controller('facility')
@ApiTags('Facilities')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class FacilityController {
  constructor(private readonly service: FacilityService) {
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: FacilityCollectionDto,
    description: 'Get list of facilities',
  })
  async list(@Query() filters: FacilityFiltersDto): Promise<FacilityCollectionDto> {
    return this.service.list(filters);
  }

  @Get('/:facilityId')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: FacilityDto,
    description: 'Get facility by id',
  })
  async getById(@Param('facilityId', new ParseIntPipe()) facilityId: number): Promise<FacilityDto> {
    const item = await this.service.getByIdOrFail(facilityId);

    return FacilityService.mapToDto(item);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOkResponse({
    type: FacilityDto,
    description: 'Create facility',
  })
  async create(@Body() facility: CreateFacilityDto): Promise<FacilityDto> {
    const item = await this.service.create(facility);

    return FacilityService.mapToDto(item);
  }

  @Put()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: FacilityDto,
    description: 'Update facility',
  })
  async update(@Body() update: UpdateFacilityDto): Promise<FacilityDto> {
    const item = await this.service.update(update);

    return FacilityService.mapToDto(item);
  }

  @Post('/conflicts')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: FacilityDto,
    description: 'Get list of conflicted schedules',
  })
  async getConflicts(@Body() update: GetConflictsFacilityDto): Promise<FacilityConflictsDto> {
    const conflicts = await this.service.getConflicts(update);

    return { conflicts };
  }

  @Delete()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: FacilityDto,
    description: 'Delete facility',
  })
  async delete(@Body() { id }: DeleteFacilityDto): Promise<FacilityDto> {
    const item = await this.service.delete(id);

    return FacilityService.mapToDto(item);
  }
}
