import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class LoginResponse {
  @ApiProperty()
  id: string;

  @ApiProperty()
  uid: number;

  @ApiProperty()
  access_token: string;

  @ApiProperty()
  token_type: string;

  @ApiProperty()
  rights: string[];

  @ApiProperty()
  name: string;

  @ApiPropertyOptional()
  expires_in?: number | undefined;

  @ApiPropertyOptional()
  refresh_token?: string | undefined;
}
