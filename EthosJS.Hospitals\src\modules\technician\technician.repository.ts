import moment from 'moment';
import { EntityRepository } from 'typeorm';
import { BadRequestException } from '@nestjs/common';
import { TechnicianEntity } from '@app/modules/technician/technician.entity';
import { IListResult } from '@app/common/types';
import { TechnicianFiltersDto } from '@app/modules/technician/dto/technician.filters.dto';
import { BaseRepository } from '@app/common/base.repository';
import { TechnicianCollectionDto } from '@app/modules/technician/dto/technician.collection.dto';
import { TechnicianCredentialEntity } from '@app/modules/technician/technician.credential.entity';
import { ISchedule } from '@app/modules/schedule/types';
import { convertRawSchedule } from '@app/modules/schedule/helpers';

@EntityRepository(TechnicianEntity)
export class TechnicianRepository extends BaseRepository<TechnicianEntity> {
  collectionDto = TechnicianCollectionDto;

  async list(filters: TechnicianFiltersDto): Promise<IListResult<TechnicianEntity>> {
    return super.list(filters, ['clinic', 'credentials', 'credentials.credential']);
  }

  async checkHasSchedules(technicianId: number): Promise<void> {
    const today = moment().startOf('day');

    const [{ count }] = await this.query('SELECT COUNT(*) as count FROM schedules WHERE date >= $1 AND technician_id = $2 AND deleted_at IS NULL', [today, technicianId]);

    if (Number(count) > 0) {
      throw new BadRequestException('Technician has related schedules');
    }
  }

  async getTechnicianSchedules(technicianId: number): Promise<ISchedule[]> {
    const today = moment().startOf('day');
    const result = await this.query('SELECT * FROM schedules WHERE date >= $1 AND technician_id = $2 AND deleted_at IS NULL', [today, technicianId]);

    return result.map(convertRawSchedule);
  }

  async updateSchedulesCapacity(technicianId: number, capacity: number): Promise<void> {
    const today = moment().startOf('day');
    await this.query('UPDATE technician_schedules SET capacity = $3 WHERE capacity IS NOT NULL AND capacity > $3 AND date >= $1 AND technician_id = $2 AND deleted_at IS NULL', [today, technicianId, capacity]);
  }

  async updateTechnicianName(technicianId: number, name: string): Promise<void> {
    const today = moment().startOf('day');
    await this.query('UPDATE schedules SET technician_name = $3 WHERE date >= $1 AND technician_id = $2 AND deleted_at IS NULL', [today, technicianId, name]);
  }
}

@EntityRepository(TechnicianCredentialEntity)
export class TechnicianCredentialRepository extends BaseRepository<TechnicianCredentialEntity> {}