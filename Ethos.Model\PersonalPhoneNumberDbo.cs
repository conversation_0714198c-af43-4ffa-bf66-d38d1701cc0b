using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

// Represents Phone contact information for a Patient
public class PersonalPhoneNumberDbo : IAuditableEntity<PersonalPhoneNumberDbo>
{
    public Guid ParentId { get; set; }
    public virtual PersonalContactDetailDbo Parent { get; set; } = null!;

    public long/*PhoneType*/ Type { get; set; }
    public string PhoneNumber { get; set; } = null!;
    public long/*PhoneUse*/ Use { get; set; }
    public bool AllowsTextMessages { get; set; }
    public bool AllowsVoice { get; set; }
    public bool IsPreferred { get; set; }
    public long PreferredTimeId { get; set; }

    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<PersonalPhoneNumberDbo>(Register);
    public new static void Register(EntityTypeBuilder<PersonalPhoneNumberDbo> entity)
    {
        IAuditableEntity<PersonalPhoneNumberDbo>.Register(entity);

        entity.Property(e => e.PhoneNumber).IsRequired().HasMaxLength(20);
        entity.Property(e => e.Type).IsRequired().HasConversion<string>().HasMaxLength(50);
        entity.Property(e => e.Use).IsRequired().HasConversion<string>().HasMaxLength(50);
        // Unique index might be complex due to Type/Use, consider application logic

        entity.HasOne(e => e.Parent)
            .WithMany(p => p.PhoneNumbers)
            .HasForeignKey(e => e.ParentId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}