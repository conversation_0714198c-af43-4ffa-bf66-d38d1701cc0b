﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Builder;
using System.Text.Json;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.DependencyInjection;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Http.Extensions;

namespace Ethos.Events.Client
{
    /// <summary>
    /// 
    /// </summary>
    public class ApiUsageEventData
    {
        public string[] Endpoints { get; set; } = [];
        public long RequestSize { get; set; }
        public long ResponseSize { get; set; }
    }

    /// <summary>
    /// API usage logging middleware.
    /// </summary>
    public class ApiUsageLogger : IMiddleware
    {
        readonly ILogger<ApiUsageLogger> _logger;
        readonly string? _eventSystemUrl, _middlewareDisableReason;
        readonly bool _enableUsageTracking, _trackDataUsage;
        readonly IEthosEventClient? _httpClient;
        JsonSerializerOptions? jsonOptions;
        string? _token, _tokenScheme;

        /// <summary>
        /// JSON serialization options.
        /// </summary>
        public JsonSerializerOptions JsonOptions
        {
            get
            {
                jsonOptions ??= new()
                {
                    AllowTrailingCommas = true,
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                    PropertyNameCaseInsensitive = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                return jsonOptions;
            }

            set
            {
                jsonOptions = value;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="httpClient"></param>
        /// <param name="options"></param>
        public ApiUsageLogger(ILogger<ApiUsageLogger> logger, IEthosEventClient httpClient, IOptions<ApiUsageLoggingOptions> options)
        {
            _logger = logger;
            _httpClient = httpClient;
            _enableUsageTracking = options.Value.Enabled;
            _trackDataUsage = options.Value.TrackDataUsage;
            _eventSystemUrl = options.Value.EventServiceUrl;
          
            if (!_enableUsageTracking)
                _middlewareDisableReason = "Disabled by configuration";
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="context"></param>
        /// <param name="next"></param>
        /// <returns></returns>
        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            var endpointStr = context.Request.Path.Value ?? "<Unknown>";

            if (!_enableUsageTracking)
            {
                _logger.LogInformation("API usage logging is not enabled on endpoint {Endpoint}: {Reason}",
                                        endpointStr, _middlewareDisableReason ?? "Unknown reason");
                await next(context);
                return;
            }

            var endpoint = context.GetEndpoint();
            if (endpoint is null)
            {
                _logger.LogWarning("No endpoint found: cannot track API usage.");
                await next(context);
                return;
            }

            if (string.IsNullOrEmpty(_eventSystemUrl) || _httpClient is null)
            {
                _logger.LogWarning("Invalid event system configuration: cannot send usage detail to event service.");
                return;
            }

            _httpClient.BaseUri = _eventSystemUrl;

            bool executedNext = false;

            try
            {
                // get the current token
                var tokenVal = context.Request.Headers.Authorization.FirstOrDefault();
                if (!string.IsNullOrEmpty(tokenVal))
                {
                    var tokenVals = tokenVal.Split(' ');
                    if (tokenVals?.Length >= 2)
                    {
                        _token = tokenVals[1];
                        _tokenScheme = tokenVals[0];
                    }
                }

                long requestSize = 0, responseSize = 0;

                if (_trackDataUsage)
                {
                    if (context.Request.ContentLength.HasValue)
                        requestSize = context.Request.ContentLength.Value;
                    else if (context.Request.Body is not null && context.Request.Body.CanSeek)
                    {
                        // Calculate the request body size
                        requestSize = await GetStreamLength(context.Request.Body);

                        // IMPORTANT: Reset the position of the stream to the beginning
                        context.Request.Body.Position = 0;
                    }

                    // capture the response body
                    var originalBodyStream = context.Response.Body;
                    using (var responseBody = new MemoryStream())
                    {
                        context.Response.Body = responseBody;
                        await next(context);
                        executedNext = true;
                        responseBody.Seek(0, SeekOrigin.Begin);
                        responseSize = responseBody.Length;
                        await responseBody.CopyToAsync(originalBodyStream);
                    }
                    context.Response.Body = originalBodyStream;
                }

                try
                {
                    if (!string.IsNullOrEmpty(_token))
                        _httpClient.BearerToken = _token;

                    var eventId = await _httpClient.CreateEventWithData("ApiCall", $"API Call: {context.Request.GetDisplayUrl()}",
                        new ApiUsageEventData()
                        {
                            RequestSize = requestSize,
                            ResponseSize = responseSize,
                            Endpoints = [context.Request.GetDisplayUrl()]
                        }, context);

                    if (eventId.HasValue && eventId.Value != default)
                        _logger.LogDebug("Created API usage event with ID {EventId}", eventId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error sending usage data to event system: {Message}", ex.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during API usage logging.");
            }

            if (!executedNext)
                await next(context);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="stream"></param>
        /// <returns></returns>
        static async Task<long> GetStreamLength(Stream stream)
        {
            long originalPosition = stream.Position;
            stream.Position = 0;

            long length = 0;
            using (var memoryStream = new MemoryStream())
            {
                await stream.CopyToAsync(memoryStream);
                length = memoryStream.Length;
            }

            stream.Position = originalPosition;
            return length;
        }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ApiUsageLoggingOptions
    {
        public bool Enabled { get; set; }
        public bool TrackDataUsage { get; set; }
        public string? EventServiceUrl { get; set; }   
    }

    /// <summary>
    /// 
    /// </summary>
    public static class ApiUsageLoggingExtensions
    {
        // Config option names
        const string ApiUsageLogging = nameof(ApiUsageLogging);
        const string Enabled = nameof(Enabled);
        const string TrackDataUsage = nameof(TrackDataUsage);
        const string EventServiceUrl = nameof(EventServiceUrl);
       
        /// <summary>
        /// 
        /// </summary>
        /// <param name="builder"></param>
        /// <returns></returns>
        public static IApplicationBuilder UseApiUsageLogging(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<ApiUsageLogger>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="serviceCollection"></param>
        /// <param name="configuration"></param>
        /// <param name="customTenantIdRetrievalFunc"></param>
        /// <returns></returns>
        public static IServiceCollection ConfigureApiUsageLogging(this IServiceCollection serviceCollection,
                                                                  IConfiguration configuration)
        {
            return serviceCollection.Configure<ApiUsageLoggingOptions>((options) =>
            {
                options.Enabled = configuration.GetValue($"{ApiUsageLogging}:{Enabled}", false);
                options.TrackDataUsage = configuration.GetValue($"{ApiUsageLogging}:{TrackDataUsage}", false);
                options.EventServiceUrl = configuration.GetValue<string>($"{ApiUsageLogging}:{EventServiceUrl}");
            }).AddScoped<ApiUsageLogger>();

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="serviceCollection"></param>
        /// <param name="optionsBuilder"></param>
        /// <returns></returns>
        public static IServiceCollection ConfigureApiUsageLogging(this IServiceCollection serviceCollection,
                                                                  Action<ApiUsageLoggingOptions> optionsBuilder)
        {
            return serviceCollection.Configure(optionsBuilder);
        }
    }
}
