﻿// <auto-generated />
using System;
using System.Collections.Generic;
using Ethos.TenantConfig;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Ethos.TenantConfig.Migrations
{
    [DbContext(typeof(ConfigDbContext))]
    [Migration("20250327044907_DynamicMigration_638786261379694045")]
    partial class DynamicMigration_638786261379694045
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Ethos.TenantConfig.TenantConfigSection", b =>
                {
                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<Dictionary<string, TenantConfigSecretValue>>("Secrets")
                        .HasColumnType("jsonb");

                    b.Property<Dictionary<string, TenantConfigStorageValue>>("Storage")
                        .HasColumnType("jsonb");

                    b.Property<Dictionary<string, TenantConfigValue>>("Values")
                        .HasColumnType("jsonb");

                    b.HasKey("Name", "TenantId");

                    b.ToTable("TenantConfig", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
