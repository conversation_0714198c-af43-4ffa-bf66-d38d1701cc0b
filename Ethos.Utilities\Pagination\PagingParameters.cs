﻿

namespace Ethos.Utilities.Pagination
{
    /// <summary>
    /// 
    /// </summary>
    public class PagingParameters
    {
        /// <summary>
        /// 
        /// </summary>
        public const int MaxPagingLimit = 500;

        /// <summary>
        /// 
        /// </summary>
        public int offset { get; set; } = 0;

        int _limit = 250;

        /// <summary>
        /// 
        /// </summary>
        public int limit
        {
            get => _limit;
            set => _limit = (value > MaxPagingLimit) ? MaxPagingLimit : value;
        }
    }
}
