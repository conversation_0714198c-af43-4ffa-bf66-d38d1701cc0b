using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

// Represents Emergency Contact information for a Patient
public class PersonalEmergencyContactDbo : IAuditableEntity<PersonalEmergencyContactDbo>
{
    public Guid ParentId { get; set; }
    public virtual PersonalContactDetailDbo Parent { get; set; } = null!;

    public virtual PersonNameDbo Name { get; set; }

    public long/*EmergencyContactRelationship*/ RelationshipId { get; set; }
    public string ContactInformation { get; set; } = null!;

    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<PersonalEmergencyContactDbo>(Register);
    public new static void Register(EntityTypeBuilder<PersonalEmergencyContactDbo> entity)
    {
        IAuditableEntity<PersonalEmergencyContactDbo>.Register(entity);

        entity.OwnsOne(p => p.Name, PersonNameDbo.Configure);
        entity.Property(e => e.ContactInformation).IsRequired().HasMaxLength(20);

        entity.HasOne(e => e.Parent)
            .WithMany(p => p.EmergencyContacts)
            .HasForeignKey(e => e.ParentId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}