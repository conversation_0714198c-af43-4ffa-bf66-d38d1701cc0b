{"compilerOptions": {"strict": true, "module": "commonjs", "declaration": false, "noImplicitAny": true, "removeComments": true, "noLib": false, "allowSyntheticDefaultImports": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "es2017", "sourceMap": true, "allowJs": true, "outDir": "./dist", "baseUrl": "./src", "paths": {"@app/*": ["./*"]}, "moduleResolution": "node", "skipLibCheck": true, "downlevelIteration": true, "strictPropertyInitialization": false, "jsx": "preserve", "noImplicitReturns": true, "noUnusedLocals": false, "noUnusedParameters": false, "noStrictGenericChecks": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "typeRoots": ["./types", "./node_modules/@types"]}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.spec.ts"]}