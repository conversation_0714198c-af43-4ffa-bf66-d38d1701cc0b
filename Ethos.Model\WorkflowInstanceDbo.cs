using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public record WorkflowInstanceDbo : IEntity<WorkflowInstanceDbo>
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    
    public string WorkflowKey { get; set; }
    
    public DateTime? CompletedAt { get; set; }
    public DateTime? AbortedAt { get; set; }
    
    public string CurrentState { get; set; }
    public int CurrentSequence { get; set; }
    public string? TransientErrorJson { get; set; }
    
    public WorkflowInstanceDbo(
        string workflowKey)
    {
        Id = Guid.NewGuid();
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
        WorkflowKey = workflowKey;
        CurrentState = "null";
        CurrentSequence = 0;
        TransientErrorJson = null;
        CompletedAt = null;
        AbortedAt = null;
    }
    
    public bool IsAborted => AbortedAt.HasValue;
    public bool IsCompleted => CompletedAt.HasValue;
    
    public virtual ICollection<WorkflowEntityLinkDbo> EntityLinks { get; set; } = new List<WorkflowEntityLinkDbo>();

    public static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema)
            .Entity<WorkflowInstanceDbo>(Register);
    
    public static void Register(EntityTypeBuilder<WorkflowInstanceDbo> entity)
    {
        entity.ToTable("WorkflowInstances");
        entity.HasKey(i => i.Id);

        entity.Property(i => i.CurrentState)
            .IsRequired();

        // optional: JSON field for transient errors
        entity.Property(i => i.TransientErrorJson)
            .HasColumnType("text"); // or use PostgreSQL jsonb if you prefer

        entity.Property(i => i.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP");
        entity.Property(i => i.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP");
        entity.Property(i => i.CompletedAt)
            .IsRequired(false);
        entity.Property(i => i.AbortedAt)
            .IsRequired(false);
    }
}

public record WorkflowEntityLinkDbo : IEntity<WorkflowEntityLinkDbo>
{
    public int Id { get; set; }
    public required Guid InstanceId { get; set; }
    public required string EntityType { get; set; }
    public required Guid EntityId { get; set; }
    
    public virtual WorkflowInstanceDbo WorkflowInstance { get; set; } = default!;
    
    public static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema)
            .Entity<WorkflowEntityLinkDbo>(Register);

    public static void Register(EntityTypeBuilder<WorkflowEntityLinkDbo> entity)
    {
        entity.ToTable("WorkflowEntityContexts");
        entity.HasKey(e => e.Id);
            
        entity.HasOne(e => e.WorkflowInstance)
            .WithMany(i => i.EntityLinks)
            .HasForeignKey(e => e.InstanceId)
            .OnDelete(DeleteBehavior.Cascade);
            
        entity.Property(e => e.EntityType)
            .HasMaxLength(100)
            .IsRequired();
            
        entity.Property(e => e.EntityId)
            .IsRequired();
            
        entity.HasIndex(e => e.InstanceId)
            .HasDatabaseName($"IX_{nameof(WorkflowEntityLinkDbo)}_InstanceId");
        entity.HasIndex(e => new { e.EntityType, e.EntityId })
            .HasDatabaseName($"IX_{nameof(WorkflowEntityLinkDbo)}_EntityType_EntityId");
    }
}

public record WorkflowTransitionDbo : IEntity<WorkflowTransitionDbo>
{
    public Guid WorkflowInstanceId { get; set; }
    public int SequenceId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    
    public string TransitionName { get; set; }
    public string TransitionData { get; set; }
    public string NewStateName { get; set; }
    public string NewStateData { get; set; }
    
    // Navigation properties:
    public WorkflowInstanceDbo WorkflowInstance { get; set; } = default!;
    
    public WorkflowTransitionDbo(
        Guid workflowInstanceId,
        int sequenceId,
        string transitionName,
        string transitionData,
        string newStateName,
        string newStateData)
    {
        WorkflowInstanceId = workflowInstanceId;
        SequenceId = sequenceId;
        TransitionName = transitionName;
        TransitionData = transitionData;
        NewStateName = newStateName;
        NewStateData = newStateData;
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    public static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema)
            .Entity<WorkflowTransitionDbo>(Register);
    
    public static void Register(EntityTypeBuilder<WorkflowTransitionDbo> entity)
    {
        entity.ToTable("WorkflowInstanceTransitions");
    
        // Define composite key using WorkflowInstanceId and SequenceId.
        entity.HasKey(t => new { t.WorkflowInstanceId, t.SequenceId });

        // Configure required properties.
        entity.Property(t => t.TransitionName)
            .IsRequired();

        entity.Property(t => t.TransitionData)
            .IsRequired();

        // Configure timestamp fields with default values.
        entity.Property(t => t.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(t => t.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        // Set up the relationship to WorkflowInstance.
        entity.HasOne(t => t.WorkflowInstance)
            .WithMany() // If WorkflowInstance had a collection navigation, you could specify it here.
            .HasForeignKey(t => t.WorkflowInstanceId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

public record WorkflowDraftTransitionDbo : IEntity<WorkflowDraftTransitionDbo>
{
    public Guid WorkflowInstanceId { get; set; }
    public string TransitionName { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string TransitionData { get; set; }
    
    public WorkflowDraftTransitionDbo(
        Guid workflowInstanceId,
        string transitionName,
        string transitionData)
    {
        WorkflowInstanceId = workflowInstanceId;
        TransitionName = transitionName;
        TransitionData = transitionData;
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }
    
    public static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema)
            .Entity<WorkflowDraftTransitionDbo>(Register);
    
    public static void Register(EntityTypeBuilder<WorkflowDraftTransitionDbo> entity)
    {
        entity.ToTable("WorkflowDraftTransitions");
        entity.HasKey(t => new { t.WorkflowInstanceId, t.TransitionName });
        entity.Property(t => t.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        entity.Property(t => t.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        entity.Property(t => t.TransitionData).IsRequired();
    }
}