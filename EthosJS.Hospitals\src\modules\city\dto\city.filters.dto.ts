import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, IsPositive, IsString } from 'class-validator';
import { BaseFiltersDto } from '@app/common/dto/base.filters.dto';
import { ECitySort } from '@app/modules/city/enums';

export class CityFiltersDto extends BaseFiltersDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  @IsPositive()
  stateId?: number;

  @ApiPropertyOptional({ enum: ECitySort })
  @IsOptional()
  @IsEnum(ECitySort)
  orderField: ECitySort = ECitySort.CreatedAt;
}
