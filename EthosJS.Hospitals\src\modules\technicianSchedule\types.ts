import { FindOperator } from 'typeorm';
import { ETechnicianScheduleShift, ETechnicianScheduleSort } from '@app/modules/technicianSchedule/enums';
import { StudyCredentialEntity } from '@app/modules/study/study.credential.entity';
import { EOrderDirection } from '@app/common/enums';
import { FacilityEntity } from '@app/modules/facility/facility.entity';

export interface IGetAvailableTechnicianParams {
    facility: FacilityEntity;
    shift: ETechnicianScheduleShift;
    date: string;
    technicianCounts: Record<number, number>;
    studyCredentials: StudyCredentialEntity[];
}

export interface ITechnicianScheduleFilters {
    technicianId?: number;
    facilityId?: number | FindOperator<any>;
    facilityIds?: number[];
    clinicId?: number;
    shift?: ETechnicianScheduleShift;
    dateFrom?: string;
    dateTo?: string;
    orderField?: ETechnicianScheduleSort;
    offset?: number;
    limit?: number;
    orderDirection?: EOrderDirection;
}