import { Column, Entity, ManyToOne } from 'typeorm';
import { BaseEntity } from '@app/common/base.entity';
import { PatientEntity } from '@app/modules/patient/patient.entity';
import { TechnicianEntity } from '@app/modules/technician/technician.entity';
import { StudyEntity } from '@app/modules/study/study.entity';
import { FacilityEntity } from '@app/modules/facility/facility.entity';
import { EDayWeek, EShift } from '@app/common/enums';
import { IScheduleEquipment } from '@app/modules/schedule/types';

@Entity({ name: 'schedules' })
export class ScheduleEntity extends BaseEntity {
  @Column()
  technicianId: number;

  @Column()
  technicianName: string;

  @ManyToOne(
    () => TechnicianEntity,
    technician => technician.schedules,
  )
  technician: TechnicianEntity;

  @Column()
  patientId: number;

  @Column()
  patientName: string;

  @ManyToOne(
    () => PatientEntity,
    patient => patient.schedules,
  )
  patient: PatientEntity;

  @Column()
  studyId: number;

  @Column()
  studyName: string;

  @ManyToOne(
    () => StudyEntity,
    study => study.schedules,
  )
  study: StudyEntity;

  @Column()
  facilityId: number;

  @Column()
  facilityName: string;

  @ManyToOne(
    () => FacilityEntity,
    facility => facility.schedules,
  )
  facility: FacilityEntity;

  @Column({ enum: EShift, type: 'enum', enumName: 'shift_enum' })
  shift: EShift;

  @Column({ type: 'date' })
  date: string;

  @Column({ type: 'enum', enum: EDayWeek })
  weekday: EDayWeek;

  @Column({ type: 'jsonb', default: '[]' })
  credentials: number[];

  @Column({ type: 'jsonb', default: '{}' })
  equipments: Record<number, IScheduleEquipment>
}
