import { AuthGuard } from '@app/common/guards/auth.guard';
import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { CityService } from '@app/modules/city/city.service';
import { CityFiltersDto } from '@app/modules/city/dto/city.filters.dto';
import { CityCollectionDto } from '@app/modules/city/dto/city.collection.dto';
import { CityEntity } from '@app/modules/city/city.entity';
import { CreateCityDto } from '@app/modules/city/dto/create.city.dto';

@Controller('city')
@ApiTags('Cities')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class CityController {
  constructor(private readonly service: CityService) {
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: CityCollectionDto,
    description: 'Get list of cities',
  })
  async list(@Query() filters: CityFiltersDto): Promise<CityCollectionDto> {
    return this.service.list(filters);
  }

  @Get('/:cityId')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: CityEntity,
    description: 'Get city by id',
  })
  async getById(@Param('cityId', new ParseIntPipe()) cityId: number): Promise<CityEntity> {
    return this.service.getByIdOrFail(cityId);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOkResponse({
    type: CityEntity,
    description: 'Create city',
  })
  async create(@Body() dto: CreateCityDto): Promise<CityEntity> {
    return this.service.create(dto);
  }
}
