using Ethos.Model;

namespace Ethos.Workflows.Api;

public sealed record CreatePatientAppointmentConfirmationDto : IInputDto
{
    public required Guid AppointmentId { get; set; }
    public required long ConfirmationTypeId { get; set; }
    public required DateTime ConfirmationDate { get; set; }
}

public sealed record PatientAppointmentConfirmationDto
{
    public required Guid Id { get; set; }
    public required Guid AppointmentId { get; set; }
    public required long ConfirmationTypeId { get; set; }
    public required DateTime ConfirmationDate { get; set; }
}

public interface IPatientAppointmentConfirmationApi : IEntityHttpClient<CreatePatientAppointmentConfirmationDto, PatientAppointmentConfirmationDto, PatientAppointmentConfirmationQ>;

public class PatientAppointmentConfirmationHttpClient(System.Net.Http.HttpClient httpClient)
    : EntityHttpClientBase<CreatePatientAppointmentConfirmationDto, PatientAppointmentConfirmationDto, PatientAppointmentConfirmationQ>(httpClient, "patientappointmentconfirmation"),
        IPatientAppointmentConfirmationApi;