using Ethos.Model.Scheduling;

namespace Ethos.Workflows.Api;

public sealed record CreateSchedulingConstraintDto : IInputDto
{
    [CheckString(100)]
    public required string Name { get; set; }
    [CheckString(500)]
    public required string Description { get; set; }
    public required Expr Expression { get; set; }
    public required bool IsHardConstraint { get; set; }
}

public sealed record SchedulingConstraintDto
{
    public required Guid Id { get; set; }
    [CheckString(100)]
    public required string Name { get; set; }
    [CheckString(500)]
    public required string Description { get; set; }
    public required Expr Expression { get; set; }
    public required bool IsHardConstraint { get; set; }
}

public interface ISchedulingConstraintApi : IEntityHttpClient<CreateSchedulingConstraintDto, SchedulingConstraintDto, SchedulingConstraintQ>;

public class SchedulingConstraintHttpClient(System.Net.Http.HttpClient httpClient)
    : EntityHttpClientBase<CreateSchedulingConstraintDto, SchedulingConstraintDto, SchedulingConstraintQ>(httpClient, "schedulingconstraint"),
        ISchedulingConstraintApi;