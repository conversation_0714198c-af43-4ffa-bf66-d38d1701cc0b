//@ts-nocheck
import '@axmit/persante-hospitals';
import { IRequestSuccess } from 'common/models';

export interface IEquipmentsCollectionPayload {}
export interface IEquipmentsCollectionDto  {}
export interface IEquipmentModelCreatePayload extends  IRequestSuccess {}
export interface IEquipmentModelUpdatePayload extends  IRequestSuccess {}
export interface IEquipmentModelDeletePayload extends IRequestSuccess {}
export interface IEquipmentModelDto  {}

export interface IEquipmentsCollection {
  data: IEquipmentsCollectionDto | null;
  loading: boolean;
}

export interface IEquipmentModel {
  data: IEquipmentModelDto | null;
  loading: boolean;
}
