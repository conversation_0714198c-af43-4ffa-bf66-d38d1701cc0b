import { Column, Entity, OneToMany } from 'typeorm';
import { BaseEntity } from '@app/common/base.entity';
import { ScheduleEntity } from '@app/modules/schedule/schedule.entity';
import { StudyCredentialEntity } from '@app/modules/study/study.credential.entity';
import { IStudyEquipment } from '@app/modules/study/types';

@Entity({ name: 'studies' })
export class StudyEntity extends BaseEntity {
  @Column()
  name: string;

  @Column({ type: 'jsonb', default: '{}' })
  equipments: Record<number, IStudyEquipment>

  @OneToMany(
    () => ScheduleEntity,
    schedule => schedule.study,
  )
  schedules: ScheduleEntity[];

  @OneToMany(
    () => StudyCredentialEntity,
    studyCredential => studyCredential.study,
  )
  credentials: StudyCredentialEntity[];
}
