pipelines:

  branches:
    dev:
      - step:
          name: Publish packages
          script:
            - cd model
            - echo "//registry.npmjs.org/:_authToken=$NPM_TOKEN" > .npmrc
            - npm version patch
            - npm publish
            - git add package.json
            - git commit -m "Increase package version [skip ci]"
            - git push
          condition:
            changesets:
              includePaths:
                - "model/type.d.ts"
                - "model/package.json"

      - step:
          name: Build dependencies
          image: node:14
          script:
            - echo "//registry.npmjs.org/:_authToken=$NPM_TOKEN" > .npmrc
            - yarn install
            - yarn compile
          caches:
            - node
          artifacts:
            - dist/**

      - step:
          name: Build image
          image: atlassian/default-image:3
          script:
            - IMAGE_NAME=celeri/regenesis-crm-back:dev
            - docker build . --file .deploy/Dockerfile --tag $IMAGE_NAME
            - docker login -u $REGISTRY_USER -p $REGISTRY_PASSWORD
            - docker push $IMAGE_NAME
          services:
            - docker
          caches:
            - docker

      - step:
          name: Pack files
          image: atlassian/default-image:3
          script:
          - cd .beanstalk
          - sed -i "s|%ENVIRONMENT_NAME%|dev|g" .ebextensions/02-filebeat-setup.config
          - zip -r ../dockerrun.zip Dockerrun.aws.json .ebextensions/*
          - zip -r ../dockerrun.zip Dockerrun.aws.json
          artifacts:
          - dockerrun.zip

      - step:
          name: Deploy dev env to AWS EBS
          script:
            - pipe: atlassian/aws-elasticbeanstalk-deploy:1.0.2
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: 'us-east-1'
                S3_BUCKET: 'elasticbeanstalk-us-east-1-071468513294'
                APPLICATION_NAME: 'regenesis-crm'
                ENVIRONMENT_NAME: 'regenesis-crm-dev'
                ZIP_FILE: 'dockerrun.zip'


    master:
      - step:
          name: Build dependencies
          image: node:14
          script:
            - echo "//registry.npmjs.org/:_authToken=$NPM_TOKEN" > .npmrc
            - yarn install
            - yarn compile
          caches:
            - node
          artifacts:
            - dist/**

      - step:
          name: Build image
          image: atlassian/default-image:3
          script:
            - IMAGE_NAME=celeri/regenesis-crm-back:master
            - docker build . --file .deploy/Dockerfile --tag $IMAGE_NAME
            - docker login -u $REGISTRY_USER -p $REGISTRY_PASSWORD
            - docker push $IMAGE_NAME
          services:
            - docker
          caches:
            - docker