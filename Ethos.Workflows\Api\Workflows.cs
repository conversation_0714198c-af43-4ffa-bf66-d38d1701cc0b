using System.Linq.Expressions;
using Ethos.Model;

namespace Ethos.Workflows.Api;


public sealed record CreateDefinitionRequest
{
    public string Name { get; init; } = default!;
    public int VersionId { get; init; }
    public string RawDefinition { get; init; } = default!;
}

public sealed record WorkflowDefinitionDto
{
    public int Id { get; init; }
    public string Name { get; init; } = default!;
    public int VersionId { get; init; }
    public string RawDefinition { get; init; } = default!;
    public DateTime CreatedAt { get; init; }
    public DateTime UpdatedAt { get; init; }
}

// For updating existing definitions (possibly similar to Create)
public sealed record UpdateDefinitionRequest
{
    public string RawDefinition { get; init; } = default!;
    // If you want to rename or increment versions, you can add fields
}

public sealed record StartWorkflowRequest
{
    public string DefinitionName { get; init; } = default!;
    public int VersionId { get; init; }
    public Guid UserId { get; init; }
    public object? InitialData { get; init; } // optional
}

public sealed record WorkflowInstanceDto
{
    public Guid Id { get; init; }
    public int WorkflowDefinitionId { get; init; }
    public string CurrentState { get; init; } = default!;
    public int VersionId { get; init; }
    public DateTime CreatedAt { get; init; }
    public DateTime UpdatedAt { get; init; }
    public string? TransientErrorJson { get; init; }
}

public abstract record WorkflowInstanceQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : WorkflowInstanceQ;

    public sealed record WithDefinitionId(Guid Id) : WorkflowInstanceQ;

    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithId id => Expression.Equal(Expression.Property(self, nameof(WorkflowInstanceDto.Id)), Expression.Constant(id.Id)),
            WithDefinitionId id => Expression.Equal(Expression.Property(self, nameof(WorkflowInstanceDto.WorkflowDefinitionId)), Expression.Constant(id.Id)),
            _ => throw new NotImplementedException()
        };
    }
}

public sealed record ExecuteTransitionRequest
{
    public Guid InstanceId { get; init; }
    public string TransitionName { get; init; } = default!;
    public Guid UserId { get; init; }
    public object? InputData { get; init; }
}

public sealed record MigrateWorkflowRequest
{
    public int OldDefinitionId { get; init; }
    public int NewDefinitionId { get; init; }
    // Additional fields if you want partial or conditional migration
}

// If you want to retrieve tasks for a user:
public sealed record MyTasksRequest
{
    public Guid UserId { get; init; }
}

public sealed record MyTasksResponse
{
    // Possibly just return the full instance objects
    public List<WorkflowInstanceDto> Instances { get; init; } = new();
}


public interface IWorkflowApi
{
    public Task<WorkflowInstanceDto> StartWorkflowAsync(StartWorkflowRequest request);

    public Task<WorkflowInstanceDto> ExecuteTransitionAsync(ExecuteTransitionRequest request);

    public Task<List<WorkflowInstanceDto>> GetAllInstancesAsync(QueryDto<WorkflowInstanceQ> query);

    public Task<WorkflowInstanceDto?> GetInstanceAsync(Guid instanceId);

    public Task<MyTasksResponse> GetMyTasksAsync(MyTasksRequest request);
}