using Ethos.Model;
using Ethos.Workflows.Api;
using Ethos.Workflows.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Controllers;



[Authorize]
[ApiController]
[Route("api/[controller]")]
public class RoomController(DbContext dbContext)
    : EntityControllerBase<RoomDbo, CreateRoomDto, RoomDto, RoomQ>(dbContext)
{
    protected override RoomDto MapToDto(RoomDbo dbo)
    {
        return new RoomDto
        {
            Id = dbo.Id,
            CareLocationId = dbo.CareLocationId,
            Name = dbo.Name,
            SupportedStudyTypes = dbo.SupportedStudyTypes?.ToList()
        };
    }

    protected override RoomDbo CreateOrUpdateEntity(RoomDbo? entity, CreateRoomDto input,
        Guid? requiredId = null)
    {
        if (entity == null)
        {
            if (input.CareLocationId == null)
                throw new ArgumentNullException(nameof(input.CareLocationId), "CareLocationId is required for a new Room.");
            var careLocation = _dbContext.Set<CareLocationDbo>()
                .FirstOrDefault(p => p.Id == input.CareLocationId);
            if (careLocation == null)
                throw new RequiredEntityDoesNotExistException(EntityType.CareLocation, input.CareLocationId.Value);

            entity = new RoomDbo
            {
                Id = requiredId ?? Guid.NewGuid(),
                Name = input.Name ?? throw new ArgumentNullException(nameof(input.Name)),
                CareLocationId = input.CareLocationId ?? throw new ArgumentNullException(nameof(input.CareLocationId)),
                SupportedStudyTypes = input.SupportedStudyTypes?.ToList(),
                Equipment = []
            };
        }
        else
        {
            if (input.Name != null) entity.Name = input.Name;
            if (input.CareLocationId != null) entity.CareLocationId = input.CareLocationId.Value;
            if (input.SupportedStudyTypes != null)
            {
                entity.SupportedStudyTypes = input.SupportedStudyTypes.ToList();
            }
        }

        return entity;
    }
}