using System.Linq.Expressions;
using System.Reflection;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

[Owned]
public class PersonNameDbo
{
    public required string FirstName { get; set; }
    public required string? MiddleName { get; set; }
    public required string LastName { get; set; }
    public long? PrefixId { get; set; } // Mr., Dr., etc.
    public long? SuffixId { get; set; } // Jr., III, etc.

    // Configuration logic to be called from OwnsMany builder
    public static void Configure<TOwner>(OwnedNavigationBuilder<TOwner, PersonNameDbo> builder) where TOwner : class
    {
        builder.Property(p => p.FirstName).IsRequired().HasMaxLength(100);
        builder.Property(p => p.MiddleName).HasMaxLength(100);
        builder.Property(p => p.LastName).IsRequired().HasMaxLength(100);
        builder.Property(p => p.PrefixId).IsRequired(false); // Optional
        builder.Property(p => p.SuffixId).IsRequired(false); // Optional
    }
}