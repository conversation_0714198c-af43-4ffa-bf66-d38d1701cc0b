﻿using System.Reflection;
using System.Text.RegularExpressions;

namespace Ethos.ReferenceData.Client
{
    /// <summary>
    /// 
    /// </summary>
    public static partial class AttributeExtensions
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="member"></param>
        /// <returns></returns>
        public static ReferenceDataSetMappingDetailDto? GetReferenceDataExternalSetDetail(this MemberInfo member, bool inherit = true)
        {
            var attributes = member.GetCustomAttributes<ReferenceDataMapToExternalSetAttribute>(inherit);
            var attribute = attributes.FirstOrDefault();
            if (attribute is null)
                return default;

            return new ReferenceDataSetMappingDetailDto()
            {
                SetName = attribute.SetName,
                Version = attribute.Version,
                Alias = string.IsNullOrEmpty(attribute.Alias) ? member.Name.CleanName() : attribute.Alias,
            };
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static bool HasExternalSetDependency(this Type type)
        {
            return type.GetExternalSetDependencies().Count > 0;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static List<ReferenceDataSetMappingDetailDto> GetExternalSetDependencies(this Type type)
        {
            var allProps = type.GetProperties(BindingFlags.Public |
                                              BindingFlags.Instance |
                                              BindingFlags.FlattenHierarchy)
                               .Cast<MemberInfo>()
                               .Union(type.GetFields(BindingFlags.Public |
                                                     BindingFlags.Instance |
                                                     BindingFlags.FlattenHierarchy)
                                          .Cast<MemberInfo>())
                               .ToArray();

            var results = allProps.Select(p => p.GetReferenceDataExternalSetDetail(true));
            return [.. results.Where(r => r is not null).Cast<ReferenceDataSetMappingDetailDto>()];
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="member"></param>
        /// <param name="inherit"></param>
        /// <returns></returns>
        public static bool ReferenceDataWillIgnore(this MemberInfo member, bool inherit = true)
        {
            return member.GetCustomAttributes(typeof(ReferenceDataIgnoreAttribute), inherit).Length > 0;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static ReferenceDataSetDto? GetReferenceDataSetDetail(this Type type)
        {
            var attributes = type.GetCustomAttributes<ReferenceDataSetAttribute>();
            var attribute = attributes.FirstOrDefault();
            if (attribute is null)
                return default;

            if (string.IsNullOrEmpty(attribute.SetName))
                attribute.SetName = type.Name.CleanTypeName();

            return new ReferenceDataSetDto()
            {
                Name = attribute.SetName,
                Version = attribute.Version,
                Authority = attribute.Authority,
                Source = attribute.Source,
                Key = attribute.Key,
            };
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static string? GetReferenceDataSetKey(this Type type)
        {
            var def = type.GetReferenceDataSetDetail();

            if (!string.IsNullOrEmpty(def?.Key))
                return def.Key;

            var attributes = type.GetCustomAttributes<ReferenceDataSetKeyAttribute>().ToList();

            if (attributes.Count > 1)
                throw new Exception("Only one key can be defined in a reference data set.");

            var allProps = type.GetProperties(BindingFlags.Public | 
                                              BindingFlags.Instance | 
                                              BindingFlags.FlattenHierarchy)
                               .Cast<MemberInfo>()
                               .Union(type.GetFields(BindingFlags.Public | 
                                                     BindingFlags.Instance | 
                                                     BindingFlags.FlattenHierarchy)
                                          .Cast<MemberInfo>())
                               .ToArray();

            var keyField = allProps.GetReferenceDataSetKey();

            if (!string.IsNullOrEmpty(keyField))
                return keyField;

            var propsWithoutIgnore = allProps.Where(p => p.GetCustomAttribute<ReferenceDataIgnoreAttribute>(true) is null);

            if (propsWithoutIgnore.Count() == 1)
                return propsWithoutIgnore.First().Name.CleanName();

            return null;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="properties"></param>
        /// <returns></returns>
        public static string? GetReferenceDataSetKey(this MemberInfo[] properties)
        {
            var property = properties.FirstOrDefault(p => p.GetCustomAttribute<ReferenceDataSetKeyAttribute>() is not null
                                                          && p.GetCustomAttribute<ReferenceDataIgnoreAttribute>(true) is null);

            if (property is not null)
                return property.Name.CleanName();
            return null;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        internal static string CleanTypeName(this string name)
        {
            if (string.IsNullOrEmpty(name))
                throw new ArgumentException($"Invalid name.", nameof(name));

            const string Entity = nameof(Entity);

            if (name.EndsWith(Entity))
                name = name.Replace(Entity, string.Empty);

            return name.CleanName();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        /// <exception cref="Exception"></exception>
        internal static string CleanName(this string name)
        {
            if (string.IsNullOrEmpty(name))
                throw new ArgumentException($"Invalid name.", nameof(name));

            var originalName = name;

            MultipleSpacesRegex().Replace(name, "-");

            if (name.Length == 0)
                throw new Exception($"Invalid name: {originalName}");

            return char.ToLower(name[0]) + name[1..];
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [GeneratedRegex(@"\s+")]
        private static partial Regex MultipleSpacesRegex();
    }
}
