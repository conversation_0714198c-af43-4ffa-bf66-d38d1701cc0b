using Ethos.Model;
using Ethos.Workflows.Api;
using Ethos.Workflows.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class TechnicianAppointmentController(DbContext dbContext)
    : EntityControllerBase<TechnicianAppointmentDbo, CreateTechnicianAppointmentDto, TechnicianAppointmentDto, TechnicianAppointmentQ>(dbContext)
{
    protected override TechnicianAppointmentDto MapToDto(TechnicianAppointmentDbo dbo)
    {
        return new TechnicianAppointmentDto
        {
            Id = dbo.Id,
            StudyId = dbo.StudyId,
            TechnicianId = dbo.TechnicianId,
            RoomId = dbo.RoomId,
            CareLocationShiftId = dbo.CareLocationShiftId,
            Date = dbo.Date
        };
    }

    protected override TechnicianAppointmentDbo CreateOrUpdateEntity(TechnicianAppointmentDbo? entity, CreateTechnicianAppointmentDto input,
        Guid? requiredId = null)
    {
        if (entity == null)
        {
            entity = new TechnicianAppointmentDbo
            {
                Id = requiredId ?? Guid.NewGuid(),
                TechnicianId = input.TechnicianId,
                StudyId = input.StudyId,
                RoomId = input.RoomId,
                CareLocationShiftId = input.CareLocationShiftId,
                Date = input.Date,
            };
        }
        else
        {
            entity.TechnicianId = input.TechnicianId;
            entity.StudyId = input.StudyId;
            entity.RoomId = input.RoomId;
            entity.CareLocationShiftId = input.CareLocationShiftId;
            entity.Date = input.Date;
        }

        return entity;
    }
}