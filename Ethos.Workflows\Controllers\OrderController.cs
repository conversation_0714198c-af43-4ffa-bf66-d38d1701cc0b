using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Ethos.Model;
using Ethos.Workflows.Api;
using Ethos.Workflows.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class OrderController(DbContext dbContext)
    : EntityControllerBase<OrderDbo, CreateOrderDto, OrderDto, OrderQ>(dbContext) 
{
    protected override OrderDto MapToDto(OrderDbo dbo)
    {
        return new OrderDto
        {
            Id = dbo.Id,
            PatientId = dbo.PatientId,
            CareLocationId = dbo.CareLocationId,
            PrimaryCarePhysicianId = dbo.PrimaryCarePhysicianId,
            ReferringPhysicianId = dbo.ReferringPhysicianId,
            InterpretingPhysicianId = dbo.InterpretingPhysicianId
        };
    }

    protected override OrderDbo CreateOrUpdateEntity(OrderDbo? entity, CreateOrderDto input, Guid? requiredId = null)
    {
        if (entity == null)
        {
            entity = new OrderDbo
            {
                Id = requiredId ?? Guid.NewGuid(),
                PatientId = input.PatientId,
                CareLocationId = input.CareLocationId,
                PrimaryCarePhysicianId = input.PrimaryCarePhysicianId,
                ReferringPhysicianId = input.ReferringPhysicianId,
                InterpretingPhysicianId = input.InterpretingPhysicianId
            };
            
            _dbSet.Add(entity);
        }
        else
        {
            entity.PatientId = input.PatientId;
            entity.CareLocationId = input.CareLocationId;
            entity.PrimaryCarePhysicianId = input.PrimaryCarePhysicianId;
            entity.ReferringPhysicianId = input.ReferringPhysicianId;
            entity.InterpretingPhysicianId = input.InterpretingPhysicianId;
        }
        return entity;
    }
}