using Ethos.Auth;
using Ethos.Events.Client;
using Ethos.Utilities.Filtering;
using Ethos.Utilities.Pagination;
using Ethos.Utilities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.PlatformManager.Controllers
{
    [ApiController]
    [Route("api/roles")]
    [Authorize]
    [EthosAuthFeature(Name = "Core")]
    public class RoleController : ControllerBase
    {
        readonly ILogger<RoleController> logger;
        readonly IServiceScopeFactory scopeFactory;
        readonly PlatformManagerDbContext dbContext;
        readonly IConfiguration configuration;
        readonly IEthosEventClient eventClient;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="scopeFactory"></param>
        public RoleController(ILogger<RoleController> logger,
                              IServiceScopeFactory scopeFactory,
                              PlatformManagerDbContext dbContext,
                              IConfiguration configuration,
                              IEthosEventClient eventClient)
        {
            this.logger = logger;
            this.scopeFactory = scopeFactory;
            this.dbContext = dbContext;
            this.configuration = configuration;
            this.eventClient = eventClient;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [EthosAuthScope(ScopeDefinitions.RoleWrite)]
        public async Task<IActionResult> CreateRole(EthosRoleDto role)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(CreateRole), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var isRoleAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.RoleAdmin);
                var tenantId = this.GetRequiredTenantId();

                if (string.IsNullOrEmpty(role.Name))
                    return this.EthosErrorBadRequest("Role name is required.");

                // handle builtin role functionality
                if (BuiltinRole.IsBuiltinName(role.Name))
                {
                    if (!isRoleAdmin)
                        return this.EthosErrorForbidden($"Builtin role '{role.Name}' cannot be created by the current user.");
                    if (!role.Id.HasValue)
                        return this.EthosErrorBadRequest($"ID is required for builtin role '{role.Name}'.");
                    var id = BuiltinRole.GetBuiltinId(role.Name);
                    if (!id.HasValue || role.Id.Value != id.Value)
                        return this.EthosErrorConflict($"Builtin role '{role.Name}' has mismatched ID {role.Id.Value}.");
                }
                else if (role.Id.HasValue && BuiltinRole.IsBuiltin(role.Id.Value))
                {
                    if (!isRoleAdmin)
                        return this.EthosErrorForbidden($"Builtin role '{role.Name}' cannot be created by the current user.");
                    var builtinRole = BuiltinRole.GetBuiltin(role.Id.Value);
                    if (builtinRole is null)
                        return this.EthosErrorBadRequest($"Invalid builtin role '{role.Id.Value}'.");
                    if (!string.Equals(role.Name, builtinRole.Name))
                        return this.EthosErrorConflict($"Builtin role with ID '{role.Id.Value}' has mismatched name '{role.Name}', expecting '{builtinRole.Name}'.");
                }

                if (!role.Id.HasValue || role.Id.Value == Guid.Empty)
                    role.Id = Guid.NewGuid();

                if (dbContext.Roles.Any(r => r.TenantId == tenantId && (r.Id == role.Id.Value || Equals(r.Name.ToLower(), role.Name.ToLower()))))
                    return this.EthosErrorConflict($"Role '{role.Name}' already exists with ID {role.Id.Value}.");

                var newRole = new EthosRole()
                {
                    Name = role.Name,
                    Id = role.Id.Value,
                    TenantId = tenantId
                };

                var scopes = new List<EthosRoleScope>();
                var messages = new List<object>();

                var isScopeAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.ScopeAdmin);

                foreach (var scope in role.Scopes ?? [])
                {
                    if (!EthosScope.TryParse(scope, null, out var _scope) || !_scope.IsFullyQualified())
                    {
                        messages.Add(new { scope, message = "Invalid scope." });
                        continue;
                    }

                    var scopeDef = dbContext.Scopes.FirstOrDefault(s => Equals(s.Name.ToLower(), _scope.ToString().ToLower()));

                    if (scopeDef is null)
                    {
                        logger.LogWarning("Scope does not exist: {Scope}", scope);
                        messages.Add(new { scope, message = "No such scope." });
                        continue;
                    }

                    if (!scopeDef.Assignable && scopeDef.Privileged)
                    {
                        if (!isScopeAdmin)
                        {
                            logger.LogWarning("Skipping non-assignable scope: {Scope}", scope);
                            messages.Add(new { scope, message = "Scope cannot be added to the role because it is globally privileged." });
                            continue;
                        }
                    }
                    else if (!scopeDef.Assignable)
                    {
                        if (!isRoleAdmin)
                        {
                            logger.LogWarning("Skipping non-assignable scope: {Scope}", scope);
                            messages.Add(new { scope, message = "Scope cannot be added to the role because it is not assignable." });
                            continue;
                        }
                    }

                    scopes.Add(new EthosRoleScope()
                    {
                        TenantId = tenantId,
                        RoleId = newRole.Id,
                        Scope = _scope.ToString(),
                        DisplayName = _scope.ToString(),
                    });
                }

                using var trans = dbContext.Database.BeginTransaction();
                try
                {
                    dbContext.Roles.Add(newRole);

                    if (scopes.Count > 0)
                    {
                        foreach (var scope in scopes)
                            dbContext.RoleScopes.Add(scope);
                    }

                    await dbContext.SaveChangesAsync();
                    trans.Commit();

                    return CreatedAtAction(nameof(GetRole), new { roleId = newRole.Id }, new EthosRoleDto()
                    {
                        Id = newRole.Id,
                        Name = newRole.Name,
                        Scopes = [.. newRole.Scopes.Select(scope => scope.Scope)],
                    });
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error creating new role.");
                    trans.Rollback();
                    await eventClient.CreateExceptionEvent(ex);
                    return Problem("Unexpected server error while creating role.", null, 500);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="pagingParameters"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("builtin")]
        [EthosAuthScope(ScopeDefinitions.RoleRead)]
        public IActionResult GetBuiltinRoles()
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetBuiltinRoles), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                return Ok(BuiltinRole.All.Select(r => new EthosRoleSummaryDto()
                {
                    Id = r.Id,
                    Name = r.Name,
                }));
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [Route("{roleId}")]
        [EthosAuthScope(ScopeDefinitions.RoleWrite)]
        public async Task<IActionResult> UpdateRole([FromRoute] Guid roleId, [FromBody] EthosRoleDto role)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(UpdateRole), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (roleId == Guid.Empty)
                    return this.EthosErrorBadRequest("Invalid role ID.");

                var isRoleAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.RoleAdmin);
                var tenantId = this.GetRequiredTenantId();

                var existingRole = dbContext.Roles.FirstOrDefault(r => r.Id == roleId && r.TenantId == tenantId);

                if (existingRole is null)
                    return this.EthosErrorNotFound($"Role with ID {roleId} was not found.");

                // handle builtin role functionality
                if (BuiltinRole.IsBuiltinName(role.Name))
                {
                    if (!isRoleAdmin)
                        return this.EthosErrorForbidden($"Builtin role '{role.Name}' cannot be updated by the current user.");
                    var id = BuiltinRole.GetBuiltinId(role.Name);
                    if (!id.HasValue || roleId != id.Value)
                        return this.EthosErrorConflict($"Builtin role '{role.Name}' has mismatched ID {roleId}.");
                }
                else if (BuiltinRole.IsBuiltin(roleId))
                {
                    if (!isRoleAdmin)
                        return this.EthosErrorForbidden($"Builtin role '{role.Name}' cannot be updated by the current user.");
                    var builtinRole = BuiltinRole.GetBuiltin(roleId);
                    if (builtinRole is null)
                        return this.EthosErrorBadRequest($"Invalid builtin role '{roleId}'.");
                    if (!string.Equals(role.Name, builtinRole.Name))
                        return this.EthosErrorConflict($"Builtin role with ID '{roleId}' has mismatched name '{role.Name}', expecting '{builtinRole.Name}'.");
                }

                if (dbContext.Roles.Any(r => r.TenantId == tenantId && Equals(r.Name.ToLower(), role.Name.ToLower()) && r.Id != roleId))
                    return this.EthosErrorConflict($"Role '{role.Name}' already exists.");

                existingRole.Name = role.Name;

                var messages = new List<object>();

                var scopes = new List<EthosRoleScope>();

                var isScopeAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.ScopeAdmin);

                foreach (var scope in role.Scopes ?? [])
                {
                    if (!EthosScope.TryParse(scope, null, out var _scope) || !_scope.IsFullyQualified())
                    {
                        messages.Add(new { scope, message = "Invalid scope." });
                        continue;
                    }

                    var scopeDef = dbContext.Scopes.FirstOrDefault(s => Equals(s.Name.ToLower(), _scope.ToString().ToLower()));

                    if (scopeDef is null)
                    {
                        logger.LogWarning("Scope does not exist: {Scope}", scope);
                        messages.Add(new { scope, message = "No such scope." });
                        continue;
                    }

                    if (!scopeDef.Assignable && scopeDef.Privileged)
                    {
                        if (!isScopeAdmin)
                        {
                            logger.LogWarning("Skipping non-assignable scope: {Scope}", scope);
                            messages.Add(new { scope, message = "Scope cannot be added to the role because it is globally privileged." });
                            continue;
                        }
                    }
                    else if (!scopeDef.Assignable)
                    {
                        if (!isRoleAdmin)
                        {
                            logger.LogWarning("Skipping non-assignable scope: {Scope}", scope);
                            messages.Add(new { scope, message = "Scope cannot be added to the role because it is not assignable." });
                            continue;
                        }
                    }

                    scopes.Add(new EthosRoleScope()
                    {
                        TenantId = existingRole.TenantId,
                        RoleId = roleId,
                        Scope = _scope.ToString(),
                        DisplayName = scopeDef.Description ?? _scope.ToString()
                    });
                }

                using var trans = dbContext.Database.BeginTransaction();
                try
                {
                    dbContext.Roles.Update(existingRole);

                    if (scopes.Count > 0)
                    {
                        await dbContext.RoleScopes.Where(s => s.RoleId == roleId).ExecuteDeleteAsync();
                        foreach (var scope in scopes)
                            dbContext.RoleScopes.Add(scope);
                    }

                    await dbContext.SaveChangesAsync();
                    trans.Commit();
                    return messages.Count == 0 ? NoContent() : StatusCode(207, messages);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error updating role.");
                    trans.Rollback();
                    await eventClient.CreateExceptionEvent(ex);
                    return Problem("Unexpected server error while updating role.", null, 500);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        [Route("{roleId}")]
        [EthosAuthScope(ScopeDefinitions.RoleDelete)]
        public async Task<IActionResult> DeleteRole([FromRoute] Guid roleId)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(DeleteRole), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (roleId == Guid.Empty)
                    return this.EthosErrorBadRequest("Invalid role ID.");

                var tenantId = this.GetRequiredTenantId();

                if (BuiltinRole.IsBuiltin(roleId) && !HttpContext.User.IsAllowedScope(ScopeDefinitions.RoleAdmin))
                    return this.EthosErrorForbidden($"Builtin role with ID {roleId} cannot be deleted by the current user.");

                var delRole = dbContext.Roles.FirstOrDefault(r => r.Id == roleId && r.TenantId == tenantId);

                if (delRole is null)
                    return this.EthosErrorNotFound($"Role with ID {roleId} was not found.");

                dbContext.Roles.Remove(delRole);
                await dbContext.SaveChangesAsync();
                return NoContent();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("{roleId}/assign/{userId}")]
        [EthosAuthScope(ScopeDefinitions.RoleAssignmentWrite)]
        public async Task<IActionResult> AssignRole([FromRoute] Guid roleId, [FromRoute] Guid userId)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(AssignRole), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (roleId == Guid.Empty)
                    return this.EthosErrorBadRequest("Invalid role ID.");

                if (userId == Guid.Empty)
                    return this.EthosErrorBadRequest("Invalid user ID.");

                var isRoleAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.RoleAdmin);
                var tenantId = this.GetRequiredTenantId();

                var role = dbContext.Roles.FirstOrDefault(r => r.Id == roleId && tenantId == r.TenantId);

                if (role is null)
                    return this.EthosErrorNotFound($"No such role with ID {roleId}.");

                if (role.TenantId != tenantId && !isRoleAdmin)
                    return this.EthosErrorForbidden($"Tenant {role.TenantId} is not accessible to the current user.");

                // does the user already have the role?
                var assignment = dbContext.RoleAssignments.FirstOrDefault(ra => ra.TenantId == tenantId && ra.RoleId == roleId && ra.UserId == userId);

                if (assignment is not null)
                    return NoContent();

                dbContext.RoleAssignments.Add(new EthosRoleAssignment()
                {
                    UserId = userId,
                    TenantId = tenantId,
                    RoleId = roleId,
                });

                await dbContext.SaveChangesAsync();
                return NoContent();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="roleId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{roleId}/assign/{userId}")]
        [EthosAuthScope(ScopeDefinitions.RoleAssignmentDelete)]
        public async Task<IActionResult> UnssignRole([FromRoute] Guid roleId, [FromRoute] Guid userId)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(UnssignRole), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (roleId == Guid.Empty)
                    return this.EthosErrorBadRequest("Invalid role ID.");

                if (userId == Guid.Empty)
                    return this.EthosErrorBadRequest("Invalid user ID.");

                var tenantId = this.GetRequiredTenantId();

                var role = dbContext.Roles.FirstOrDefault(r => r.Id == roleId && tenantId == r.TenantId);

                if (role is null)
                    return this.EthosErrorNotFound($"No such role with ID {roleId}.");

                // does the user have the role?
                var assignment = dbContext.RoleAssignments.FirstOrDefault(ra => ra.TenantId == tenantId && ra.RoleId == roleId && ra.UserId == userId);

                if (assignment is null)
                    return this.EthosErrorNotFound($"User is not assigned to role '{role.Name}'");

                dbContext.RoleAssignments.Remove(assignment);
                await dbContext.SaveChangesAsync();
                return NoContent();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="roleId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("assignments/{userId}")]
        [EthosAuthScope(ScopeDefinitions.RoleAssignmentRead)]
        public IActionResult GetRoleAssignments([FromRoute] Guid userId)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetRoleAssignments), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (userId == Guid.Empty)
                    return this.EthosErrorBadRequest("Invalid user ID.");

                var isRoleReadAll = HttpContext.User.IsAllowedScope(ScopeDefinitions.RoleAssignmentReadAll);
                var tenantId = this.GetRequiredTenantId();

                if (userId != HttpContext.User.GetRequiredUniqueId() && !isRoleReadAll)
                    return this.EthosErrorForbidden("User does not have permission to view role assignments for other users.");

                var assignments = dbContext.RoleAssignments.Include(ra => ra.Role)
                                                           .ThenInclude(r => r.Scopes)
                                                           .Where(ra => ra.TenantId == tenantId && ra.UserId == userId)
                                                           .OrderBy(ra => ra.RoleId);

                return Ok(new EthosUserRoleAssignmentDto
                {
                    UserId = userId,
                    Roles = [.. assignments.Select(a => new EthosRoleAssignmentSummaryDto()
                    {
                        Id = a.RoleId,
                        Name = a.Role.Name,
                        TenantId = a.TenantId,
                    }).Distinct()],

                    Scopes = [.. assignments.GroupBy(a => a.TenantId).Select(a => new EthosScopeAssignmentSummaryDto() {
                        TenantId = a.Key,
                        EffectiveScopes = a.SelectMany(r => r.Role.Scopes.Select(s => s.ToString()).Cast<string>().Distinct()).ToArray()
                    })]

                    //Filters = [.. assignments.SelectMany(a => a.Role.Filters).Distinct()]
                });
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("{roleId}/scopes")]
        [EthosAuthScope(ScopeDefinitions.RoleWrite, ScopeDefinitions.ScopeRead)]
        public async Task<IActionResult> AddScopesToRole([FromRoute] Guid roleId, [FromBody] string[] scopes)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(AddScopesToRole), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (roleId == Guid.Empty)
                    return this.EthosErrorBadRequest("Invalid role ID.");

                var isScopeAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.ScopeAdmin);
                var isRoleAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.RoleAdmin);
                var tenantId = this.GetRequiredTenantId();

                var role = dbContext.Roles.FirstOrDefault(r => r.Id == roleId && tenantId == r.TenantId);

                if (role is null)
                    return this.EthosErrorNotFound($"Role with ID {roleId} was not found.");

                if (BuiltinRole.IsBuiltin(roleId) && !isRoleAdmin)
                    return this.EthosErrorForbidden($"Builtin role with ID {roleId} cannot be modified by the current user.");

                var messages = new List<object>();

                using var trans = dbContext.Database.BeginTransaction();

                try
                {
                    foreach (var scope in scopes)
                    {
                        if (!EthosScope.TryParse(scope, null, out var _scope) || !_scope.IsFullyQualified())
                        {
                            logger.LogWarning("Skipping invalid scope: {Scope}", scope);
                            messages.Add(new { scope, message = "Invalid scope." });
                            continue;
                        }

                        var scopeDef = dbContext.Scopes.FirstOrDefault(s => Equals(s.Name.ToLower(), _scope.ToString().ToLower()));

                        if (scopeDef is null)
                        {
                            logger.LogWarning("Scope does not exist: {Scope}", scope);
                            messages.Add(new { scope, message = "No such scope." });
                            continue;
                        }

                        if (!scopeDef.Assignable && scopeDef.Privileged)
                        {
                            if (!isScopeAdmin)
                            {
                                logger.LogWarning("Skipping non-assignable scope: {Scope}", scope);
                                messages.Add(new { scope, message = "Scope cannot be added to the role because it is globally privileged." });
                                continue;
                            }
                        }
                        else if (!scopeDef.Assignable)
                        {
                            if (!isRoleAdmin)
                            {
                                logger.LogWarning("Skipping non-assignable scope: {Scope}", scope);
                                messages.Add(new { scope, message = "Scope cannot be added to the role because it is not assignable." });
                                continue;
                            }
                        }

                        var scopeExists = dbContext.RoleScopes.FirstOrDefault(rs => rs.RoleId == roleId &&
                                                                              Equals(rs.Scope.ToLower(), _scope.ToString().ToLower())) != null;

                        if (scopeExists)
                        {
                            logger.LogInformation("Scope {Scope} already exists for role {RoleId}", scope, roleId);
                            messages.Add(new { scope = _scope.ToString(), message = "Scope already exists on role." });
                            continue;
                        }

                        dbContext.RoleScopes.Add(new EthosRoleScope()
                        {
                            Scope = _scope.ToString(),
                            RoleId = roleId,
                            TenantId = role.TenantId,
                            DisplayName = _scope.ToString(),
                        });

                        await dbContext.SaveChangesAsync();
                    }

                    await trans.CommitAsync();
                    return messages.Count == 0 ? NoContent() : StatusCode(207, messages);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error adding scope(s) to role {RoleId} for tenant {TenantId}", roleId, tenantId);
                    await trans.RollbackAsync();
                    await eventClient.CreateExceptionEvent(ex);
                    return Problem($"Error adding scope(s) to role: {ex.Message}", null, 500);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="roleId"></param>
        /// <param name="scopes"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{roleId}/scopes")]
        [EthosAuthScope(ScopeDefinitions.RoleWrite, ScopeDefinitions.ScopeRead)]
        public async Task<IActionResult> RemoveScopesFromRole([FromRoute] Guid roleId, [FromBody] string[] scopes, [FromQuery] bool? removeAll = false)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(RemoveScopesFromRole), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (roleId == Guid.Empty)
                    return this.EthosErrorBadRequest("Invalid role ID.");

                var isScopeAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.ScopeAdmin);
                var isRoleAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.RoleAdmin);
                var tenantId = this.GetRequiredTenantId();

                if (BuiltinRole.IsBuiltin(roleId) && !isRoleAdmin)
                    return this.EthosErrorForbidden($"Builtin role with ID {roleId} cannot be modified by the current user.");

                var role = dbContext.Roles.FirstOrDefault(r => r.Id == roleId && tenantId == r.TenantId);

                if (role is null)
                    return this.EthosErrorNotFound($"Role with ID {roleId} was not found.");

                var messages = new List<object>();

                using var trans = dbContext.Database.BeginTransaction();

                try
                {
                    if (removeAll.HasValue && removeAll.Value)
                    {
                        if (!isScopeAdmin)
                            return this.EthosErrorForbidden("Remove all option is not allowed for the current user.");

                        await dbContext.RoleScopes.Where(rs => rs.RoleId == roleId).ExecuteDeleteAsync();
                    }
                    else
                    {
                        foreach (var scope in scopes)
                        {
                            if (!EthosScope.TryParse(scope, null, out var _scope) || !_scope.IsFullyQualified())
                            {
                                logger.LogWarning("Skipping invalid scope: {Scope}", scope);
                                messages.Add(new { scope, message = "Invalid scope." });
                                continue;
                            }

                            var scopeDef = dbContext.Scopes.FirstOrDefault(s => Equals(s.Name.ToLower(), _scope.ToString().ToLower()));

                            var existingScope = dbContext.RoleScopes.FirstOrDefault(rs => rs.RoleId == roleId &&
                                                                                          Equals(rs.Scope.ToLower(), _scope.ToString().ToLower()));

                            if (existingScope is null)
                            {
                                if (scopeDef is not null)
                                {
                                    logger.LogInformation("Scope {Scope} does not exist for role {RoleId}", scope, roleId);
                                    messages.Add(new { scope = _scope.ToString(), message = "Scope does not exist on role." });
                                    continue;
                                }
                                else
                                {
                                    logger.LogWarning("Skipping non-existent scope: {Scope}", scope);
                                    messages.Add(new { scope, message = "No such scope." });
                                    continue;
                                }
                            }
                            else if (scopeDef is not null && scopeDef.Privileged && !scopeDef.Assignable && !isScopeAdmin)
                            {
                                logger.LogWarning("Current user cannot remove privileged scope from role: {Scope}", scope);
                                messages.Add(new { scope, message = "Globally privileged scope cannot be removed from the role by the current user." });
                                continue;
                            }
                            dbContext.RoleScopes.Remove(existingScope);
                        }
                    }

                    await dbContext.SaveChangesAsync();
                    await trans.CommitAsync();
                    return messages.Count == 0 ? NoContent() : StatusCode(207, messages);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error removing scope(s) from role {RoleId} for tenant {TenantId}", roleId, tenantId);
                    await trans.RollbackAsync();
                    await eventClient.CreateExceptionEvent(ex);
                    return Problem($"Error removing scope(s) from role: {ex.Message}", null, 500);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.RoleRead, ScopeDefinitions.ScopeRead)]
        public async Task<ActionResult<PagedResponse<EthosRoleDto>>> GetRoles([FromQuery] PagingParameters pagingParameters, [FromQuery] string? filter = null)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetRoles), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var tenantId = this.GetRequiredTenantId();

                var roles = dbContext.Roles.Include(r => r.Scopes).Where(r => r.TenantId == tenantId)
                                     .Filter(filter)
                                     .OrderBy(r => r.Name);

                return Ok(await roles.PaginateWithLinksAsync(this, (roles) =>
                {
                    var _roles = new List<EthosRoleDto>();
                    _roles.AddRange(roles.Select(r => new EthosRoleDto()
                    {
                        Id = r.Id,
                        Name = r.Name,
                        Scopes = [.. r.Scopes.Select(s => s.Scope)],
                    }));
                    return _roles;
                }, pagingParameters.limit, pagingParameters.offset));
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("{roleId}")]
        [EthosAuthScope(ScopeDefinitions.RoleRead, ScopeDefinitions.ScopeRead)]
        public async Task<IActionResult> GetRole([FromRoute] Guid roleId)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetRole), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var tenantId = this.GetRequiredTenantId();
                var role = await dbContext.Roles.Include(r => r.Scopes)
                                                .FirstOrDefaultAsync(r => r.TenantId == tenantId && r.Id == roleId);

                if (role is null)
                    return this.EthosErrorNotFound($"Role with ID {roleId} was not found.");

                return Ok(new EthosRoleDto()
                {
                    Id = role.Id,
                    Name = role.Name,
                    Scopes = [.. role.Scopes.Select(s => s.Scope)]
                });
            }
        }
    }
}
