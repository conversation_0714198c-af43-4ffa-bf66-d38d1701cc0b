﻿using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace Ethos.Events
{
    /// <summary>
    /// 
    /// </summary>
    public class EventDbContext : DbContext
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="options"></param>
        public EventDbContext(DbContextOptions<EventDbContext> options) : base(options) { }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<Event> Events { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<EventCorrelationId> CorrelationIds { get; set; }


        private static readonly JsonSerializerSettings jsonSerializerSettings = new()
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            NullValueHandling = NullValueHandling.Ignore,
            Converters = [ new NewtonsoftJsonDynamicObjectConverter() ]
        };

        /// <summary>
        /// 
        /// </summary>
        /// <param name="modelBuilder"></param>
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Event>(entity =>
            {
                entity.ToTable("Events", "Evt");

                entity.HasKey(e => e.Id);

                entity.Property(s => s.Type)
                      .IsRequired()
                      .HasDefaultValue("Unknown");

                entity.Property(s => s.Name)
                      .IsRequired();

                entity.Property(s => s.EventTime)
                      .IsRequired();

                entity.Property(e => e.EventData)
                      .HasColumnType("jsonb")
                      .HasConversion(
                                v => JsonConvert.SerializeObject(v, jsonSerializerSettings),
                                v => JsonConvert.DeserializeObject<object?>(v, jsonSerializerSettings))
                      .IsRequired(false);

                entity
                    .HasMany(ex => ex.CorrelationIds)
                    .WithOne(e => e.Event)
                    .HasForeignKey(ex => ex.EventId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            modelBuilder.Entity<EventCorrelationId>(entity =>
            {
                entity.ToTable("CorrelationIds", "Evt");

                entity.HasKey(s => new { s.EventId, s.Type, s.Value });

                entity.Property(s => s.Type)
                      .IsRequired();

                entity.Property(s => s.Value)
                      .IsRequired();

                entity
                    .HasOne(ex => ex.Event)
                    .WithMany(e => e.CorrelationIds)
                    .HasForeignKey(ex => ex.EventId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }
    }
}
